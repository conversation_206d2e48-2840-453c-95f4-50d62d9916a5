import{ag as at,ak as ot,S as M,i as U,s as Z,$ as z,W as w,J as tt,c as p,Y as g,ac as K,e as x,f as v,a9 as ct,a0 as j,a1 as q,a2 as A,ad as it,u as h,q as F,t as y,r as J,h as C,Z as I,_ as rt,ah as E,al as dt,a as _,j as st,A as et,N as P,b as S,n as H,E as O,F as W,g as pt,a4 as ut,I as Y}from"./SpinnerAugment-BJAAUt-n.js";import{I as $t}from"./IconButtonAugment-CqdkuyT6.js";const nt=Symbol("collapsible");function ft(){return at(nt)}function mt(n,t){const{onStuck:s,onUnstuck:e,offset:a=0}=t,l=document.createElement("div");l.style.position="absolute",l.style.top=a?`${a}px`:"0",l.style.height="1px",l.style.width="100%",l.style.pointerEvents="none",l.style.opacity="0",l.style.zIndex="-1";const c=n.parentNode;if(!c)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(c).position==="static"&&(c.style.position="relative"),c.insertBefore(l,n);const i=new IntersectionObserver(([o])=>{o.isIntersecting?e==null||e():s==null||s()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return i.observe(l),{update(o){t.onStuck=o.onStuck,t.onUnstuck=o.onUnstuck,o.offset!==void 0&&o.offset!==a&&(l.style.top=`${o.offset}px`)},destroy(){i.disconnect(),l.remove()}}}const gt=n=>({}),Q=n=>({}),ht=n=>({}),R=n=>({});function V(n){let t,s,e,a;const l=n[14].default,c=z(l,n,n[13],null);let i=n[9].footer&&X(n);return{c(){t=w("div"),c&&c.c(),s=tt(),i&&i.c(),e=et(),p(t,"class","c-collapsible__body svelte-gbhym3")},m(o,r){x(o,t,r),c&&c.m(t,null),x(o,s,r),i&&i.m(o,r),x(o,e,r),a=!0},p(o,r){c&&c.p&&(!a||8192&r)&&j(c,l,o,o[13],a?A(l,o[13],r,null):q(o[13]),null),o[9].footer?i?(i.p(o,r),512&r&&h(i,1)):(i=X(o),i.c(),h(i,1),i.m(e.parentNode,e)):i&&(F(),y(i,1,1,()=>{i=null}),J())},i(o){a||(h(c,o),h(i),a=!0)},o(o){y(c,o),y(i),a=!1},d(o){o&&(C(t),C(s),C(e)),c&&c.d(o),i&&i.d(o)}}}function X(n){let t,s;const e=n[14].footer,a=z(e,n,n[13],Q);return{c(){t=w("footer"),a&&a.c(),p(t,"class","c-collapsible__footer svelte-gbhym3")},m(l,c){x(l,t,c),a&&a.m(t,null),s=!0},p(l,c){a&&a.p&&(!s||8192&c)&&j(a,e,l,l[13],s?A(e,l[13],c,gt):q(l[13]),Q)},i(l){s||(h(a,l),s=!0)},o(l){y(a,l),s=!1},d(l){l&&C(t),a&&a.d(l)}}}function yt(n){let t,s,e,a,l,c,i,o,r,k,b;const L=n[14].header,m=z(L,n,n[13],R);let $=n[4]&&n[5]&&V(n);return{c(){t=w("div"),s=w("header"),e=w("div"),m&&m.c(),l=tt(),c=w("div"),i=w("div"),$&&$.c(),p(e,"class","c-collapsible__header-inner svelte-gbhym3"),g(e,"is-collapsed",n[3]),g(e,"is-header-stuck",n[0]),g(e,"has-header-padding",n[2]>0),p(s,"class","c-collapsible__header svelte-gbhym3"),g(s,"is-sticky",n[1]),p(i,"class","c-collapsible__content-inner svelte-gbhym3"),p(c,"class","c-collapsible__content svelte-gbhym3"),g(c,"is-collapsed",n[3]),p(t,"class",o="c-collapsible "+n[6]+" svelte-gbhym3"),g(t,"is-collapsed",n[3]),g(t,"is-expandable",n[4]),K(t,"--sticky-header-top",`${n[2]}px`)},m(d,u){x(d,t,u),v(t,s),v(s,e),m&&m.m(e,null),v(t,l),v(t,c),v(c,i),$&&$.m(i,null),r=!0,k||(b=ct(a=mt.call(null,s,{offset:-n[2],onStuck:n[15],onUnstuck:n[16]})),k=!0)},p(d,[u]){m&&m.p&&(!r||8192&u)&&j(m,L,d,d[13],r?A(L,d[13],u,ht):q(d[13]),R),(!r||8&u)&&g(e,"is-collapsed",d[3]),(!r||1&u)&&g(e,"is-header-stuck",d[0]),(!r||4&u)&&g(e,"has-header-padding",d[2]>0),a&&it(a.update)&&5&u&&a.update.call(null,{offset:-d[2],onStuck:d[15],onUnstuck:d[16]}),(!r||2&u)&&g(s,"is-sticky",d[1]),d[4]&&d[5]?$?($.p(d,u),48&u&&h($,1)):($=V(d),$.c(),h($,1),$.m(i,null)):$&&(F(),y($,1,1,()=>{$=null}),J()),(!r||8&u)&&g(c,"is-collapsed",d[3]),(!r||64&u&&o!==(o="c-collapsible "+d[6]+" svelte-gbhym3"))&&p(t,"class",o),(!r||72&u)&&g(t,"is-collapsed",d[3]),(!r||80&u)&&g(t,"is-expandable",d[4]),4&u&&K(t,"--sticky-header-top",`${d[2]}px`)},i(d){r||(h(m,d),h($),r=!0)},o(d){y(m,d),y($),r=!1},d(d){d&&C(t),m&&m.d(d),$&&$.d(),k=!1,b()}}}function kt(n,t,s){let e;const a=["collapsed","stickyHeader","expandable","isHeaderStuck","stickyHeaderTop","toggle"];let l,c,i=I(t,a),{$$slots:o={},$$scope:r}=t;const k=rt(o);let{collapsed:b=!1}=t,{stickyHeader:L=!1}=t,{expandable:m=!0}=t,{isHeaderStuck:$=!1}=t,{stickyHeaderTop:d=-.5}=t;const u=P(b);E(n,u,f=>s(3,l=f));const lt=dt(u,f=>f),T=P(m);E(n,T,f=>s(4,c=f));let N,B=!1;function D(f){m?u.set(f):u.set(!0)}const G=function(){D(!l)};return ot(nt,{collapsed:lt,setCollapsed:D,toggle:G,expandable:T}),n.$$set=f=>{t=_(_({},t),st(f)),s(22,i=I(t,a)),"collapsed"in f&&s(10,b=f.collapsed),"stickyHeader"in f&&s(1,L=f.stickyHeader),"expandable"in f&&s(11,m=f.expandable),"isHeaderStuck"in f&&s(0,$=f.isHeaderStuck),"stickyHeaderTop"in f&&s(2,d=f.stickyHeaderTop),"$$scope"in f&&s(13,r=f.$$scope)},n.$$.update=()=>{16&n.$$.dirty&&s(11,m=c),2048&n.$$.dirty&&T.set(m),8&n.$$.dirty&&s(10,b=l),1024&n.$$.dirty&&u.set(b),2048&n.$$.dirty&&(m||u.set(!0)),1024&n.$$.dirty&&(b?(clearTimeout(N),N=setTimeout(()=>{s(5,B=!1)},200)):(clearTimeout(N),s(5,B=!0))),s(6,{class:e}=i,e)},[$,L,d,l,c,B,e,u,T,k,b,m,G,r,o,()=>{s(0,$=!0)},()=>{s(0,$=!1)}]}class Mt extends M{constructor(t){super(),U(this,t,kt,yt,Z,{collapsed:10,stickyHeader:1,expandable:11,isHeaderStuck:0,stickyHeaderTop:2,toggle:12})}get toggle(){return this.$$.ctx[12]}}function bt(n){let t,s;return{c(){t=S("svg"),s=S("path"),p(s,"fill-rule","evenodd"),p(s,"clip-rule","evenodd"),p(s,"d","M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z"),p(s,"fill","currentColor"),p(t,"width","15"),p(t,"height","15"),p(t,"viewBox","0 0 15 15"),p(t,"fill","none"),p(t,"xmlns","http://www.w3.org/2000/svg")},m(e,a){x(e,t,a),v(t,s)},p:H,i:H,o:H,d(e){e&&C(t)}}}class vt extends M{constructor(t){super(),U(this,t,null,bt,Z,{})}}function xt(n){let t,s,e;return{c(){t=S("svg"),s=S("path"),e=S("path"),p(s,"fill-rule","evenodd"),p(s,"clip-rule","evenodd"),p(s,"d","M5.26045 11.9272C5.07304 12.1146 5.07304 12.4185 5.26045 12.606C5.44792 12.7934 5.75183 12.7934 5.93929 12.606L7.99988 10.5454L10.0605 12.606C10.2479 12.7934 10.5518 12.7934 10.7393 12.606C10.9267 12.4185 10.9267 12.1146 10.7393 11.9272L8.33929 9.52716C8.15184 9.33975 7.84792 9.33975 7.66046 9.52716L5.26045 11.9272Z"),p(s,"fill","currentColor"),p(e,"d","M10.7393 3.39387C10.9267 3.58132 10.9267 3.88524 10.7393 4.07269L8.33929 6.47269C8.24928 6.56271 8.12718 6.61328 7.99988 6.61328C7.87258 6.61328 7.75049 6.56271 7.66046 6.47269L5.26045 4.0727C5.07304 3.88524 5.07304 3.58132 5.26045 3.39387C5.44792 3.20642 5.75183 3.20642 5.93929 3.39387L7.99988 5.45447L10.0605 3.39387C10.2479 3.20642 10.5518 3.20642 10.7393 3.39387Z"),p(e,"fill","currentColor"),p(t,"width","16"),p(t,"height","16"),p(t,"viewBox","0 0 16 16"),p(t,"fill","none"),p(t,"xmlns","http://www.w3.org/2000/svg")},m(a,l){x(a,t,l),v(t,s),v(t,e)},p:H,i:H,o:H,d(a){a&&C(t)}}}class Ct extends M{constructor(t){super(),U(this,t,null,xt,Z,{})}}function wt(n){let t,s;return t=new Ct({}),{c(){O(t.$$.fragment)},m(e,a){W(t,e,a),s=!0},i(e){s||(h(t.$$.fragment,e),s=!0)},o(e){y(t.$$.fragment,e),s=!1},d(e){Y(t,e)}}}function Lt(n){let t,s;return t=new vt({}),{c(){O(t.$$.fragment)},m(e,a){W(t,e,a),s=!0},i(e){s||(h(t.$$.fragment,e),s=!0)},o(e){y(t.$$.fragment,e),s=!1},d(e){Y(t,e)}}}function Ht(n){let t,s,e,a;const l=[Lt,wt],c=[];function i(o,r){return o[0]?0:1}return t=i(n),s=c[t]=l[t](n),{c(){s.c(),e=et()},m(o,r){c[t].m(o,r),x(o,e,r),a=!0},p(o,r){let k=t;t=i(o),t!==k&&(F(),y(c[k],1,1,()=>{c[k]=null}),J(),s=c[t],s||(s=c[t]=l[t](o),s.c()),h(s,1),s.m(e.parentNode,e))},i(o){a||(h(s),a=!0)},o(o){y(s),a=!1},d(o){o&&C(e),c[t].d(o)}}}function St(n){let t,s;const e=[{variant:"ghost-block"},{color:"neutral"},{size:1},n[3]];let a={$$slots:{default:[Ht]},$$scope:{ctx:n}};for(let l=0;l<e.length;l+=1)a=_(a,e[l]);return t=new $t({props:a}),t.$on("click",n[2]),{c(){O(t.$$.fragment)},m(l,c){W(t,l,c),s=!0},p(l,[c]){const i=8&c?pt(e,[e[0],e[1],e[2],ut(l[3])]):{};33&c&&(i.$$scope={dirty:c,ctx:l}),t.$set(i)},i(l){s||(h(t.$$.fragment,l),s=!0)},o(l){y(t.$$.fragment,l),s=!1},d(l){Y(t,l)}}}function _t(n,t,s){const e=[];let a,l=I(t,e);const{collapsed:c,setCollapsed:i}=ft();return E(n,c,o=>s(0,a=o)),n.$$set=o=>{t=_(_({},t),st(o)),s(3,l=I(t,e))},[a,c,function(){i(!a)},l]}class Ut extends M{constructor(t){super(),U(this,t,_t,St,Z,{})}}export{Ut as C,Mt as a,Ct as b,ft as g};
