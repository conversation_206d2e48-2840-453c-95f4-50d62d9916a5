import React from 'react';
import { Message } from '../App';
import { CodeBlock } from './CodeBlock';

interface MessageBubbleProps {
  message: Message;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const { text, sender, timestamp, isError } = message;

  // Parse message for code blocks
  const parseMessageContent = (content: string) => {
    // Simple regex to detect code blocks (```language\ncode\n```)
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const parts: Array<{ type: 'text' | 'code'; content: string; language?: string }> = [];
    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      // Add text before code block
      if (match.index > lastIndex) {
        const textContent = content.slice(lastIndex, match.index).trim();
        if (textContent) {
          parts.push({ type: 'text', content: textContent });
        }
      }

      // Add code block
      parts.push({
        type: 'code',
        content: match[2].trim(),
        language: match[1] || 'javascript'
      });

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      const textContent = content.slice(lastIndex).trim();
      if (textContent) {
        parts.push({ type: 'text', content: textContent });
      }
    }

    // If no code blocks found, return the entire content as text
    if (parts.length === 0) {
      parts.push({ type: 'text', content: content });
    }

    return parts;
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch {
      return '';
    }
  };

  const contentParts = parseMessageContent(text);

  return (
    <div className={`message-bubble ${sender}-message ${isError ? 'error-message' : ''}`}>
      <div className="message-content">
        {contentParts.map((part, index) => (
          <div key={index}>
            {part.type === 'text' ? (
              <div className="message-text">{part.content}</div>
            ) : (
              <CodeBlock
                code={part.content}
                language={part.language || 'javascript'}
              />
            )}
          </div>
        ))}
      </div>
      
      <div className="message-timestamp">
        {formatTimestamp(timestamp)}
      </div>
    </div>
  );
};
