{"D:\\Project\\New folder\\uiorbit\\src\\extension.ts": {"path": "D:\\Project\\New folder\\uiorbit\\src\\extension.ts", "statementMap": {"0": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 7}}, "1": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 7}}, "2": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 33}}, "3": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 59}}, "4": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 40}}, "5": {"start": {"line": 12, "column": 2}, "end": {"line": 33, "column": 3}}, "6": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 59}}, "7": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 46}}, "8": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 31}}, "9": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 61}}, "10": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 6}}, "11": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 65}}, "12": {"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 6}}, "13": {"start": {"line": 40, "column": 2}, "end": {"line": 52, "column": 3}}, "14": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 61}}, "15": {"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}, "16": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 35}}, "17": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 28}}, "18": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 63}}, "19": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 64}}}, "fnMap": {"0": {"name": "activate", "decl": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 30}}, "loc": {"start": {"line": 11, "column": 63}, "end": {"line": 34, "column": 1}}}, "1": {"name": "deactivate", "decl": {"start": {"line": 39, "column": 22}, "end": {"line": 39, "column": 32}}, "loc": {"start": {"line": 39, "column": 32}, "end": {"line": 53, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 37}, "end": {"line": 31, "column": 93}}, "type": "cond-expr", "locations": [{"start": {"line": 31, "column": 62}, "end": {"line": 31, "column": 75}}, {"start": {"line": 31, "column": 78}, "end": {"line": 31, "column": 93}}]}, "1": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 7, "6": 7, "7": 7, "8": 7, "9": 5, "10": 5, "11": 2, "12": 2, "13": 5, "14": 5, "15": 5, "16": 4, "17": 3, "18": 4, "19": 1}, "f": {"0": 7, "1": 5}, "b": {"0": [1, 1], "1": [4]}}, "D:\\Project\\New folder\\uiorbit\\src\\core\\ServiceRegistry.ts": {"path": "D:\\Project\\New folder\\uiorbit\\src\\core\\ServiceRegistry.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 41}}, "1": {"start": {"line": 8, "column": 10}, "end": {"line": 8, "column": 49}}, "2": {"start": {"line": 9, "column": 10}, "end": {"line": 9, "column": 71}}, "3": {"start": {"line": 15, "column": 4}, "end": {"line": 17, "column": 5}}, "4": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 77}}, "5": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 37}}, "6": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 62}}, "7": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "8": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 37}}, "9": {"start": {"line": 32, "column": 20}, "end": {"line": 32, "column": 43}}, "10": {"start": {"line": 33, "column": 4}, "end": {"line": 36, "column": 5}}, "11": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 61}}, "12": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 23}}, "13": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 24}}, "14": {"start": {"line": 44, "column": 20}, "end": {"line": 44, "column": 37}}, "15": {"start": {"line": 45, "column": 4}, "end": {"line": 47, "column": 5}}, "16": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 74}}, "17": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 19}}, "18": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 35}}, "19": {"start": {"line": 62, "column": 20}, "end": {"line": 62, "column": 43}}, "20": {"start": {"line": 63, "column": 4}, "end": {"line": 73, "column": 5}}, "21": {"start": {"line": 65, "column": 20}, "end": {"line": 65, "column": 53}}, "22": {"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}, "23": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 42}}, "24": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 33}}, "25": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 66}}, "26": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 18}}, "27": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 17}}, "28": {"start": {"line": 81, "column": 4}, "end": {"line": 81, "column": 44}}, "29": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 26}}, "30": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 26}}, "31": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 55}}, "32": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 45}}, "33": {"start": {"line": 99, "column": 28}, "end": {"line": 107, "column": 6}}, "34": {"start": {"line": 100, "column": 6}, "end": {"line": 106, "column": 7}}, "35": {"start": {"line": 101, "column": 8}, "end": {"line": 103, "column": 9}}, "36": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 34}}, "37": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 56}}, "38": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 39}}, "39": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 17}}, "40": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 41}}, "41": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 52}}, "42": {"start": {"line": 130, "column": 4}, "end": {"line": 134, "column": 6}}, "43": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 13}}, "loc": {"start": {"line": 7, "column": 0}, "end": {"line": 136, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 10}}, "loc": {"start": {"line": 14, "column": 38}, "end": {"line": 26, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 5}}, "loc": {"start": {"line": 31, "column": 21}, "end": {"line": 38, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 13}}, "loc": {"start": {"line": 43, "column": 29}, "end": {"line": 49, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 5}}, "loc": {"start": {"line": 54, "column": 18}, "end": {"line": 56, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 12}}, "loc": {"start": {"line": 61, "column": 25}, "end": {"line": 75, "column": 3}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": 17}}, "loc": {"start": {"line": 80, "column": 17}, "end": {"line": 82, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 7}}, "loc": {"start": {"line": 87, "column": 7}, "end": {"line": 91, "column": 3}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 7}}, "loc": {"start": {"line": 96, "column": 15}, "end": {"line": 113, "column": 3}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 99, "column": 49}, "end": {"line": 99, "column": 54}}, "loc": {"start": {"line": 99, "column": 65}, "end": {"line": 107, "column": 5}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 118, "column": 10}, "end": {"line": 118, "column": 22}}, "loc": {"start": {"line": 118, "column": 31}, "end": {"line": 120, "column": 3}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 10}}, "loc": {"start": {"line": 125, "column": 10}, "end": {"line": 135, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 17, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 17, "column": 5}}]}, "1": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}]}, "2": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 36, "column": 5}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 36, "column": 5}}]}, "3": {"loc": {"start": {"line": 45, "column": 4}, "end": {"line": 47, "column": 5}}, "type": "if", "locations": [{"start": {"line": 45, "column": 4}, "end": {"line": 47, "column": 5}}]}, "4": {"loc": {"start": {"line": 63, "column": 4}, "end": {"line": 73, "column": 5}}, "type": "if", "locations": [{"start": {"line": 63, "column": 4}, "end": {"line": 73, "column": 5}}]}, "5": {"loc": {"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}, "type": "if", "locations": [{"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}]}, "6": {"loc": {"start": {"line": 101, "column": 8}, "end": {"line": 103, "column": 9}}, "type": "if", "locations": [{"start": {"line": 101, "column": 8}, "end": {"line": 103, "column": 9}}]}, "7": {"loc": {"start": {"line": 119, "column": 11}, "end": {"line": 119, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 11}, "end": {"line": 119, "column": 14}}, {"start": {"line": 119, "column": 18}, "end": {"line": 119, "column": 51}}]}}, "s": {"0": 2, "1": 16, "2": 16, "3": 18, "4": 2, "5": 18, "6": 18, "7": 18, "8": 3, "9": 12, "10": 12, "11": 2, "12": 2, "13": 10, "14": 0, "15": 0, "16": 0, "17": 0, "18": 2, "19": 2, "20": 2, "21": 1, "22": 1, "23": 0, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 19, "30": 19, "31": 19, "32": 19, "33": 19, "34": 3, "35": 3, "36": 3, "37": 1, "38": 19, "39": 19, "40": 19, "41": 18, "42": 0, "43": 2}, "f": {"0": 16, "1": 18, "2": 12, "3": 0, "4": 2, "5": 2, "6": 1, "7": 19, "8": 19, "9": 3, "10": 18, "11": 0}, "b": {"0": [2], "1": [3], "2": [2], "3": [0], "4": [1], "5": [0], "6": [3], "7": [18, 18]}}, "D:\\Project\\New folder\\uiorbit\\src\\core\\UIOrbitExtension.ts": {"path": "D:\\Project\\New folder\\uiorbit\\src\\core\\UIOrbitExtension.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 33}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 52}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 72}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 69}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 60}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 41}}, "6": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 54}}, "7": {"start": {"line": 15, "column": 10}, "end": {"line": 15, "column": 48}}, "8": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 49}}, "9": {"start": {"line": 25, "column": 4}, "end": {"line": 45, "column": 5}}, "10": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 55}}, "11": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 38}}, "12": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 40}}, "13": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 36}}, "14": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 33}}, "15": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 64}}, "16": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 67}}, "17": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 18}}, "18": {"start": {"line": 52, "column": 4}, "end": {"line": 67, "column": 5}}, "19": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 55}}, "20": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 67}}, "21": {"start": {"line": 56, "column": 45}, "end": {"line": 56, "column": 65}}, "22": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 28}}, "23": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 43}}, "24": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 51}}, "25": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 66}}, "26": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 18}}, "27": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 49}}, "28": {"start": {"line": 77, "column": 26}, "end": {"line": 77, "column": 52}}, "29": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 66}}, "30": {"start": {"line": 81, "column": 4}, "end": {"line": 81, "column": 37}}, "31": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 45}}, "32": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 55}}, "33": {"start": {"line": 92, "column": 4}, "end": {"line": 95, "column": 6}}, "34": {"start": {"line": 98, "column": 21}, "end": {"line": 105, "column": null}}, "35": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 36}}, "36": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 46}}, "37": {"start": {"line": 111, "column": 4}, "end": {"line": 111, "column": 57}}, "38": {"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": 53}}, "39": {"start": {"line": 120, "column": 27}, "end": {"line": 120, "column": 67}}, "40": {"start": {"line": 123, "column": 21}, "end": {"line": 133, "column": 6}}, "41": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 34}}, "42": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 46}}, "43": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 37}}, "44": {"start": {"line": 136, "column": 4}, "end": {"line": 139, "column": 7}}, "45": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 37}}, "46": {"start": {"line": 138, "column": 6}, "end": {"line": 138, "column": 47}}, "47": {"start": {"line": 141, "column": 4}, "end": {"line": 141, "column": 49}}, "48": {"start": {"line": 148, "column": 4}, "end": {"line": 148, "column": 49}}, "49": {"start": {"line": 151, "column": 26}, "end": {"line": 156, "column": 6}}, "50": {"start": {"line": 152, "column": 6}, "end": {"line": 155, "column": 7}}, "51": {"start": {"line": 153, "column": 8}, "end": {"line": 153, "column": 67}}, "52": {"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": 35}}, "53": {"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 41}}, "54": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 51}}, "55": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 51}}, "56": {"start": {"line": 168, "column": 4}, "end": {"line": 176, "column": 5}}, "57": {"start": {"line": 169, "column": 28}, "end": {"line": 169, "column": 91}}, "58": {"start": {"line": 170, "column": 6}, "end": {"line": 173, "column": 7}}, "59": {"start": {"line": 171, "column": 8}, "end": {"line": 171, "column": 37}}, "60": {"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 59}}, "61": {"start": {"line": 175, "column": 6}, "end": {"line": 175, "column": 61}}, "62": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_9)", "decl": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 22}}, "loc": {"start": {"line": 17, "column": 54}, "end": {"line": 19, "column": 3}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 7}}, "loc": {"start": {"line": 24, "column": 16}, "end": {"line": 46, "column": 3}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 7}}, "loc": {"start": {"line": 51, "column": 18}, "end": {"line": 68, "column": 3}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 56, "column": 31}, "end": {"line": 56, "column": 41}}, "loc": {"start": {"line": 56, "column": 45}, "end": {"line": 56, "column": 65}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 73, "column": 10}, "end": {"line": 73, "column": 15}}, "loc": {"start": {"line": 73, "column": 34}, "end": {"line": 84, "column": 3}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 89, "column": 10}, "end": {"line": 89, "column": 15}}, "loc": {"start": {"line": 89, "column": 36}, "end": {"line": 112, "column": 3}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 117, "column": 10}, "end": {"line": 117, "column": 15}}, "loc": {"start": {"line": 117, "column": 32}, "end": {"line": 142, "column": 3}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 124, "column": 58}, "end": {"line": 124, "column": 61}}, "loc": {"start": {"line": 124, "column": 63}, "end": {"line": 126, "column": 7}}}, "8": {"name": "(anonymous_17)", "decl": {"start": {"line": 127, "column": 67}, "end": {"line": 127, "column": 68}}, "loc": {"start": {"line": 127, "column": 87}, "end": {"line": 129, "column": 7}}}, "9": {"name": "(anonymous_18)", "decl": {"start": {"line": 130, "column": 61}, "end": {"line": 130, "column": 64}}, "loc": {"start": {"line": 130, "column": 66}, "end": {"line": 132, "column": 7}}}, "10": {"name": "(anonymous_19)", "decl": {"start": {"line": 136, "column": 21}, "end": {"line": 136, "column": 28}}, "loc": {"start": {"line": 136, "column": 31}, "end": {"line": 139, "column": 5}}}, "11": {"name": "(anonymous_20)", "decl": {"start": {"line": 147, "column": 10}, "end": {"line": 147, "column": 29}}, "loc": {"start": {"line": 147, "column": 29}, "end": {"line": 162, "column": 3}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 151, "column": 68}, "end": {"line": 151, "column": 73}}, "loc": {"start": {"line": 151, "column": 76}, "end": {"line": 156, "column": 5}}}, "13": {"name": "(anonymous_22)", "decl": {"start": {"line": 167, "column": 10}, "end": {"line": 167, "column": 15}}, "loc": {"start": {"line": 167, "column": 35}, "end": {"line": 177, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 152, "column": 6}, "end": {"line": 155, "column": 7}}, "type": "if", "locations": [{"start": {"line": 152, "column": 6}, "end": {"line": 155, "column": 7}}]}, "1": {"loc": {"start": {"line": 170, "column": 6}, "end": {"line": 173, "column": 7}}, "type": "if", "locations": [{"start": {"line": 170, "column": 6}, "end": {"line": 173, "column": 7}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 16, "7": 16, "8": 16, "9": 13, "10": 13, "11": 13, "12": 12, "13": 12, "14": 12, "15": 12, "16": 1, "17": 1, "18": 4, "19": 4, "20": 4, "21": 10, "22": 4, "23": 4, "24": 3, "25": 1, "26": 1, "27": 13, "28": 13, "29": 13, "30": 13, "31": 12, "32": 12, "33": 12, "34": 12, "35": 12, "36": 12, "37": 12, "38": 12, "39": 12, "40": 12, "41": 1, "42": 1, "43": 1, "44": 12, "45": 36, "46": 36, "47": 12, "48": 12, "49": 12, "50": 3, "51": 2, "52": 2, "53": 12, "54": 12, "55": 12, "56": 2, "57": 2, "58": 2, "59": 2, "60": 1, "61": 1, "62": 1}, "f": {"0": 16, "1": 13, "2": 4, "3": 10, "4": 13, "5": 12, "6": 12, "7": 1, "8": 1, "9": 1, "10": 36, "11": 12, "12": 3, "13": 2}, "b": {"0": [2], "1": [2]}}, "D:\\Project\\New folder\\uiorbit\\src\\services\\CommandService.ts": {"path": "D:\\Project\\New folder\\uiorbit\\src\\services\\CommandService.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 33}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 41}}, "2": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 54}}, "3": {"start": {"line": 17, "column": 4}, "end": {"line": 29, "column": 5}}, "4": {"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 45}}, "5": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 69}}, "6": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 120}}, "7": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 50}}, "8": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 87}}, "9": {"start": {"line": 36, "column": 4}, "end": {"line": 108, "column": 5}}, "10": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 45}}, "11": {"start": {"line": 40, "column": 27}, "end": {"line": 40, "column": 65}}, "12": {"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}, "13": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 15}}, "14": {"start": {"line": 46, "column": 28}, "end": {"line": 46, "column": 91}}, "15": {"start": {"line": 47, "column": 6}, "end": {"line": 57, "column": 7}}, "16": {"start": {"line": 48, "column": 8}, "end": {"line": 55, "column": 11}}, "17": {"start": {"line": 52, "column": 10}, "end": {"line": 54, "column": 11}}, "18": {"start": {"line": 53, "column": 12}, "end": {"line": 53, "column": 100}}, "19": {"start": {"line": 56, "column": 8}, "end": {"line": 56, "column": 15}}, "20": {"start": {"line": 60, "column": 28}, "end": {"line": 72, "column": 8}}, "21": {"start": {"line": 64, "column": 10}, "end": {"line": 66, "column": 11}}, "22": {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 48}}, "23": {"start": {"line": 67, "column": 10}, "end": {"line": 69, "column": 11}}, "24": {"start": {"line": 68, "column": 12}, "end": {"line": 68, "column": 106}}, "25": {"start": {"line": 70, "column": 10}, "end": {"line": 70, "column": 22}}, "26": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, "27": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 15}}, "28": {"start": {"line": 78, "column": 35}, "end": {"line": 81, "column": 8}}, "29": {"start": {"line": 84, "column": 6}, "end": {"line": 99, "column": 9}}, "30": {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 85}}, "31": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 64}}, "32": {"start": {"line": 93, "column": 37}, "end": {"line": 93, "column": 62}}, "33": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 84}}, "34": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 64}}, "35": {"start": {"line": 96, "column": 37}, "end": {"line": 96, "column": 62}}, "36": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 90}}, "37": {"start": {"line": 101, "column": 6}, "end": {"line": 103, "column": 8}}, "38": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 59}}, "39": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 88}}, "40": {"start": {"line": 115, "column": 4}, "end": {"line": 169, "column": 5}}, "41": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 39}}, "42": {"start": {"line": 118, "column": 21}, "end": {"line": 118, "column": 51}}, "43": {"start": {"line": 119, "column": 6}, "end": {"line": 122, "column": 7}}, "44": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 109}}, "45": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 15}}, "46": {"start": {"line": 124, "column": 24}, "end": {"line": 124, "column": 40}}, "47": {"start": {"line": 125, "column": 27}, "end": {"line": 125, "column": 61}}, "48": {"start": {"line": 127, "column": 6}, "end": {"line": 130, "column": 7}}, "49": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 98}}, "50": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 15}}, "51": {"start": {"line": 133, "column": 28}, "end": {"line": 133, "column": 91}}, "52": {"start": {"line": 134, "column": 6}, "end": {"line": 144, "column": 7}}, "53": {"start": {"line": 135, "column": 8}, "end": {"line": 142, "column": 11}}, "54": {"start": {"line": 139, "column": 10}, "end": {"line": 141, "column": 11}}, "55": {"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 100}}, "56": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 15}}, "57": {"start": {"line": 147, "column": 6}, "end": {"line": 159, "column": 9}}, "58": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 73}}, "59": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 64}}, "60": {"start": {"line": 156, "column": 37}, "end": {"line": 156, "column": 62}}, "61": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 75}}, "62": {"start": {"line": 162, "column": 6}, "end": {"line": 164, "column": 8}}, "63": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": 53}}, "64": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 82}}, "65": {"start": {"line": 176, "column": 28}, "end": {"line": 176, "column": 66}}, "66": {"start": {"line": 177, "column": 4}, "end": {"line": 180, "column": 5}}, "67": {"start": {"line": 178, "column": 6}, "end": {"line": 178, "column": 97}}, "68": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 23}}, "69": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 31}}, "70": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_9)", "decl": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 22}}, "loc": {"start": {"line": 11, "column": 54}, "end": {"line": 11, "column": 58}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 7}}, "loc": {"start": {"line": 16, "column": 16}, "end": {"line": 30, "column": 3}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 7}}, "loc": {"start": {"line": 35, "column": 42}, "end": {"line": 109, "column": 3}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 51, "column": 15}, "end": {"line": 51, "column": 24}}, "loc": {"start": {"line": 51, "column": 27}, "end": {"line": 55, "column": 9}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 24}}, "loc": {"start": {"line": 63, "column": 33}, "end": {"line": 71, "column": 9}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 88, "column": 9}, "end": {"line": 88, "column": 14}}, "loc": {"start": {"line": 88, "column": 28}, "end": {"line": 99, "column": 7}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 93, "column": 26}, "end": {"line": 93, "column": 33}}, "loc": {"start": {"line": 93, "column": 37}, "end": {"line": 93, "column": 62}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 96, "column": 26}, "end": {"line": 96, "column": 33}}, "loc": {"start": {"line": 96, "column": 37}, "end": {"line": 96, "column": 62}}}, "8": {"name": "(anonymous_17)", "decl": {"start": {"line": 114, "column": 2}, "end": {"line": 114, "column": 7}}, "loc": {"start": {"line": 114, "column": 19}, "end": {"line": 170, "column": 3}}}, "9": {"name": "(anonymous_18)", "decl": {"start": {"line": 138, "column": 15}, "end": {"line": 138, "column": 24}}, "loc": {"start": {"line": 138, "column": 27}, "end": {"line": 142, "column": 9}}}, "10": {"name": "(anonymous_19)", "decl": {"start": {"line": 151, "column": 9}, "end": {"line": 151, "column": 14}}, "loc": {"start": {"line": 151, "column": 28}, "end": {"line": 159, "column": 7}}}, "11": {"name": "(anonymous_20)", "decl": {"start": {"line": 156, "column": 26}, "end": {"line": 156, "column": 33}}, "loc": {"start": {"line": 156, "column": 37}, "end": {"line": 156, "column": 62}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 175, "column": 10}, "end": {"line": 175, "column": 15}}, "loc": {"start": {"line": 175, "column": 34}, "end": {"line": 185, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 40, "column": 27}, "end": {"line": 40, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 27}, "end": {"line": 40, "column": 30}}, {"start": {"line": 40, "column": 34}, "end": {"line": 40, "column": 65}}]}, "1": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 43, "column": 7}}]}, "2": {"loc": {"start": {"line": 47, "column": 6}, "end": {"line": 57, "column": 7}}, "type": "if", "locations": [{"start": {"line": 47, "column": 6}, "end": {"line": 57, "column": 7}}]}, "3": {"loc": {"start": {"line": 52, "column": 10}, "end": {"line": 54, "column": 11}}, "type": "if", "locations": [{"start": {"line": 52, "column": 10}, "end": {"line": 54, "column": 11}}]}, "4": {"loc": {"start": {"line": 64, "column": 10}, "end": {"line": 66, "column": 11}}, "type": "if", "locations": [{"start": {"line": 64, "column": 10}, "end": {"line": 66, "column": 11}}]}, "5": {"loc": {"start": {"line": 64, "column": 14}, "end": {"line": 64, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 14}, "end": {"line": 64, "column": 20}}, {"start": {"line": 64, "column": 24}, "end": {"line": 64, "column": 49}}]}, "6": {"loc": {"start": {"line": 67, "column": 10}, "end": {"line": 69, "column": 11}}, "type": "if", "locations": [{"start": {"line": 67, "column": 10}, "end": {"line": 69, "column": 11}}]}, "7": {"loc": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, "type": "if", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}]}, "8": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 122, "column": 7}}, "type": "if", "locations": [{"start": {"line": 119, "column": 6}, "end": {"line": 122, "column": 7}}]}, "9": {"loc": {"start": {"line": 127, "column": 6}, "end": {"line": 130, "column": 7}}, "type": "if", "locations": [{"start": {"line": 127, "column": 6}, "end": {"line": 130, "column": 7}}]}, "10": {"loc": {"start": {"line": 127, "column": 10}, "end": {"line": 127, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 127, "column": 10}, "end": {"line": 127, "column": 23}}, {"start": {"line": 127, "column": 27}, "end": {"line": 127, "column": 59}}]}, "11": {"loc": {"start": {"line": 134, "column": 6}, "end": {"line": 144, "column": 7}}, "type": "if", "locations": [{"start": {"line": 134, "column": 6}, "end": {"line": 144, "column": 7}}]}, "12": {"loc": {"start": {"line": 139, "column": 10}, "end": {"line": 141, "column": 11}}, "type": "if", "locations": [{"start": {"line": 139, "column": 10}, "end": {"line": 141, "column": 11}}]}, "13": {"loc": {"start": {"line": 177, "column": 4}, "end": {"line": 180, "column": 5}}, "type": "if", "locations": [{"start": {"line": 177, "column": 4}, "end": {"line": 180, "column": 5}}]}}, "s": {"0": 2, "1": 2, "2": 18, "3": 6, "4": 6, "5": 6, "6": 3, "7": 3, "8": 3, "9": 6, "10": 6, "11": 6, "12": 6, "13": 0, "14": 6, "15": 6, "16": 4, "17": 4, "18": 0, "19": 4, "20": 2, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 2, "27": 0, "28": 2, "29": 2, "30": 2, "31": 2, "32": 2, "33": 2, "34": 2, "35": 2, "36": 2, "37": 2, "38": 0, "39": 0, "40": 6, "41": 6, "42": 6, "43": 6, "44": 1, "45": 1, "46": 5, "47": 5, "48": 5, "49": 1, "50": 1, "51": 4, "52": 4, "53": 3, "54": 3, "55": 0, "56": 3, "57": 1, "58": 1, "59": 1, "60": 1, "61": 1, "62": 1, "63": 0, "64": 0, "65": 5, "66": 5, "67": 0, "68": 0, "69": 5, "70": 2}, "f": {"0": 18, "1": 6, "2": 6, "3": 4, "4": 0, "5": 2, "6": 2, "7": 2, "8": 6, "9": 3, "10": 1, "11": 1, "12": 5}, "b": {"0": [6, 5], "1": [0], "2": [4], "3": [0], "4": [0], "5": [0, 0], "6": [0], "7": [0], "8": [1], "9": [1], "10": [5, 4], "11": [3], "12": [0], "13": [0]}}, "D:\\Project\\New folder\\uiorbit\\src\\services\\ConfigurationService.ts": {"path": "D:\\Project\\New folder\\uiorbit\\src\\services\\ConfigurationService.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 33}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 29}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 31}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 41}}, "4": {"start": {"line": 12, "column": 10}, "end": {"line": 12, "column": 49}}, "5": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 57}}, "6": {"start": {"line": 20, "column": 4}, "end": {"line": 31, "column": 5}}, "7": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 31}}, "8": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 36}}, "9": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 68}}, "10": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 73}}, "11": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 18}}, "12": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 46}}, "13": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 28}}, "14": {"start": {"line": 46, "column": 4}, "end": {"line": 48, "column": 5}}, "15": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 81}}, "16": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 23}}, "17": {"start": {"line": 56, "column": 19}, "end": {"line": 56, "column": 48}}, "18": {"start": {"line": 57, "column": 22}, "end": {"line": 57, "column": 94}}, "19": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 37}}, "20": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 45}}, "21": {"start": {"line": 73, "column": 4}, "end": {"line": 75, "column": 19}}, "22": {"start": {"line": 82, "column": 4}, "end": {"line": 84, "column": 22}}, "23": {"start": {"line": 91, "column": 21}, "end": {"line": 91, "column": 57}}, "24": {"start": {"line": 92, "column": 24}, "end": {"line": 92, "column": 94}}, "25": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 44}}, "26": {"start": {"line": 101, "column": 20}, "end": {"line": 101, "column": 66}}, "27": {"start": {"line": 102, "column": 23}, "end": {"line": 102, "column": 103}}, "28": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 55}}, "29": {"start": {"line": 111, "column": 26}, "end": {"line": 111, "column": 76}}, "30": {"start": {"line": 112, "column": 29}, "end": {"line": 112, "column": 112}}, "31": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 67}}, "32": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 70}}, "33": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 67}}, "34": {"start": {"line": 135, "column": 4}, "end": {"line": 153, "column": 5}}, "35": {"start": {"line": 136, "column": 30}, "end": {"line": 136, "column": 68}}, "36": {"start": {"line": 137, "column": 6}, "end": {"line": 140, "column": 7}}, "37": {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 78}}, "38": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 15}}, "39": {"start": {"line": 142, "column": 22}, "end": {"line": 142, "column": 67}}, "40": {"start": {"line": 144, "column": 6}, "end": {"line": 150, "column": 7}}, "41": {"start": {"line": 145, "column": 27}, "end": {"line": 145, "column": 62}}, "42": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 41}}, "43": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": 71}}, "44": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 61}}, "45": {"start": {"line": 152, "column": 6}, "end": {"line": 152, "column": 54}}, "46": {"start": {"line": 160, "column": 18}, "end": {"line": 160, "column": 37}}, "47": {"start": {"line": 162, "column": 4}, "end": {"line": 180, "column": 5}}, "48": {"start": {"line": 163, "column": 26}, "end": {"line": 163, "column": 37}}, "49": {"start": {"line": 166, "column": 6}, "end": {"line": 168, "column": 7}}, "50": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 17}}, "51": {"start": {"line": 171, "column": 25}, "end": {"line": 171, "column": 49}}, "52": {"start": {"line": 172, "column": 6}, "end": {"line": 179, "column": 7}}, "53": {"start": {"line": 173, "column": 20}, "end": {"line": 173, "column": 63}}, "54": {"start": {"line": 174, "column": 22}, "end": {"line": 174, "column": 66}}, "55": {"start": {"line": 177, "column": 27}, "end": {"line": 177, "column": 60}}, "56": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 41}}, "57": {"start": {"line": 187, "column": 25}, "end": {"line": 187, "column": 69}}, "58": {"start": {"line": 189, "column": 4}, "end": {"line": 198, "column": 6}}, "59": {"start": {"line": 200, "column": 4}, "end": {"line": 200, "column": 63}}, "60": {"start": {"line": 207, "column": 4}, "end": {"line": 207, "column": 28}}, "61": {"start": {"line": 208, "column": 4}, "end": {"line": 208, "column": 24}}, "62": {"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": 51}}, "63": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_9)", "decl": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 13}}, "loc": {"start": {"line": 10, "column": 0}, "end": {"line": 211, "column": 1}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 7}}, "loc": {"start": {"line": 17, "column": 18}, "end": {"line": 32, "column": 3}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 7}}, "loc": {"start": {"line": 37, "column": 14}, "end": {"line": 40, "column": 3}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 11}}, "loc": {"start": {"line": 45, "column": 11}, "end": {"line": 50, "column": 3}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 17}}, "loc": {"start": {"line": 55, "column": 17}, "end": {"line": 60, "column": 3}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 17}}, "loc": {"start": {"line": 65, "column": 17}, "end": {"line": 67, "column": 3}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 21}}, "loc": {"start": {"line": 72, "column": 21}, "end": {"line": 76, "column": 3}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 19}}, "loc": {"start": {"line": 81, "column": 19}, "end": {"line": 85, "column": 3}}}, "8": {"name": "(anonymous_17)", "decl": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 13}}, "loc": {"start": {"line": 90, "column": 13}, "end": {"line": 95, "column": 3}}}, "9": {"name": "(anonymous_18)", "decl": {"start": {"line": 100, "column": 2}, "end": {"line": 100, "column": 24}}, "loc": {"start": {"line": 100, "column": 24}, "end": {"line": 105, "column": 3}}}, "10": {"name": "(anonymous_19)", "decl": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 27}}, "loc": {"start": {"line": 110, "column": 27}, "end": {"line": 115, "column": 3}}}, "11": {"name": "(anonymous_20)", "decl": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": 17}}, "loc": {"start": {"line": 120, "column": 17}, "end": {"line": 122, "column": 3}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 26}}, "loc": {"start": {"line": 127, "column": 26}, "end": {"line": 129, "column": 3}}}, "13": {"name": "(anonymous_22)", "decl": {"start": {"line": 134, "column": 10}, "end": {"line": 134, "column": 15}}, "loc": {"start": {"line": 134, "column": 27}, "end": {"line": 154, "column": 3}}}, "14": {"name": "(anonymous_23)", "decl": {"start": {"line": 159, "column": 10}, "end": {"line": 159, "column": 25}}, "loc": {"start": {"line": 159, "column": 41}, "end": {"line": 181, "column": 3}}}, "15": {"name": "(anonymous_24)", "decl": {"start": {"line": 186, "column": 10}, "end": {"line": 186, "column": 15}}, "loc": {"start": {"line": 186, "column": 32}, "end": {"line": 201, "column": 3}}}, "16": {"name": "(anonymous_25)", "decl": {"start": {"line": 206, "column": 2}, "end": {"line": 206, "column": 9}}, "loc": {"start": {"line": 206, "column": 9}, "end": {"line": 210, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 4}, "end": {"line": 48, "column": 5}}, "type": "if", "locations": [{"start": {"line": 46, "column": 4}, "end": {"line": 48, "column": 5}}]}, "1": {"loc": {"start": {"line": 59, "column": 11}, "end": {"line": 59, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 59, "column": 11}, "end": {"line": 59, "column": 17}}, {"start": {"line": 59, "column": 21}, "end": {"line": 59, "column": 30}}, {"start": {"line": 59, "column": 34}, "end": {"line": 59, "column": 36}}]}, "2": {"loc": {"start": {"line": 73, "column": 11}, "end": {"line": 75, "column": 18}}, "type": "binary-expr", "locations": [{"start": {"line": 73, "column": 11}, "end": {"line": 73, "column": 43}}, {"start": {"line": 74, "column": 11}, "end": {"line": 74, "column": 87}}, {"start": {"line": 75, "column": 11}, "end": {"line": 75, "column": 18}}]}, "3": {"loc": {"start": {"line": 82, "column": 11}, "end": {"line": 84, "column": 21}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 11}, "end": {"line": 82, "column": 41}}, {"start": {"line": 83, "column": 11}, "end": {"line": 83, "column": 85}}, {"start": {"line": 84, "column": 11}, "end": {"line": 84, "column": 21}}]}, "4": {"loc": {"start": {"line": 94, "column": 11}, "end": {"line": 94, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 94, "column": 11}, "end": {"line": 94, "column": 19}}, {"start": {"line": 94, "column": 23}, "end": {"line": 94, "column": 34}}, {"start": {"line": 94, "column": 38}, "end": {"line": 94, "column": 43}}]}, "5": {"loc": {"start": {"line": 104, "column": 11}, "end": {"line": 104, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 104, "column": 11}, "end": {"line": 104, "column": 28}}, {"start": {"line": 104, "column": 33}, "end": {"line": 104, "column": 53}}]}, "6": {"loc": {"start": {"line": 114, "column": 11}, "end": {"line": 114, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 11}, "end": {"line": 114, "column": 34}}, {"start": {"line": 114, "column": 39}, "end": {"line": 114, "column": 65}}]}, "7": {"loc": {"start": {"line": 121, "column": 20}, "end": {"line": 121, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 121, "column": 20}, "end": {"line": 121, "column": 60}}, {"start": {"line": 121, "column": 64}, "end": {"line": 121, "column": 68}}]}, "8": {"loc": {"start": {"line": 128, "column": 20}, "end": {"line": 128, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 128, "column": 20}, "end": {"line": 128, "column": 58}}, {"start": {"line": 128, "column": 62}, "end": {"line": 128, "column": 65}}]}, "9": {"loc": {"start": {"line": 137, "column": 6}, "end": {"line": 140, "column": 7}}, "type": "if", "locations": [{"start": {"line": 137, "column": 6}, "end": {"line": 140, "column": 7}}]}, "10": {"loc": {"start": {"line": 144, "column": 6}, "end": {"line": 150, "column": 7}}, "type": "if", "locations": [{"start": {"line": 144, "column": 6}, "end": {"line": 150, "column": 7}}, {"start": {"line": 148, "column": 13}, "end": {"line": 150, "column": 7}}]}, "11": {"loc": {"start": {"line": 166, "column": 6}, "end": {"line": 168, "column": 7}}, "type": "if", "locations": [{"start": {"line": 166, "column": 6}, "end": {"line": 168, "column": 7}}]}, "12": {"loc": {"start": {"line": 166, "column": 10}, "end": {"line": 166, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 166, "column": 10}, "end": {"line": 166, "column": 22}}, {"start": {"line": 166, "column": 26}, "end": {"line": 166, "column": 53}}]}, "13": {"loc": {"start": {"line": 172, "column": 6}, "end": {"line": 179, "column": 7}}, "type": "if", "locations": [{"start": {"line": 172, "column": 6}, "end": {"line": 179, "column": 7}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 21, "5": 20, "6": 20, "7": 20, "8": 20, "9": 18, "10": 2, "11": 2, "12": 2, "13": 2, "14": 2, "15": 1, "16": 1, "17": 23, "18": 23, "19": 23, "20": 2, "21": 20, "22": 19, "23": 19, "24": 19, "25": 19, "26": 19, "27": 19, "28": 19, "29": 19, "30": 19, "31": 19, "32": 19, "33": 19, "34": 20, "35": 20, "36": 20, "37": 20, "38": 20, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 20, "58": 18, "59": 18, "60": 2, "61": 2, "62": 2, "63": 2}, "f": {"0": 21, "1": 20, "2": 2, "3": 2, "4": 23, "5": 2, "6": 20, "7": 19, "8": 19, "9": 19, "10": 19, "11": 19, "12": 19, "13": 20, "14": 0, "15": 20, "16": 2}, "b": {"0": [1], "1": [23, 23, 12], "2": [20, 20, 8], "3": [19, 19, 8], "4": [19, 19, 17], "5": [19, 0], "6": [19, 0], "7": [19, 19], "8": [19, 19], "9": [20], "10": [0, 0], "11": [0], "12": [0, 0], "13": [0]}}, "D:\\Project\\New folder\\uiorbit\\src\\utils\\Logger.ts": {"path": "D:\\Project\\New folder\\uiorbit\\src\\utils\\Logger.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 33}}, "1": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": null}}, "2": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": null}}, "3": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "4": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "5": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": null}}, "6": {"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}, "7": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 72}}, "8": {"start": {"line": 30, "column": 19}, "end": {"line": 30, "column": 63}}, "9": {"start": {"line": 31, "column": 22}, "end": {"line": 31, "column": 61}}, "10": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 63}}, "11": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 47}}, "12": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 46}}, "13": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 46}}, "14": {"start": {"line": 60, "column": 23}, "end": {"line": 60, "column": 30}}, "15": {"start": {"line": 62, "column": 4}, "end": {"line": 71, "column": 5}}, "16": {"start": {"line": 63, "column": 6}, "end": {"line": 70, "column": 7}}, "17": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 45}}, "18": {"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": 9}}, "19": {"start": {"line": 66, "column": 10}, "end": {"line": 66, "column": 58}}, "20": {"start": {"line": 69, "column": 8}, "end": {"line": 69, "column": 53}}, "21": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 43}}, "22": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "23": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 13}}, "24": {"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}, "25": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 24}}, "26": {"start": {"line": 91, "column": 22}, "end": {"line": 91, "column": 46}}, "27": {"start": {"line": 92, "column": 21}, "end": {"line": 92, "column": 46}}, "28": {"start": {"line": 95, "column": 27}, "end": {"line": 95, "column": 68}}, "29": {"start": {"line": 97, "column": 4}, "end": {"line": 102, "column": 5}}, "30": {"start": {"line": 98, "column": 22}, "end": {"line": 100, "column": 17}}, "31": {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 76}}, "32": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 40}}, "33": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 53}}, "34": {"start": {"line": 108, "column": 4}, "end": {"line": 123, "column": 5}}, "35": {"start": {"line": 109, "column": 6}, "end": {"line": 122, "column": 7}}, "36": {"start": {"line": 111, "column": 10}, "end": {"line": 111, "column": 42}}, "37": {"start": {"line": 112, "column": 10}, "end": {"line": 112, "column": 16}}, "38": {"start": {"line": 114, "column": 10}, "end": {"line": 114, "column": 41}}, "39": {"start": {"line": 115, "column": 10}, "end": {"line": 115, "column": 16}}, "40": {"start": {"line": 117, "column": 10}, "end": {"line": 117, "column": 41}}, "41": {"start": {"line": 118, "column": 10}, "end": {"line": 118, "column": 16}}, "42": {"start": {"line": 120, "column": 10}, "end": {"line": 120, "column": 42}}, "43": {"start": {"line": 121, "column": 10}, "end": {"line": 121, "column": 16}}, "44": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 31}}, "45": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 32}}, "46": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 26}}, "47": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 54}}, "48": {"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": 25}}, "49": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 34}}, "50": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 35}}, "51": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 13}}, "52": {"start": {"line": 19, "column": 17}, "end": {"line": 19, "column": 52}}, "53": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 20}}}, "fnMap": {"0": {"name": "(anonymous_9)", "decl": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 12}}, "loc": {"start": {"line": 6, "column": 20}, "end": {"line": 11, "column": 1}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 8}}, "loc": {"start": {"line": 24, "column": 19}, "end": {"line": 33, "column": 3}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 8}}, "loc": {"start": {"line": 38, "column": 46}, "end": {"line": 40, "column": 3}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 8}}, "loc": {"start": {"line": 45, "column": 45}, "end": {"line": 47, "column": 3}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 8}}, "loc": {"start": {"line": 52, "column": 45}, "end": {"line": 54, "column": 3}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 8}}, "loc": {"start": {"line": 59, "column": 43}, "end": {"line": 74, "column": 3}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 79, "column": 10}, "end": {"line": 79, "column": 16}}, "loc": {"start": {"line": 79, "column": 69}, "end": {"line": 124, "column": 3}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 98, "column": 31}, "end": {"line": 98, "column": 34}}, "loc": {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 76}}}, "8": {"name": "(anonymous_17)", "decl": {"start": {"line": 129, "column": 2}, "end": {"line": 129, "column": 8}}, "loc": {"start": {"line": 129, "column": 13}, "end": {"line": 131, "column": 3}}}, "9": {"name": "(anonymous_18)", "decl": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": 8}}, "loc": {"start": {"line": 136, "column": 14}, "end": {"line": 138, "column": 3}}}, "10": {"name": "(anonymous_19)", "decl": {"start": {"line": 143, "column": 2}, "end": {"line": 143, "column": 8}}, "loc": {"start": {"line": 143, "column": 36}, "end": {"line": 146, "column": 3}}}, "11": {"name": "(anonymous_20)", "decl": {"start": {"line": 151, "column": 2}, "end": {"line": 151, "column": 8}}, "loc": {"start": {"line": 151, "column": 20}, "end": {"line": 153, "column": 3}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 158, "column": 2}, "end": {"line": 158, "column": 8}}, "loc": {"start": {"line": 158, "column": 16}, "end": {"line": 161, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 20}}, {"start": {"line": 6, "column": 20}, "end": {"line": 6, "column": null}}]}, "1": {"loc": {"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}, "type": "if", "locations": [{"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 5}}]}, "2": {"loc": {"start": {"line": 32, "column": 20}, "end": {"line": 32, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 32, "column": 32}, "end": {"line": 32, "column": 46}}, {"start": {"line": 32, "column": 49}, "end": {"line": 32, "column": 62}}]}, "3": {"loc": {"start": {"line": 62, "column": 4}, "end": {"line": 71, "column": 5}}, "type": "if", "locations": [{"start": {"line": 62, "column": 4}, "end": {"line": 71, "column": 5}}]}, "4": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 70, "column": 7}}, "type": "if", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 70, "column": 7}}, {"start": {"line": 68, "column": 13}, "end": {"line": 70, "column": 7}}]}, "5": {"loc": {"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": 9}}, "type": "if", "locations": [{"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": 9}}]}, "6": {"loc": {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 23}}, {"start": {"line": 65, "column": 27}, "end": {"line": 65, "column": 59}}]}, "7": {"loc": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "type": "if", "locations": [{"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}]}, "8": {"loc": {"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}, "type": "if", "locations": [{"start": {"line": 86, "column": 4}, "end": {"line": 88, "column": 5}}]}, "9": {"loc": {"start": {"line": 97, "column": 4}, "end": {"line": 102, "column": 5}}, "type": "if", "locations": [{"start": {"line": 97, "column": 4}, "end": {"line": 102, "column": 5}}]}, "10": {"loc": {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 76}}, "type": "cond-expr", "locations": [{"start": {"line": 99, "column": 34}, "end": {"line": 99, "column": 62}}, {"start": {"line": 99, "column": 65}, "end": {"line": 99, "column": 76}}]}, "11": {"loc": {"start": {"line": 108, "column": 4}, "end": {"line": 123, "column": 5}}, "type": "if", "locations": [{"start": {"line": 108, "column": 4}, "end": {"line": 123, "column": 5}}]}, "12": {"loc": {"start": {"line": 109, "column": 6}, "end": {"line": 122, "column": 7}}, "type": "switch", "locations": [{"start": {"line": 110, "column": 8}, "end": {"line": 112, "column": 16}}, {"start": {"line": 113, "column": 8}, "end": {"line": 115, "column": 16}}, {"start": {"line": 116, "column": 8}, "end": {"line": 118, "column": 16}}, {"start": {"line": 119, "column": 8}, "end": {"line": 121, "column": 16}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 2, "11": 38, "12": 38, "13": 4, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 0, "20": 0, "21": 1, "22": 81, "23": 38, "24": 43, "25": 0, "26": 43, "27": 43, "28": 43, "29": 43, "30": 0, "31": 0, "32": 0, "33": 43, "34": 43, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 2, "52": 2, "53": 2}, "f": {"0": 2, "1": 2, "2": 38, "3": 38, "4": 4, "5": 1, "6": 81, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [2, 2], "1": [2], "2": [0, 2], "3": [1], "4": [1, 0], "5": [0], "6": [1, 1], "7": [38], "8": [0], "9": [0], "10": [0, 0], "11": [0], "12": [0, 0, 0, 0]}}, "D:\\Project\\New folder\\uiorbit\\src\\webview\\ChatWebviewProvider.ts": {"path": "D:\\Project\\New folder\\uiorbit\\src\\webview\\ChatWebviewProvider.ts", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 41}}, "1": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 46}}, "2": {"start": {"line": 16, "column": 21}, "end": {"line": 16, "column": 53}}, "3": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 29}}, "4": {"start": {"line": 29, "column": 4}, "end": {"line": 35, "column": 6}}, "5": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 76}}, "6": {"start": {"line": 40, "column": 4}, "end": {"line": 44, "column": 6}}, "7": {"start": {"line": 41, "column": 17}, "end": {"line": 41, "column": 44}}, "8": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 54}}, "9": {"start": {"line": 53, "column": 4}, "end": {"line": 71, "column": 5}}, "10": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 62}}, "11": {"start": {"line": 56, "column": 6}, "end": {"line": 68, "column": 7}}, "12": {"start": {"line": 58, "column": 10}, "end": {"line": 58, "column": 53}}, "13": {"start": {"line": 59, "column": 10}, "end": {"line": 59, "column": 16}}, "14": {"start": {"line": 61, "column": 10}, "end": {"line": 61, "column": 42}}, "15": {"start": {"line": 62, "column": 10}, "end": {"line": 62, "column": 16}}, "16": {"start": {"line": 64, "column": 10}, "end": {"line": 64, "column": 56}}, "17": {"start": {"line": 65, "column": 10}, "end": {"line": 65, "column": 16}}, "18": {"start": {"line": 67, "column": 10}, "end": {"line": 67, "column": 61}}, "19": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 61}}, "20": {"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}, "21": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 13}}, "22": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 50}}, "23": {"start": {"line": 85, "column": 4}, "end": {"line": 88, "column": 7}}, "24": {"start": {"line": 90, "column": 4}, "end": {"line": 119, "column": 5}}, "25": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 62}}, "26": {"start": {"line": 93, "column": 35}, "end": {"line": 93, "column": 60}}, "27": {"start": {"line": 95, "column": 23}, "end": {"line": 95, "column": 156}}, "28": {"start": {"line": 98, "column": 6}, "end": {"line": 102, "column": 9}}, "29": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 60}}, "30": {"start": {"line": 107, "column": 6}, "end": {"line": 112, "column": 9}}, "31": {"start": {"line": 115, "column": 6}, "end": {"line": 118, "column": 9}}, "32": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 36}}, "33": {"start": {"line": 129, "column": 4}, "end": {"line": 133, "column": 7}}, "34": {"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}, "35": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 46}}, "36": {"start": {"line": 151, "column": 4}, "end": {"line": 359, "column": 9}}, "37": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 13}}, "38": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 55}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "loc": {"start": {"line": 16, "column": 53}, "end": {"line": 17, "column": 6}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 9}, "end": {"line": 22, "column": 27}}, "loc": {"start": {"line": 25, "column": 36}, "end": {"line": 47, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 13}}, "loc": {"start": {"line": 41, "column": 17}, "end": {"line": 41, "column": 44}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 52, "column": 10}, "end": {"line": 52, "column": 15}}, "loc": {"start": {"line": 52, "column": 42}, "end": {"line": 72, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 77, "column": 10}, "end": {"line": 77, "column": 15}}, "loc": {"start": {"line": 77, "column": 46}, "end": {"line": 120, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 93, "column": 24}, "end": {"line": 93, "column": 31}}, "loc": {"start": {"line": 93, "column": 35}, "end": {"line": 93, "column": 60}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 125, "column": 10}, "end": {"line": 125, "column": 15}}, "loc": {"start": {"line": 125, "column": 34}, "end": {"line": 134, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 139, "column": 10}, "end": {"line": 139, "column": 21}}, "loc": {"start": {"line": 139, "column": 34}, "end": {"line": 143, "column": 3}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 148, "column": 10}, "end": {"line": 148, "column": 28}}, "loc": {"start": {"line": 148, "column": 52}, "end": {"line": 360, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 56, "column": 6}, "end": {"line": 68, "column": 7}}, "type": "switch", "locations": [{"start": {"line": 57, "column": 8}, "end": {"line": 59, "column": 16}}, {"start": {"line": 60, "column": 8}, "end": {"line": 62, "column": 16}}, {"start": {"line": 63, "column": 8}, "end": {"line": 65, "column": 16}}, {"start": {"line": 66, "column": 8}, "end": {"line": 67, "column": 61}}]}, "1": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}]}, "2": {"loc": {"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 13}}, {"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 41}}]}, "3": {"loc": {"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}, "type": "if", "locations": [{"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}]}}, "s": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 1, "38": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0, 0, 0], "1": [0], "2": [0, 0], "3": [0]}}}