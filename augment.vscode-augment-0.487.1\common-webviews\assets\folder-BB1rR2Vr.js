var fr=Object.defineProperty;var Cn=r=>{throw TypeError(r)};var gr=(r,t,e)=>t in r?fr(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var u=(r,t,e)=>gr(r,typeof t!="symbol"?t+"":t,e),Ns=(r,t,e)=>t.has(r)||Cn("Cannot "+e);var h=(r,t,e)=>(Ns(r,t,"read from private field"),e?e.call(r):t.get(r)),q=(r,t,e)=>t.has(r)?Cn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e),E=(r,t,e,s)=>(Ns(r,t,"write to private field"),s?s.call(r,e):t.set(r,e),e),x=(r,t,e)=>(Ns(r,t,"access private method"),e);var ns=(r,t,e,s)=>({set _(n){E(r,t,n,e)},get _(){return h(r,t,s)}});import{q as mr,t as wt,a as _r,l as yr,C as vr,m as wr,n as Sn}from"./types-xGAhb6Qr.js";import{S as kr,W as it,e as Me,u as Ui,o as Li}from"./BaseButton-7bccWxEO.js";import{g as Ct,o as we,E as ht,r as Ce,k as ze,s as Fe,S as be,C as xe,t as Fi,n as br,u as qs,v as xr,w as $n,x as En,y as Tr,z as An,B as Cr,D as In,F as Sr,G as Mn,H as zs,I as $r}from"./arrow-up-right-from-square-CdEOBPRR.js";import{S as W,i as j,s as V,a as z,b as X,H as Xt,w as Kt,x as Jt,y as te,h as S,d as dt,z as ee,g as Ut,n as M,j as rt,N as st,am as B,P as Oi,al as Te,aD as Rn,c as y,e as D,f as tt,W as et,J as Gt,u as k,q as kt,t as T,r as bt,T as Ni,E as U,F as L,I as F,K as Ye,M as fn,aA as Re,ac as is,$ as Pe,a3 as Dn,a0 as He,a1 as Be,a2 as Ge,Y as $t,a8 as Vt,aa as Er,_ as Ar,A as se,ab as K,C as Qt,D as De,G as Ue,Z as gs,a4 as gn,L as Ir,ao as Mr,an as qi,B as Rr,ag as Dr,aj as Ur}from"./SpinnerAugment-BJAAUt-n.js";import{C as J,P as Dt,b as Ps,I as Yt,a as Bt,E as Lr,L as Hs}from"./chat-types-D7sox8tw.js";import{h as Un,g as Fr,D as Or,i as Nr,j as qr,U as zr,d as Pr,f as zi,C as Hr,T as Br}from"./github-DDehkTJf.js";import{d as Gr,T as Us}from"./Content-BldOFwN2.js";import{F as Wr,s as jr,P as Vr}from"./folder-opened-BWTQdsic.js";import{T as le,a as Ln,b as Bs}from"./check-DWGOhZOn.js";import{C as Zr}from"./types-CGlLNakm.js";import{I as Je}from"./IconButtonAugment-CqdkuyT6.js";import{C as Yr,D as ms,T as Qr}from"./index-BAb5fkIe.js";import{_ as Xr,a as Kr,i as Jr,b as mn}from"./isObjectLike-B3cfrJ3d.js";import{T as to}from"./TextAreaAugment-CfENnz8O.js";import{T as _n}from"./TextTooltipAugment-DGOJQXY9.js";import{e as eo,f as so,b as Fn,i as no,d as io}from"./diff-utils-RpWUB_Gw.js";function ro(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 224c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64C28.7 32 0 60.7 0 96v64c0 35.3 28.7 64 64 64h168v86.1l-23-23c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0l64-64c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-23 23V224h168zM64 288c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-64c0-35.3-28.7-64-64-64h-74.3c4.8 16 2.2 33.8-7.7 48h82c8.8 0 16 7.2 16 16v64c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-64c0-8.8 7.2-16 16-16h82c-9.9-14.2-12.5-32-7.7-48z"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function oo(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class ao extends W{constructor(t){super(),j(this,t,oo,ro,V,{})}}function lo(r){var t;return((t=r.extraData)==null?void 0:t.isAutofix)===!0}function Se(r){var t;return((t=r.extraData)==null?void 0:t.isAgentConversation)===!0}var at=(r=>(r[r.active=0]="active",r[r.inactive=1]="inactive",r))(at||{}),co=(r=>(r.normal="Normal",r.autofixCommand="AutofixCommand",r.autofixPrompt="AutofixPrompt",r))(co||{});function ho(r,t,e=1e3){let s=null,n=0;const i=st(t),o=()=>{const a=(()=>{const l=Date.now();if(s!==null&&l-n<e)return s;const c=r();return s=c,n=l,c})();i.set(a)};return{subscribe:i.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Pi=(r=>(r[r.unset=0]="unset",r[r.positive=1]="positive",r[r.negative=2]="negative",r))(Pi||{});const vt="__NEW_AGENT__";function uo(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let ye={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function On(r){ye=r}const Hi=/[&<>"']/,po=new RegExp(Hi.source,"g"),Bi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,fo=new RegExp(Bi.source,"g"),go={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Nn=r=>go[r];function Rt(r,t){if(t){if(Hi.test(r))return r.replace(po,Nn)}else if(Bi.test(r))return r.replace(fo,Nn);return r}const mo=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function _o(r){return r.replace(mo,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const yo=/(^|[^\[])\^/g;function G(r,t){let e=typeof r=="string"?r:r.source;t=t||"";const s={replace:(n,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(yo,"$1"),e=e.replace(n,o),s},getRegex:()=>new RegExp(e,t)};return s}function qn(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const We={exec:()=>null};function zn(r,t){const e=r.replace(/\|/g,(n,i,o)=>{let a=!1,l=i;for(;--l>=0&&o[l]==="\\";)a=!a;return a?"|":" |"}).split(/ \|/);let s=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;s<e.length;s++)e[s]=e[s].trim().replace(/\\\|/g,"|");return e}function rs(r,t,e){const s=r.length;if(s===0)return"";let n=0;for(;n<s;){const i=r.charAt(s-n-1);if(i!==t||e){if(i===t||!e)break;n++}else n++}return r.slice(0,s-n)}function Pn(r,t,e,s){const n=t.href,i=t.title?Rt(t.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){s.state.inLink=!0;const a={type:"link",raw:e,href:n,title:i,text:o,tokens:s.inlineTokens(o)};return s.state.inLink=!1,a}return{type:"image",raw:e,href:n,title:i,text:Rt(o)}}class _s{constructor(t){u(this,"options");u(this,"rules");u(this,"lexer");this.options=t||ye}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const s=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?s:rs(s,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const s=e[0],n=function(i,o){const a=i.match(/^(\s+)(?:```)/);if(a===null)return o;const l=a[1];return o.split(`
`).map(c=>{const d=c.match(/^\s+/);if(d===null)return c;const[p]=d;return p.length>=l.length?c.slice(l.length):c}).join(`
`)}(s,e[3]||"");return{type:"code",raw:s,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let s=e[2].trim();if(/#$/.test(s)){const n=rs(s,"#");this.options.pedantic?s=n.trim():n&&!/ $/.test(n)||(s=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:s,tokens:this.lexer.inline(s)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){const s=rs(e[0].replace(/^ *>[ \t]?/gm,""),`
`),n=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(s);return this.lexer.state.top=n,{type:"blockquote",raw:e[0],tokens:i,text:s}}}list(t){let e=this.rules.block.list.exec(t);if(e){let s=e[1].trim();const n=s.length>1,i={type:"list",raw:"",ordered:n,start:n?+s.slice(0,-1):"",loose:!1,items:[]};s=n?`\\d{1,9}\\${s.slice(-1)}`:`\\${s}`,this.options.pedantic&&(s=n?s:"[*+-]");const o=new RegExp(`^( {0,3}${s})((?:[	 ][^\\n]*)?(?:\\n|$))`);let a="",l="",c=!1;for(;t;){let d=!1;if(!(e=o.exec(t))||this.rules.block.hr.test(t))break;a=e[0],t=t.substring(a.length);let p=e[2].split(`
`,1)[0].replace(/^\t+/,_=>" ".repeat(3*_.length)),f=t.split(`
`,1)[0],g=0;this.options.pedantic?(g=2,l=p.trimStart()):(g=e[2].search(/[^ ]/),g=g>4?1:g,l=p.slice(g),g+=e[1].length);let v=!1;if(!p&&/^ *$/.test(f)&&(a+=f+`
`,t=t.substring(f.length+1),d=!0),!d){const _=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),A=new RegExp(`^ {0,${Math.min(3,g-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),R=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:\`\`\`|~~~)`),O=new RegExp(`^ {0,${Math.min(3,g-1)}}#`);for(;t;){const b=t.split(`
`,1)[0];if(f=b,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),R.test(f)||O.test(f)||_.test(f)||A.test(t))break;if(f.search(/[^ ]/)>=g||!f.trim())l+=`
`+f.slice(g);else{if(v||p.search(/[^ ]/)>=4||R.test(p)||O.test(p)||A.test(p))break;l+=`
`+f}v||f.trim()||(v=!0),a+=b+`
`,t=t.substring(b.length+1),p=f.slice(g)}}i.loose||(c?i.loose=!0:/\n *\n *$/.test(a)&&(c=!0));let m,C=null;this.options.gfm&&(C=/^\[[ xX]\] /.exec(l),C&&(m=C[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:a,task:!!C,checked:m,loose:!1,text:l,tokens:[]}),i.raw+=a}i.items[i.items.length-1].raw=a.trimEnd(),i.items[i.items.length-1].text=l.trimEnd(),i.raw=i.raw.trimEnd();for(let d=0;d<i.items.length;d++)if(this.lexer.state.top=!1,i.items[d].tokens=this.lexer.blockTokens(i.items[d].text,[]),!i.loose){const p=i.items[d].tokens.filter(g=>g.type==="space"),f=p.length>0&&p.some(g=>/\n.*\n/.test(g.raw));i.loose=f}if(i.loose)for(let d=0;d<i.items.length;d++)i.items[d].loose=!0;return i}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const s=e[1].toLowerCase().replace(/\s+/g," "),n=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:s,raw:e[0],href:n,title:i}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const s=zn(e[1]),n=e[2].replace(/^\||\| *$/g,"").split("|"),i=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:e[0],header:[],align:[],rows:[]};if(s.length===n.length){for(const a of n)/^ *-+: *$/.test(a)?o.align.push("right"):/^ *:-+: *$/.test(a)?o.align.push("center"):/^ *:-+ *$/.test(a)?o.align.push("left"):o.align.push(null);for(const a of s)o.header.push({text:a,tokens:this.lexer.inline(a)});for(const a of i)o.rows.push(zn(a,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const s=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:s,tokens:this.lexer.inline(s)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:Rt(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const s=e[2].trim();if(!this.options.pedantic&&/^</.test(s)){if(!/>$/.test(s))return;const o=rs(s.slice(0,-1),"\\");if((s.length-o.length)%2==0)return}else{const o=function(a,l){if(a.indexOf(l[1])===-1)return-1;let c=0;for(let d=0;d<a.length;d++)if(a[d]==="\\")d++;else if(a[d]===l[0])c++;else if(a[d]===l[1]&&(c--,c<0))return d;return-1}(e[2],"()");if(o>-1){const a=(e[0].indexOf("!")===0?5:4)+e[1].length+o;e[2]=e[2].substring(0,o),e[0]=e[0].substring(0,a).trim(),e[3]=""}}let n=e[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);o&&(n=o[1],i=o[3])}else i=e[3]?e[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(s)?n.slice(1):n.slice(1,-1)),Pn(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let s;if((s=this.rules.inline.reflink.exec(t))||(s=this.rules.inline.nolink.exec(t))){const n=e[(s[2]||s[1]).replace(/\s+/g," ").toLowerCase()];if(!n){const i=s[0].charAt(0);return{type:"text",raw:i,text:i}}return Pn(s,n,s[0],this.lexer)}}emStrong(t,e,s=""){let n=this.rules.inline.emStrongLDelim.exec(t);if(n&&!(n[3]&&s.match(/[\p{L}\p{N}]/u))&&(!(n[1]||n[2])||!s||this.rules.inline.punctuation.exec(s))){const i=[...n[0]].length-1;let o,a,l=i,c=0;const d=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(d.lastIndex=0,e=e.slice(-1*t.length+i);(n=d.exec(e))!=null;){if(o=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!o)continue;if(a=[...o].length,n[3]||n[4]){l+=a;continue}if((n[5]||n[6])&&i%3&&!((i+a)%3)){c+=a;continue}if(l-=a,l>0)continue;a=Math.min(a,a+l+c);const p=[...n[0]][0].length,f=t.slice(0,i+n.index+p+a);if(Math.min(i,a)%2){const v=f.slice(1,-1);return{type:"em",raw:f,text:v,tokens:this.lexer.inlineTokens(v)}}const g=f.slice(2,-2);return{type:"strong",raw:f,text:g,tokens:this.lexer.inlineTokens(g)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let s=e[2].replace(/\n/g," ");const n=/[^ ]/.test(s),i=/^ /.test(s)&&/ $/.test(s);return n&&i&&(s=s.substring(1,s.length-1)),s=Rt(s,!0),{type:"codespan",raw:e[0],text:s}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let s,n;return e[2]==="@"?(s=Rt(e[1]),n="mailto:"+s):(s=Rt(e[1]),n=s),{type:"link",raw:e[0],text:s,href:n,tokens:[{type:"text",raw:s,text:s}]}}}url(t){var s;let e;if(e=this.rules.inline.url.exec(t)){let n,i;if(e[2]==="@")n=Rt(e[0]),i="mailto:"+n;else{let o;do o=e[0],e[0]=((s=this.rules.inline._backpedal.exec(e[0]))==null?void 0:s[0])??"";while(o!==e[0]);n=Rt(e[0]),i=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:n,href:i,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let s;return s=this.lexer.state.inRawBlock?e[0]:Rt(e[0]),{type:"text",raw:e[0],text:s}}}}const ts=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Gi=/(?:[*+-]|\d{1,9}[.)])/,Wi=G(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Gi).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),yn=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,vn=/(?!\s*\])(?:\\.|[^\[\]\\])+/,vo=G(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",vn).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),wo=G(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Gi).getRegex(),Ls="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",wn=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,ko=G("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",wn).replace("tag",Ls).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Hn=G(yn).replace("hr",ts).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ls).getRegex(),kn={blockquote:G(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Hn).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:vo,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:ts,html:ko,lheading:Wi,list:wo,newline:/^(?: *(?:\n|$))+/,paragraph:Hn,table:We,text:/^[^\n]+/},Bn=G("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",ts).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ls).getRegex(),bo={...kn,table:Bn,paragraph:G(yn).replace("hr",ts).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Bn).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ls).getRegex()},xo={...kn,html:G(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",wn).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:We,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:G(yn).replace("hr",ts).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Wi).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ji=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Vi=/^( {2,}|\\)\n(?!\s*$)/,es="\\p{P}\\p{S}",To=G(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,es).getRegex(),Co=G(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,es).getRegex(),So=G("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,es).getRegex(),$o=G("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,es).getRegex(),Eo=G(/\\([punct])/,"gu").replace(/punct/g,es).getRegex(),Ao=G(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Io=G(wn).replace("(?:-->|$)","-->").getRegex(),Mo=G("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Io).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),ys=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ro=G(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",ys).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Gn=G(/^!?\[(label)\]\[(ref)\]/).replace("label",ys).replace("ref",vn).getRegex(),Wn=G(/^!?\[(ref)\](?:\[\])?/).replace("ref",vn).getRegex(),bn={_backpedal:We,anyPunctuation:Eo,autolink:Ao,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:Vi,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:We,emStrongLDelim:Co,emStrongRDelimAst:So,emStrongRDelimUnd:$o,escape:ji,link:Ro,nolink:Wn,punctuation:To,reflink:Gn,reflinkSearch:G("reflink|nolink(?!\\()","g").replace("reflink",Gn).replace("nolink",Wn).getRegex(),tag:Mo,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:We},Do={...bn,link:G(/^!?\[(label)\]\((.*?)\)/).replace("label",ys).getRegex(),reflink:G(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ys).getRegex()},Ks={...bn,escape:G(ji).replace("])","~|])").getRegex(),url:G(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Uo={...Ks,br:G(Vi).replace("{2,}","*").getRegex(),text:G(Ks.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},os={normal:kn,gfm:bo,pedantic:xo},Oe={normal:bn,gfm:Ks,breaks:Uo,pedantic:Do};class Wt{constructor(t){u(this,"tokens");u(this,"options");u(this,"state");u(this,"tokenizer");u(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||ye,this.options.tokenizer=this.options.tokenizer||new _s,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:os.normal,inline:Oe.normal};this.options.pedantic?(e.block=os.pedantic,e.inline=Oe.pedantic):this.options.gfm&&(e.block=os.gfm,this.options.breaks?e.inline=Oe.breaks:e.inline=Oe.gfm),this.tokenizer.rules=e}static get rules(){return{block:os,inline:Oe}}static lex(t,e){return new Wt(e).lex(t)}static lexInline(t,e){return new Wt(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const s=this.inlineQueue[e];this.inlineTokens(s.src,s.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let s,n,i,o;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(a,l,c)=>l+"    ".repeat(c.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>!!(s=a.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.space(t))t=t.substring(s.raw.length),s.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(s);else if(s=this.tokenizer.code(t))t=t.substring(s.raw.length),n=e[e.length-1],!n||n.type!=="paragraph"&&n.type!=="text"?e.push(s):(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(s=this.tokenizer.fences(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.heading(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.hr(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.blockquote(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.list(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.html(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.def(t))t=t.substring(s.raw.length),n=e[e.length-1],!n||n.type!=="paragraph"&&n.type!=="text"?this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title}):(n.raw+=`
`+s.raw,n.text+=`
`+s.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(s=this.tokenizer.table(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.lheading(t))t=t.substring(s.raw.length),e.push(s);else{if(i=t,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=t.slice(1);let c;this.options.extensions.startBlock.forEach(d=>{c=d.call({lexer:this},l),typeof c=="number"&&c>=0&&(a=Math.min(a,c))}),a<1/0&&a>=0&&(i=t.substring(0,a+1))}if(this.state.top&&(s=this.tokenizer.paragraph(i)))n=e[e.length-1],o&&n.type==="paragraph"?(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):e.push(s),o=i.length!==t.length,t=t.substring(s.raw.length);else if(s=this.tokenizer.text(t))t=t.substring(s.raw.length),n=e[e.length-1],n&&n.type==="text"?(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):e.push(s);else if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}throw new Error(a)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let s,n,i,o,a,l,c=t;if(this.tokens.links){const d=Object.keys(this.tokens.links);if(d.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(c))!=null;)d.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(c))!=null;)c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(c))!=null;)c=c.slice(0,o.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(a||(l=""),a=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(d=>!!(s=d.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.escape(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.tag(t))t=t.substring(s.raw.length),n=e[e.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(s=this.tokenizer.link(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(s.raw.length),n=e[e.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(s=this.tokenizer.emStrong(t,c,l))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.codespan(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.br(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.del(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.autolink(t))t=t.substring(s.raw.length),e.push(s);else if(this.state.inLink||!(s=this.tokenizer.url(t))){if(i=t,this.options.extensions&&this.options.extensions.startInline){let d=1/0;const p=t.slice(1);let f;this.options.extensions.startInline.forEach(g=>{f=g.call({lexer:this},p),typeof f=="number"&&f>=0&&(d=Math.min(d,f))}),d<1/0&&d>=0&&(i=t.substring(0,d+1))}if(s=this.tokenizer.inlineText(i))t=t.substring(s.raw.length),s.raw.slice(-1)!=="_"&&(l=s.raw.slice(-1)),a=!0,n=e[e.length-1],n&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(t){const d="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(d);break}throw new Error(d)}}else t=t.substring(s.raw.length),e.push(s);return e}}class vs{constructor(t){u(this,"options");this.options=t||ye}code(t,e,s){var i;const n=(i=(e||"").match(/^\S*/))==null?void 0:i[0];return t=t.replace(/\n$/,"")+`
`,n?'<pre><code class="language-'+Rt(n)+'">'+(s?t:Rt(t,!0))+`</code></pre>
`:"<pre><code>"+(s?t:Rt(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,s){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,s){const n=e?"ol":"ul";return"<"+n+(e&&s!==1?' start="'+s+'"':"")+`>
`+t+"</"+n+`>
`}listitem(t,e,s){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const s=e.header?"th":"td";return(e.align?`<${s} align="${e.align}">`:`<${s}>`)+t+`</${s}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,s){const n=qn(t);if(n===null)return s;let i='<a href="'+(t=n)+'"';return e&&(i+=' title="'+e+'"'),i+=">"+s+"</a>",i}image(t,e,s){const n=qn(t);if(n===null)return s;let i=`<img src="${t=n}" alt="${s}"`;return e&&(i+=` title="${e}"`),i+=">",i}text(t){return t}}class xn{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,s){return""+s}image(t,e,s){return""+s}br(){return""}}class jt{constructor(t){u(this,"options");u(this,"renderer");u(this,"textRenderer");this.options=t||ye,this.options.renderer=this.options.renderer||new vs,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new xn}static parse(t,e){return new jt(e).parse(t)}static parseInline(t,e){return new jt(e).parseInline(t)}parse(t,e=!0){let s="";for(let n=0;n<t.length;n++){const i=t[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,a=this.options.extensions.renderers[o.type].call({parser:this},o);if(a!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){s+=a||"";continue}}switch(i.type){case"space":continue;case"hr":s+=this.renderer.hr();continue;case"heading":{const o=i;s+=this.renderer.heading(this.parseInline(o.tokens),o.depth,_o(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;s+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let a="",l="";for(let d=0;d<o.header.length;d++)l+=this.renderer.tablecell(this.parseInline(o.header[d].tokens),{header:!0,align:o.align[d]});a+=this.renderer.tablerow(l);let c="";for(let d=0;d<o.rows.length;d++){const p=o.rows[d];l="";for(let f=0;f<p.length;f++)l+=this.renderer.tablecell(this.parseInline(p[f].tokens),{header:!1,align:o.align[f]});c+=this.renderer.tablerow(l)}s+=this.renderer.table(a,c);continue}case"blockquote":{const o=i,a=this.parse(o.tokens);s+=this.renderer.blockquote(a);continue}case"list":{const o=i,a=o.ordered,l=o.start,c=o.loose;let d="";for(let p=0;p<o.items.length;p++){const f=o.items[p],g=f.checked,v=f.task;let m="";if(f.task){const C=this.renderer.checkbox(!!g);c?f.tokens.length>0&&f.tokens[0].type==="paragraph"?(f.tokens[0].text=C+" "+f.tokens[0].text,f.tokens[0].tokens&&f.tokens[0].tokens.length>0&&f.tokens[0].tokens[0].type==="text"&&(f.tokens[0].tokens[0].text=C+" "+f.tokens[0].tokens[0].text)):f.tokens.unshift({type:"text",text:C+" "}):m+=C+" "}m+=this.parse(f.tokens,c),d+=this.renderer.listitem(m,v,!!g)}s+=this.renderer.list(d,a,l);continue}case"html":{const o=i;s+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;s+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,a=o.tokens?this.parseInline(o.tokens):o.text;for(;n+1<t.length&&t[n+1].type==="text";)o=t[++n],a+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);s+=e?this.renderer.paragraph(a):a;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return s}parseInline(t,e){e=e||this.renderer;let s="";for(let n=0;n<t.length;n++){const i=t[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){s+=o||"";continue}}switch(i.type){case"escape":{const o=i;s+=e.text(o.text);break}case"html":{const o=i;s+=e.html(o.text);break}case"link":{const o=i;s+=e.link(o.href,o.title,this.parseInline(o.tokens,e));break}case"image":{const o=i;s+=e.image(o.href,o.title,o.text);break}case"strong":{const o=i;s+=e.strong(this.parseInline(o.tokens,e));break}case"em":{const o=i;s+=e.em(this.parseInline(o.tokens,e));break}case"codespan":{const o=i;s+=e.codespan(o.text);break}case"br":s+=e.br();break;case"del":{const o=i;s+=e.del(this.parseInline(o.tokens,e));break}case"text":{const o=i;s+=e.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return s}}class je{constructor(t){u(this,"options");this.options=t||ye}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}u(je,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var _e,Js,Zi,Ii;const ue=new(Ii=class{constructor(...r){q(this,_e);u(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});u(this,"options",this.setOptions);u(this,"parse",x(this,_e,Js).call(this,Wt.lex,jt.parse));u(this,"parseInline",x(this,_e,Js).call(this,Wt.lexInline,jt.parseInline));u(this,"Parser",jt);u(this,"Renderer",vs);u(this,"TextRenderer",xn);u(this,"Lexer",Wt);u(this,"Tokenizer",_s);u(this,"Hooks",je);this.use(...r)}walkTokens(r,t){var s,n;let e=[];for(const i of r)switch(e=e.concat(t.call(this,i)),i.type){case"table":{const o=i;for(const a of o.header)e=e.concat(this.walkTokens(a.tokens,t));for(const a of o.rows)for(const l of a)e=e.concat(this.walkTokens(l.tokens,t));break}case"list":{const o=i;e=e.concat(this.walkTokens(o.items,t));break}default:{const o=i;(n=(s=this.defaults.extensions)==null?void 0:s.childTokens)!=null&&n[o.type]?this.defaults.extensions.childTokens[o.type].forEach(a=>{const l=o[a].flat(1/0);e=e.concat(this.walkTokens(l,t))}):o.tokens&&(e=e.concat(this.walkTokens(o.tokens,t)))}}return e}use(...r){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(e=>{const s={...e};if(s.async=this.defaults.async||s.async||!1,e.extensions&&(e.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const i=t.renderers[n.name];t.renderers[n.name]=i?function(...o){let a=n.renderer.apply(this,o);return a===!1&&(a=i.apply(this,o)),a}:n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=t[n.level];i?i.unshift(n.tokenizer):t[n.level]=[n.tokenizer],n.start&&(n.level==="block"?t.startBlock?t.startBlock.push(n.start):t.startBlock=[n.start]:n.level==="inline"&&(t.startInline?t.startInline.push(n.start):t.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(t.childTokens[n.name]=n.childTokens)}),s.extensions=t),e.renderer){const n=this.defaults.renderer||new vs(this.defaults);for(const i in e.renderer){if(!(i in n))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,a=e.renderer[o],l=n[o];n[o]=(...c)=>{let d=a.apply(n,c);return d===!1&&(d=l.apply(n,c)),d||""}}s.renderer=n}if(e.tokenizer){const n=this.defaults.tokenizer||new _s(this.defaults);for(const i in e.tokenizer){if(!(i in n))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,a=e.tokenizer[o],l=n[o];n[o]=(...c)=>{let d=a.apply(n,c);return d===!1&&(d=l.apply(n,c)),d}}s.tokenizer=n}if(e.hooks){const n=this.defaults.hooks||new je;for(const i in e.hooks){if(!(i in n))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,a=e.hooks[o],l=n[o];je.passThroughHooks.has(i)?n[o]=c=>{if(this.defaults.async)return Promise.resolve(a.call(n,c)).then(p=>l.call(n,p));const d=a.call(n,c);return l.call(n,d)}:n[o]=(...c)=>{let d=a.apply(n,c);return d===!1&&(d=l.apply(n,c)),d}}s.hooks=n}if(e.walkTokens){const n=this.defaults.walkTokens,i=e.walkTokens;s.walkTokens=function(o){let a=[];return a.push(i.call(this,o)),n&&(a=a.concat(n.call(this,o))),a}}this.defaults={...this.defaults,...s}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,t){return Wt.lex(r,t??this.defaults)}parser(r,t){return jt.parse(r,t??this.defaults)}},_e=new WeakSet,Js=function(r,t){return(e,s)=>{const n={...s},i={...this.defaults,...n};this.defaults.async===!0&&n.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=x(this,_e,Zi).call(this,!!i.silent,!!i.async);if(e==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(e):e).then(a=>r(a,i)).then(a=>i.hooks?i.hooks.processAllTokens(a):a).then(a=>i.walkTokens?Promise.all(this.walkTokens(a,i.walkTokens)).then(()=>a):a).then(a=>t(a,i)).then(a=>i.hooks?i.hooks.postprocess(a):a).catch(o);try{i.hooks&&(e=i.hooks.preprocess(e));let a=r(e,i);i.hooks&&(a=i.hooks.processAllTokens(a)),i.walkTokens&&this.walkTokens(a,i.walkTokens);let l=t(a,i);return i.hooks&&(l=i.hooks.postprocess(l)),l}catch(a){return o(a)}}},Zi=function(r,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const s="<p>An error occurred:</p><pre>"+Rt(e.message+"",!0)+"</pre>";return t?Promise.resolve(s):s}if(t)return Promise.reject(e);throw e}},Ii);function H(r,t){return ue.parse(r,t)}H.options=H.setOptions=function(r){return ue.setOptions(r),H.defaults=ue.defaults,On(H.defaults),H},H.getDefaults=uo,H.defaults=ye,H.use=function(...r){return ue.use(...r),H.defaults=ue.defaults,On(H.defaults),H},H.walkTokens=function(r,t){return ue.walkTokens(r,t)},H.parseInline=ue.parseInline,H.Parser=jt,H.parser=jt.parse,H.Renderer=vs,H.TextRenderer=xn,H.Lexer=Wt,H.lexer=Wt.lex,H.Tokenizer=_s,H.Hooks=je,H.parse=H,H.options,H.setOptions,H.use,H.walkTokens,H.parseInline,jt.parse,Wt.lex;const Yc=async(r,t)=>{if(!Se(r)||t.chatItemType!==void 0||!(t!=null&&t.request_message))return;const e=mr.create();e.setFlag(wt.start);try{await Lo(r,t,e)}catch(s){e.setFlag(wt.exceptionThrown),console.error("Failed to classify and distill memories",s)}finally{e.setFlag(wt.end),r.extensionClient.reportAgentSessionEvent({eventName:_r.classifyAndDistill,conversationId:r.id,eventData:{classifyAndDistillData:e}})}},Lo=async(r,t,e)=>{const s=crypto.randomUUID();e.setRequestId(wt.memoriesRequestId,s);const n=B(r).id;e.setFlag(wt.startSendSilentExchange);const{responseText:i,requestId:o}=await r.sendSilentExchange({model_id:r.selectedModelId??void 0,request_message:t.request_message,disableRetrieval:!0,disableSelectedCodeDetails:!0,memoriesInfo:{isClassifyAndDistill:!0}});if(e.setStringStats(wt.sendSilentExchangeResponseStats,i),o?e.setRequestId(wt.sendSilentExchangeRequestId,o):e.setFlag(wt.noRequestId),B(r).id!==n)return void e.setFlag(wt.conversationChanged);let a;try{let c=i;try{const d=H.lexer(i);d.length===1&&d[0].type==="code"&&d[0].text&&(c=d[0].text)}catch(d){console.warn("Markdown lexing failed during response parsing, attempting to parse as raw string:",d)}a=JSON.parse(c)}catch{throw e.setFlag(wt.invalidResponse),new Error("Invalid response from classify and distill")}if(typeof a.explanation!="string"||typeof a.content!="string"||typeof a.worthRemembering!="boolean")throw e.setFlag(wt.invalidResponse),new Error("Invalid response from classify and distill");e.setStringStats(wt.explanationStats,a.explanation),e.setStringStats(wt.contentStats,a.content),e.setFlag(wt.worthRemembering,a.worthRemembering);const l=a.worthRemembering?a.content:void 0;l&&Oo(r,l,s,e)},Qc=r=>{var s;const t=r.chatHistory.at(-1);if(!t||!Ct(t))return we.notRunning;if(!(t.status===ht.success||t.status===ht.failed||t.status===ht.cancelled))return we.running;const e=(((s=t.structured_output_nodes)==null?void 0:s.filter(n=>n.type===J.TOOL_USE&&!!n.tool_use))??[]).at(-1);if(!e)return we.notRunning;switch(r.getToolUseState(t.request_id,e.tool_use.tool_use_id).phase){case le.runnable:return we.awaitingUserAction;case le.cancelled:return we.notRunning;default:return we.running}},tn=r=>Ct(r)&&!!r.request_message,Fs=r=>r.chatHistory.findLast(t=>tn(t)),Xc=r=>{const t=Fs(r);if(t!=null&&t.structured_output_nodes){const e=t.structured_output_nodes.find(s=>s.type===J.AGENT_MEMORY);if(e)try{const{memoriesRequestId:s,memory:n}=JSON.parse(e.content);return{memoriesRequestId:s,memory:n}}catch(s){return void console.error("Failed to parse JSON from agent memory node",s)}}},Kc=r=>Fo(r,t=>{var e;return!!((e=t.structured_output_nodes)!=null&&e.some(s=>{var n;return s.type===J.TOOL_USE&&((n=s.tool_use)==null?void 0:n.tool_name)==="remember"}))}).length>0,Fo=(r,t)=>{const e=Fs(r);return e!=null&&e.request_id?r.historyFrom(e.request_id,!0).filter(s=>Ct(s)&&(!t||t(s))):[]},Jc=r=>{var s;const t=r.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!Ct(t))return!1;const e=((s=t.structured_output_nodes)==null?void 0:s.filter(n=>n.type===J.TOOL_USE))??[];for(const n of e)if(n.tool_use&&r.getToolUseState(t.request_id,n.tool_use.tool_use_id).phase===le.runnable)return r.updateToolUseState({requestId:t.request_id,toolUseId:n.tool_use.tool_use_id,phase:le.cancelled}),!0;return!1},Oo=(r,t,e,s)=>{const n=JSON.stringify({memoriesRequestId:e,memory:t}),i=Fs(r);i!=null&&i.request_id?(s.setRequestId(wt.lastUserExchangeRequestId,i.request_id),r.updateChatItem(i.request_id,{...i,structured_output_nodes:[...i.structured_output_nodes??[],{id:0,type:J.AGENT_MEMORY,content:n}]})):s.setFlag(wt.noLastUserExchangeRequestId)},th=(r,t)=>{const e=Fs(r);if(!(e!=null&&e.request_id)||e.request_id!==t)return!1;const s=(e.structured_output_nodes||[]).filter(n=>n.type!==J.AGENT_MEMORY);return s.length!==(e.structured_output_nodes||[]).length&&(r.updateChatItem(t,{...e,structured_output_nodes:s}),!0)};function No(r,t){const e=r.customPersonalityPrompts;if(e)switch(t){case Dt.DEFAULT:if(e.agent&&e.agent.trim()!=="")return e.agent;break;case Dt.PROTOTYPER:if(e.prototyper&&e.prototyper.trim()!=="")return e.prototyper;break;case Dt.BRAINSTORM:if(e.brainstorm&&e.brainstorm.trim()!=="")return e.brainstorm;break;case Dt.REVIEWER:if(e.reviewer&&e.reviewer.trim()!=="")return e.reviewer}return qo[t]}const qo={[Dt.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[Dt.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[Dt.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[Dt.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};function zo(r){var e;if(!r)return Yt.IMAGE_FORMAT_UNSPECIFIED;switch((e=r.split("/")[1])==null?void 0:e.toLowerCase()){case"jpeg":case"jpg":return Yt.JPEG;case"png":return Yt.PNG;default:return Yt.IMAGE_FORMAT_UNSPECIFIED}}function eh(r,t){return r.map(e=>e.type===Un.ContentText?{type:Ps.CONTENT_TEXT,text_content:e.text_content}:e.type===Un.ContentImage&&e.image_content&&t?{type:Ps.CONTENT_IMAGE,image_content:{image_data:e.image_content.image_data,format:zo(e.image_content.media_type)}}:{type:Ps.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}function Po(r=[]){let t;for(const e of r){if(e.type===J.TOOL_USE)return e;e.type===J.TOOL_USE_START&&(t=e)}return t}class Q{constructor(t,e,s,n){u(this,"_state");u(this,"_subscribers",new Set);u(this,"_focusModel",new Wr);u(this,"_onSendExchangeListeners",[]);u(this,"_onNewConversationListeners",[]);u(this,"_onHistoryDeleteListeners",[]);u(this,"_onBeforeChangeConversationListeners",[]);u(this,"_totalCharactersCacheThrottleMs",1e3);u(this,"_totalCharactersStore");u(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));u(this,"setConversation",(t,e=!0,s=!0)=>{const n=t.id!==this._state.id;n&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,a])=>{if(a.requestId&&a.toolUseId){const{requestId:l,toolUseId:c}=Ln(o);return l===a.requestId&&c===a.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",Bs(a)),[o,a]}return[o,{...a,...Ln(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&n&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const i=Q.isEmpty(t);if(n&&i){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(Ct)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),n&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});u(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});u(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});u(this,"setName",t=>{this.update({name:t})});u(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});u(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});u(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Bs(t)]:t}})});u(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:le.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[Bs({requestId:t,toolUseId:e})]||{phase:le.new});u(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:le.unknown};const e=function(n=[]){let i;for(const o of n){if(o.type===J.TOOL_USE)return o;o.type===J.TOOL_USE_START&&(i=o)}return i}(t==null?void 0:t.structured_output_nodes);return e?this.getToolUseState(t.request_id,(s=e.tool_use)==null?void 0:s.tool_use_id):{phase:le.unknown}});u(this,"addExchange",t=>{const e=[...this._state.chatHistory,t];let s;Ct(t)&&(s=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Pi.unset,feedbackNote:""}}:void 0),this.update({chatHistory:e,...s?{feedbackStates:s}:{},lastUrl:void 0})});u(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});u(this,"updateExchangeById",(t,e,s=!1)=>{var a;const n=this.exchangeWithRequestId(e);if(n===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(n.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=function(l=[]){const c=Po(l);return c&&c.type===J.TOOL_USE?l.filter(d=>d.type!==J.TOOL_USE_START):l}([...n.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==n.stop_reason&&n.stop_reason&&t.stop_reason===Zr.REASON_UNSPECIFIED&&(t.stop_reason=n.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...n.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const i=(a=(t.structured_output_nodes||[]).find(l=>l.type===J.MAIN_TEXT_FINISHED))==null?void 0:a.content;i&&i!==t.response_text&&(t.response_text=i);let o=this._state.isShareable||Ce({...n,...t});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===e?{...l,...t}:l),isShareable:o}),!0});u(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>!e.request_id||!t.has(e.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});u(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});u(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),n=s.map(i=>i.request_id).filter(i=>i!==void 0);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:n}),s.forEach(i=>{this._onHistoryDeleteListeners.forEach(o=>o(i))})});u(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});u(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});u(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});u(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});u(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:ht.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id})));u(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});u(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);u(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});u(this,"historySummaryVersion",1);u(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:be.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});u(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));u(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});u(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});u(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(n=>t.has(n.id)||n.recentFile||n.selection||n.sourceFolder),s=this._specialContextInputModel.recentItems.filter(n=>!(t.has(n.id)||n.recentFile||n.selection||n.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});u(this,"saveDraftExchange",(t,e)=>{var o,a,l;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),n=e!==((a=this.draftExchange)==null?void 0:a.rich_text_json_repr);if(!s&&!n)return;const i=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:i,status:ht.draft}})});u(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});u(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var o,a;const s=!this.name&&this.chatHistory.length===1&&((o=this.firstExchange)==null?void 0:o.request_id)===this.chatHistory[0].request_id,n=Se(this)&&((a=this._state.extraData)==null?void 0:a.hasAgentOnboarded)&&(i=this.chatHistory,i.filter(l=>tn(l))).length===2;var i;this._chatFlagModel.summaryTitles&&(s||n)&&this.updateConversationTitle()}).finally(()=>{var s;Se(this)&&this._extensionClient.reportAgentRequestEvent({eventName:yr.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});u(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:ht.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});u(this,"sendInstructionExchange",async(t,e)=>{let s=`temp-fe-${crypto.randomUUID()}`;const n={status:ht.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:be.unseen,timestamp:new Date().toISOString()};this.addExchange(n);for await(const i of this._extensionClient.sendInstructionMessage(n,e)){if(!this.updateExchangeById(i,s,!0))return;s=i.request_id||s}});u(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});u(this,"sendSummaryExchange",()=>{const t={status:ht.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:xe.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});u(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const e={status:ht.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:be.unseen,chatItemType:xe.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});u(this,"sendExchange",async(t,e=!1)=>{var o;this.updateLastInteraction();let s=`temp-fe-${crypto.randomUUID()}`,n=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&Q.isNew(this._state)){const a=crypto.randomUUID(),l=this._state.id;try{await this._extensionClient.migrateConversationId(l,a)}catch(c){console.error("Failed to migrate conversation checkpoints:",c)}this._state={...this._state,id:a},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(a),this._subscribers.forEach(c=>c(this))}t=Vn(t);let i={status:ht.sent,request_id:s,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:n,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:be.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(i),this._loadContextFromExchange(i),this._onSendExchangeListeners.forEach(a=>a(i)),this._chatFlagModel.useHistorySummary&&(this._clearStaleHistorySummaryNodes(),await this.maybeAddHistorySummaryNode()),i=await this._addIdeStateNode(i),this.updateExchangeById({structured_request_nodes:i.structured_request_nodes},s,!1);for await(const a of this.sendUserMessage(s,i,e)){if(((o=this.exchangeWithRequestId(s))==null?void 0:o.status)!==ht.sent||!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});u(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:ht.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(vr.chatUseSuggestedQuestion)});u(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});u(this,"recoverExchange",async t=>{var n;if(!t.request_id||t.status!==ht.sent)return;let e=t.request_id;const s=(n=t.structured_output_nodes)==null?void 0:n.filter(i=>i.type===J.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:s??[]},e);for await(const i of this.getChatStream(t)){if(!this.updateExchangeById(i,e,!0))return;e=i.request_id||e}});u(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{Ct(e)&&this._loadContextFromExchange(e)})});u(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});u(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{Ct(e)&&this._unloadContextFromExchange(e)})});u(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});u(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});u(this,"_jsonToStructuredRequest",t=>{const e=[],s=i=>{var a;const o=e.at(-1);if((o==null?void 0:o.type)===Bt.TEXT){const l=((a=o.text_node)==null?void 0:a.content)??"",c={...o,text_node:{content:l+i}};e[e.length-1]=c}else e.push({id:e.length,type:Bt.TEXT,text_node:{content:i}})},n=i=>{var o,a,l,c;if(i.type==="doc"||i.type==="paragraph")for(const d of i.content??[])n(d);else if(i.type==="hardBreak")s(`
`);else if(i.type==="text")s(i.text??"");else if(i.type==="image"){if(typeof((o=i.attrs)==null?void 0:o.src)!="string")return void console.error("Image source is not a string: ",(a=i.attrs)==null?void 0:a.src);if(i.attrs.isLoading)return;const d=(l=i.attrs)==null?void 0:l.title,p=this._fileNameToImageFormat(d);e.push({id:e.length,type:Bt.IMAGE_ID,image_id_node:{image_id:i.attrs.src,format:p}})}else if(i.type==="mention"){const d=(c=i.attrs)==null?void 0:c.data;d&&Fi(d)?e.push({id:e.length,type:Bt.TEXT,text_node:{content:No(this._chatFlagModel,d.personality.type)}}):s(`@\`${(d==null?void 0:d.name)??(d==null?void 0:d.id)}\``)}};return n(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=n,this._state={...Q.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return ho(()=>{let t=0;const e=this._state.chatHistory;return this._convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,n)=>s+n,0))||0)<=4?Dt.PROTOTYPER:Dt.DEFAULT}catch(e){return console.error("Error determining persona type:",e),Dt.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Dt.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const e=t.chatHistory.find(Ct);return e&&e.request_message?Q.toSentenceCase(e.request_message):lo(t)?"Autofix Chat":Se(t)?"New Agent":"New Chat"}static isNew(t){return t.id===vt}static isEmpty(t){var e;return!(t.chatHistory.some(Ct)||(e=t.draftExchange)!=null&&e.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static isThreadEmpty(t){return!!t.isNew||!t.conversation||Q.isEmpty(t.conversation)}static getTime(t,e){return e==="lastMessageTimestamp"?Q.lastMessageTimestamp(t):e==="lastInteractedAt"?Q.lastInteractedAt(t):Q.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const e=(s=t.chatHistory.findLast(Ct))==null?void 0:s.timestamp;return e?new Date(e):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!Q.isEmpty(t)||Q.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const n of this._onBeforeChangeConversationListeners){const i=n(t,s);i!==void 0&&(s=i)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return Q.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Dt.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return Q.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return Q.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=n=>Array.isArray(n)?n.some(e):!!n&&(n.type==="image"||!(!n.content||!Array.isArray(n.content))&&n.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(Ct)??null}get lastExchange(){return this.chatHistory.findLast(Ct)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>Ct(t)&&t.status===ht.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Ce(t)||ze(t)||Fe(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const e=(t=t.filter(n=>!Fe(n)||n.summaryVersion===this.historySummaryVersion)).findLastIndex(n=>Fe(n));this._chatFlagModel.useHistorySummary&&e>0&&(console.info("Using history summary node found at index %d",e),t=t.slice(e));const s=[];for(const n of t)if(Ce(n))s.push(jn(n));else if(ze(n)&&n.fromTimestamp!==void 0&&n.toTimestamp!==void 0){if(n.revertTarget){const i=Ho(n,1),o={request_message:"",response_text:"",request_id:n.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};s.push(o)}}else this._chatFlagModel.useHistorySummary&&Fe(n)&&s.push(jn(n));return s}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===ht.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,n="";const i=await this._addIdeStateNode(Vn({...t,request_id:e,status:ht.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(e,i,!0))o.response_text&&(n+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:n,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}async*sendUserMessage(t,e,s){var d;const n=this._specialContextInputModel.chatActiveContext;let i;if(e.chatHistory!==void 0)i=e.chatHistory;else{let p=this.successfulMessages;if(e.chatItemType===xe.summaryTitle){const f=p.findIndex(g=>g.chatItemType!==xe.agentOnboarding&&tn(g));f!==-1&&(p=p.slice(f))}i=this._convertHistoryToExchanges(p)}let o=this.personaType;if(e.structured_request_nodes){const p=e.structured_request_nodes.find(f=>f.type===Bt.CHANGE_PERSONALITY);p&&p.change_personality_node&&(o=p.change_personality_node.personality_type)}const a={text:e.request_message,chatHistory:i,silent:s,modelId:e.model_id,context:n,userSpecifiedFiles:n.userSpecifiedFiles,externalSourceIds:(d=n.externalSources)==null?void 0:d.map(p=>p.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:o,conversationId:this.id,createdTimestamp:Date.now()},l=this._createStreamStateHandlers(t,a,{flags:this._chatFlagModel}),c=this._extensionClient.startChatStreamWithRetry(t,a,{flags:this._chatFlagModel});for await(const p of c){let f=p;for(const g of l)f=g.handleChunk(f)??f;yield f}for(const p of l)yield*p.handleComplete()}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}_fileNameToImageFormat(t){var s;switch((s=t.split(".").at(-1))==null?void 0:s.toLowerCase()){case"jpeg":case"jpg":return Yt.JPEG;case"png":return Yt.PNG;case"gif":return Yt.GIF;case"webp":return Yt.WEBP;default:return Yt.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let e,s=(t.structured_request_nodes??[]).filter(n=>n.type!==Bt.IDE_STATE);try{e=await this._extensionClient.getChatRequestIdeState()}catch(n){console.error("Failed to add IDE state to exchange:",n)}return e?(s=[...s,{id:Yi(s)+1,type:Bt.IDE_STATE,ide_state_node:e}],{...t,structured_request_nodes:s}):t}async maybeAddHistorySummaryNode(){var f;const t=this._chatFlagModel.historySummaryPrompt;if(!t||t.trim()==="")return!1;const e=this._convertHistoryToExchanges(this.chatHistory),[s,n]=jr(e,this._chatFlagModel.historySummaryLowerChars,this._chatFlagModel.historySummaryMaxChars);if(s.length===0)return!1;let i=((f=s.at(-1))==null?void 0:f.response_nodes)??[],o=i.filter(g=>g.type===J.TOOL_USE);o.length>0&&(s.at(-1).response_nodes=i.filter(g=>g.type!==J.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",s.length);const{responseText:a,requestId:l}=await this.sendSilentExchange({request_message:t,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:s}),c={chatItemType:xe.historySummary,summaryVersion:this.historySummaryVersion,request_id:l,request_message:t,response_text:a,structured_output_nodes:[{id:o.map(g=>g.id).reduce((g,v)=>Math.max(g,v),-1)+1,type:J.RAW_RESPONSE,content:a},...o],status:ht.success,seen_state:be.seen,timestamp:new Date().toISOString()},d=this.chatHistory.findIndex(g=>g.request_id===s.at(-1).request_id)+1;console.info("Adding a history summary node at index %d",d);const p=[...this._state.chatHistory];return p.splice(d,0,c),this.update({chatHistory:p}),!0}_clearStaleHistorySummaryNodes(){this.update({chatHistory:this.chatHistory.filter(t=>!Fe(t)||t.summaryVersion===this.historySummaryVersion)})}}function Ho(r,t){const e=(ze(r),r.fromTimestamp),s=(ze(r),r.toTimestamp),n=ze(r)&&r.revertTarget!==void 0;return{id:t,type:Bt.CHECKPOINT_REF,checkpoint_ref_node:{request_id:r.request_id||"",from_timestamp:e,to_timestamp:s,source:n?Lr.CHECKPOINT_REVERT:void 0}}}function jn(r){const t=(r.structured_output_nodes??[]).filter(e=>e.type===J.RAW_RESPONSE||e.type===J.TOOL_USE||e.type===J.TOOL_USE_START).map(e=>e.type===J.TOOL_USE_START?{...e,tool_use:{...e.tool_use,input_json:"{}"},type:J.TOOL_USE}:e);return{request_message:r.request_message,response_text:r.response_text??"",request_id:r.request_id||"",request_nodes:r.structured_request_nodes??[],response_nodes:t}}function Yi(r){return r.length>0?Math.max(...r.map(t=>t.id)):0}function Vn(r){var t;if(r.request_message.length>0&&!((t=r.structured_request_nodes)!=null&&t.some(e=>e.type===Bt.TEXT))){let e=r.structured_request_nodes??[];return e=[...e,{id:Yi(e)+1,type:Bt.TEXT,text_node:{content:r.request_message}}],{...r,structured_request_nodes:e}}return r}class Bo{constructor(t=!0,e=setTimeout){u(this,"_notify",new Set);u(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});u(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});u(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});u(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,n){if(!t&&!n)return()=>{};const i={timeout:t,notify:e,once:s,date:n};return this._notify.add(i),this._schedule(i),()=>{this._clearTimeout(i),this._notify.delete(i)}}}class Go{constructor(t=0,e=0,s=new Bo,n=st("busy"),i=st(!1)){u(this,"unsubNotify");u(this,"unsubMessage");u(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});u(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=n,this.focusAfterIdle=i,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var ds=(r=>(r.send="send",r.addTask="addTask",r))(ds||{});const sh=[{id:"send",label:"Send to Agent",icon:Vr,description:"Send message to agent"},{id:"addTask",label:"Add Task",icon:ao,description:"Add task with the message content"}];class Wo{constructor(){u(this,"_mode",st(ds.send));u(this,"_currentMode",ds.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(ds).includes(t)&&this._mode.set(t)}}const as=st("idle");var pt=(r=>(r.manual="manual",r.auto="auto",r))(pt||{});class jo{constructor(t,e,s,n={}){u(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});u(this,"extensionClient");u(this,"_chatFlagsModel");u(this,"_currConversationModel");u(this,"_chatModeModel");u(this,"_sendModeModel");u(this,"_currentChatMode");u(this,"_flagsLoaded",st(!1));u(this,"subscribers",new Set);u(this,"idleMessageModel",new Go);u(this,"isPanelCollapsed");u(this,"agentExecutionMode");u(this,"sortConversationsBy");u(this,"displayedAnnouncements");u(this,"onLoaded",async()=>{var s,n;const t=await this.extensionClient.getChatInitData(),e=!this._chatFlagsModel.doUseNewDraftFunctionality&&(t.enableBackgroundAgents||t.enableNewThreadsList);this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??Or,bigSyncThreshold:t.bigSyncThreshold??Nr,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??qr,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??kr.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:(t.enableBackgroundAgents??!1)||(t.enableNewThreadsList??!1),enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryMaxChars:t.historySummaryMaxChars??0,historySummaryLowerChars:t.historySummaryLowerChars??0,historySummaryPrompt:t.historySummaryPrompt??""}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._currentChatMode=t.currentChatMode,e&&this.onDoUseNewDraftFunctionalityChanged(),this._flagsLoaded.set(!0),(n=(s=this.options).onLoaded)==null||n.call(s),this.notifySubscribers()});u(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));u(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==qs&&this.currentConversationId!==qs||(delete this._state.conversations[qs],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===vt||Q.isValid(s))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});u(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const n=s.chatHistory.some(i=>Ce(i));t[e]={...s,isShareable:n}}this._state.conversations=t});u(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[n,i]of Object.entries(e))i.isPinned&&s.add(n);this.setState(this._state),this.notifySubscribers()});u(this,"saveImmediate",()=>{this._host.setState(this._state)});u(this,"setState",Gr(t=>{this._host.setState({...t,isPanelCollapsed:B(this.isPanelCollapsed),agentExecutionMode:B(this.agentExecutionMode),sortConversationsBy:B(this.sortConversationsBy),displayedAnnouncements:B(this.displayedAnnouncements),sendMode:this._sendModeModel.getCurrentMode()})},1e3,{maxWait:15e3}));u(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});u(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));u(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[vt];if(this.currentConversationId&&this.currentConversationId!==vt&&this._state.conversations[this.currentConversationId]&&Q.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const e={...this._state.conversations[this.currentConversationId],id:vt};this._state.conversations[vt]=e,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=vt,this._currConversationModel.setConversation(e)}});u(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:xe.educateFeatures,request_id:crypto.randomUUID(),seen_state:be.seen})});u(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});u(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let n;this.flags.doUseNewDraftFunctionality?(t===void 0&&(t=vt),n=this._state.conversations[t]??Q.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===vt&&(n.id=vt),s!=null&&s.newTaskUuid&&(n.rootTaskUuid=s.newTaskUuid)):t===void 0?(this.deleteInvalidConversations(Se(this._currConversationModel)?"agent":"chat"),n=Q.create({personaType:await this._currConversationModel.decidePersonaType()})):n=this._state.conversations[t]??Q.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid});const i=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(n,!i,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});u(this,"saveConversation",(t,e)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),e&&delete this._state.conversations[vt]});u(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});u(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});u(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;as.set("copying");const s=e==null?void 0:e.chatHistory,n=s.reduce((a,l)=>(Ce(l)&&a.push({request_id:l.request_id||"",request_message:l.request_message,response_text:l.response_text||""}),a),[]);if(n.length===0)throw new Error("No chat history to share");const i=Q.getDisplayName(e),o=await this.extensionClient.saveChat(t,n,i);if(o.data){let a=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:a}}}),a}throw new Error("Failed to create URL")});u(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void as.set("idle");navigator.clipboard.writeText(e),as.set("copied")}catch{as.set("failed")}});u(this,"deleteConversations",async(t,e=void 0,s=[],n)=>{const i=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${i>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const o=new Set(t);this.deleteConversationIds(o)}if(s.length>0&&n)for(const o of s)try{await n.deleteAgent(o,!0)}catch(a){console.error(`Failed to delete remote agent ${o}:`,a)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});u(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});u(this,"deleteConversationIds",async t=>{var s;const e=[];for(const n of t){const i=((s=this._state.conversations[n])==null?void 0:s.requestIds)??[];e.push(...i)}for(const n of Object.values(this._state.conversations))if(t.has(n.id)){for(const o of n.chatHistory)Ct(o)&&this.deleteImagesInExchange(o);const i=n.draftExchange;i&&this.deleteImagesInExchange(i)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([n])=>!t.has(n)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t)})});u(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});u(this,"findImagesInJson",t=>{const e=[],s=n=>{var i;if(n.type==="image"&&((i=n.attrs)!=null&&i.src))e.push(n.attrs.src);else if((n.type==="doc"||n.type==="paragraph")&&n.content)for(const o of n.content)s(o)};return s(t),e});u(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===Bt.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));u(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});u(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});u(this,"smartPaste",(t,e,s,n)=>{const i=this._currConversationModel.historyTo(t,!0).filter(o=>Ce(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:i,targetFile:s??void 0,options:n})});u(this,"saveImage",async t=>await this.extensionClient.saveImage(t));u(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));u(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=n,this._chatFlagsModel=new Fr(n.initialFlags),this.extensionClient=new br(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new Q(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new Wo,this.initialize(n.initialConversation);const i=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=st(i),this.agentExecutionMode=st(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=st(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=st(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get currentChatMode(){return this._currentChatMode}setCurrentChatMode(t){this._currentChatMode=t,this.extensionClient.setLastUsedChatMode(t)}get flagsLoaded(){return this._flagsLoaded}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}orderedConversations(t,e="desc",s){const n=t||this._state.sortConversationsBy||"lastMessageTimestamp";let i=Object.values(this._state.conversations);return s&&(i=i.filter(s)),i.sort((o,a)=>{const l=Q.getTime(o,n).getTime(),c=Q.getTime(a,n).getTime();return e==="asc"?l-c:c-l})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===vt)return!1;const n=!Q.isValid(this.conversations[s]),i=Se(this.conversations[s]);return n&&(t==="agent"&&i||t==="chat"&&!i||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===it.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.setToAgent("manual"):s.toLowerCase()==="chat"?this._chatModeModel.setToChat():console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}u(jo,"NEW_AGENT_KEY",vt);const ke=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Zn=new Set,en=typeof process=="object"&&process?process:{},Qi=(r,t,e,s)=>{typeof en.emitWarning=="function"?en.emitWarning(r,t,e,s):console.error(`[${e}] ${t}: ${r}`)};let ws=globalThis.AbortController,Yn=globalThis.AbortSignal;var Mi;if(ws===void 0){Yn=class{constructor(){u(this,"onabort");u(this,"_onabort",[]);u(this,"reason");u(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},ws=class{constructor(){u(this,"signal",new Yn);t()}abort(e){var s,n;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const i of this.signal._onabort)i(e);(n=(s=this.signal).onabort)==null||n.call(s,e)}}};let r=((Mi=en.env)==null?void 0:Mi.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{r&&(r=!1,Qi("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const ne=r=>r&&r===Math.floor(r)&&r>0&&isFinite(r),Xi=r=>ne(r)?r<=Math.pow(2,8)?Uint8Array:r<=Math.pow(2,16)?Uint16Array:r<=Math.pow(2,32)?Uint32Array:r<=Number.MAX_SAFE_INTEGER?us:null:null;class us extends Array{constructor(t){super(t),this.fill(0)}}var $e;const pe=class pe{constructor(t,e){u(this,"heap");u(this,"length");if(!h(pe,$e))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=Xi(t);if(!e)return[];E(pe,$e,!0);const s=new pe(t,e);return E(pe,$e,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};$e=new WeakMap,q(pe,$e,!1);let sn=pe;var Ri,Di,Lt,Et,Ft,Ot,Ee,Ae,ot,Nt,nt,Y,I,xt,At,yt,lt,qt,ct,zt,Pt,It,Ht,ae,Tt,w,rn,fe,Zt,Xe,Mt,Ki,ge,Ie,Ke,ie,re,on,ps,fs,Z,an,Ne,oe,ln;const Tn=class Tn{constructor(t){q(this,w);q(this,Lt);q(this,Et);q(this,Ft);q(this,Ot);q(this,Ee);q(this,Ae);u(this,"ttl");u(this,"ttlResolution");u(this,"ttlAutopurge");u(this,"updateAgeOnGet");u(this,"updateAgeOnHas");u(this,"allowStale");u(this,"noDisposeOnSet");u(this,"noUpdateTTL");u(this,"maxEntrySize");u(this,"sizeCalculation");u(this,"noDeleteOnFetchRejection");u(this,"noDeleteOnStaleGet");u(this,"allowStaleOnFetchAbort");u(this,"allowStaleOnFetchRejection");u(this,"ignoreFetchAbort");q(this,ot);q(this,Nt);q(this,nt);q(this,Y);q(this,I);q(this,xt);q(this,At);q(this,yt);q(this,lt);q(this,qt);q(this,ct);q(this,zt);q(this,Pt);q(this,It);q(this,Ht);q(this,ae);q(this,Tt);q(this,fe,()=>{});q(this,Zt,()=>{});q(this,Xe,()=>{});q(this,Mt,()=>!1);q(this,ge,t=>{});q(this,Ie,(t,e,s)=>{});q(this,Ke,(t,e,s,n)=>{if(s||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});u(this,Ri,"LRUCache");const{max:e=0,ttl:s,ttlResolution:n=1,ttlAutopurge:i,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:l,dispose:c,disposeAfter:d,noDisposeOnSet:p,noUpdateTTL:f,maxSize:g=0,maxEntrySize:v=0,sizeCalculation:m,fetchMethod:C,memoMethod:_,noDeleteOnFetchRejection:A,noDeleteOnStaleGet:R,allowStaleOnFetchRejection:O,allowStaleOnFetchAbort:b,ignoreFetchAbort:$}=t;if(e!==0&&!ne(e))throw new TypeError("max option must be a nonnegative integer");const P=e?Xi(e):Array;if(!P)throw new Error("invalid max value: "+e);if(E(this,Lt,e),E(this,Et,g),this.maxEntrySize=v||h(this,Et),this.sizeCalculation=m,this.sizeCalculation){if(!h(this,Et)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(_!==void 0&&typeof _!="function")throw new TypeError("memoMethod must be a function if defined");if(E(this,Ae,_),C!==void 0&&typeof C!="function")throw new TypeError("fetchMethod must be a function if specified");if(E(this,Ee,C),E(this,ae,!!C),E(this,nt,new Map),E(this,Y,new Array(e).fill(void 0)),E(this,I,new Array(e).fill(void 0)),E(this,xt,new P(e)),E(this,At,new P(e)),E(this,yt,0),E(this,lt,0),E(this,qt,sn.create(e)),E(this,ot,0),E(this,Nt,0),typeof c=="function"&&E(this,Ft,c),typeof d=="function"?(E(this,Ot,d),E(this,ct,[])):(E(this,Ot,void 0),E(this,ct,void 0)),E(this,Ht,!!h(this,Ft)),E(this,Tt,!!h(this,Ot)),this.noDisposeOnSet=!!p,this.noUpdateTTL=!!f,this.noDeleteOnFetchRejection=!!A,this.allowStaleOnFetchRejection=!!O,this.allowStaleOnFetchAbort=!!b,this.ignoreFetchAbort=!!$,this.maxEntrySize!==0){if(h(this,Et)!==0&&!ne(h(this,Et)))throw new TypeError("maxSize must be a positive integer if specified");if(!ne(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");x(this,w,Ki).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!R,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=ne(n)||n===0?n:1,this.ttlAutopurge=!!i,this.ttl=s||0,this.ttl){if(!ne(this.ttl))throw new TypeError("ttl must be a positive integer if specified");x(this,w,rn).call(this)}if(h(this,Lt)===0&&this.ttl===0&&h(this,Et)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!h(this,Lt)&&!h(this,Et)){const ve="LRU_CACHE_UNBOUNDED";(ss=>!Zn.has(ss))(ve)&&(Zn.add(ve),Qi("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",ve,Tn))}}static unsafeExposeInternals(t){return{starts:h(t,Pt),ttls:h(t,It),sizes:h(t,zt),keyMap:h(t,nt),keyList:h(t,Y),valList:h(t,I),next:h(t,xt),prev:h(t,At),get head(){return h(t,yt)},get tail(){return h(t,lt)},free:h(t,qt),isBackgroundFetch:e=>{var s;return x(s=t,w,Z).call(s,e)},backgroundFetch:(e,s,n,i)=>{var o;return x(o=t,w,fs).call(o,e,s,n,i)},moveToTail:e=>{var s;return x(s=t,w,Ne).call(s,e)},indexes:e=>{var s;return x(s=t,w,ie).call(s,e)},rindexes:e=>{var s;return x(s=t,w,re).call(s,e)},isStale:e=>{var s;return h(s=t,Mt).call(s,e)}}}get max(){return h(this,Lt)}get maxSize(){return h(this,Et)}get calculatedSize(){return h(this,Nt)}get size(){return h(this,ot)}get fetchMethod(){return h(this,Ee)}get memoMethod(){return h(this,Ae)}get dispose(){return h(this,Ft)}get disposeAfter(){return h(this,Ot)}getRemainingTTL(t){return h(this,nt).has(t)?1/0:0}*entries(){for(const t of x(this,w,ie).call(this))h(this,I)[t]===void 0||h(this,Y)[t]===void 0||x(this,w,Z).call(this,h(this,I)[t])||(yield[h(this,Y)[t],h(this,I)[t]])}*rentries(){for(const t of x(this,w,re).call(this))h(this,I)[t]===void 0||h(this,Y)[t]===void 0||x(this,w,Z).call(this,h(this,I)[t])||(yield[h(this,Y)[t],h(this,I)[t]])}*keys(){for(const t of x(this,w,ie).call(this)){const e=h(this,Y)[t];e===void 0||x(this,w,Z).call(this,h(this,I)[t])||(yield e)}}*rkeys(){for(const t of x(this,w,re).call(this)){const e=h(this,Y)[t];e===void 0||x(this,w,Z).call(this,h(this,I)[t])||(yield e)}}*values(){for(const t of x(this,w,ie).call(this))h(this,I)[t]===void 0||x(this,w,Z).call(this,h(this,I)[t])||(yield h(this,I)[t])}*rvalues(){for(const t of x(this,w,re).call(this))h(this,I)[t]===void 0||x(this,w,Z).call(this,h(this,I)[t])||(yield h(this,I)[t])}[(Di=Symbol.iterator,Ri=Symbol.toStringTag,Di)](){return this.entries()}find(t,e={}){for(const s of x(this,w,ie).call(this)){const n=h(this,I)[s],i=x(this,w,Z).call(this,n)?n.__staleWhileFetching:n;if(i!==void 0&&t(i,h(this,Y)[s],this))return this.get(h(this,Y)[s],e)}}forEach(t,e=this){for(const s of x(this,w,ie).call(this)){const n=h(this,I)[s],i=x(this,w,Z).call(this,n)?n.__staleWhileFetching:n;i!==void 0&&t.call(e,i,h(this,Y)[s],this)}}rforEach(t,e=this){for(const s of x(this,w,re).call(this)){const n=h(this,I)[s],i=x(this,w,Z).call(this,n)?n.__staleWhileFetching:n;i!==void 0&&t.call(e,i,h(this,Y)[s],this)}}purgeStale(){let t=!1;for(const e of x(this,w,re).call(this,{allowStale:!0}))h(this,Mt).call(this,e)&&(x(this,w,oe).call(this,h(this,Y)[e],"expire"),t=!0);return t}info(t){const e=h(this,nt).get(t);if(e===void 0)return;const s=h(this,I)[e],n=x(this,w,Z).call(this,s)?s.__staleWhileFetching:s;if(n===void 0)return;const i={value:n};if(h(this,It)&&h(this,Pt)){const o=h(this,It)[e],a=h(this,Pt)[e];if(o&&a){const l=o-(ke.now()-a);i.ttl=l,i.start=Date.now()}}return h(this,zt)&&(i.size=h(this,zt)[e]),i}dump(){const t=[];for(const e of x(this,w,ie).call(this,{allowStale:!0})){const s=h(this,Y)[e],n=h(this,I)[e],i=x(this,w,Z).call(this,n)?n.__staleWhileFetching:n;if(i===void 0||s===void 0)continue;const o={value:i};if(h(this,It)&&h(this,Pt)){o.ttl=h(this,It)[e];const a=ke.now()-h(this,Pt)[e];o.start=Math.floor(Date.now()-a)}h(this,zt)&&(o.size=h(this,zt)[e]),t.unshift([s,o])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const n=Date.now()-s.start;s.start=ke.now()-n}this.set(e,s.value,s)}}set(t,e,s={}){var f,g,v,m,C;if(e===void 0)return this.delete(t),this;const{ttl:n=this.ttl,start:i,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:l}=s;let{noUpdateTTL:c=this.noUpdateTTL}=s;const d=h(this,Ke).call(this,t,e,s.size||0,a);if(this.maxEntrySize&&d>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),x(this,w,oe).call(this,t,"set"),this;let p=h(this,ot)===0?void 0:h(this,nt).get(t);if(p===void 0)p=h(this,ot)===0?h(this,lt):h(this,qt).length!==0?h(this,qt).pop():h(this,ot)===h(this,Lt)?x(this,w,ps).call(this,!1):h(this,ot),h(this,Y)[p]=t,h(this,I)[p]=e,h(this,nt).set(t,p),h(this,xt)[h(this,lt)]=p,h(this,At)[p]=h(this,lt),E(this,lt,p),ns(this,ot)._++,h(this,Ie).call(this,p,d,l),l&&(l.set="add"),c=!1;else{x(this,w,Ne).call(this,p);const _=h(this,I)[p];if(e!==_){if(h(this,ae)&&x(this,w,Z).call(this,_)){_.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:A}=_;A===void 0||o||(h(this,Ht)&&((f=h(this,Ft))==null||f.call(this,A,t,"set")),h(this,Tt)&&((g=h(this,ct))==null||g.push([A,t,"set"])))}else o||(h(this,Ht)&&((v=h(this,Ft))==null||v.call(this,_,t,"set")),h(this,Tt)&&((m=h(this,ct))==null||m.push([_,t,"set"])));if(h(this,ge).call(this,p),h(this,Ie).call(this,p,d,l),h(this,I)[p]=e,l){l.set="replace";const A=_&&x(this,w,Z).call(this,_)?_.__staleWhileFetching:_;A!==void 0&&(l.oldValue=A)}}else l&&(l.set="update")}if(n===0||h(this,It)||x(this,w,rn).call(this),h(this,It)&&(c||h(this,Xe).call(this,p,n,i),l&&h(this,Zt).call(this,l,p)),!o&&h(this,Tt)&&h(this,ct)){const _=h(this,ct);let A;for(;A=_==null?void 0:_.shift();)(C=h(this,Ot))==null||C.call(this,...A)}return this}pop(){var t;try{for(;h(this,ot);){const e=h(this,I)[h(this,yt)];if(x(this,w,ps).call(this,!0),x(this,w,Z).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(h(this,Tt)&&h(this,ct)){const e=h(this,ct);let s;for(;s=e==null?void 0:e.shift();)(t=h(this,Ot))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:n}=e,i=h(this,nt).get(t);if(i!==void 0){const o=h(this,I)[i];if(x(this,w,Z).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!h(this,Mt).call(this,i))return s&&h(this,fe).call(this,i),n&&(n.has="hit",h(this,Zt).call(this,n,i)),!0;n&&(n.has="stale",h(this,Zt).call(this,n,i))}else n&&(n.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,n=h(this,nt).get(t);if(n===void 0||!s&&h(this,Mt).call(this,n))return;const i=h(this,I)[n];return x(this,w,Z).call(this,i)?i.__staleWhileFetching:i}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:l=0,sizeCalculation:c=this.sizeCalculation,noUpdateTTL:d=this.noUpdateTTL,noDeleteOnFetchRejection:p=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:f=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:v=this.allowStaleOnFetchAbort,context:m,forceRefresh:C=!1,status:_,signal:A}=e;if(!h(this,ae))return _&&(_.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:i,status:_});const R={allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:i,ttl:o,noDisposeOnSet:a,size:l,sizeCalculation:c,noUpdateTTL:d,noDeleteOnFetchRejection:p,allowStaleOnFetchRejection:f,allowStaleOnFetchAbort:v,ignoreFetchAbort:g,status:_,signal:A};let O=h(this,nt).get(t);if(O===void 0){_&&(_.fetch="miss");const b=x(this,w,fs).call(this,t,O,R,m);return b.__returned=b}{const b=h(this,I)[O];if(x(this,w,Z).call(this,b)){const ss=s&&b.__staleWhileFetching!==void 0;return _&&(_.fetch="inflight",ss&&(_.returnedStale=!0)),ss?b.__staleWhileFetching:b.__returned=b}const $=h(this,Mt).call(this,O);if(!C&&!$)return _&&(_.fetch="hit"),x(this,w,Ne).call(this,O),n&&h(this,fe).call(this,O),_&&h(this,Zt).call(this,_,O),b;const P=x(this,w,fs).call(this,t,O,R,m),ve=P.__staleWhileFetching!==void 0&&s;return _&&(_.fetch=$?"stale":"refresh",ve&&$&&(_.returnedStale=!0)),ve?P.__staleWhileFetching:P.__returned=P}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=h(this,Ae);if(!s)throw new Error("no memoMethod provided to constructor");const{context:n,forceRefresh:i,...o}=e,a=this.get(t,o);if(!i&&a!==void 0)return a;const l=s(t,a,{options:o,context:n});return this.set(t,l,o),l}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,status:o}=e,a=h(this,nt).get(t);if(a!==void 0){const l=h(this,I)[a],c=x(this,w,Z).call(this,l);return o&&h(this,Zt).call(this,o,a),h(this,Mt).call(this,a)?(o&&(o.get="stale"),c?(o&&s&&l.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?l.__staleWhileFetching:void 0):(i||x(this,w,oe).call(this,t,"expire"),o&&s&&(o.returnedStale=!0),s?l:void 0)):(o&&(o.get="hit"),c?l.__staleWhileFetching:(x(this,w,Ne).call(this,a),n&&h(this,fe).call(this,a),l))}o&&(o.get="miss")}delete(t){return x(this,w,oe).call(this,t,"delete")}clear(){return x(this,w,ln).call(this,"delete")}};Lt=new WeakMap,Et=new WeakMap,Ft=new WeakMap,Ot=new WeakMap,Ee=new WeakMap,Ae=new WeakMap,ot=new WeakMap,Nt=new WeakMap,nt=new WeakMap,Y=new WeakMap,I=new WeakMap,xt=new WeakMap,At=new WeakMap,yt=new WeakMap,lt=new WeakMap,qt=new WeakMap,ct=new WeakMap,zt=new WeakMap,Pt=new WeakMap,It=new WeakMap,Ht=new WeakMap,ae=new WeakMap,Tt=new WeakMap,w=new WeakSet,rn=function(){const t=new us(h(this,Lt)),e=new us(h(this,Lt));E(this,It,t),E(this,Pt,e),E(this,Xe,(i,o,a=ke.now())=>{if(e[i]=o!==0?a:0,t[i]=o,o!==0&&this.ttlAutopurge){const l=setTimeout(()=>{h(this,Mt).call(this,i)&&x(this,w,oe).call(this,h(this,Y)[i],"expire")},o+1);l.unref&&l.unref()}}),E(this,fe,i=>{e[i]=t[i]!==0?ke.now():0}),E(this,Zt,(i,o)=>{if(t[o]){const a=t[o],l=e[o];if(!a||!l)return;i.ttl=a,i.start=l,i.now=s||n();const c=i.now-l;i.remainingTTL=a-c}});let s=0;const n=()=>{const i=ke.now();if(this.ttlResolution>0){s=i;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return i};this.getRemainingTTL=i=>{const o=h(this,nt).get(i);if(o===void 0)return 0;const a=t[o],l=e[o];return!a||!l?1/0:a-((s||n())-l)},E(this,Mt,i=>{const o=e[i],a=t[i];return!!a&&!!o&&(s||n())-o>a})},fe=new WeakMap,Zt=new WeakMap,Xe=new WeakMap,Mt=new WeakMap,Ki=function(){const t=new us(h(this,Lt));E(this,Nt,0),E(this,zt,t),E(this,ge,e=>{E(this,Nt,h(this,Nt)-t[e]),t[e]=0}),E(this,Ke,(e,s,n,i)=>{if(x(this,w,Z).call(this,s))return 0;if(!ne(n)){if(!i)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof i!="function")throw new TypeError("sizeCalculation must be a function");if(n=i(s,e),!ne(n))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return n}),E(this,Ie,(e,s,n)=>{if(t[e]=s,h(this,Et)){const i=h(this,Et)-t[e];for(;h(this,Nt)>i;)x(this,w,ps).call(this,!0)}E(this,Nt,h(this,Nt)+t[e]),n&&(n.entrySize=s,n.totalCalculatedSize=h(this,Nt))})},ge=new WeakMap,Ie=new WeakMap,Ke=new WeakMap,ie=function*({allowStale:t=this.allowStale}={}){if(h(this,ot))for(let e=h(this,lt);x(this,w,on).call(this,e)&&(!t&&h(this,Mt).call(this,e)||(yield e),e!==h(this,yt));)e=h(this,At)[e]},re=function*({allowStale:t=this.allowStale}={}){if(h(this,ot))for(let e=h(this,yt);x(this,w,on).call(this,e)&&(!t&&h(this,Mt).call(this,e)||(yield e),e!==h(this,lt));)e=h(this,xt)[e]},on=function(t){return t!==void 0&&h(this,nt).get(h(this,Y)[t])===t},ps=function(t){var i,o;const e=h(this,yt),s=h(this,Y)[e],n=h(this,I)[e];return h(this,ae)&&x(this,w,Z).call(this,n)?n.__abortController.abort(new Error("evicted")):(h(this,Ht)||h(this,Tt))&&(h(this,Ht)&&((i=h(this,Ft))==null||i.call(this,n,s,"evict")),h(this,Tt)&&((o=h(this,ct))==null||o.push([n,s,"evict"]))),h(this,ge).call(this,e),t&&(h(this,Y)[e]=void 0,h(this,I)[e]=void 0,h(this,qt).push(e)),h(this,ot)===1?(E(this,yt,E(this,lt,0)),h(this,qt).length=0):E(this,yt,h(this,xt)[e]),h(this,nt).delete(s),ns(this,ot)._--,e},fs=function(t,e,s,n){const i=e===void 0?void 0:h(this,I)[e];if(x(this,w,Z).call(this,i))return i;const o=new ws,{signal:a}=s;a==null||a.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});const l={signal:o.signal,options:s,context:n},c=(g,v=!1)=>{const{aborted:m}=o.signal,C=s.ignoreFetchAbort&&g!==void 0;if(s.status&&(m&&!v?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,C&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),m&&!C&&!v)return d(o.signal.reason);const _=p;return h(this,I)[e]===p&&(g===void 0?_.__staleWhileFetching?h(this,I)[e]=_.__staleWhileFetching:x(this,w,oe).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,g,l.options))),g},d=g=>{const{aborted:v}=o.signal,m=v&&s.allowStaleOnFetchAbort,C=m||s.allowStaleOnFetchRejection,_=C||s.noDeleteOnFetchRejection,A=p;if(h(this,I)[e]===p&&(!_||A.__staleWhileFetching===void 0?x(this,w,oe).call(this,t,"fetch"):m||(h(this,I)[e]=A.__staleWhileFetching)),C)return s.status&&A.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),A.__staleWhileFetching;if(A.__returned===A)throw g};s.status&&(s.status.fetchDispatched=!0);const p=new Promise((g,v)=>{var C;const m=(C=h(this,Ee))==null?void 0:C.call(this,t,i,l);m&&m instanceof Promise&&m.then(_=>g(_===void 0?void 0:_),v),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(g(void 0),s.allowStaleOnFetchAbort&&(g=_=>c(_,!0)))})}).then(c,g=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=g),d(g))),f=Object.assign(p,{__abortController:o,__staleWhileFetching:i,__returned:void 0});return e===void 0?(this.set(t,f,{...l.options,status:void 0}),e=h(this,nt).get(t)):h(this,I)[e]=f,f},Z=function(t){if(!h(this,ae))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof ws},an=function(t,e){h(this,At)[e]=t,h(this,xt)[t]=e},Ne=function(t){t!==h(this,lt)&&(t===h(this,yt)?E(this,yt,h(this,xt)[t]):x(this,w,an).call(this,h(this,At)[t],h(this,xt)[t]),x(this,w,an).call(this,h(this,lt),t),E(this,lt,t))},oe=function(t,e){var n,i,o,a;let s=!1;if(h(this,ot)!==0){const l=h(this,nt).get(t);if(l!==void 0)if(s=!0,h(this,ot)===1)x(this,w,ln).call(this,e);else{h(this,ge).call(this,l);const c=h(this,I)[l];if(x(this,w,Z).call(this,c)?c.__abortController.abort(new Error("deleted")):(h(this,Ht)||h(this,Tt))&&(h(this,Ht)&&((n=h(this,Ft))==null||n.call(this,c,t,e)),h(this,Tt)&&((i=h(this,ct))==null||i.push([c,t,e]))),h(this,nt).delete(t),h(this,Y)[l]=void 0,h(this,I)[l]=void 0,l===h(this,lt))E(this,lt,h(this,At)[l]);else if(l===h(this,yt))E(this,yt,h(this,xt)[l]);else{const d=h(this,At)[l];h(this,xt)[d]=h(this,xt)[l];const p=h(this,xt)[l];h(this,At)[p]=h(this,At)[l]}ns(this,ot)._--,h(this,qt).push(l)}}if(h(this,Tt)&&((o=h(this,ct))!=null&&o.length)){const l=h(this,ct);let c;for(;c=l==null?void 0:l.shift();)(a=h(this,Ot))==null||a.call(this,...c)}return s},ln=function(t){var e,s,n;for(const i of x(this,w,re).call(this,{allowStale:!0})){const o=h(this,I)[i];if(x(this,w,Z).call(this,o))o.__abortController.abort(new Error("deleted"));else{const a=h(this,Y)[i];h(this,Ht)&&((e=h(this,Ft))==null||e.call(this,o,a,t)),h(this,Tt)&&((s=h(this,ct))==null||s.push([o,a,t]))}}if(h(this,nt).clear(),h(this,I).fill(void 0),h(this,Y).fill(void 0),h(this,It)&&h(this,Pt)&&(h(this,It).fill(0),h(this,Pt).fill(0)),h(this,zt)&&h(this,zt).fill(0),E(this,yt,0),E(this,lt,0),h(this,qt).length=0,E(this,Nt,0),E(this,ot,0),h(this,Tt)&&h(this,ct)){const i=h(this,ct);let o;for(;o=i==null?void 0:i.shift();)(n=h(this,Ot))==null||n.call(this,...o)}};let nn=Tn;class nh{constructor(){u(this,"_syncStatus",{status:wr.done,foldersProgress:[]});u(this,"_syncEnabledState",Sn.initializing);u(this,"_workspaceGuidelines",[]);u(this,"_openUserGuidelinesInput",!1);u(this,"_userGuidelines");u(this,"_contextStore",new Vo);u(this,"_prevOpenFiles",[]);u(this,"_disableContext",!1);u(this,"_enableAgentMemories",!1);u(this,"subscribers",new Set);u(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));u(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case it.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case it.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case it.fileRangesSelected:this.updateSelections(e.data);break;case it.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case it.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case it.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});u(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:at.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesEnabled)+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});u(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});u(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});u(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});u(this,"addFile",t=>{this.addFiles([t])});u(this,"addFiles",t=>{this.updateFiles(t,[])});u(this,"removeFile",t=>{this.removeFiles([t])});u(this,"removeFiles",t=>{this.updateFiles([],t)});u(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});u(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});u(this,"updateFiles",(t,e)=>{const s=o=>({file:o,...zs(o)}),n=t.map(s),i=e.map(s);this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});u(this,"updateRules",(t,e)=>{const s=o=>({rule:o,...$r(o)}),n=t.map(s),i=e.map(s);this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});u(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});u(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});u(this,"setCurrentlyOpenFiles",t=>{const e=t.map(n=>({recentFile:n,...zs(n)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,n=>n.id),s.forEach(n=>{const i=this._contextStore.peekKey(n.id);i!=null&&i.recentFile&&(i.file=i.recentFile,delete i.recentFile)}),e.forEach(n=>{const i=this._contextStore.peekKey(n.id);i!=null&&i.file&&(i.recentFile=i.file,delete i.file)}),this.notifySubscribers()});u(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});u(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,n=t.overLimit||((e==null?void 0:e.overLimit)??!1),i={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:at.active,referenceCount:1,showWarning:n,rulesAndGuidelinesState:e};this._contextStore.update([i],s,o=>o.id),this.notifySubscribers()});u(this,"onGuidelinesStateUpdate",t=>{var n;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const i=e||{enabled:!1,overLimit:!1,contents:"",lengthLimit:((n=t.rulesAndGuidelines)==null?void 0:n.lengthLimit)??2e3};this.updateUserGuidelines(i,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(i=>i.sourceFolder))});u(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(n=>n.workspaceFolder===e.folderRoot);return{...e,guidelinesEnabled:(s==null?void 0:s.enabled)??!1,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));u(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});u(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});u(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});u(this,"updateSelections",t=>{const e=this._contextStore.values.filter(En),s=t.map(n=>({selection:n,...zs(n)}));this._contextStore.update([],e,n=>n.id),this._contextStore.update(s,[],n=>n.id),this.notifySubscribers()});u(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});u(this,"markInactive",t=>{this.markItemsInactive([t])});u(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,at.inactive)}),this.notifySubscribers()});u(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});u(this,"markActive",t=>{this.markItemsActive([t])});u(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,at.active)}),this.notifySubscribers()});u(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});u(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});u(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});u(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>xr(t)&&!$n(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter($n)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(En)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Tr)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(An)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(Cr)}get userGuidelines(){return this._contextStore.values.filter(In)}get agentMemories(){return[{...Sr,status:this._enableAgentMemories?at.active:at.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>Mn(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===at.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===at.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===at.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===at.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===at.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===at.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var l;if(this.syncEnabledState===Sn.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(c=>c.progress!==void 0);if(t.length===0)return;const e=t.reduce((c,d)=>{var p;return c+(((p=d==null?void 0:d.progress)==null?void 0:p.trackedFiles)??0)},0),s=t.reduce((c,d)=>{var p;return c+(((p=d==null?void 0:d.progress)==null?void 0:p.backlogSize)??0)},0),n=Math.max(e,0),i=Math.min(Math.max(s,0),n),o=n-i,a=[];for(const c of t)(l=c==null?void 0:c.progress)!=null&&l.newlyTracked&&a.push(c.folderRoot);return{status:this._syncStatus.status,totalFiles:n,syncedCount:o,backlogSize:i,newlyTrackedFolders:a}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(An(t)||In(t)||Fi(t)||Mn(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===at.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===at.inactive)}get isContextDisabled(){return this._disableContext}}class Vo{constructor(){u(this,"_cache",new nn({max:1e3}));u(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));u(this,"clear",()=>{this._cache.clear()});u(this,"update",(t,e,s)=>{t.forEach(n=>this.addInPlace(n,s)),e.forEach(n=>this.removeInPlace(n,s))});u(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});u(this,"addInPlace",(t,e)=>{const s=e(t),n=t.referenceCount??1,i=this._cache.get(s),o=t.status??(i==null?void 0:i.status)??at.active;i?(i.referenceCount+=n,i.status=o,i.pinned=t.pinned??i.pinned,i.showWarning=t.showWarning??i.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in i&&(i.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in i&&(i.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:n,status:o})});u(this,"removeInPlace",(t,e)=>{const s=e(t),n=this._cache.get(s);n&&(n.referenceCount-=1,n.referenceCount===0&&this._cache.delete(s))});u(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});u(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});u(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});u(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});u(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===at.active?at.inactive:at.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Zo{constructor(t){u(this,"_githubTimeoutMs",3e4);u(this,"_currentRepositoryUrl",st(void 0));this._asyncMsgSender=t}async listUserRepos(){try{const t=await this._asyncMsgSender.send({type:it.listGithubReposForAuthenticatedUserRequest},this._githubTimeoutMs);return{repos:t.data.repos,error:t.data.error,isDevDeploy:t.data.isDevDeploy}}catch(t){return console.error("Failed to list user repos:",t),{repos:[],error:`Error: ${t instanceof Error?t.message:String(t)}`}}}async listRepoBranches(t,e){try{const s=await this._asyncMsgSender.send({type:it.listGithubRepoBranchesRequest,data:{repo:t,page:e}},this._githubTimeoutMs);return{branches:s.data.branches,hasNextPage:s.data.hasNextPage,nextPage:s.data.nextPage,error:s.data.error,isDevDeploy:s.data.isDevDeploy}}catch(s){return console.error("Failed to list repo branches:",s),{branches:[],hasNextPage:!1,nextPage:0,error:`Error: ${s instanceof Error?s.message:String(s)}`}}}async getGithubRepo(t){try{const e=await this._asyncMsgSender.send({type:it.getGithubRepoRequest,data:{repo:t}},this._githubTimeoutMs);return{repo:e.data.repo,error:e.data.error,isDevDeploy:e.data.isDevDeploy}}catch(e){return console.error("Failed to get GitHub repo:",e),{repo:t,error:`Error: ${e instanceof Error?e.message:String(e)}`}}}async getCurrentLocalBranch(){try{return(await this._asyncMsgSender.send({type:it.getCurrentLocalBranchRequest},1e4)).data.branch}catch(t){return void console.error("Failed to get current local branch:",t)}}async listBranches(t=""){try{return{branches:(await this._asyncMsgSender.send({type:it.getGitBranchesRequest,data:{prefix:t}},1e4)).data.branches}}catch(e){return console.error("Failed to fetch branches:",e),{branches:[]}}}async getWorkspaceDiff(t){try{return(await this._asyncMsgSender.send({type:it.getWorkspaceDiffRequest,data:{branchName:t}},1e4)).data.diff}catch(e){return console.error("Failed to get workspace diff:",e),""}}get currentRepositoryUrl(){return this._currentRepositoryUrl}async getRemoteUrl(){const t=B(this._currentRepositoryUrl);if(t)return{remoteUrl:t};try{const e=await this._asyncMsgSender.send({type:it.getRemoteUrlRequest},1e4);return e.data.error?this._currentRepositoryUrl.set(void 0):this._currentRepositoryUrl.set(e.data.remoteUrl),{remoteUrl:e.data.remoteUrl}}catch(e){return console.error("Failed to get remote url:",e),{remoteUrl:"",error:`Error: ${e instanceof Error?e.message:String(e)}`}}}async fetch(){try{await this._asyncMsgSender.send({type:it.gitFetchRequest},1e4)}catch(t){console.error("Failed to fetch remote branch:",t)}}async isGitRepository(){try{return(await this._asyncMsgSender.send({type:it.isGitRepositoryRequest},1e4)).data.isGitRepository}catch(t){return console.error("Failed to check if is git repository:",t),!1}}async isGithubAuthenticated(){try{return(await this._asyncMsgSender.send({type:it.isGithubAuthenticatedRequest},this._githubTimeoutMs)).data.isAuthenticated}catch(t){return console.error("Failed to check GitHub authentication status:",t),!1}}async authenticateGithub(){try{const t=await this._asyncMsgSender.send({type:it.authenticateGithubRequest},this._githubTimeoutMs);return{success:t.data.success,message:t.data.message,url:t.data.url}}catch(t){return console.error("Failed to authenticate with GitHub:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}async revokeGithubAccess(){try{const t=await this._asyncMsgSender.send({type:it.revokeGithubAccessRequest},1e4);return{success:t.data.success,message:t.data.message}}catch(t){return console.error("Failed to revoke GitHub access:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}}u(Zo,"key","gitReferenceModel");const Yo={doHideStatusBars:!1,doHideSlashActions:!1,doHideAtMentions:!1,doHideNewThreadButton:!1,doHideMultimodalActions:!1,doHideContextBar:!1,doShowTurnSelector:!1},Qo={doHideStatusBars:!0,doHideSlashActions:!0,doHideAtMentions:!0,doHideNewThreadButton:!0,doHideMultimodalActions:!0,doHideContextBar:!0,doShowTurnSelector:!0},ih="selectedTurnIndex";function rh(r){let t=Yo;return r!=null&&r.isActive&&(t=Qo,r.isRemoteAgentSshWindow&&(t.doHideAtMentions=!1)),t}var Xo=Xr,Ko=/\s/,Jo=function(r){for(var t=r.length;t--&&Ko.test(r.charAt(t)););return t},ta=/^\s+/,ea=Kr,sa=Jr,na=function(r){return r&&r.slice(0,Jo(r)+1).replace(ta,"")},Qn=mn,ia=function(r){return typeof r=="symbol"||sa(r)&&ea(r)=="[object Symbol]"},ra=/^[-+]0x[0-9a-f]+$/i,oa=/^0b[01]+$/i,aa=/^0o[0-7]+$/i,la=parseInt,ca=mn,Gs=function(){return Xo.Date.now()},Xn=function(r){if(typeof r=="number")return r;if(ia(r))return NaN;if(Qn(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=Qn(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=na(r);var e=oa.test(r);return e||aa.test(r)?la(r.slice(2),e?2:8):ra.test(r)?NaN:+r},ha=Math.max,da=Math.min,ua=function(r,t,e){var s,n,i,o,a,l,c=0,d=!1,p=!1,f=!0;if(typeof r!="function")throw new TypeError("Expected a function");function g(A){var R=s,O=n;return s=n=void 0,c=A,o=r.apply(O,R)}function v(A){var R=A-l;return l===void 0||R>=t||R<0||p&&A-c>=i}function m(){var A=Gs();if(v(A))return C(A);a=setTimeout(m,function(R){var O=t-(R-l);return p?da(O,i-(R-c)):O}(A))}function C(A){return a=void 0,f&&s?g(A):(s=n=void 0,o)}function _(){var A=Gs(),R=v(A);if(s=arguments,n=this,l=A,R){if(a===void 0)return function(O){return c=O,a=setTimeout(m,t),d?g(O):o}(l);if(p)return clearTimeout(a),a=setTimeout(m,t),g(l)}return a===void 0&&(a=setTimeout(m,t)),o}return t=Xn(t)||0,ca(e)&&(d=!!e.leading,i=(p="maxWait"in e)?ha(Xn(e.maxWait)||0,t):i,f="trailing"in e?!!e.trailing:f),_.cancel=function(){a!==void 0&&clearTimeout(a),c=0,s=l=n=a=void 0},_.flush=function(){return a===void 0?o:C(Gs())},_},pa=mn;const fa=Oi(function(r,t,e){var s=!0,n=!0;if(typeof r!="function")throw new TypeError("Expected a function");return pa(e)&&(s="leading"in e?!!e.leading:s,n="trailing"in e?!!e.trailing:n),ua(r,t,{leading:s,maxWait:t,trailing:n})});function ga(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M160 368c26.5 0 48 21.5 48 48v16l72.5-54.4c8.3-6.2 18.4-9.6 28.8-9.6H448c8.8 0 16-7.2 16-16V64c0-8.8-7.2-16-16-16H64c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16zm48 124-.2.2-5.1 3.8-17.1 12.8c-4.8 3.6-11.3 4.2-16.8 1.5s-8.8-8.2-8.8-14.3v-80H64c-35.3 0-64-28.7-64-64V64C0 28.7 28.7 0 64 0h384c35.3 0 64 28.7 64 64v288c0 35.3-28.7 64-64 64H309.3z"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function ma(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class _a extends W{constructor(t){super(),j(this,t,ma,ga,V,{})}}function ya(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M410.8 134.2c-19.3 8.6-42 3.5-55.9-12.5C332.8 96.1 300.3 80 264 80c-66.3 0-120 53.7-120 120v.2c0 20.4-12.8 38.5-32 45.3-37.4 13.2-64 48.8-64 90.5 0 53 43 96 96 96h363.3c.6-.1 1.3-.1 1.9-.2 46.2-2.7 82.8-41 82.8-87.8 0-36-21.6-67.1-52.8-80.7-20.1-8.8-31.6-30-28.1-51.7.6-3.8.9-7.7.9-11.7 0-39.8-32.2-72-72-72-10.5 0-20.4 2.2-29.2 6.2zM512 479.8v.2H144C64.5 480 0 415.5 0 336c0-62.7 40.1-116 96-135.8v-.2c0-92.8 75.2-168 168-168 50.9 0 96.4 22.6 127.3 58.3C406.2 83.7 422.6 80 440 80c66.3 0 120 53.7 120 120 0 6.6-.5 13-1.5 19.3 48 21 81.5 68.9 81.5 124.7 0 72.4-56.6 131.6-128 135.8"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function va(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class wa extends W{constructor(t){super(),j(this,t,va,ya,V,{})}}function ka(r){const{isConversationAgentic:t,agentExecutionMode:e,isBackgroundAgent:s}=r;return s?{icon:wa,type:"remoteAgent",primaryText:"Remote Agent",bannerText:"Running in the cloud"}:t?(e===pt.manual||pt.auto,{icon:zr,type:"localAgent",primaryText:"Agent",bannerText:"Running locally"}):{icon:_a,type:"chat",primaryText:"Chat",bannerText:"Running locally"}}class ba{constructor(t,e,s){u(this,"_prevRemoteAgentWindowOpts",{remoteAgentId:void 0,isRemoteAgentSshWindow:!1});this._chatModel=t,this._agentConversationModel=e,this._remoteAgentsModel=s,this._remoteAgentsModel.subscribe(n=>{n.isRemoteAgentSshWindow===this._prevRemoteAgentWindowOpts.isRemoteAgentSshWindow&&n.remoteAgentSshWindowId===this._prevRemoteAgentWindowOpts.remoteAgentId||(this._prevRemoteAgentWindowOpts={remoteAgentId:n.remoteAgentSshWindowId,isRemoteAgentSshWindow:n.isRemoteAgentSshWindow},this.handleisRemoteAgentSshWindow(n))})}handleisRemoteAgentSshWindow(t){t.isRemoteAgentSshWindow&&this.setToRemoteAgent(t.remoteAgentSshWindowId)}async toggleChatAgentMode(t=!1){t?await this.toggleChatAgentModeReverse():await this.toggleChatAgentModeForward()}async toggleChatAgentModeForward(){const t=B(this._agentConversationModel.isCurrConversationAgentic),e=B(this._chatModel.agentExecutionMode),s=this._remoteAgentsModel.isActive,n=this._chatModel.flags.enableBackgroundAgents,i=this._chatModel.flags.enableAgentAutoMode;s?this.handleSetToChat():t?t&&e===pt.manual&&i?await this.handleSetToAgent(pt.auto):t&&e===pt.auto&&n?(this.handleSetToBackgroundAgent(),this._chatModel.agentExecutionMode.set(pt.manual)):this.handleSetToChat():await this.handleSetToAgent(pt.manual)}async toggleChatAgentModeReverse(){const t=B(this._agentConversationModel.isCurrConversationAgentic),e=B(this._chatModel.agentExecutionMode),s=this._remoteAgentsModel.isActive,n=this._chatModel.flags.enableBackgroundAgents,i=this._chatModel.flags.enableAgentAutoMode;s?await this.handleSetToAgent(pt.auto):t?t&&e===pt.auto?await this.handleSetToAgent(pt.manual):(t&&pt.manual,this.handleSetToChat()):n?this.handleSetToBackgroundAgent():i?await this.handleSetToAgent(pt.auto):await this.handleSetToAgent(pt.manual)}get isRemoteAgentSshWindow(){return this._remoteAgentsModel.isRemoteAgentSshWindow}get remoteAgentSshWindowId(){return this._remoteAgentsModel.remoteAgentSshWindowId}setToChat(){this.isRemoteAgentSshWindow||(this._agentConversationModel?(this._remoteAgentsModel.setIsActive(!1),this._agentConversationModel.setToChat(),this._agentConversationModel.refreshAutoModeAcceptance(),this._chatModel.currentConversationModel.resetTotalCharactersCache(),this._chatModel.setCurrentChatMode(Hs.chat)):console.error("AgentConversationModel is not initialized"))}async setToAgent(t){this.isRemoteAgentSshWindow||(this._agentConversationModel?(this._remoteAgentsModel.setIsActive(!1),await this._agentConversationModel.setToAgentic(),this._chatModel.agentExecutionMode.set(t),await this._agentConversationModel.refreshAutoModeAcceptance(),this._chatModel.currentConversationModel.resetTotalCharactersCache(),this._chatModel.setCurrentChatMode(Hs.agent)):console.error("AgentConversationModel is not initialized"))}async setToRemoteAgent(t){var e;if(this._agentConversationModel){if(this.isRemoteAgentSshWindow&&(t&&this.remoteAgentSshWindowId&&t!==this.remoteAgentSshWindowId&&(t=this.remoteAgentSshWindowId),!t&&this.remoteAgentSshWindowId&&(t=this.remoteAgentSshWindowId)),this._remoteAgentsModel.setIsActive(!0),t)this._remoteAgentsModel.setCurrentAgent(t);else if(t===null)this._remoteAgentsModel.clearCurrentAgent();else if(!((e=this._remoteAgentsModel)!=null&&e.currentAgent)){const s=this._remoteAgentsModel.agentOverviews.length>0?this._remoteAgentsModel.agentOverviews[0]:void 0;s&&this._remoteAgentsModel.setCurrentAgent(s.remote_agent_id)}this._chatModel.currentConversationModel.resetTotalCharactersCache(),this._chatModel.setCurrentChatMode(Hs.remoteAgent)}else console.error("AgentConversationModel is not initialized")}async createOrUseThread(t,e){if(t==="chat")await this._chatModel.setCurrentConversation(void 0,!0,{noopIfSameConversation:!0}),this.setToChat();else if(t==="localAgent"){await this._chatModel.setCurrentConversation(void 0,!0,{noopIfSameConversation:!0});const s=e??B(this._chatModel.agentExecutionMode)??pt.manual;await this.setToAgent(s)}else t==="remoteAgent"&&(this._remoteAgentsModel.setIsActive(!0),this.setToRemoteAgent(null))}async createNewChatThread(){await this.createOrUseThread("chat")}async createNewLocalAgentThread(t){await this.createOrUseThread("localAgent",t)}async createNewRemoteAgentThread(){await this.createOrUseThread("remoteAgent")}async createThreadOfCurrentType(){const t=B(this._agentConversationModel.isCurrConversationAgentic),e=B(this._chatModel.agentExecutionMode),s=ka({isConversationAgentic:t,agentExecutionMode:e,isBackgroundAgent:B(this._remoteAgentsModel).isActive});if(s.type==="remoteAgent")await this.createNewRemoteAgentThread();else if(s.type==="localAgent"){const n=e||pt.manual;await this.createNewLocalAgentThread(n)}else s.type,await this.createNewChatThread()}findLatestChatThread(){const t=this._chatModel.orderedConversations().filter(e=>{var s;return!((s=e.extraData)!=null&&s.isAgentConversation)&&e.id!==vt});return t.length>0?t[0].id:void 0}findLatestLocalAgentThread(){const t=this._chatModel.orderedConversations().filter(e=>{var s,n;return((s=e.extraData)==null?void 0:s.isAgentConversation)===!0&&!((n=e.extraData)!=null&&n.isRemoteAgentConversation)&&e.id!==vt});return t.length>0?t[0].id:void 0}findLatestRemoteAgentThread(){if(this._remoteAgentsModel.currentAgent)return this._remoteAgentsModel.currentAgent.remote_agent_id;const t=this._remoteAgentsModel.agentOverviews||[];return t.length>0?t[0].remote_agent_id:void 0}handleSetToChat(){const t=this.findLatestChatThread();t?this.switchToThread("chat",t):this.createNewChatThread()}async handleSetToAgent(t){const e=this.findLatestLocalAgentThread();e?this.switchToThread("localAgent",e,t):this.createNewLocalAgentThread(t)}handleSetToBackgroundAgent(){if(!this._remoteAgentsModel)return void console.error("No remote agents model found");const t=this.findLatestRemoteAgentThread();t?this.switchToThread("remoteAgent",t):this.createNewRemoteAgentThread()}switchToThread(t,e,s=pt.manual){return(!this.isRemoteAgentSshWindow||!(t!=="remoteAgent"||this.remoteAgentSshWindowId&&e!==this.remoteAgentSshWindowId))&&(t==="remoteAgent"?e===vt?this.setToRemoteAgent(null):this.setToRemoteAgent(e):(this._chatModel.setCurrentConversation(e,!0,{noopIfSameConversation:!0}),t==="chat"?this.setToChat():this.setToAgent(s)),!0)}handleMessageFromExtension(t){const e=t.data;return e.type===it.remoteAgentSelectAgentId&&(this.setToRemoteAgent(e.data.agentId),!0)}}u(ba,"key","chatModeModel");var N=(r=>(r.NOT_STARTED="NOT_STARTED",r.IN_PROGRESS="IN_PROGRESS",r.CANCELLED="CANCELLED",r.COMPLETE="COMPLETE",r))(N||{}),St=(r=>(r.USER="USER",r.AGENT="AGENT",r))(St||{}),Ji={},ks={},bs={};let ls;Object.defineProperty(bs,"__esModule",{value:!0}),bs.default=function(){if(!ls&&(ls=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!ls))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ls(xa)};const xa=new Uint8Array(16);var de={},me={},xs={};Object.defineProperty(xs,"__esModule",{value:!0}),xs.default=void 0;xs.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(me,"__esModule",{value:!0}),me.default=void 0;var cs,Ta=(cs=xs)&&cs.__esModule?cs:{default:cs},Ca=function(r){return typeof r=="string"&&Ta.default.test(r)};me.default=Ca,Object.defineProperty(de,"__esModule",{value:!0}),de.default=void 0,de.unsafeStringify=tr;var Sa=function(r){return r&&r.__esModule?r:{default:r}}(me);const ut=[];for(let r=0;r<256;++r)ut.push((r+256).toString(16).slice(1));function tr(r,t=0){return ut[r[t+0]]+ut[r[t+1]]+ut[r[t+2]]+ut[r[t+3]]+"-"+ut[r[t+4]]+ut[r[t+5]]+"-"+ut[r[t+6]]+ut[r[t+7]]+"-"+ut[r[t+8]]+ut[r[t+9]]+"-"+ut[r[t+10]]+ut[r[t+11]]+ut[r[t+12]]+ut[r[t+13]]+ut[r[t+14]]+ut[r[t+15]]}var $a=function(r,t=0){const e=tr(r,t);if(!(0,Sa.default)(e))throw TypeError("Stringified UUID is invalid");return e};de.default=$a,Object.defineProperty(ks,"__esModule",{value:!0}),ks.default=void 0;var Ea=function(r){return r&&r.__esModule?r:{default:r}}(bs),Aa=de;let Kn,Ws,js=0,Vs=0;var Ia=function(r,t,e){let s=t&&e||0;const n=t||new Array(16);let i=(r=r||{}).node||Kn,o=r.clockseq!==void 0?r.clockseq:Ws;if(i==null||o==null){const f=r.random||(r.rng||Ea.default)();i==null&&(i=Kn=[1|f[0],f[1],f[2],f[3],f[4],f[5]]),o==null&&(o=Ws=16383&(f[6]<<8|f[7]))}let a=r.msecs!==void 0?r.msecs:Date.now(),l=r.nsecs!==void 0?r.nsecs:Vs+1;const c=a-js+(l-Vs)/1e4;if(c<0&&r.clockseq===void 0&&(o=o+1&16383),(c<0||a>js)&&r.nsecs===void 0&&(l=0),l>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");js=a,Vs=l,Ws=o,a+=122192928e5;const d=(1e4*(268435455&a)+l)%4294967296;n[s++]=d>>>24&255,n[s++]=d>>>16&255,n[s++]=d>>>8&255,n[s++]=255&d;const p=a/4294967296*1e4&268435455;n[s++]=p>>>8&255,n[s++]=255&p,n[s++]=p>>>24&15|16,n[s++]=p>>>16&255,n[s++]=o>>>8|128,n[s++]=255&o;for(let f=0;f<6;++f)n[s+f]=i[f];return t||(0,Aa.unsafeStringify)(n)};ks.default=Ia;var Ts={},ce={},Qe={};Object.defineProperty(Qe,"__esModule",{value:!0}),Qe.default=void 0;var Ma=function(r){return r&&r.__esModule?r:{default:r}}(me),Ra=function(r){if(!(0,Ma.default)(r))throw TypeError("Invalid UUID");let t;const e=new Uint8Array(16);return e[0]=(t=parseInt(r.slice(0,8),16))>>>24,e[1]=t>>>16&255,e[2]=t>>>8&255,e[3]=255&t,e[4]=(t=parseInt(r.slice(9,13),16))>>>8,e[5]=255&t,e[6]=(t=parseInt(r.slice(14,18),16))>>>8,e[7]=255&t,e[8]=(t=parseInt(r.slice(19,23),16))>>>8,e[9]=255&t,e[10]=(t=parseInt(r.slice(24,36),16))/1099511627776&255,e[11]=t/4294967296&255,e[12]=t>>>24&255,e[13]=t>>>16&255,e[14]=t>>>8&255,e[15]=255&t,e};Qe.default=Ra,Object.defineProperty(ce,"__esModule",{value:!0}),ce.URL=ce.DNS=void 0,ce.default=function(r,t,e){function s(n,i,o,a){var l;if(typeof n=="string"&&(n=function(d){d=unescape(encodeURIComponent(d));const p=[];for(let f=0;f<d.length;++f)p.push(d.charCodeAt(f));return p}(n)),typeof i=="string"&&(i=(0,Ua.default)(i)),((l=i)===null||l===void 0?void 0:l.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let c=new Uint8Array(16+n.length);if(c.set(i),c.set(n,i.length),c=e(c),c[6]=15&c[6]|t,c[8]=63&c[8]|128,o){a=a||0;for(let d=0;d<16;++d)o[a+d]=c[d];return o}return(0,Da.unsafeStringify)(c)}try{s.name=r}catch{}return s.DNS=er,s.URL=sr,s};var Da=de,Ua=function(r){return r&&r.__esModule?r:{default:r}}(Qe);const er="6ba7b810-9dad-11d1-80b4-00c04fd430c8";ce.DNS=er;const sr="6ba7b811-9dad-11d1-80b4-00c04fd430c8";ce.URL=sr;var Cs={};function Jn(r){return 14+(r+64>>>9<<4)+1}function he(r,t){const e=(65535&r)+(65535&t);return(r>>16)+(t>>16)+(e>>16)<<16|65535&e}function Os(r,t,e,s,n,i){return he((o=he(he(t,r),he(s,i)))<<(a=n)|o>>>32-a,e);var o,a}function ft(r,t,e,s,n,i,o){return Os(t&e|~t&s,r,t,n,i,o)}function gt(r,t,e,s,n,i,o){return Os(t&s|e&~s,r,t,n,i,o)}function mt(r,t,e,s,n,i,o){return Os(t^e^s,r,t,n,i,o)}function _t(r,t,e,s,n,i,o){return Os(e^(t|~s),r,t,n,i,o)}Object.defineProperty(Cs,"__esModule",{value:!0}),Cs.default=void 0;var La=function(r){if(typeof r=="string"){const t=unescape(encodeURIComponent(r));r=new Uint8Array(t.length);for(let e=0;e<t.length;++e)r[e]=t.charCodeAt(e)}return function(t){const e=[],s=32*t.length,n="0123456789abcdef";for(let i=0;i<s;i+=8){const o=t[i>>5]>>>i%32&255,a=parseInt(n.charAt(o>>>4&15)+n.charAt(15&o),16);e.push(a)}return e}(function(t,e){t[e>>5]|=128<<e%32,t[Jn(e)-1]=e;let s=1732584193,n=-271733879,i=-1732584194,o=271733878;for(let a=0;a<t.length;a+=16){const l=s,c=n,d=i,p=o;s=ft(s,n,i,o,t[a],7,-680876936),o=ft(o,s,n,i,t[a+1],12,-389564586),i=ft(i,o,s,n,t[a+2],17,606105819),n=ft(n,i,o,s,t[a+3],22,-1044525330),s=ft(s,n,i,o,t[a+4],7,-176418897),o=ft(o,s,n,i,t[a+5],12,1200080426),i=ft(i,o,s,n,t[a+6],17,-1473231341),n=ft(n,i,o,s,t[a+7],22,-45705983),s=ft(s,n,i,o,t[a+8],7,1770035416),o=ft(o,s,n,i,t[a+9],12,-1958414417),i=ft(i,o,s,n,t[a+10],17,-42063),n=ft(n,i,o,s,t[a+11],22,-1990404162),s=ft(s,n,i,o,t[a+12],7,1804603682),o=ft(o,s,n,i,t[a+13],12,-40341101),i=ft(i,o,s,n,t[a+14],17,-1502002290),n=ft(n,i,o,s,t[a+15],22,1236535329),s=gt(s,n,i,o,t[a+1],5,-165796510),o=gt(o,s,n,i,t[a+6],9,-1069501632),i=gt(i,o,s,n,t[a+11],14,643717713),n=gt(n,i,o,s,t[a],20,-373897302),s=gt(s,n,i,o,t[a+5],5,-701558691),o=gt(o,s,n,i,t[a+10],9,38016083),i=gt(i,o,s,n,t[a+15],14,-660478335),n=gt(n,i,o,s,t[a+4],20,-405537848),s=gt(s,n,i,o,t[a+9],5,568446438),o=gt(o,s,n,i,t[a+14],9,-1019803690),i=gt(i,o,s,n,t[a+3],14,-187363961),n=gt(n,i,o,s,t[a+8],20,1163531501),s=gt(s,n,i,o,t[a+13],5,-1444681467),o=gt(o,s,n,i,t[a+2],9,-51403784),i=gt(i,o,s,n,t[a+7],14,1735328473),n=gt(n,i,o,s,t[a+12],20,-1926607734),s=mt(s,n,i,o,t[a+5],4,-378558),o=mt(o,s,n,i,t[a+8],11,-2022574463),i=mt(i,o,s,n,t[a+11],16,1839030562),n=mt(n,i,o,s,t[a+14],23,-35309556),s=mt(s,n,i,o,t[a+1],4,-1530992060),o=mt(o,s,n,i,t[a+4],11,1272893353),i=mt(i,o,s,n,t[a+7],16,-155497632),n=mt(n,i,o,s,t[a+10],23,-1094730640),s=mt(s,n,i,o,t[a+13],4,681279174),o=mt(o,s,n,i,t[a],11,-358537222),i=mt(i,o,s,n,t[a+3],16,-722521979),n=mt(n,i,o,s,t[a+6],23,76029189),s=mt(s,n,i,o,t[a+9],4,-640364487),o=mt(o,s,n,i,t[a+12],11,-421815835),i=mt(i,o,s,n,t[a+15],16,530742520),n=mt(n,i,o,s,t[a+2],23,-995338651),s=_t(s,n,i,o,t[a],6,-198630844),o=_t(o,s,n,i,t[a+7],10,1126891415),i=_t(i,o,s,n,t[a+14],15,-1416354905),n=_t(n,i,o,s,t[a+5],21,-57434055),s=_t(s,n,i,o,t[a+12],6,1700485571),o=_t(o,s,n,i,t[a+3],10,-1894986606),i=_t(i,o,s,n,t[a+10],15,-1051523),n=_t(n,i,o,s,t[a+1],21,-2054922799),s=_t(s,n,i,o,t[a+8],6,1873313359),o=_t(o,s,n,i,t[a+15],10,-30611744),i=_t(i,o,s,n,t[a+6],15,-1560198380),n=_t(n,i,o,s,t[a+13],21,1309151649),s=_t(s,n,i,o,t[a+4],6,-145523070),o=_t(o,s,n,i,t[a+11],10,-1120210379),i=_t(i,o,s,n,t[a+2],15,718787259),n=_t(n,i,o,s,t[a+9],21,-343485551),s=he(s,l),n=he(n,c),i=he(i,d),o=he(o,p)}return[s,n,i,o]}(function(t){if(t.length===0)return[];const e=8*t.length,s=new Uint32Array(Jn(e));for(let n=0;n<e;n+=8)s[n>>5]|=(255&t[n/8])<<n%32;return s}(r),8*r.length))};Cs.default=La,Object.defineProperty(Ts,"__esModule",{value:!0}),Ts.default=void 0;var Fa=nr(ce),Oa=nr(Cs);function nr(r){return r&&r.__esModule?r:{default:r}}var Na=(0,Fa.default)("v3",48,Oa.default);Ts.default=Na;var Ss={},$s={};Object.defineProperty($s,"__esModule",{value:!0}),$s.default=void 0;var qa={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};$s.default=qa,Object.defineProperty(Ss,"__esModule",{value:!0}),Ss.default=void 0;var ti=ir($s),za=ir(bs),Pa=de;function ir(r){return r&&r.__esModule?r:{default:r}}var Ha=function(r,t,e){if(ti.default.randomUUID&&!t&&!r)return ti.default.randomUUID();const s=(r=r||{}).random||(r.rng||za.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){e=e||0;for(let n=0;n<16;++n)t[e+n]=s[n];return t}return(0,Pa.unsafeStringify)(s)};Ss.default=Ha;var Es={},As={};function Ba(r,t,e,s){switch(r){case 0:return t&e^~t&s;case 1:case 3:return t^e^s;case 2:return t&e^t&s^e&s}}function Zs(r,t){return r<<t|r>>>32-t}Object.defineProperty(As,"__esModule",{value:!0}),As.default=void 0;var Ga=function(r){const t=[1518500249,1859775393,2400959708,3395469782],e=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof r=="string"){const o=unescape(encodeURIComponent(r));r=[];for(let a=0;a<o.length;++a)r.push(o.charCodeAt(a))}else Array.isArray(r)||(r=Array.prototype.slice.call(r));r.push(128);const s=r.length/4+2,n=Math.ceil(s/16),i=new Array(n);for(let o=0;o<n;++o){const a=new Uint32Array(16);for(let l=0;l<16;++l)a[l]=r[64*o+4*l]<<24|r[64*o+4*l+1]<<16|r[64*o+4*l+2]<<8|r[64*o+4*l+3];i[o]=a}i[n-1][14]=8*(r.length-1)/Math.pow(2,32),i[n-1][14]=Math.floor(i[n-1][14]),i[n-1][15]=8*(r.length-1)&4294967295;for(let o=0;o<n;++o){const a=new Uint32Array(80);for(let g=0;g<16;++g)a[g]=i[o][g];for(let g=16;g<80;++g)a[g]=Zs(a[g-3]^a[g-8]^a[g-14]^a[g-16],1);let l=e[0],c=e[1],d=e[2],p=e[3],f=e[4];for(let g=0;g<80;++g){const v=Math.floor(g/20),m=Zs(l,5)+Ba(v,c,d,p)+f+t[v]+a[g]>>>0;f=p,p=d,d=Zs(c,30)>>>0,c=l,l=m}e[0]=e[0]+l>>>0,e[1]=e[1]+c>>>0,e[2]=e[2]+d>>>0,e[3]=e[3]+p>>>0,e[4]=e[4]+f>>>0}return[e[0]>>24&255,e[0]>>16&255,e[0]>>8&255,255&e[0],e[1]>>24&255,e[1]>>16&255,e[1]>>8&255,255&e[1],e[2]>>24&255,e[2]>>16&255,e[2]>>8&255,255&e[2],e[3]>>24&255,e[3]>>16&255,e[3]>>8&255,255&e[3],e[4]>>24&255,e[4]>>16&255,e[4]>>8&255,255&e[4]]};As.default=Ga,Object.defineProperty(Es,"__esModule",{value:!0}),Es.default=void 0;var Wa=rr(ce),ja=rr(As);function rr(r){return r&&r.__esModule?r:{default:r}}var Va=(0,Wa.default)("v5",80,ja.default);Es.default=Va;var Is={};Object.defineProperty(Is,"__esModule",{value:!0}),Is.default=void 0;Is.default="00000000-0000-0000-0000-000000000000";var Ms={};Object.defineProperty(Ms,"__esModule",{value:!0}),Ms.default=void 0;var Za=function(r){return r&&r.__esModule?r:{default:r}}(me),Ya=function(r){if(!(0,Za.default)(r))throw TypeError("Invalid UUID");return parseInt(r.slice(14,15),16)};function cn(r,t){if(!(r&&t&&r.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=r,this.dstAlphabet=t}Ms.default=Ya,function(r){Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"NIL",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(r,"parse",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(r,"stringify",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(r,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(r,"v3",{enumerable:!0,get:function(){return e.default}}),Object.defineProperty(r,"v4",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(r,"v5",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(r,"validate",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(r,"version",{enumerable:!0,get:function(){return o.default}});var t=d(ks),e=d(Ts),s=d(Ss),n=d(Es),i=d(Is),o=d(Ms),a=d(me),l=d(de),c=d(Qe);function d(p){return p&&p.__esModule?p:{default:p}}}(Ji),cn.prototype.convert=function(r){var t,e,s,n={},i=this.srcAlphabet.length,o=this.dstAlphabet.length,a=r.length,l=typeof r=="string"?"":[];if(!this.isValid(r))throw new Error('Number "'+r+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return r;for(t=0;t<a;t++)n[t]=this.srcAlphabet.indexOf(r[t]);do{for(e=0,s=0,t=0;t<a;t++)(e=e*i+n[t])>=o?(n[s++]=parseInt(e/o,10),e%=o):s>0&&(n[s++]=0);a=s,l=this.dstAlphabet.slice(e,e+1).concat(l)}while(s!==0);return l},cn.prototype.isValid=function(r){for(var t=0;t<r.length;++t)if(this.srcAlphabet.indexOf(r[t])===-1)return!1;return!0};var Qa=cn;function qe(r,t){var e=new Qa(r,t);return function(s){return e.convert(s)}}qe.BIN="01",qe.OCT="01234567",qe.DEC="0123456789",qe.HEX="0123456789abcdef";var Xa=qe;const{v4:Ys,validate:Ka}=Ji,hs=Xa,Qs={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},Ja={consistentLength:!0};let Xs;const ei=(r,t,e)=>{const s=t(r.toLowerCase().replace(/-/g,""));return e&&e.consistentLength?s.padStart(e.shortIdLength,e.paddingChar):s},si=(r,t)=>{const e=t(r).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[e[1],e[2],e[3],e[4],e[5]].join("-")};var tl=(()=>{const r=(t,e)=>{const s=t||Qs.flickrBase58,n={...Ja,...e};if([...new Set(Array.from(s))].length!==s.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const i=(o=s.length,Math.ceil(Math.log(2**128)/Math.log(o)));var o;const a={shortIdLength:i,consistentLength:n.consistentLength,paddingChar:s[0]},l=hs(hs.HEX,s),c=hs(s,hs.HEX),d=()=>ei(Ys(),l,a),p={alphabet:s,fromUUID:f=>ei(f,l,a),maxLength:i,generate:d,new:d,toUUID:f=>si(f,c),uuid:Ys,validate:(f,g=!1)=>{if(!f||typeof f!="string")return!1;const v=n.consistentLength?f.length===i:f.length<=i,m=f.split("").every(C=>s.includes(C));return g===!1?v&&m:v&&m&&Ka(si(f,c))}};return Object.freeze(p),p};return r.constants=Qs,r.uuid=Ys,r.generate=()=>(Xs||(Xs=r(Qs.flickrBase58).generate),Xs()),r})();const el=Oi(tl),or={[N.NOT_STARTED]:"[ ]",[N.IN_PROGRESS]:"[/]",[N.COMPLETE]:"[x]",[N.CANCELLED]:"[-]"},ar=el(void 0,{consistentLength:!0});function lr(r,t){if(r.uuid===t)return r;if(r.subTasksData)for(const e of r.subTasksData){const s=lr(e,t);if(s)return s}}function Rs(r,t={}){const{shallow:e=!1,excludeUuid:s=!1,shortUuid:n=!0}=t;return cr(r,{shallow:e,excludeUuid:s,shortUuid:n}).join(`
`)}function cr(r,t={}){const{shallow:e=!1,excludeUuid:s=!1,shortUuid:n=!0}=t;let i="";s||(i=`UUID:${n?function(a){try{return ar.fromUUID(a)}catch{return a}}(r.uuid):r.uuid} `);const o=`${or[r.state]} ${i}NAME:${r.name} DESCRIPTION:${r.description}`;return e||!r.subTasksData||r.subTasksData.length===0?[o]:[o,...(r.subTasksData||[]).map(a=>cr(a,t).map(l=>`-${l}`)).flat()]}function hn(r,t){var s;const e=(s=r.subTasksData)==null?void 0:s.map(n=>hn(n,t));return{...r,uuid:t!=null&&t.keepUuid?r.uuid:crypto.randomUUID(),subTasks:(e==null?void 0:e.map(n=>n.uuid))||[],subTasksData:e}}function ni(r,t={}){if(!r.trim())throw new Error("Empty markdown");const e=r.split(`
`);let s=0;for(const c of e)if(c.trim()&&ii(c)===0)try{dn(c,t),s++}catch{}if(s===0)throw new Error("No root task found");if(s>1)throw new Error(`Multiple root tasks found (${s}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const n=r.split(`
`);function i(){for(;n.length>0;){const c=n.shift(),d=ii(c);try{return{task:dn(c,t),level:d}}catch{}}}const o=i();if(!o)throw new Error("No root task found");const a=[o.task];let l;for(;l=i();){const c=a[l.level-1];if(!c)throw new Error(`Invalid markdown: level ${l.level+1} has no parent
Line: ${l.task.name} is missing a parent
Current tasks: 
${Rs(o.task)}`);c.subTasksData&&c.subTasks||(c.subTasks=[],c.subTasksData=[]),c.subTasksData.push(l.task),c.subTasks.push(l.task.uuid),a[l.level]=l.task,a.splice(l.level+1)}return o.task}function ii(r){let t=0,e=0;for(;e<r.length&&(r[e]===" "||r[e]==="	");)r[e]===" "?t+=.5:r[e]==="	"&&(t+=1),e++;for(;e<r.length&&r[e]==="-";)t+=1,e++;return Math.floor(t)}function dn(r,t={}){const{excludeUuid:e=!1,shortUuid:s=!0}=t;let n=0;for(;n<r.length&&(r[n]===" "||r[n]==="	"||r[n]==="-");)n++;const i=r.substring(n),o=i.match(/^\s*\[([ x\-/?])\]/);if(!o)throw new Error(`Invalid task line: ${r} (missing state)`);const a=o[1],l=Object.entries(or).reduce((g,[v,m])=>(g[m.substring(1,2)]=v,g),{})[a]||N.NOT_STARTED,c=i.substring(o.index+o[0].length).trim();let d,p,f;if(e){const g=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,v=c.match(g);if(!v){const m=/\b(?:name|NAME):/i.test(c),C=/\b(?:description|DESCRIPTION):/i.test(c);throw!m||!C?new Error(`Invalid task line: ${r} (missing required fields)`):c.toLowerCase().indexOf("name:")<c.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${r} (invalid format)`):new Error(`Invalid task line: ${r} (incorrect field order)`)}if(p=v[1].trim(),f=v[2].trim(),!p)throw new Error(`Invalid task line: ${r} (missing required fields)`);d=crypto.randomUUID()}else{const g=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,v=c.match(g);if(!v){const m=/\b(?:uuid|UUID):/i.test(c),C=/\b(?:name|NAME):/i.test(c),_=/\b(?:description|DESCRIPTION):/i.test(c);if(!m||!C||!_)throw new Error(`Invalid task line: ${r} (missing required fields)`);const A=c.toLowerCase().indexOf("uuid:"),R=c.toLowerCase().indexOf("name:"),O=c.toLowerCase().indexOf("description:");throw A<R&&R<O?new Error(`Invalid task line: ${r} (invalid format)`):new Error(`Invalid task line: ${r} (incorrect field order)`)}if(d=v[1].trim(),p=v[2].trim(),f=v[3].trim(),!d||!p)throw new Error(`Invalid task line: ${r} (missing required fields)`);if(d==="NEW_UUID")d=crypto.randomUUID();else if(s)try{d=function(m){try{return ar.toUUID(m)}catch{return m}}(d)}catch{}}return{uuid:d,name:p,description:f,state:l,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:St.USER}}const Le=r=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:N.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:St.USER,...r}),ri=Le({name:"Task 1.1",description:"This is the first sub task",state:N.IN_PROGRESS}),oi=Le({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:N.NOT_STARTED}),ai=Le({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:N.IN_PROGRESS}),li=Le({name:"Task 1.2",description:"This is the second sub task",state:N.COMPLETE,subTasks:[oi.uuid,ai.uuid],subTasksData:[oi,ai]}),ci=Le({name:"Task 1.3",description:"This is the third sub task",state:N.CANCELLED}),sl=Rs(Le({name:"Task 1",description:"This is the first task",state:N.NOT_STARTED,subTasks:[ri.uuid,li.uuid,ci.uuid],subTasksData:[ri,li,ci]}));function hr(r){const t=r.split(`
`);let e=null;const s={created:[],updated:[],deleted:[]};for(const n of t){const i=n.trim();if(i!=="## Created Tasks")if(i!=="## Updated Tasks")if(i!=="## Deleted Tasks"){if(e&&(i.startsWith("[ ]")||i.startsWith("[/]")||i.startsWith("[x]")||i.startsWith("[-]")))try{const o=dn(i,{excludeUuid:!1,shortUuid:!0});o&&s[e].push(o)}catch{}}else e="deleted";else e="updated";else e="created"}return s}function oh(r){const t=r.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const e=hr(dr(r));return{created:e.created.length,updated:e.updated.length,deleted:e.deleted.length}}function dr(r){const t=r.indexOf("# Task Changes");if(t===-1)return"";const e=r.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let n=e.length;for(const a of s){const l=e.indexOf(a);l!==-1&&l<n&&(n=l)}const i=e.substring(0,n),o=i.indexOf(`
`);return o===-1?"":i.substring(o+1).trim()}function ah(r){return hr(dr(r))}function nl(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256a208 208 0 1 0-416 0 208 208 0 1 0 416 0M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function il(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class rl extends W{constructor(t){super(),j(this,t,il,nl,V,{})}}function ol(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256c0-114.9-93.1-208-208-208v416c114.9 0 208-93.1 208-208M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function al(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class ll extends W{constructor(t){super(),j(this,t,al,ol,V,{})}}function un(r){switch(r){case N.IN_PROGRESS:return"info";case N.COMPLETE:return"success";case N.CANCELLED:return"error";case N.NOT_STARTED:default:return"neutral"}}function hi(r){switch(r){case N.IN_PROGRESS:return"In Progress";case N.COMPLETE:return"Completed";case N.CANCELLED:return"Cancelled";case N.NOT_STARTED:default:return"Not Started"}}function ur(r){switch(r){case N.IN_PROGRESS:return ll;case N.COMPLETE:return zi;case N.CANCELLED:return Pr;case N.NOT_STARTED:default:return rl}}function di(r){if(!r)return;let t,e,s=-1;return function n(i,o){if(o>0&&i.state===N.IN_PROGRESS&&((!i.subTasksData||i.subTasksData.length===0)&&(t=i),o>=s&&(e=i,s=o)),i.subTasksData&&i.subTasksData.length>0)for(const a of i.subTasksData)n(a,o+1)}(r,0),t||e}function ui(r){if(!r)return{completed:0,total:0};let t=0,e=0;return function s(n,i=0){if(i>0&&(e++,n.state===N.COMPLETE&&t++),n.subTasksData&&n.subTasksData.length>0)for(const o of n.subTasksData)s(o,i+1)}(r),{completed:t,total:e}}const Ds=class Ds extends Event{constructor(t){super(Ds.type),this.task=t}};u(Ds,"type","task-added");let Ve=Ds;const Ze=class Ze{constructor(t,e,s,n){u(this,"_eventTarget",new EventTarget);u(this,"_disposables",[]);u(this,"rootTaskUuid");u(this,"_rootTask",st(void 0));u(this,"rootTask",Rn(this._rootTask));u(this,"activeTask",Te(this._rootTask,t=>di(t)));u(this,"taskProgress",Te(this._rootTask,t=>ui(t)));u(this,"_isImportingExporting",st(!1));u(this,"isImportingExporting",Rn(this._isImportingExporting));u(this,"canShowTaskList");u(this,"refreshTasksThrottled");u(this,"_backlinks",st({}));u(this,"uuidToTask",Te(this._rootTask,t=>{const e=new Map;if(!t)return e;const s={},n=t?[t]:[];for(;n.length>0;){const i=n.pop();if(e.set(i.uuid,i),i.subTasksData){n.push(...i.subTasksData);for(const o of i.subTasks)s[o]=i.uuid}}return this._backlinks.set(s),e}));this._chatModel=t,this._extensionClient=e,this._conversationModel=s,this._agentConversationModel=n,this.refreshTasksThrottled=fa(()=>{this.refreshTasks().catch(console.error)},Ze.REFRESH_THROTTLE_MS),this.rootTaskUuid=Te(this._conversationModel,i=>i.rootTaskUuid),this._disposables.push({dispose:this.rootTaskUuid.subscribe(async i=>{i&&(this._extensionClient.setCurrentRootTaskUuid(i),await this.refreshTasks())})}),this._disposables.push({dispose:this._conversationModel.onNewConversation(async()=>{await this._maybeInitializeConversationRootTask()})}),this.canShowTaskList=Te([this._chatModel.flags,this._agentConversationModel.isCurrConversationAgentic],([i,o])=>i.enableTaskList&&o),this._maybeInitializeConversationRootTask()}static createReadOnlyStore(t){const e=st(t),s=new Map;if(t){const n=[t];for(;n.length>0;){const i=n.pop();s.set(i.uuid,i),i.subTasksData&&n.push(...i.subTasksData)}}return{rootTaskUuid:st(t==null?void 0:t.uuid),rootTask:e,activeTask:st(di(t)),taskProgress:st(ui(t)),canShowTaskList:st(!0),uuidToTask:st(s),createTask:()=>Promise.resolve(""),updateTask:()=>Promise.resolve(),getHydratedTask:()=>Promise.resolve(void 0),cloneHydratedTask:()=>Promise.resolve(void 0),refreshTasks:()=>Promise.resolve(),refreshTasksThrottled:()=>{},updateTaskListStatuses:()=>Promise.resolve(),syncTaskListWithConversation:()=>Promise.resolve(),deleteTask:()=>Promise.resolve(),getParentTask:()=>{},addNewTaskAfter:()=>Promise.resolve(void 0),saveHydratedTask:()=>Promise.resolve(),runHydratedTask:()=>Promise.resolve(),dispose:()=>{},handleMessageFromExtension:()=>!1,runAllTasks:()=>Promise.resolve(),exportTask:()=>Promise.resolve(),exportTasksToMarkdown:()=>Promise.resolve(),importTasksFromMarkdown:()=>Promise.resolve(),isImportingExporting:st(!1),onTaskAdded:()=>()=>{}}}dispose(){for(const t of this._disposables)t.dispose();this._disposables=[]}handleMessageFromExtension(t){return!1}async createTask(t,e,s){const n=await this._extensionClient.createTask(t,e,s);await this.refreshTasks();const i=B(this.rootTask);return i&&this._eventTarget.dispatchEvent(new Ve(i)),n}async updateTask(t,e,s){await this._extensionClient.updateTask(t,e,s),await this.refreshTasks()}async getHydratedTask(t){return this._extensionClient.getHydratedTask(t)}async refreshTasks(){const t=B(this.rootTaskUuid);if(t){const e=await this._extensionClient.getHydratedTask(t);this._rootTask.set(e)}else this._rootTask.set(void 0)}async syncTaskListWithConversation(t){await this._updateTaskList(t,"Update the task list to reflect the current state of the conversation. Add any new tasks that have been created, update the status of existing tasks, and remove any tasks that are no longer relevant. The updated task list should reflect the current state of the conversation. If the tasks are not detailed enough, please add new tasks to fill in the steps you think are necessary, provide more details by adding more information to the description, or add sub-tasks",["You should add new tasks if any tasks are missing.","You should update the details of tasks if their details are outdated.","You should update the status of tasks if their status is outdated.","You should remove any tasks that are no longer relevant by not including them in the task list."])}async updateTaskListStatuses(t){await this._updateTaskList(t,"Update the status of each task in the list to reflect the current state of the conversation.",["You may update the status of tasks if necessary.","Do not add any new tasks.","Do not remove any tasks.","Do not update the details of any tasks."])}async _maybeInitializeConversationRootTask(){const t=B(this.rootTaskUuid);if(t)return t;const e=`Root task for conversation ${B(this._conversationModel).id}`,s=await this._extensionClient.createTask("Current Task List",e);return this._conversationModel.rootTaskUuid=s,this._extensionClient.setCurrentRootTaskUuid(s),s}async _updateTaskList(t,e="",s=[]){try{const n=B(this._rootTask);if(!n)return;const i=lr(n,t);if(!i)return;const o=Rs(i),a=e+`
Follow these rules when updating the task list:
`+(s==null?void 0:s.join(`
`))+`
Maintain the hierarchical structure, given by the \`Example task list structure\`, with proper indentation. If a task is new, give it a UUID of "NEW_UUID". Always structure each task with [ ] for not started, [/] for in progress, [x] for completed, and [-] for cancelled. 
Example task list structure: 
`+sl+`

Current working task list - This is ACTUAL working task list to use, read from, and modify:
`+o+`

Only output the updated markdown without any additional explanation. Do not include any sentences before or after the markdown, ONLY the markdown itself. Do not use a tool call, just return the markdown in plaintext, without tools, or anything else. Just plaintext markdown.`,{responseText:l}=await this._conversationModel.sendSilentExchange({request_message:a,disableSelectedCodeDetails:!0});console.log("Updating task list for conversation",B(this._conversationModel).id),console.log({instructions:a,currentTaskListMarkdown:o,enhancedTaskList:l});const c=ni(l);c.uuid=i.uuid;const{created:d,updated:p,deleted:f}=await this._extensionClient.updateHydratedTask(c,St.AGENT);console.log("Task tree update results:",{created:d,updated:p,deleted:f})}finally{await this.refreshTasks()}}getParentTask(t){const e=B(this._backlinks)[t];if(e)return B(this.uuidToTask).get(e)}async addNewTaskAfter(t,e){const s=this.getParentTask(t);if(!s)return;const n=s.subTasks.indexOf(t);if(n===-1)return;const i=await this.cloneHydratedTask(e);return i?(s.subTasks.splice(n+1,0,i.uuid),await this.saveHydratedTask(s),i):void 0}async deleteTask(t){var s;const e=B(this._backlinks)[t];if(e){const n=await this.getHydratedTask(e);if(!n)return;n.subTasks=n.subTasks.filter(i=>i!==t),n.subTasksData=(s=n.subTasksData)==null?void 0:s.filter(i=>i.uuid!==t),await this.updateTask(n.uuid,{subTasks:n.subTasks},St.USER)}else{const n=B(this._rootTask);if(!n||!n.subTasks.includes(t))return;const i=n.subTasks.filter(o=>o!==t);await this.updateTask(n.uuid,{subTasks:i},St.USER)}}async saveHydratedTask(t){await this._extensionClient.updateHydratedTask(t,St.USER),await this.refreshTasks()}async cloneHydratedTask(t){const e=hn(t),s=await this.createTask(e.name,e.description);if(s)return e.uuid=s,await this._extensionClient.updateHydratedTask(e,St.USER),await this.getHydratedTask(s)}async runAllTasks(){const t=B(this._rootTask);t&&this.runHydratedTask(t,{message:"Please run all tasks in the current task list to completion",includeTaskMention:!1})}async runHydratedTask(t,e){const s=this._chatModel.currentConversationId;if(await this._agentConversationModel.interruptAgent(),s!==this._chatModel.currentConversationId)return;if(e!=null&&e.newConversation){const a=await this.cloneHydratedTask(t);if(!a)return;const l=await this.createTask(t.name,t.description);if(await this.saveHydratedTask({uuid:l,name:t.name,description:t.description,state:N.NOT_STARTED,subTasks:[a.uuid],subTasksData:[a],lastUpdated:Date.now(),lastUpdatedBy:St.USER}),s!==this._chatModel.currentConversationId)return;await this._chatModel.setCurrentConversation(void 0,!0,{newTaskUuid:l})}const n=[{type:"text",text:(e==null?void 0:e.message)??"Please shift focus to work on task: "}],{includeTaskMention:i=!0}=e??{};i&&n.push({type:"mention",attrs:{id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name,data:{...t,id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name}}});const o={type:"doc",content:[{type:"paragraph",content:n}]};await this._chatModel.currentConversationModel.sendExchange({request_message:"",rich_text_json_repr:o,structured_request_nodes:this._chatModel.currentConversationModel.createStructuredRequestNodes(o),status:ht.draft,model_id:this._chatModel.currentConversationModel.selectedModelId??void 0}),await this.refreshTasks()}async exportTask(t,e){try{this._isImportingExporting.set(!0);const s=(e==null?void 0:e.format)||"markdown";let n,i,o;if(s!=="markdown")throw new Error(`Unsupported export format: ${s}`);n=Rs(t,{excludeUuid:!0,shallow:e==null?void 0:e.shallow}),i="md",(e==null?void 0:e.fileName)?o=e.fileName:o=`${((e==null?void 0:e.baseName)||t.name||"Task").replace(/[/:*?"<>|\s]/g,"_")}_${new Date().toISOString().replace(/[:.]/g,"-").slice(0,19)}.${i}`,this._extensionClient.createFile(n,o)}catch(s){console.error("Error exporting task:",s)}finally{this._isImportingExporting.set(!1)}}async exportTasksToMarkdown(){const t=B(this._rootTask);if(!t)return;const e=B(this._conversationModel).name||"Tasks";await this.exportTask(t,{baseName:e,format:"markdown"})}async importTasksFromMarkdown(){try{this._isImportingExporting.set(!0);const t=document.createElement("input");t.type="file",t.accept=".md,text/markdown",t.style.display="none";const e=new Promise(n=>{t.onchange=()=>{n(t.files?t.files[0]:null)},t.oncancel=()=>{n(null)}});document.body.appendChild(t),t.click();const s=await e;if(document.body.removeChild(t),s){const n=await s.text();await this._processImportedFileContent(n)}}catch(t){console.error("Error importing tasks from markdown:",t)}finally{this._isImportingExporting.set(!1)}}async _processImportedFileContent(t){try{if(!t.trim())return void console.error("Empty file content");const e=ni(t,{excludeUuid:!0});if(!e)return void console.error("Failed to parse task tree from markdown");const s=B(this._rootTask);if(!s)return void console.error("No root task found");const n=(e.subTasksData||[]).map(d=>hn(d,{keepUuid:!1})),i=s.subTasksData||[],o=s.subTasks||[],a=[...i,...n],l=[...o,...n.map(d=>d.uuid)],c={...s,subTasks:l,subTasksData:a};await this._extensionClient.updateHydratedTask(c,St.USER),await this.refreshTasks()}catch(e){console.error("Error processing imported file content:",e)}}onTaskAdded(t){const e=s=>t(s);return this._eventTarget.addEventListener(Ve.type,e),()=>{this._eventTarget.removeEventListener(Ve.type,e)}}};u(Ze,"key","currentConversationTaskStore"),u(Ze,"REFRESH_THROTTLE_MS",2e3);let pn=Ze;function cl(r){let t,e;return{c(){t=X("svg"),e=X("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M3.13523 8.84197C3.3241 9.04343 3.64052 9.05363 3.84197 8.86477L7.5 5.43536L11.158 8.86477C11.3595 9.05363 11.6759 9.04343 11.8648 8.84197C12.0536 8.64051 12.0434 8.32409 11.842 8.13523L7.84197 4.38523C7.64964 4.20492 7.35036 4.20492 7.15803 4.38523L3.15803 8.13523C2.95657 8.32409 2.94637 8.64051 3.13523 8.84197Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){D(s,t,n),tt(t,e)},p:M,i:M,o:M,d(s){s&&S(t)}}}class lh extends W{constructor(t){super(),j(this,t,null,cl,V,{})}}function hl(r){let t,e;return{c(){t=X("svg"),e=X("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){D(s,t,n),tt(t,e)},p:M,i:M,o:M,d(s){s&&S(t)}}}class ch extends W{constructor(t){super(),j(this,t,null,hl,V,{})}}function dl(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M354.9 121.7c13.8 16 36.5 21.1 55.9 12.5 8.9-3.9 18.7-6.2 29.2-6.2 39.8 0 72 32.2 72 72 0 4-.3 7.9-.9 11.7-3.5 21.6 8.1 42.9 28.1 51.7C570.4 276.9 592 308 592 344c0 46.8-36.6 85.2-82.8 87.8-.6 0-1.3.1-1.9.2H144c-53 0-96-43-96-96 0-41.7 26.6-77.3 64-90.5 19.2-6.8 32-24.9 32-45.3v-.2c0-66.3 53.7-120 120-120 36.3 0 68.8 16.1 90.9 41.7M512 480v-.2c71.4-4.1 128-63.3 128-135.8 0-55.7-33.5-103.7-81.5-124.7 1-6.3 1.5-12.8 1.5-19.3 0-66.3-53.7-120-120-120-17.4 0-33.8 3.7-48.7 10.3C360.4 54.6 314.9 32 264 32c-92.8 0-168 75.2-168 168v.2C40.1 220 0 273.3 0 336c0 79.5 64.5 144 144 144h360zM223 255c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 384c0 13.3 10.7 24 24 24s24-10.7 24-24V249.9l39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0z"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function ul(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class hh extends W{constructor(t){super(),j(this,t,ul,dl,V,{})}}function pi(r){return r.replace(/\.git$/,"")}function dh(r){if(!r)return"";const t=r.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return t[2].replace(/\.git$/,"");const e=r.split("/").filter(Boolean);return(e.length>0?e[e.length-1]:"").replace(/\.git$/,"")}function uh(r){if(!r)return"";const t=r.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return`${t[1]}/${t[2].replace(/\.git$/,"")}`;const e=r.split("/").filter(Boolean);return e.length>1?`${e[e.length-2]}/${e[e.length-1].replace(/\.git$/,"")}`:e.length>0?e[e.length-1].replace(/\.git$/,""):""}function ph(r){return Te(r,t=>e=>{if(!t)return!0;const s=pi(t),n=pi(function(i){var o,a,l;return((l=(a=(o=i.workspace_setup)==null?void 0:o.starting_files)==null?void 0:a.github_commit_ref)==null?void 0:l.repository_url)||""}(e));return!!n&&!!s&&n!==s})}function pl(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 128c0 53-43 96-96 96-28.9 0-54.8-12.8-72.4-33l-89.7 44.9c1.4 6.5 2.1 13.2 2.1 20.1s-.7 13.6-2.1 20.1l89.7 44.9c17.6-20.2 43.5-33 72.4-33 53 0 96 43 96 96s-43 96-96 96-96-43-96-96c0-6.9.7-13.6 2.1-20.1L168.4 319c-17.6 20.2-43.5 33-72.4 33-53 0-96-43-96-96s43-96 96-96c28.9 0 54.8 12.8 72.4 33l89.7-44.9c-1.4-6.5-2.1-13.2-2.1-20.1 0-53 43-96 96-96s96 43 96 96M96 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96m304-176a48 48 0 1 0-96 0 48 48 0 1 0 96 0m-48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function fl(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class fh extends W{constructor(t){super(),j(this,t,fl,pl,V,{})}}function fi(r){let t,e;return t=new Ni({props:{class:"edit-item__added-lines",size:1,$$slots:{default:[gl]},$$scope:{ctx:r}}}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};5&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function gl(r){let t,e;return{c(){t=Ye("+"),e=Ye(r[0])},m(s,n){D(s,t,n),D(s,e,n)},p(s,n){1&n&&fn(e,s[0])},d(s){s&&(S(t),S(e))}}}function gi(r){let t,e;return t=new Ni({props:{class:"edit-item__removed-lines",size:1,$$slots:{default:[ml]},$$scope:{ctx:r}}}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};6&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function ml(r){let t,e;return{c(){t=Ye("-"),e=Ye(r[1])},m(s,n){D(s,t,n),D(s,e,n)},p(s,n){2&n&&fn(e,s[1])},d(s){s&&(S(t),S(e))}}}function _l(r){let t,e,s,n=r[0]>0&&fi(r),i=r[1]>0&&gi(r);return{c(){t=et("div"),n&&n.c(),e=Gt(),i&&i.c(),y(t,"class","edit-item__changes svelte-1k8sltp")},m(o,a){D(o,t,a),n&&n.m(t,null),tt(t,e),i&&i.m(t,null),s=!0},p(o,[a]){o[0]>0?n?(n.p(o,a),1&a&&k(n,1)):(n=fi(o),n.c(),k(n,1),n.m(t,e)):n&&(kt(),T(n,1,1,()=>{n=null}),bt()),o[1]>0?i?(i.p(o,a),2&a&&k(i,1)):(i=gi(o),i.c(),k(i,1),i.m(t,null)):i&&(kt(),T(i,1,1,()=>{i=null}),bt())},i(o){s||(k(n),k(i),s=!0)},o(o){T(n),T(i),s=!1},d(o){o&&S(t),n&&n.d(),i&&i.d()}}}function yl(r,t,e){let{totalAddedLines:s=0}=t,{totalRemovedLines:n=0}=t;return r.$$set=i=>{"totalAddedLines"in i&&e(0,s=i.totalAddedLines),"totalRemovedLines"in i&&e(1,n=i.totalRemovedLines)},[s,n]}class gh extends W{constructor(t){super(),j(this,t,yl,_l,V,{totalAddedLines:0,totalRemovedLines:1})}}function vl(r){let t,e;return{c(){t=X("svg"),e=X("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M3.5 2.82672C3.5 2.55058 3.72386 2.32672 4 2.32672H9.79289L12.5 5.03383V12.8267C12.5 13.1028 12.2761 13.3267 12 13.3267H4C3.72386 13.3267 3.5 13.1028 3.5 12.8267V2.82672ZM4 1.32672C3.17157 1.32672 2.5 1.99829 2.5 2.82672V12.8267C2.5 13.6551 3.17157 14.3267 4 14.3267H12C12.8284 14.3267 13.5 13.6551 13.5 12.8267V4.93027C13.5 4.73136 13.421 4.5406 13.2803 4.39994L10.3535 1.47317C10.2598 1.3794 10.1326 1.32672 10 1.32672H4ZM10.25 6.6595C10.5261 6.6595 10.75 6.43564 10.75 6.1595C10.75 5.88336 10.5261 5.6595 10.25 5.6595H8.49996L8.49996 3.9095C8.49996 3.6334 8.2761 3.4095 7.99996 3.4095C7.72382 3.4095 7.49996 3.6334 7.49996 3.9095V5.6595H5.74996C5.47386 5.6595 5.24996 5.88336 5.24996 6.1595C5.24996 6.43564 5.47386 6.6595 5.74996 6.6595L7.49996 6.6595V8.4095C7.49996 8.68564 7.72382 8.9095 7.99996 8.9095C8.2761 8.9095 8.49996 8.68564 8.49996 8.4095V6.6595H10.25ZM10.4999 11.4188C10.4999 11.695 10.2761 11.9188 9.99993 11.9188H5.99993C5.72379 11.9188 5.49993 11.695 5.49993 11.4188C5.49993 11.1427 5.72379 10.9188 5.99993 10.9188H9.99993C10.2761 10.9188 10.4999 11.1427 10.4999 11.4188Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){D(s,t,n),tt(t,e)},p:M,i:M,o:M,d(s){s&&S(t)}}}class mh extends W{constructor(t){super(),j(this,t,null,vl,V,{})}}function wl(r){let t,e,s;var n=r[2];function i(o,a){return{props:{width:o[1],height:o[3]}}}return n&&(e=Re(n,i(r))),{c(){t=et("span"),e&&U(e.$$.fragment),y(t,"class","c-task-icon svelte-1dmbt8o"),is(t,"--icon-color","var(--ds-color-"+un(r[0])+"-9)"),is(t,"--icon-size",r[1])},m(o,a){D(o,t,a),e&&L(e,t,null),s=!0},p(o,[a]){if(4&a&&n!==(n=o[2])){if(e){kt();const l=e;T(l.$$.fragment,1,0,()=>{F(l,1)}),bt()}n?(e=Re(n,i(o)),U(e.$$.fragment),k(e.$$.fragment,1),L(e,t,null)):e=null}else if(n){const l={};2&a&&(l.width=o[1]),8&a&&(l.height=o[3]),e.$set(l)}(!s||1&a)&&is(t,"--icon-color","var(--ds-color-"+un(o[0])+"-9)"),(!s||2&a)&&is(t,"--icon-size",o[1])},i(o){s||(e&&k(e.$$.fragment,o),s=!0)},o(o){e&&T(e.$$.fragment,o),s=!1},d(o){o&&S(t),e&&F(e)}}}function kl(r,t,e){let s,n,i,{taskState:o}=t,{size:a=1}=t;return r.$$set=l=>{"taskState"in l&&e(0,o=l.taskState),"size"in l&&e(4,a=l.size)},r.$$.update=()=>{16&r.$$.dirty&&e(1,s={1:"14px",2:"16px",3:"18px",4:"20px"}[a]),2&r.$$.dirty&&e(3,n=s),1&r.$$.dirty&&e(2,i=ur(o))},[o,s,i,n,a]}class bl extends W{constructor(t){super(),j(this,t,kl,wl,V,{taskState:0,size:4})}}function xl(r){let t,e,s;const n=r[5].default,i=Pe(n,r,r[4],null);return{c(){t=et("div"),i&&i.c(),y(t,"class",e=Dn(r[1])+" svelte-1uzel5q"),y(t,"role","status"),y(t,"aria-label",r[0])},m(o,a){D(o,t,a),i&&i.m(t,null),s=!0},p(o,[a]){i&&i.p&&(!s||16&a)&&He(i,n,o,o[4],s?Ge(n,o[4],a,null):Be(o[4]),null),(!s||2&a&&e!==(e=Dn(o[1])+" svelte-1uzel5q"))&&y(t,"class",e),(!s||1&a)&&y(t,"aria-label",o[0])},i(o){s||(k(i,o),s=!0)},o(o){T(i,o),s=!1},d(o){o&&S(t),i&&i.d(o)}}}function Tl(r,t,e){let s,{$$slots:n={},$$scope:i}=t,{color:o="neutral"}=t,{size:a=1}=t,{weight:l="medium"}=t;return r.$$set=c=>{"color"in c&&e(0,o=c.color),"size"in c&&e(2,a=c.size),"weight"in c&&e(3,l=c.weight),"$$scope"in c&&e(4,i=c.$$scope)},r.$$.update=()=>{13&r.$$.dirty&&e(1,s=["c-status-badge",`c-status-badge--${o}`,`c-status-badge--size-${a}`,`c-text--size-${a}`,`c-text--weight-${l}`].join(" "))},[o,s,a,l,i,n]}class _h extends W{constructor(t){super(),j(this,t,Tl,xl,V,{color:0,size:2,weight:3})}}const Cl=r=>({item:16&r}),mi=r=>({item:r[4]}),Sl=r=>({item:16&r}),_i=r=>({item:r[4]}),$l=r=>({item:16&r}),yi=r=>({item:r[4]}),El=r=>({item:16&r}),vi=r=>({item:r[4]});function wi(r){let t,e;const s=r[11].handle,n=Pe(s,r,r[22],vi);return{c(){t=et("div"),n&&n.c(),y(t,"class","c-draggable-list-item__handle svelte-1u76bcr")},m(i,o){D(i,t,o),n&&n.m(t,null),e=!0},p(i,o){n&&n.p&&(!e||4194320&o)&&He(n,s,i,i[22],e?Ge(s,i[22],o,El):Be(i[22]),vi)},i(i){e||(k(n,i),e=!0)},o(i){T(n,i),e=!1},d(i){i&&S(t),n&&n.d(i)}}}function ki(r){let t,e;return t=new Je({props:{class:"c-draggable-list-item__expand-collapse-button",size:1,variant:"ghost",color:"neutral","aria-expanded":r[0],"aria-label":r[0]?"Collapse":"Expand",$$slots:{default:[Ml]},$$scope:{ctx:r}}}),t.$on("click",r[9]),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};1&n&&(i["aria-expanded"]=s[0]),1&n&&(i["aria-label"]=s[0]?"Collapse":"Expand"),4194305&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Al(r){let t,e;return t=new Yr({}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Il(r){let t,e;return t=new Hr({}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Ml(r){let t,e,s,n;const i=[Il,Al],o=[];function a(l,c){return l[0]?0:1}return t=a(r),e=o[t]=i[t](r),{c(){e.c(),s=se()},m(l,c){o[t].m(l,c),D(l,s,c),n=!0},p(l,c){let d=t;t=a(l),t!==d&&(kt(),T(o[d],1,1,()=>{o[d]=null}),bt(),e=o[t],e||(e=o[t]=i[t](l),e.c()),k(e,1),e.m(s.parentNode,s))},i(l){n||(k(e),n=!0)},o(l){T(e),n=!1},d(l){l&&S(s),o[t].d(l)}}}function Rl(r){let t,e,s,n,i,o,a,l,c,d,p,f,g,v=r[10].handle&&wi(r),m=r[6]&&ki(r);const C=r[11]["header-contents"],_=Pe(C,r,r[22],yi),A=r[11].actions,R=Pe(A,r,r[22],_i),O=r[11].contents,b=Pe(O,r,r[22],mi);return{c(){t=et("div"),e=et("div"),v&&v.c(),s=Gt(),m&&m.c(),n=Gt(),i=et("div"),_&&_.c(),o=Gt(),a=et("div"),R&&R.c(),l=Gt(),c=et("div"),b&&b.c(),y(i,"class","c-draggable-list-item__main svelte-1u76bcr"),y(a,"class","c-draggable-list-item__actions"),y(e,"class","c-draggable-list-item__content svelte-1u76bcr"),y(c,"class","c-draggable-list-item__contents"),$t(c,"c-draggable-list-item__show-connectors",r[8]),$t(c,"c-draggable-list-item__has-handle",r[10].handle),y(t,"class",d="c-draggable-list-item "+r[2]+" svelte-1u76bcr"),y(t,"id",r[3]),y(t,"data-item-id",r[3]),y(t,"data-testid","draggable-list-item"),y(t,"tabindex","0"),y(t,"role","button"),$t(t,"is-disabled",r[5]),$t(t,"has-nested-items",r[6]),$t(t,"is-expanded",r[0]),$t(t,"is-selected",r[7])},m($,P){D($,t,P),tt(t,e),v&&v.m(e,null),tt(e,s),m&&m.m(e,null),tt(e,n),tt(e,i),_&&_.m(i,null),tt(e,o),tt(e,a),R&&R.m(a,null),tt(t,l),tt(t,c),b&&b.m(c,null),r[21](t),p=!0,f||(g=[Vt(t,"mousedown",r[12]),Vt(t,"click",r[13]),Vt(t,"keydown",r[14]),Vt(t,"keyup",r[15]),Vt(t,"keypress",r[16]),Vt(t,"focus",r[17]),Vt(t,"blur",r[18]),Vt(t,"focusin",r[19]),Vt(t,"focusout",r[20])],f=!0)},p($,[P]){$[10].handle?v?(v.p($,P),1024&P&&k(v,1)):(v=wi($),v.c(),k(v,1),v.m(e,s)):v&&(kt(),T(v,1,1,()=>{v=null}),bt()),$[6]?m?(m.p($,P),64&P&&k(m,1)):(m=ki($),m.c(),k(m,1),m.m(e,n)):m&&(kt(),T(m,1,1,()=>{m=null}),bt()),_&&_.p&&(!p||4194320&P)&&He(_,C,$,$[22],p?Ge(C,$[22],P,$l):Be($[22]),yi),R&&R.p&&(!p||4194320&P)&&He(R,A,$,$[22],p?Ge(A,$[22],P,Sl):Be($[22]),_i),b&&b.p&&(!p||4194320&P)&&He(b,O,$,$[22],p?Ge(O,$[22],P,Cl):Be($[22]),mi),(!p||256&P)&&$t(c,"c-draggable-list-item__show-connectors",$[8]),(!p||1024&P)&&$t(c,"c-draggable-list-item__has-handle",$[10].handle),(!p||4&P&&d!==(d="c-draggable-list-item "+$[2]+" svelte-1u76bcr"))&&y(t,"class",d),(!p||8&P)&&y(t,"id",$[3]),(!p||8&P)&&y(t,"data-item-id",$[3]),(!p||36&P)&&$t(t,"is-disabled",$[5]),(!p||68&P)&&$t(t,"has-nested-items",$[6]),(!p||5&P)&&$t(t,"is-expanded",$[0]),(!p||132&P)&&$t(t,"is-selected",$[7])},i($){p||(k(v),k(m),k(_,$),k(R,$),k(b,$),p=!0)},o($){T(v),T(m),T(_,$),T(R,$),T(b,$),p=!1},d($){$&&S(t),v&&v.d(),m&&m.d(),_&&_.d($),R&&R.d($),b&&b.d($),r[21](null),f=!1,Er(g)}}}function Dl(r,t,e){let{$$slots:s={},$$scope:n}=t;const i=Ar(s);let{class:o=""}=t,{id:a}=t,{item:l}=t,{disabled:c=!1}=t,{hasNestedItems:d=!1}=t,{expanded:p=!0}=t,{selected:f=!1}=t,{showConnectors:g=!0}=t,{element:v}=t;return r.$$set=m=>{"class"in m&&e(2,o=m.class),"id"in m&&e(3,a=m.id),"item"in m&&e(4,l=m.item),"disabled"in m&&e(5,c=m.disabled),"hasNestedItems"in m&&e(6,d=m.hasNestedItems),"expanded"in m&&e(0,p=m.expanded),"selected"in m&&e(7,f=m.selected),"showConnectors"in m&&e(8,g=m.showConnectors),"element"in m&&e(1,v=m.element),"$$scope"in m&&e(22,n=m.$$scope)},[p,v,o,a,l,c,d,f,g,function(){e(0,p=!p)},i,s,function(m){K.call(this,r,m)},function(m){K.call(this,r,m)},function(m){K.call(this,r,m)},function(m){K.call(this,r,m)},function(m){K.call(this,r,m)},function(m){K.call(this,r,m)},function(m){K.call(this,r,m)},function(m){K.call(this,r,m)},function(m){K.call(this,r,m)},function(m){Qt[m?"unshift":"push"](()=>{v=m,e(1,v)})},n]}class Ul extends W{constructor(t){super(),j(this,t,Dl,Rl,V,{class:2,id:3,item:4,disabled:5,hasNestedItems:6,expanded:0,selected:7,showConnectors:8,element:1})}}function Ll(r){let t,e,s,n,i,o,a;function l(p){r[7](p)}function c(p){r[8](p)}let d={size:1,variant:"surface",placeholder:"Add task description...",disabled:!r[0],rows:1,resize:"vertical"};return r[2]!==void 0&&(d.textInput=r[2]),r[1]!==void 0&&(d.value=r[1]),n=new to({props:d}),Qt.push(()=>De(n,"textInput",l)),Qt.push(()=>De(n,"value",c)),n.$on("keydown",r[4]),n.$on("blur",r[3]),{c(){t=et("div"),e=et("div"),s=et("div"),U(n.$$.fragment),y(s,"class","c-task-details__section c-task-details__description-contents svelte-1k3razm"),y(e,"class","c-task-details__content svelte-1k3razm"),y(t,"class","c-task-details svelte-1k3razm")},m(p,f){D(p,t,f),tt(t,e),tt(e,s),L(n,s,null),a=!0},p(p,[f]){const g={};1&f&&(g.disabled=!p[0]),!i&&4&f&&(i=!0,g.textInput=p[2],Ue(()=>i=!1)),!o&&2&f&&(o=!0,g.value=p[1],Ue(()=>o=!1)),n.$set(g)},i(p){a||(k(n.$$.fragment,p),a=!0)},o(p){T(n.$$.fragment,p),a=!1},d(p){p&&S(t),F(n)}}}function Fl(r,t,e){let s,{task:n}=t,{taskStore:i}=t,{editable:o=!0}=t,a=n.description;function l(){a.trim()!==n.description&&i.updateTask(n.uuid,{description:a.trim()},St.USER),s==null||s.blur()}return r.$$set=c=>{"task"in c&&e(5,n=c.task),"taskStore"in c&&e(6,i=c.taskStore),"editable"in c&&e(0,o=c.editable)},[o,a,s,l,function(c){c.key!=="Enter"||c.shiftKey||c.ctrlKey||c.metaKey?c.key==="Escape"&&(c.preventDefault(),c.stopPropagation(),e(1,a=n.description),s==null||s.blur()):(c.preventDefault(),c.stopPropagation(),l())},n,i,function(c){s=c,e(2,s)},function(c){a=c,e(1,a)}]}class Ol extends W{constructor(t){super(),j(this,t,Fl,Ll,V,{task:5,taskStore:6,editable:0})}}function bi(r,t,e){const s=r.slice();return s[25]=t[e],s}function Nl(r){let t,e;const s=[{size:r[1]},{variant:"ghost"},{color:r[9]},r[12]];let n={$$slots:{default:[zl]},$$scope:{ctx:r}};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return t=new Je({props:n}),t.$on("click",r[16]),t.$on("keyup",r[17]),t.$on("keydown",r[18]),t.$on("mousedown",r[19]),t.$on("mouseover",r[20]),t.$on("focus",r[21]),t.$on("mouseleave",r[22]),t.$on("blur",r[23]),t.$on("contextmenu",r[24]),{c(){U(t.$$.fragment)},m(i,o){L(t,i,o),e=!0},p(i,o){const a=4610&o?Ut(s,[2&o&&{size:i[1]},s[1],512&o&&{color:i[9]},4096&o&&gn(i[12])]):{};268436482&o&&(a.$$scope={dirty:o,ctx:i}),t.$set(a)},i(i){e||(k(t.$$.fragment,i),e=!0)},o(i){T(t.$$.fragment,i),e=!1},d(i){F(t,i)}}}function ql(r){let t,e;return t=new ms.Root({props:{triggerOn:[Us.Click],open:r[6],onOpenChange:r[15],$$slots:{default:[Vl]},$$scope:{ctx:r}}}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};64&n&&(i.open=s[6]),64&n&&(i.onOpenChange=s[15]),268441587&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function zl(r){let t,e,s;var n=r[10];function i(o,a){return{props:{size:o[1]}}}return n&&(t=Re(n,i(r))),{c(){t&&U(t.$$.fragment),e=se()},m(o,a){t&&L(t,o,a),D(o,e,a),s=!0},p(o,a){if(1024&a&&n!==(n=o[10])){if(t){kt();const l=t;T(l.$$.fragment,1,0,()=>{F(l,1)}),bt()}n?(t=Re(n,i(o)),U(t.$$.fragment),k(t.$$.fragment,1),L(t,e.parentNode,e)):t=null}else if(n){const l={};2&a&&(l.size=o[1]),t.$set(l)}},i(o){s||(t&&k(t.$$.fragment,o),s=!0)},o(o){t&&T(t.$$.fragment,o),s=!1},d(o){o&&S(e),t&&F(t,o)}}}function Pl(r){let t,e,s;var n=r[10];function i(o,a){return{props:{size:o[1]}}}return n&&(t=Re(n,i(r))),{c(){t&&U(t.$$.fragment),e=se()},m(o,a){t&&L(t,o,a),D(o,e,a),s=!0},p(o,a){if(1024&a&&n!==(n=o[10])){if(t){kt();const l=t;T(l.$$.fragment,1,0,()=>{F(l,1)}),bt()}n?(t=Re(n,i(o)),U(t.$$.fragment),k(t.$$.fragment,1),L(t,e.parentNode,e)):t=null}else if(n){const l={};2&a&&(l.size=o[1]),t.$set(l)}},i(o){s||(t&&k(t.$$.fragment,o),s=!0)},o(o){t&&T(t.$$.fragment,o),s=!1},d(o){o&&S(e),t&&F(t,o)}}}function Hl(r){let t,e;const s=[{size:r[1]},{variant:"ghost"},{color:r[9]},{disabled:r[5]},r[12]];let n={$$slots:{default:[Pl]},$$scope:{ctx:r}};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return t=new Je({props:n}),t.$on("click",r[13]),{c(){U(t.$$.fragment)},m(i,o){L(t,i,o),e=!0},p(i,o){const a=4642&o?Ut(s,[2&o&&{size:i[1]},s[1],512&o&&{color:i[9]},32&o&&{disabled:i[5]},4096&o&&gn(i[12])]):{};268436482&o&&(a.$$scope={dirty:o,ctx:i}),t.$set(a)},i(i){e||(k(t.$$.fragment,i),e=!0)},o(i){T(t.$$.fragment,i),e=!1},d(i){F(t,i)}}}function Bl(r){let t,e;return t=new _n({props:{content:r[8],triggerOn:[Us.Hover],$$slots:{default:[Hl]},$$scope:{ctx:r}}}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};256&n&&(i.content=s[8]),268441186&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Gl(r){let t,e,s=hi(r[25])+"";return{c(){t=Ye(s),e=Gt()},m(n,i){D(n,t,i),D(n,e,i)},p(n,i){128&i&&s!==(s=hi(n[25])+"")&&fn(t,s)},d(n){n&&(S(t),S(e))}}}function Wl(r){let t,e;return t=new bl({props:{slot:"iconLeft",taskState:r[25],size:1}}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};128&n&&(i.taskState=s[25]),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function xi(r){let t,e;function s(){return r[14](r[25])}return t=new ms.Item({props:{onSelect:s,highlight:r[25]===r[0],disabled:!r[4]||r[25]===r[0],$$slots:{iconLeft:[Wl],default:[Gl]},$$scope:{ctx:r}}}),{c(){U(t.$$.fragment)},m(n,i){L(t,n,i),e=!0},p(n,i){r=n;const o={};128&i&&(o.onSelect=s),129&i&&(o.highlight=r[25]===r[0]),145&i&&(o.disabled=!r[4]||r[25]===r[0]),268435584&i&&(o.$$scope={dirty:i,ctx:r}),t.$set(o)},i(n){e||(k(t.$$.fragment,n),e=!0)},o(n){T(t.$$.fragment,n),e=!1},d(n){F(t,n)}}}function jl(r){let t,e,s=Me(r[7]),n=[];for(let o=0;o<s.length;o+=1)n[o]=xi(bi(r,s,o));const i=o=>T(n[o],1,1,()=>{n[o]=null});return{c(){for(let o=0;o<n.length;o+=1)n[o].c();t=se()},m(o,a){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,a);D(o,t,a),e=!0},p(o,a){if(2193&a){let l;for(s=Me(o[7]),l=0;l<s.length;l+=1){const c=bi(o,s,l);n[l]?(n[l].p(c,a),k(n[l],1)):(n[l]=xi(c),n[l].c(),k(n[l],1),n[l].m(t.parentNode,t))}for(kt(),l=s.length;l<n.length;l+=1)i(l);bt()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)k(n[a]);e=!0}},o(o){n=n.filter(Boolean);for(let a=0;a<n.length;a+=1)T(n[a]);e=!1},d(o){o&&S(t),Ir(n,o)}}}function Vl(r){let t,e,s,n;return t=new ms.Trigger({props:{$$slots:{default:[Bl]},$$scope:{ctx:r}}}),s=new ms.Content({props:{size:1,side:"bottom",align:"start",$$slots:{default:[jl]},$$scope:{ctx:r}}}),{c(){U(t.$$.fragment),e=Gt(),U(s.$$.fragment)},m(i,o){L(t,i,o),D(i,e,o),L(s,i,o),n=!0},p(i,o){const a={};268441442&o&&(a.$$scope={dirty:o,ctx:i}),t.$set(a);const l={};268435601&o&&(l.$$scope={dirty:o,ctx:i}),s.$set(l)},i(i){n||(k(t.$$.fragment,i),k(s.$$.fragment,i),n=!0)},o(i){T(t.$$.fragment,i),T(s.$$.fragment,i),n=!1},d(i){i&&S(e),F(t,i),F(s,i)}}}function Zl(r){let t,e,s,n;const i=[ql,Nl],o=[];function a(l,c){return l[4]&&l[2]&&l[3]&&!l[5]?0:1}return t=a(r),e=o[t]=i[t](r),{c(){e.c(),s=se()},m(l,c){o[t].m(l,c),D(l,s,c),n=!0},p(l,[c]){let d=t;t=a(l),t===d?o[t].p(l,c):(kt(),T(o[d],1,1,()=>{o[d]=null}),bt(),e=o[t],e?e.p(l,c):(e=o[t]=i[t](l),e.c()),k(e,1),e.m(s.parentNode,s))},i(l){n||(k(e),n=!0)},o(l){T(e),n=!1},d(l){l&&S(s),o[t].d(l)}}}function Yl(r,t,e){let s,n,i,o;const a=["taskState","size","taskUuid","taskStore","interactive","disabled"];let l=gs(t,a),{taskState:c}=t,{size:d=1}=t,{taskUuid:p}=t,{taskStore:f}=t,{interactive:g=!0}=t,{disabled:v=!1}=t,m=!1;async function C(_){p&&f&&!v&&_!==c&&(await f.updateTask(p,{state:_},St.USER),e(6,m=!1))}return r.$$set=_=>{t=z(z({},t),rt(_)),e(12,l=gs(t,a)),"taskState"in _&&e(0,c=_.taskState),"size"in _&&e(1,d=_.size),"taskUuid"in _&&e(2,p=_.taskUuid),"taskStore"in _&&e(3,f=_.taskStore),"interactive"in _&&e(4,g=_.interactive),"disabled"in _&&e(5,v=_.disabled)},r.$$.update=()=>{1&r.$$.dirty&&e(10,s=ur(c)),1&r.$$.dirty&&e(9,n=un(c))},e(8,i="Change Status"),e(7,o=Object.values(N)),[c,d,p,f,g,v,m,o,"Change Status",n,s,C,l,()=>e(6,m=!m),_=>C(_),_=>e(6,m=_),function(_){K.call(this,r,_)},function(_){K.call(this,r,_)},function(_){K.call(this,r,_)},function(_){K.call(this,r,_)},function(_){K.call(this,r,_)},function(_){K.call(this,r,_)},function(_){K.call(this,r,_)},function(_){K.call(this,r,_)},function(_){K.call(this,r,_)}]}class Ql extends W{constructor(t){super(),j(this,t,Yl,Zl,V,{taskState:0,size:1,taskUuid:2,taskStore:3,interactive:4,disabled:5})}}function Xl(r){let t,e,s,n,i,o;const a=[{size:r[2]},{variant:r[1]},{color:r[3]},{placeholder:r[0]},r[11]];function l(p){r[18](p)}function c(p){r[19](p)}let d={};for(let p=0;p<a.length;p+=1)d=z(d,a[p]);return r[7]!==void 0&&(d.textInput=r[7]),r[6]!==void 0&&(d.value=r[6]),e=new Qr({props:d}),Qt.push(()=>De(e,"textInput",l)),Qt.push(()=>De(e,"value",c)),e.$on("keydown",r[10]),e.$on("focus",r[9]),e.$on("blur",r[20]),e.$on("keydown",r[21]),e.$on("click",r[22]),e.$on("blur",r[23]),e.$on("focus",r[24]),{c(){t=et("div"),U(e.$$.fragment),y(t,"class",i="c-editable-text "+r[4]+" svelte-jooyia")},m(p,f){D(p,t,f),L(e,t,null),r[25](t),o=!0},p(p,[f]){const g=2063&f?Ut(a,[4&f&&{size:p[2]},2&f&&{variant:p[1]},8&f&&{color:p[3]},1&f&&{placeholder:p[0]},2048&f&&gn(p[11])]):{};!s&&128&f&&(s=!0,g.textInput=p[7],Ue(()=>s=!1)),!n&&64&f&&(n=!0,g.value=p[6],Ue(()=>n=!1)),e.$set(g),(!o||16&f&&i!==(i="c-editable-text "+p[4]+" svelte-jooyia"))&&y(t,"class",i)},i(p){o||(k(e.$$.fragment,p),o=!0)},o(p){T(e.$$.fragment,p),o=!1},d(p){p&&S(t),F(e),r[25](null)}}}function Kl(r,t,e){const s=["value","disabled","placeholder","clickToEdit","variant","size","color","class","editing","startEdit","acceptEdit","cancelEdit"];let n=gs(t,s);const i=Mr();let o,a,{value:l=""}=t,{disabled:c=!1}=t,{placeholder:d=""}=t,{clickToEdit:p=!1}=t,{variant:f="surface"}=t,{size:g=2}=t,{color:v}=t,{class:m=""}=t,{editing:C=!1}=t,_=l;async function A(b){c||!o||C||(e(6,_=l),i("startEdit",{value:l}),o.focus(),b!=null&&b.selectAll&&(await qi(),o==null||o.select()),e(13,C=!0))}function R(){const b=l,$=_.trim();b!==$?(e(12,l=$),i("acceptEdit",{oldValue:b,newValue:$}),document.activeElement===o&&(o==null||o.blur()),e(13,C=!1)):O()}function O(){e(6,_=l),i("cancelEdit",{value:l}),document.activeElement===o&&(o==null||o.blur()),e(13,C=!1)}return r.$$set=b=>{t=z(z({},t),rt(b)),e(11,n=gs(t,s)),"value"in b&&e(12,l=b.value),"disabled"in b&&e(14,c=b.disabled),"placeholder"in b&&e(0,d=b.placeholder),"clickToEdit"in b&&e(15,p=b.clickToEdit),"variant"in b&&e(1,f=b.variant),"size"in b&&e(2,g=b.size),"color"in b&&e(3,v=b.color),"class"in b&&e(4,m=b.class),"editing"in b&&e(13,C=b.editing)},[d,f,g,v,m,R,_,o,a,function(){!p||c||C||A()},function(b){b.key==="Enter"?R():b.key==="Escape"&&O()},n,l,C,c,p,A,O,function(b){o=b,e(7,o)},function(b){_=b,e(6,_)},()=>R(),function(b){K.call(this,r,b)},function(b){K.call(this,r,b)},function(b){K.call(this,r,b)},function(b){K.call(this,r,b)},function(b){Qt[b?"unshift":"push"](()=>{a=b,e(8,a)})}]}class Jl extends W{constructor(t){super(),j(this,t,Kl,Xl,V,{value:12,disabled:14,placeholder:0,clickToEdit:15,variant:1,size:2,color:3,class:4,editing:13,startEdit:16,acceptEdit:5,cancelEdit:17})}get startEdit(){return this.$$.ctx[16]}get acceptEdit(){return this.$$.ctx[5]}get cancelEdit(){return this.$$.ctx[17]}}function tc(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m48 432 288-176L48 80zM24.5 38.1C39.7 29.6 58.2 30 73 39l288 176c14.3 8.7 23 24.2 23 41s-8.7 32.2-23 41L73 473c-14.8 9.1-33.4 9.4-48.5.9S0 449.4 0 432V80c0-17.4 9.4-33.4 24.5-41.9"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function ec(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class sc extends W{constructor(t){super(),j(this,t,ec,tc,V,{})}}function Ti(r){let t,e;return t=new _n({props:{content:"Run Task",triggerOn:[Us.Hover],$$slots:{default:[ic]},$$scope:{ctx:r}}}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};513&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function nc(r){let t,e;return t=new sc({}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function ic(r){let t,e;return t=new Je({props:{size:1,variant:"ghost",color:"neutral",disabled:!r[0],class:"c-task-action-button c-task-action-button--play",$$slots:{default:[nc]},$$scope:{ctx:r}}}),t.$on("click",r[4]),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.disabled=!s[0]),512&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function rc(r){let t,e;return t=new Br({}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function oc(r){let t,e;return t=new Je({props:{size:1,variant:"ghost",color:"error",disabled:!r[0],class:"c-task-action-button c-task-action-button--delete",$$slots:{default:[rc]},$$scope:{ctx:r}}}),t.$on("click",r[3]),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},p(s,n){const i={};1&n&&(i.disabled=!s[0]),512&n&&(i.$$scope={dirty:n,ctx:s}),t.$set(i)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function ac(r){let t,e,s,n,i=r[1]&&Ti(r);return s=new _n({props:{content:"Delete Task",triggerOn:[Us.Hover],$$slots:{default:[oc]},$$scope:{ctx:r}}}),{c(){t=et("div"),i&&i.c(),e=Gt(),U(s.$$.fragment),y(t,"class","c-task-action-buttons svelte-55fcbs")},m(o,a){D(o,t,a),i&&i.m(t,null),tt(t,e),L(s,t,null),n=!0},p(o,[a]){o[1]?i?(i.p(o,a),2&a&&k(i,1)):(i=Ti(o),i.c(),k(i,1),i.m(t,e)):i&&(kt(),T(i,1,1,()=>{i=null}),bt());const l={};513&a&&(l.$$scope={dirty:a,ctx:o}),s.$set(l)},i(o){n||(k(i),k(s.$$.fragment,o),n=!0)},o(o){T(i),T(s.$$.fragment,o),n=!1},d(o){o&&S(t),i&&i.d(),F(s)}}}function lc(r,t,e){let s,n,i,o,a=M;r.$$.on_destroy.push(()=>a());let{taskUuid:l}=t,{taskStore:c}=t,{editable:d=!0}=t;return r.$$set=p=>{"taskUuid"in p&&e(5,l=p.taskUuid),"taskStore"in p&&e(6,c=p.taskStore),"editable"in p&&e(0,d=p.editable)},r.$$.update=()=>{64&r.$$.dirty&&(e(2,s=c.uuidToTask),a(),a=Rr(s,p=>e(8,o=p))),288&r.$$.dirty&&e(7,n=o.get(l)),128&r.$$.dirty&&e(1,i=(n==null?void 0:n.state)===N.NOT_STARTED)},[d,i,s,async function(){d&&await c.deleteTask(l)},async function(){n&&d&&n.state===N.NOT_STARTED&&(await c.updateTask(l,{state:N.IN_PROGRESS},St.USER),await c.runHydratedTask(n))},l,c,n,o]}class cc extends W{constructor(t){super(),j(this,t,lc,ac,V,{taskUuid:5,taskStore:6,editable:0})}}function hc(r,t=new Set(Object.values(N))){const e={...r,isVisible:!1,subTasksData:[]};let s=!1;if(r.subTasksData&&r.subTasksData.length>0){const i=[];for(const o of r.subTasksData){const a=hc(o,t);i.push(a),a.isVisible&&(s=!0)}e.subTasksData=i}const n=function(i,o){return o.size!==0&&o.has(i.state)}(r,t);return e.isVisible=n||s,e}function Ci(r,t,e){const s=r.slice();return s[15]=t[e],s}function Si(r,t,e){const s=r.slice();return s[15]=t[e],s}function dc(r){let t,e,s,n;function i(l){r[12](l)}function o(l){r[13](l)}let a={class:"c-task-tree-item",item:r[0],id:`task-${r[0].uuid}`,hasNestedItems:!!r[0].subTasksData&&r[0].subTasksData.length>0,disabled:!r[2],$$slots:{contents:[gc],actions:[fc],"header-contents":[pc]},$$scope:{ctx:r}};return r[5]!==void 0&&(a.element=r[5]),r[6]!==void 0&&(a.expanded=r[6]),t=new Ul({props:a}),Qt.push(()=>De(t,"element",i)),Qt.push(()=>De(t,"expanded",o)),{c(){U(t.$$.fragment)},m(l,c){L(t,l,c),n=!0},p(l,c){const d={};1&c&&(d.item=l[0]),1&c&&(d.id=`task-${l[0].uuid}`),1&c&&(d.hasNestedItems=!!l[0].subTasksData&&l[0].subTasksData.length>0),4&c&&(d.disabled=!l[2]),1049047&c&&(d.$$scope={dirty:c,ctx:l}),!e&&32&c&&(e=!0,d.element=l[5],Ue(()=>e=!1)),!s&&64&c&&(s=!0,d.expanded=l[6],Ue(()=>s=!1)),t.$set(d)},i(l){n||(k(t.$$.fragment,l),n=!0)},o(l){T(t.$$.fragment,l),n=!1},d(l){F(t,l)}}}function uc(r){let t,e,s=[],n=new Map,i=Me(r[7]);const o=a=>a[15].uuid;for(let a=0;a<i.length;a+=1){let l=Si(r,i,a),c=o(l);n.set(c,s[a]=Ai(c,l))}return{c(){t=et("div");for(let a=0;a<s.length;a+=1)s[a].c();y(t,"class","c-task-tree-root-children svelte-10aflq0")},m(a,l){D(a,t,l);for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(t,null);e=!0},p(a,l){134&l&&(i=Me(a[7]),kt(),s=Ui(s,l,o,1,a,i,n,t,Li,Ai,null,Si),bt())},i(a){if(!e){for(let l=0;l<i.length;l+=1)k(s[l]);e=!0}},o(a){for(let l=0;l<s.length;l+=1)T(s[l]);e=!1},d(a){a&&S(t);for(let l=0;l<s.length;l+=1)s[l].d()}}}function pc(r){let t,e,s,n,i,o,a;s=new Ql({props:{taskState:r[0].state,taskUuid:r[0].uuid,taskStore:r[1],disabled:!r[2],size:1}});let l={class:"c-task-tree-item__name-editable",value:r[0].name,placeholder:"Name this task...",size:1,disabled:!r[2],clickToEdit:!0};return o=new Jl({props:l}),r[11](o),o.$on("keydown",r[10]),o.$on("blur",r[9]),{c(){t=et("div"),e=et("div"),U(s.$$.fragment),n=Gt(),i=et("div"),U(o.$$.fragment),y(e,"class","c-task-tree-item__status-cell svelte-10aflq0"),y(i,"class","c-task-tree-item__name svelte-10aflq0"),$t(i,"c-task-tree-item__text--cancelled",r[8]),y(t,"slot","header-contents"),y(t,"class","c-task-tree-item__header svelte-10aflq0")},m(c,d){D(c,t,d),tt(t,e),L(s,e,null),tt(t,n),tt(t,i),L(o,i,null),a=!0},p(c,d){const p={};1&d&&(p.taskState=c[0].state),1&d&&(p.taskUuid=c[0].uuid),2&d&&(p.taskStore=c[1]),4&d&&(p.disabled=!c[2]),s.$set(p);const f={};1&d&&(f.value=c[0].name),4&d&&(f.disabled=!c[2]),o.$set(f),(!a||256&d)&&$t(i,"c-task-tree-item__text--cancelled",c[8])},i(c){a||(k(s.$$.fragment,c),k(o.$$.fragment,c),a=!0)},o(c){T(s.$$.fragment,c),T(o.$$.fragment,c),a=!1},d(c){c&&S(t),F(s),r[11](null),F(o)}}}function fc(r){let t,e,s;return e=new cc({props:{taskUuid:r[0].uuid,taskStore:r[1],editable:r[2]}}),{c(){t=et("div"),U(e.$$.fragment),y(t,"class","c-task-tree-item__action-buttons svelte-10aflq0"),y(t,"slot","actions")},m(n,i){D(n,t,i),L(e,t,null),s=!0},p(n,i){const o={};1&i&&(o.taskUuid=n[0].uuid),2&i&&(o.taskStore=n[1]),4&i&&(o.editable=n[2]),e.$set(o)},i(n){s||(k(e.$$.fragment,n),s=!0)},o(n){T(e.$$.fragment,n),s=!1},d(n){n&&S(t),F(e)}}}function $i(r){let t,e,s=[],n=new Map,i=Me(r[7]);const o=a=>a[15].uuid;for(let a=0;a<i.length;a+=1){let l=Ci(r,i,a),c=o(l);n.set(c,s[a]=Ei(c,l))}return{c(){t=et("div");for(let a=0;a<s.length;a+=1)s[a].c();y(t,"class","c-task-tree-item__subtasks")},m(a,l){D(a,t,l);for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(t,null);e=!0},p(a,l){134&l&&(i=Me(a[7]),kt(),s=Ui(s,l,o,1,a,i,n,t,Li,Ei,null,Ci),bt())},i(a){if(!e){for(let l=0;l<i.length;l+=1)k(s[l]);e=!0}},o(a){for(let l=0;l<s.length;l+=1)T(s[l]);e=!1},d(a){a&&S(t);for(let l=0;l<s.length;l+=1)s[l].d()}}}function Ei(r,t){let e,s,n;return s=new pr({props:{taskStore:t[1],task:t[15],editable:t[2],isRootTask:!1}}),{key:r,first:null,c(){e=se(),U(s.$$.fragment),this.first=e},m(i,o){D(i,e,o),L(s,i,o),n=!0},p(i,o){t=i;const a={};2&o&&(a.taskStore=t[1]),128&o&&(a.task=t[15]),4&o&&(a.editable=t[2]),s.$set(a)},i(i){n||(k(s.$$.fragment,i),n=!0)},o(i){T(s.$$.fragment,i),n=!1},d(i){i&&S(e),F(s,i)}}}function gc(r){let t,e,s,n,i;e=new Ol({props:{task:r[0],taskStore:r[1],editable:r[2]}});let o=r[7]&&r[7].length>0&&r[6]&&$i(r);return{c(){t=et("div"),U(e.$$.fragment),s=Gt(),o&&o.c(),n=se(),y(t,"class","c-task-tree-item__details")},m(a,l){D(a,t,l),L(e,t,null),D(a,s,l),o&&o.m(a,l),D(a,n,l),i=!0},p(a,l){const c={};1&l&&(c.task=a[0]),2&l&&(c.taskStore=a[1]),4&l&&(c.editable=a[2]),e.$set(c),a[7]&&a[7].length>0&&a[6]?o?(o.p(a,l),192&l&&k(o,1)):(o=$i(a),o.c(),k(o,1),o.m(n.parentNode,n)):o&&(kt(),T(o,1,1,()=>{o=null}),bt())},i(a){i||(k(e.$$.fragment,a),k(o),i=!0)},o(a){T(e.$$.fragment,a),T(o),i=!1},d(a){a&&(S(t),S(s),S(n)),F(e),o&&o.d(a)}}}function Ai(r,t){let e,s,n;return s=new pr({props:{taskStore:t[1],task:t[15],editable:t[2],isRootTask:!1}}),{key:r,first:null,c(){e=se(),U(s.$$.fragment),this.first=e},m(i,o){D(i,e,o),L(s,i,o),n=!0},p(i,o){t=i;const a={};2&o&&(a.taskStore=t[1]),128&o&&(a.task=t[15]),4&o&&(a.editable=t[2]),s.$set(a)},i(i){n||(k(s.$$.fragment,i),n=!0)},o(i){T(s.$$.fragment,i),n=!1},d(i){i&&S(e),F(s,i)}}}function mc(r){let t,e,s,n;const i=[uc,dc],o=[];function a(l,c){return l[3]?0:l[0].isVisible?1:-1}return~(t=a(r))&&(e=o[t]=i[t](r)),{c(){e&&e.c(),s=se()},m(l,c){~t&&o[t].m(l,c),D(l,s,c),n=!0},p(l,[c]){let d=t;t=a(l),t===d?~t&&o[t].p(l,c):(e&&(kt(),T(o[d],1,1,()=>{o[d]=null}),bt()),~t?(e=o[t],e?e.p(l,c):(e=o[t]=i[t](l),e.c()),k(e,1),e.m(s.parentNode,s)):e=null)},i(l){n||(k(e),n=!0)},o(l){T(e),n=!1},d(l){l&&S(s),~t&&o[t].d(l)}}}function _c(r,t,e){let s,n,i,o,a,{task:l}=t,{taskStore:c=Dr(pn.key)}=t,{editable:d=!0}=t,{isRootTask:p=!0}=t;async function f(g){const v=g.trim();v!==l.name&&v&&await c.updateTask(l.uuid,{name:v},St.USER)}return r.$$set=g=>{"task"in g&&e(0,l=g.task),"taskStore"in g&&e(1,c=g.taskStore),"editable"in g&&e(2,d=g.editable),"isRootTask"in g&&e(3,p=g.isRootTask)},r.$$.update=()=>{1&r.$$.dirty&&e(8,s=l.state===N.CANCELLED),1&r.$$.dirty&&e(7,n=function(g){var v;return((v=g.subTasksData)==null?void 0:v.filter(m=>m.isVisible))||[]}(l))},[l,c,d,p,i,o,a,n,s,async function(g){var m;const v=(m=g.target)==null?void 0:m.value;await f(v||"")},async function(g){var v,m;if(!(g.shiftKey||g.ctrlKey||g.metaKey))switch(g.key){case"Enter":{const C=(v=g.target)==null?void 0:v.value;await f(C||"");const _=await c.addNewTaskAfter(l.uuid,{uuid:"new-task",name:"",description:"",state:N.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:St.USER});if(!_)return;await qi();const A=document.getElementById(`task-${_.uuid}`);if(!A)return;const R=A.querySelector(".c-task-tree-item__name-editable");if(!R)return;const O=R.querySelector("input");if(!O)return;O.focus(),O.select();break}case"Tab":{const C=(m=g.target)==null?void 0:m.value;await f(C||"");break}}},function(g){Qt[g?"unshift":"push"](()=>{i=g,e(4,i)})},function(g){o=g,e(5,o)},function(g){a=g,e(6,a)}]}class pr extends W{constructor(t){super(),j(this,t,_c,mc,V,{task:0,taskStore:1,editable:2,isRootTask:3})}}function yc(r){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],n={};for(let i=0;i<s.length;i+=1)n=z(n,s[i]);return{c(){t=X("svg"),e=new Xt(!0),this.h()},l(i){t=Kt(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Jt(t);e=te(o,!0),o.forEach(S),this.h()},h(){e.a=null,dt(t,n)},m(i,o){ee(i,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M153.8 72.1c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 79c-9.4-9.3-24.6-9.3-34 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zm0 160c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zM216 120h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24s10.7 24 24 24m-24 136c0 13.3 10.7 24 24 24h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24m-32 160c0 13.3 10.7 24 24 24h304c13.3 0 24-10.7 24-24s-10.7-24-24-24H184c-13.3 0-24 10.7-24 24m-64 0a32 32 0 1 0-64 0 32 32 0 1 0 64 0"/>',t)},p(i,[o]){dt(t,n=Ut(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&i[0]]))},i:M,o:M,d(i){i&&S(t)}}}function vc(r,t,e){return r.$$set=s=>{e(0,t=z(z({},t),rt(s)))},[t=rt(t)]}class yh extends W{constructor(t){super(),j(this,t,vc,yc,V,{})}}class vh{static generateDiff(t,e,s,n){return eo(t,e,s,n)}static generateDiffs(t){return so(t)}static getDiffStats(t){return Fn(t)}static getDiffObjectStats(t){return Fn(t.diff)}static isNewFile(t){return no(t)}static isDeletedFile(t){return io(t)}}function wc(r){let t,e;return{c(){t=X("svg"),e=X("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M1.43555 8.19985C1.43555 4.29832 4.59837 1.1355 8.4999 1.1355C12.4014 1.1355 15.5642 4.29832 15.5642 8.19985C15.5642 12.1013 12.4014 15.2642 8.4999 15.2642C4.59837 15.2642 1.43555 12.1013 1.43555 8.19985ZM8.4999 2.14883C5.15802 2.14883 2.44889 4.85797 2.44889 8.19985C2.44889 11.5417 5.15802 14.2509 8.4999 14.2509C11.8418 14.2509 14.5509 11.5417 14.5509 8.19985C14.5509 4.85797 11.8418 2.14883 8.4999 2.14883ZM11.0105 5.68952C11.2187 5.8978 11.2187 6.23549 11.0105 6.44377L9.25427 8.19997L11.0105 9.95619C11.2187 10.1645 11.2187 10.5022 11.0105 10.7104C10.8022 10.9187 10.4645 10.9187 10.2562 10.7104L8.50002 8.95422L6.74382 10.7104C6.53554 10.9187 6.19784 10.9187 5.98957 10.7104C5.78129 10.5022 5.78129 10.1645 5.98957 9.95619L7.74578 8.19997L5.98957 6.44377C5.78129 6.23549 5.78129 5.8978 5.98957 5.68952C6.19784 5.48124 6.53554 5.48124 6.74382 5.68952L8.50002 7.44573L10.2562 5.68952C10.4645 5.48124 10.8022 5.48124 11.0105 5.68952Z"),y(e,"fill","currentColor"),y(t,"width","17"),y(t,"height","17"),y(t,"viewBox","0 0 17 17"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){D(s,t,n),tt(t,e)},p:M,i:M,o:M,d(s){s&&S(t)}}}class kc extends W{constructor(t){super(),j(this,t,null,wc,V,{})}}function bc(r){let t,e;return{c(){t=X("svg"),e=X("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M7.49991 0.877075C3.84222 0.877075 0.877075 3.84222 0.877075 7.49991C0.877075 11.1576 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1576 14.1227 7.49991C14.1227 3.84222 11.1576 0.877075 7.49991 0.877075ZM3.85768 3.15057C4.84311 2.32448 6.11342 1.82708 7.49991 1.82708C10.6329 1.82708 13.1727 4.36689 13.1727 7.49991C13.1727 8.88638 12.6753 10.1567 11.8492 11.1421L3.85768 3.15057ZM3.15057 3.85768C2.32448 4.84311 1.82708 6.11342 1.82708 7.49991C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C8.88638 13.1727 10.1567 12.6753 11.1421 11.8492L3.15057 3.85768Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){D(s,t,n),tt(t,e)},p:M,i:M,o:M,d(s){s&&S(t)}}}class xc extends W{constructor(t){super(),j(this,t,null,bc,V,{})}}function Tc(r){let t,e;return t=new xc({}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Cc(r){let t,e;return t=new kc({}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Sc(r){let t,e;return t=new zi({}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function $c(r){let t,e;return t=new Ur({props:{size:1}}),{c(){U(t.$$.fragment)},m(s,n){L(t,s,n),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){T(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Ec(r){let t,e,s,n,i;const o=[$c,Sc,Cc,Tc],a=[];function l(c,d){return c[0]==="loading"?0:c[0]==="success"?1:c[0]==="error"?2:c[0]==="skipped"?3:-1}return~(e=l(r))&&(s=a[e]=o[e](r)),{c(){t=et("div"),s&&s.c(),y(t,"class",n="c-setup-script-command-status c-setup-script-command-status--"+r[0]+" svelte-1azgu93")},m(c,d){D(c,t,d),~e&&a[e].m(t,null),i=!0},p(c,[d]){let p=e;e=l(c),e!==p&&(s&&(kt(),T(a[p],1,1,()=>{a[p]=null}),bt()),~e?(s=a[e],s||(s=a[e]=o[e](c),s.c()),k(s,1),s.m(t,null)):s=null),(!i||1&d&&n!==(n="c-setup-script-command-status c-setup-script-command-status--"+c[0]+" svelte-1azgu93"))&&y(t,"class",n)},i(c){i||(k(s),i=!0)},o(c){T(s),i=!1},d(c){c&&S(t),~e&&a[e].d()}}}function Ac(r,t,e){let{commandResult:s}=t;return r.$$set=n=>{"commandResult"in n&&e(0,s=n.commandResult)},[s]}class wh extends W{constructor(t){super(),j(this,t,Ac,Ec,V,{commandResult:0})}}function Ic(r){let t,e;return{c(){t=X("svg"),e=X("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M1.90321 7.29677C1.90321 10.341 4.11041 12.4147 6.58893 12.8439C6.87255 12.893 7.06266 13.1627 7.01355 13.4464C6.96444 13.73 6.69471 13.9201 6.41109 13.871C3.49942 13.3668 0.86084 10.9127 0.86084 7.29677C0.860839 5.76009 1.55996 4.55245 2.37639 3.63377C2.96124 2.97568 3.63034 2.44135 4.16846 2.03202L2.53205 2.03202C2.25591 2.03202 2.03205 1.80816 2.03205 1.53202C2.03205 1.25588 2.25591 1.03202 2.53205 1.03202L5.53205 1.03202C5.80819 1.03202 6.03205 1.25588 6.03205 1.53202L6.03205 4.53202C6.03205 4.80816 5.80819 5.03202 5.53205 5.03202C5.25591 5.03202 5.03205 4.80816 5.03205 4.53202L5.03205 2.68645L5.03054 2.68759L5.03045 2.68766L5.03044 2.68767L5.03043 2.68767C4.45896 3.11868 3.76059 3.64538 3.15554 4.3262C2.44102 5.13021 1.90321 6.10154 1.90321 7.29677ZM13.0109 7.70321C13.0109 4.69115 10.8505 2.6296 8.40384 2.17029C8.12093 2.11718 7.93465 1.84479 7.98776 1.56188C8.04087 1.27898 8.31326 1.0927 8.59616 1.14581C11.4704 1.68541 14.0532 4.12605 14.0532 7.70321C14.0532 9.23988 13.3541 10.4475 12.5377 11.3662C11.9528 12.0243 11.2837 12.5586 10.7456 12.968L12.3821 12.968C12.6582 12.968 12.8821 13.1918 12.8821 13.468C12.8821 13.7441 12.6582 13.968 12.3821 13.968L9.38205 13.968C9.10591 13.968 8.88205 13.7441 8.88205 13.468L8.88205 10.468C8.88205 10.1918 9.10591 9.96796 9.38205 9.96796C9.65819 9.96796 9.88205 10.1918 9.88205 10.468L9.88205 12.3135L9.88362 12.3123C10.4551 11.8813 11.1535 11.3546 11.7585 10.6738C12.4731 9.86976 13.0109 8.89844 13.0109 7.70321Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){D(s,t,n),tt(t,e)},p:M,i:M,o:M,d(s){s&&S(t)}}}class kh extends W{constructor(t){super(),j(this,t,null,Ic,V,{})}}function Mc(r){let t,e;return{c(){t=X("svg"),e=X("path"),y(e,"d","M14.5 3H7.70996L6.85999 2.15002L6.51001 2H1.51001L1.01001 2.5V6.5V13.5L1.51001 14H14.51L15.01 13.5V9V3.5L14.5 3ZM13.99 11.49V13H1.98999V11.49V7.48999V7H6.47998L6.82996 6.84998L7.68994 5.98999H14V7.48999L13.99 11.49ZM13.99 5H7.48999L7.14001 5.15002L6.28003 6.01001H2V3.01001H6.29004L7.14001 3.85999L7.5 4.01001H14L13.99 5Z"),y(e,"fill","#C5C5C5"),y(t,"width","16"),y(t,"height","16"),y(t,"viewBox","0 0 16 16"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){D(s,t,n),tt(t,e)},p:M,i:M,o:M,d(s){s&&S(t)}}}class bh extends W{constructor(t){super(),j(this,t,null,Mc,V,{})}}export{St as $,pt as A,hh as B,jo as C,sh as D,gh as E,mh as F,Zo as G,N as H,hi as I,_h as J,hc as K,pr as L,_a as M,vt as N,yh as O,vh as P,ih as Q,ph as R,nh as S,bl as T,wh as U,kh as V,co as W,bh as X,Pi as Y,kc as Z,ah as _,ch as a,oh as a0,ni as a1,ao as a2,xc as b,Qc as c,Q as d,Yc as e,Fs as f,Xc as g,Kc as h,Se as i,jn as j,ba as k,Po as l,eh as m,Jc as n,ds as o,ka as p,lh as q,th as r,at as s,fa as t,pn as u,rh as v,lo as w,dh as x,uh as y,fh as z};
