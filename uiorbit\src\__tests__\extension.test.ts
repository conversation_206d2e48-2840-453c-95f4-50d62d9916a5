/**
 * Unit tests for extension entry point
 */

// Mock Logger before importing extension
jest.mock('../utils/Logger', () => ({
  Logger: {
    initialize: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock UIOrbitExtension
jest.mock('../core/UIOrbitExtension', () => ({
  UIOrbitExtension: jest.fn().mockImplementation(() => ({
    activate: jest.fn(),
    deactivate: jest.fn(),
  })),
}));

import * as extension from '../extension';
import { UIOrbitExtension } from '../core/UIOrbitExtension';

describe('Extension Entry Point', () => {
  let mockContext: any;
  let mockVscode: any;

  beforeEach(() => {
    // Setup mock context
    mockContext = global.testUtils.createMockContext();

    // Get the mocked vscode module
    mockVscode = require('vscode');

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Activation', () => {
    it('should activate extension successfully', async () => {
      const mockExtensionInstance = {
        activate: jest.fn().mockResolvedValue(undefined),
        deactivate: jest.fn().mockResolvedValue(undefined),
      };

      (UIOrbitExtension as jest.MockedClass<typeof UIOrbitExtension>).mockImplementation(() => mockExtensionInstance as any);

      await extension.activate(mockContext);

      expect(UIOrbitExtension).toHaveBeenCalledWith(mockContext);
      expect(mockExtensionInstance.activate).toHaveBeenCalled();
      expect(mockVscode.window.showInformationMessage).toHaveBeenCalledWith(
        'UIOrbit is ready! Your AI frontend development assistant is now active.'
      );
    });

    it('should handle activation errors gracefully', async () => {
      const mockExtensionInstance = {
        activate: jest.fn().mockRejectedValue(new Error('Activation failed')),
        deactivate: jest.fn().mockResolvedValue(undefined),
      };

      (UIOrbitExtension as jest.MockedClass<typeof UIOrbitExtension>).mockImplementation(() => mockExtensionInstance as any);

      await extension.activate(mockContext);

      expect(mockVscode.window.showErrorMessage).toHaveBeenCalledWith(
        'Failed to activate UIOrbit: Activation failed'
      );
    });

    it('should handle unknown errors during activation', async () => {
      const mockExtensionInstance = {
        activate: jest.fn().mockRejectedValue('Unknown error'),
        deactivate: jest.fn().mockResolvedValue(undefined),
      };

      (UIOrbitExtension as jest.MockedClass<typeof UIOrbitExtension>).mockImplementation(() => mockExtensionInstance as any);

      await extension.activate(mockContext);

      expect(mockVscode.window.showErrorMessage).toHaveBeenCalledWith(
        'Failed to activate UIOrbit: Unknown error'
      );
    });
  });

  describe('Deactivation', () => {
    it('should deactivate extension successfully', async () => {
      const mockExtensionInstance = {
        activate: jest.fn().mockResolvedValue(undefined),
        deactivate: jest.fn().mockResolvedValue(undefined),
      };

      (UIOrbitExtension as jest.MockedClass<typeof UIOrbitExtension>).mockImplementation(() => mockExtensionInstance as any);

      // First activate
      await extension.activate(mockContext);

      // Then deactivate
      await extension.deactivate();

      expect(mockExtensionInstance.deactivate).toHaveBeenCalled();
    });

    it('should handle deactivation when extension was not activated', async () => {
      await expect(extension.deactivate()).resolves.not.toThrow();
    });

    it('should handle deactivation errors gracefully', async () => {
      const mockExtensionInstance = {
        activate: jest.fn().mockResolvedValue(undefined),
        deactivate: jest.fn().mockRejectedValue(new Error('Deactivation failed')),
      };

      (UIOrbitExtension as jest.MockedClass<typeof UIOrbitExtension>).mockImplementation(() => mockExtensionInstance as any);

      // First activate
      await extension.activate(mockContext);

      // Then deactivate with error
      await extension.deactivate();

      expect(mockExtensionInstance.deactivate).toHaveBeenCalled();
    });
  });

  describe('Extension Lifecycle', () => {
    it('should handle multiple activation/deactivation cycles', async () => {
      const mockExtensionInstance1 = {
        activate: jest.fn().mockResolvedValue(undefined),
        deactivate: jest.fn().mockResolvedValue(undefined),
      };

      const mockExtensionInstance2 = {
        activate: jest.fn().mockResolvedValue(undefined),
        deactivate: jest.fn().mockResolvedValue(undefined),
      };

      // First cycle
      (UIOrbitExtension as jest.MockedClass<typeof UIOrbitExtension>).mockImplementationOnce(() => mockExtensionInstance1 as any);
      await extension.activate(mockContext);
      await extension.deactivate();

      // Second cycle
      (UIOrbitExtension as jest.MockedClass<typeof UIOrbitExtension>).mockImplementationOnce(() => mockExtensionInstance2 as any);
      await extension.activate(mockContext);
      await extension.deactivate();

      expect(mockExtensionInstance1.activate).toHaveBeenCalled();
      expect(mockExtensionInstance1.deactivate).toHaveBeenCalled();
      expect(mockExtensionInstance2.activate).toHaveBeenCalled();
      expect(mockExtensionInstance2.deactivate).toHaveBeenCalled();
    });
  });
});
