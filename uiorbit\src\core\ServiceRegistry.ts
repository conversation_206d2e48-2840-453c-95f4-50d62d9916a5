import { Logger } from '../utils/Logger';

/**
 * Service registry for dependency injection and service management
 * Implements the service locator pattern for clean architecture
 */
export class ServiceRegistry {
  private services: Map<string, any> = new Map();
  private disposables: Array<{ dispose(): Promise<void> | void }> = [];

  /**
   * Register a service with the given name
   */
  register<T>(name: string, service: T): void {
    if (this.services.has(name)) {
      Logger.warn(`Service '${name}' is already registered. Overwriting...`);
    }

    this.services.set(name, service);
    Logger.debug(`Service '${name}' registered successfully`);

    // Track disposable services
    if (this.isDisposable(service)) {
      this.disposables.push(service);
    }
  }

  /**
   * Get a service by name
   */
  get<T>(name: string): T | undefined {
    const service = this.services.get(name);
    if (!service) {
      Logger.warn(`Service '${name}' not found in registry`);
      return undefined;
    }
    return service as T;
  }

  /**
   * Get a service by name, throw error if not found
   */
  getRequired<T>(name: string): T {
    const service = this.get<T>(name);
    if (!service) {
      throw new Error(`Required service '${name}' not found in registry`);
    }
    return service;
  }

  /**
   * Check if a service is registered
   */
  has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Unregister a service
   */
  unregister(name: string): boolean {
    const service = this.services.get(name);
    if (service) {
      // Remove from disposables if it was tracked
      const index = this.disposables.indexOf(service);
      if (index > -1) {
        this.disposables.splice(index, 1);
      }

      this.services.delete(name);
      Logger.debug(`Service '${name}' unregistered successfully`);
      return true;
    }
    return false;
  }

  /**
   * Get all registered service names
   */
  getServiceNames(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Clear all services
   */
  clear(): void {
    this.services.clear();
    this.disposables = [];
    Logger.debug('All services cleared from registry');
  }

  /**
   * Dispose all disposable services
   */
  async dispose(): Promise<void> {
    Logger.info('Disposing all services...');

    const disposePromises = this.disposables.map(async service => {
      try {
        if (typeof service.dispose === 'function') {
          await service.dispose();
        }
      } catch (error) {
        Logger.error('Error disposing service:', error);
      }
    });

    await Promise.all(disposePromises);
    this.clear();

    Logger.info('All services disposed');
  }

  /**
   * Check if an object has a dispose method
   */
  private isDisposable(obj: any): obj is { dispose(): Promise<void> | void } {
    return obj && typeof obj.dispose === 'function';
  }

  /**
   * Get service registry statistics
   */
  getStats(): {
    totalServices: number;
    disposableServices: number;
    serviceNames: string[];
  } {
    return {
      totalServices: this.services.size,
      disposableServices: this.disposables.length,
      serviceNames: this.getServiceNames(),
    };
  }
}
