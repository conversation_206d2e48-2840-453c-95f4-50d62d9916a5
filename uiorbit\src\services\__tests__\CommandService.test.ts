/**
 * Unit tests for CommandService
 */

import * as vscode from 'vscode';

// Mock Logger before importing CommandService
jest.mock('../../utils/Logger', () => ({
  Logger: {
    initialize: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

import { CommandService } from '../CommandService';
import { ServiceRegistry } from '../../core/ServiceRegistry';

describe('CommandService', () => {
  let commandService: CommandService;
  let mockServiceRegistry: jest.Mocked<ServiceRegistry>;
  let mockVscode: any;

  beforeEach(() => {
    // Setup mock service registry
    mockServiceRegistry = {
      get: jest.fn(),
      register: jest.fn(),
      has: jest.fn(),
      unregister: jest.fn(),
      getServiceNames: jest.fn(),
      dispose: jest.fn(),
    } as any;

    // Get the mocked vscode module
    mockVscode = require('vscode');

    commandService = new CommandService(mockServiceRegistry);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Construction', () => {
    it('should create command service instance successfully', () => {
      expect(commandService).toBeInstanceOf(CommandService);
    });
  });

  describe('Open Chat Command', () => {
    it('should execute openChat command', async () => {
      await expect(commandService.openChat()).resolves.not.toThrow();
    });

    it('should execute VS Code command to focus chat view', async () => {
      await commandService.openChat();

      expect(mockVscode.commands.executeCommand).toHaveBeenCalledWith('uiorbit.chatView.focus');
    });

    it('should show information message when opening chat', async () => {
      await commandService.openChat();

      expect(mockVscode.window.showInformationMessage).toHaveBeenCalledWith(
        'UIOrbit chat is now open. Start chatting with your AI frontend assistant!'
      );
    });

    it('should handle errors when opening chat', async () => {
      mockVscode.commands.executeCommand.mockRejectedValue(new Error('Command failed'));

      await commandService.openChat();

      expect(mockVscode.window.showErrorMessage).toHaveBeenCalledWith(
        'Failed to open UIOrbit chat. Please try again.'
      );
    });
  });

  describe('Generate Component Command', () => {
    beforeEach(() => {
      // Mock workspace folders
      mockVscode.workspace.workspaceFolders = [
        { uri: { fsPath: '/test/workspace' } }
      ];

      // Mock progress
      mockVscode.window.withProgress.mockImplementation(async (options: any, task: any) => {
        const progress = { report: jest.fn() };
        return await task(progress);
      });
    });

    it('should execute generateComponent command with URI', async () => {
      // Mock configuration service
      const mockConfigService = {
        hasOpenAIApiKey: jest.fn(() => true),
      };
      mockServiceRegistry.get.mockReturnValue(mockConfigService);

      // Mock user inputs
      mockVscode.window.showInputBox
        .mockResolvedValueOnce('TestComponent') // component name
        .mockResolvedValueOnce('A test component'); // description

      const mockUri = vscode.Uri.file('/test/path');

      await expect(commandService.generateComponent(mockUri)).resolves.not.toThrow();
    });

    it('should execute generateComponent command without URI', async () => {
      // Mock configuration service
      const mockConfigService = {
        hasOpenAIApiKey: jest.fn(() => true),
      };
      mockServiceRegistry.get.mockReturnValue(mockConfigService);

      // Mock user inputs
      mockVscode.window.showInputBox
        .mockResolvedValueOnce('TestComponent') // component name
        .mockResolvedValueOnce('A test component'); // description

      await expect(commandService.generateComponent()).resolves.not.toThrow();
    });

    it('should show warning when API key is not configured', async () => {
      const mockConfigService = {
        hasOpenAIApiKey: jest.fn(() => false),
      };
      mockServiceRegistry.get.mockReturnValue(mockConfigService);

      await commandService.generateComponent();

      expect(mockVscode.window.showWarningMessage).toHaveBeenCalledWith(
        'OpenAI API key not configured. Please set your API key in settings or .env file.',
        'Open Settings'
      );
    });

    it('should return early when no component name is provided', async () => {
      // Clear previous mocks and set up new ones
      mockVscode.window.showInputBox.mockClear();
      mockVscode.window.withProgress.mockClear();

      mockVscode.window.showInputBox.mockResolvedValueOnce(undefined);

      await commandService.generateComponent();

      // Should not proceed to show progress
      expect(mockVscode.window.withProgress).not.toHaveBeenCalled();
    });
  });

  describe('Analyze Code Command', () => {
    beforeEach(() => {
      // Mock configuration service
      const mockConfigService = {
        hasOpenAIApiKey: jest.fn(() => true),
      };
      mockServiceRegistry.get.mockReturnValue(mockConfigService);

      // Mock progress
      mockVscode.window.withProgress.mockImplementation(async (options: any, task: any) => {
        const progress = { report: jest.fn() };
        return await task(progress);
      });
    });

    it('should execute analyzeCode command', async () => {
      mockVscode.window.activeTextEditor = {
        selection: { isEmpty: false },
        document: { getText: jest.fn(() => 'const test = "code";') },
      };

      await expect(commandService.analyzeCode()).resolves.not.toThrow();
    });

    it('should handle case when no active editor is available', async () => {
      mockVscode.window.activeTextEditor = undefined;

      await commandService.analyzeCode();

      expect(mockVscode.window.showWarningMessage).toHaveBeenCalledWith(
        'No active editor found. Please open a file and select some code.'
      );
    });

    it('should handle case when no code is selected', async () => {
      mockVscode.window.activeTextEditor = {
        selection: { isEmpty: true },
        document: { getText: jest.fn(() => '') },
      };

      await commandService.analyzeCode();

      expect(mockVscode.window.showWarningMessage).toHaveBeenCalledWith(
        'No code selected. Please select some code to analyze.'
      );
    });

    it('should show warning when API key is not configured', async () => {
      const mockConfigService = {
        hasOpenAIApiKey: jest.fn(() => false),
      };
      mockServiceRegistry.get.mockReturnValue(mockConfigService);

      mockVscode.window.activeTextEditor = {
        selection: { isEmpty: false },
        document: { getText: jest.fn(() => 'const test = "code";') },
      };

      await commandService.analyzeCode();

      expect(mockVscode.window.showWarningMessage).toHaveBeenCalledWith(
        'OpenAI API key not configured. Please set your API key in settings or .env file.',
        'Open Settings'
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle errors in openChat gracefully', () => {
      mockVscode.window.showInformationMessage.mockImplementation(() => {
        throw new Error('Mock error');
      });
      
      expect(() => commandService.openChat()).not.toThrow();
    });

    it('should handle errors in generateComponent gracefully', () => {
      mockVscode.window.showInformationMessage.mockImplementation(() => {
        throw new Error('Mock error');
      });
      
      expect(() => commandService.generateComponent()).not.toThrow();
    });

    it('should handle errors in analyzeCode gracefully', () => {
      mockVscode.window.showInformationMessage.mockImplementation(() => {
        throw new Error('Mock error');
      });
      
      expect(() => commandService.analyzeCode()).not.toThrow();
    });
  });

  describe('Service Integration', () => {
    it('should use service registry for dependencies', () => {
      // The CommandService should be able to work with the service registry
      expect(mockServiceRegistry).toBeDefined();
    });

    it('should handle missing services gracefully', () => {
      mockServiceRegistry.get.mockReturnValue(undefined);
      
      // Commands should still work even if services are not available
      expect(() => commandService.openChat()).not.toThrow();
      expect(() => commandService.generateComponent()).not.toThrow();
      expect(() => commandService.analyzeCode()).not.toThrow();
    });
  });
});
