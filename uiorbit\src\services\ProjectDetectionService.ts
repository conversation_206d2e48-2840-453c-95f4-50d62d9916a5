import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from '../utils/Logger';
import { FileOperationsService } from './FileOperationsService';
import { WorkspaceAnalysisService, ProjectAnalysis } from './WorkspaceAnalysisService';

export interface ProjectContext {
  hasProject: boolean;
  projectType: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla' | 'none';
  framework: string;
  styling: string;
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'unknown';
  components: ComponentMap;
  patterns: DesignPattern[];
  shouldAutoCreate: boolean;
  analysis?: ProjectAnalysis;
}

export interface ComponentMap {
  [componentName: string]: {
    path: string;
    type: string;
  };
}

export interface DesignPattern {
  name: string;
  type: string;
  description: string;
}

export interface ProjectCreationOptions {
  name: string;
  template: 'react' | 'vue' | 'angular' | 'svelte';
  styling: 'css' | 'tailwind' | 'styled-components';
  typescript: boolean;
  packageManager: 'npm' | 'yarn' | 'pnpm';
}

/**
 * Project Detection Service for UIOrbit
 * Handles project detection and automatic project creation
 */
export class ProjectDetectionService {
  private fileOps: FileOperationsService;
  private workspaceAnalysis: WorkspaceAnalysisService;
  private currentContext: ProjectContext | null = null;

  constructor(
    fileOperationsService: FileOperationsService,
    workspaceAnalysisService: WorkspaceAnalysisService
  ) {
    this.fileOps = fileOperationsService;
    this.workspaceAnalysis = workspaceAnalysisService;
  }

  /**
   * Detect project type and context
   */
  async detectProjectType(): Promise<ProjectContext> {
    try {
      Logger.info('Detecting project type...');

      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        return this.createEmptyContext();
      }

      const rootPath = workspaceFolder.uri.fsPath;

      // Check if project exists
      const hasProject = await this.hasExistingProject();
      
      if (!hasProject) {
        return {
          hasProject: false,
          projectType: 'none',
          framework: 'none',
          styling: 'none',
          packageManager: 'npm',
          components: {},
          patterns: [],
          shouldAutoCreate: true
        };
      }

      // Analyze existing project
      const analysis = await this.workspaceAnalysis.analyzeWorkspace();
      
      const context: ProjectContext = {
        hasProject: true,
        projectType: analysis.framework === 'unknown' ? 'none' : analysis.framework,
        framework: analysis.framework,
        styling: analysis.styling,
        packageManager: analysis.packageManager === 'unknown' ? 'npm' : analysis.packageManager,
        components: this.convertComponentMap(analysis.components),
        patterns: this.convertPatterns(analysis.patterns),
        shouldAutoCreate: false,
        analysis
      };

      this.currentContext = context;
      Logger.info('Project detected:', {
        type: context.projectType,
        framework: context.framework,
        styling: context.styling
      });

      return context;

    } catch (error) {
      Logger.error('Error detecting project type:', error);
      return this.createEmptyContext();
    }
  }

  /**
   * Check if existing project exists
   */
  async hasExistingProject(): Promise<boolean> {
    try {
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        return false;
      }

      const rootPath = workspaceFolder.uri.fsPath;

      // Check for package.json
      const packageJsonPath = path.join(rootPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        return true;
      }

      // Check for common project files
      const projectFiles = [
        'index.html',
        'src/main.js',
        'src/main.ts',
        'src/index.js',
        'src/index.ts',
        'src/App.js',
        'src/App.ts',
        'src/App.jsx',
        'src/App.tsx'
      ];

      for (const file of projectFiles) {
        const filePath = path.join(rootPath, file);
        if (await fs.pathExists(filePath)) {
          return true;
        }
      }

      return false;

    } catch (error) {
      Logger.error('Error checking for existing project:', error);
      return false;
    }
  }

  /**
   * Create Vite React project
   */
  async createViteReactProject(name: string): Promise<boolean> {
    try {
      Logger.info(`Creating Vite React project: ${name}`);

      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        throw new Error('No workspace folder found');
      }

      const rootPath = workspaceFolder.uri.fsPath;

      // Check if directory is empty or only contains .git, .vscode, etc.
      const isEmpty = await this.isDirectoryEmpty(rootPath);
      if (!isEmpty) {
        throw new Error('Directory is not empty. Cannot create project.');
      }

      // Create project structure
      await this.createProjectStructure(rootPath, {
        name,
        template: 'react',
        styling: 'tailwind',
        typescript: true,
        packageManager: 'npm'
      });

      Logger.info('Vite React project created successfully');
      
      // Update context
      await this.detectProjectType();
      
      return true;

    } catch (error) {
      Logger.error('Error creating Vite React project:', error);
      throw error;
    }
  }

  /**
   * Analyze existing codebase
   */
  async analyzeExistingCodebase(): Promise<ProjectAnalysis> {
    try {
      Logger.info('Analyzing existing codebase...');
      
      const analysis = await this.workspaceAnalysis.analyzeWorkspace();
      
      Logger.info('Codebase analysis completed:', {
        framework: analysis.framework,
        componentCount: Object.keys(analysis.components).length,
        patterns: analysis.patterns.length
      });

      return analysis;

    } catch (error) {
      Logger.error('Error analyzing existing codebase:', error);
      throw error;
    }
  }

  /**
   * Get current project context
   */
  async getProjectContext(): Promise<ProjectContext> {
    if (!this.currentContext) {
      this.currentContext = await this.detectProjectType();
    }
    return this.currentContext;
  }

  /**
   * Refresh project context
   */
  async refreshProjectContext(): Promise<ProjectContext> {
    this.currentContext = null;
    this.workspaceAnalysis.clearCache();
    return await this.getProjectContext();
  }

  /**
   * Create project structure
   */
  private async createProjectStructure(rootPath: string, options: ProjectCreationOptions): Promise<void> {
    // Create package.json
    const packageJson = {
      name: options.name,
      private: true,
      version: "0.0.0",
      type: "module",
      scripts: {
        dev: "vite",
        build: "vite build",
        preview: "vite preview"
      },
      dependencies: {
        react: "^18.2.0",
        "react-dom": "^18.2.0"
      },
      devDependencies: {
        "@types/react": "^18.2.66",
        "@types/react-dom": "^18.2.22",
        "@vitejs/plugin-react": "^4.2.1",
        typescript: "^5.2.2",
        vite: "^5.2.0"
      }
    };

    // Add Tailwind if selected
    if (options.styling === 'tailwind') {
      Object.assign(packageJson.devDependencies, {
        "tailwindcss": "^3.4.1",
        "autoprefixer": "^10.4.18",
        "postcss": "^8.4.35"
      });
    }

    await this.fileOps.createFile(
      path.join(rootPath, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );

    // Create vite.config.ts
    const viteConfig = `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
})
`;

    await this.fileOps.createFile(
      path.join(rootPath, 'vite.config.ts'),
      viteConfig
    );

    // Create tsconfig.json
    const tsConfig = {
      compilerOptions: {
        target: "ES2020",
        useDefineForClassFields: true,
        lib: ["ES2020", "DOM", "DOM.Iterable"],
        module: "ESNext",
        skipLibCheck: true,
        moduleResolution: "bundler",
        allowImportingTsExtensions: true,
        resolveJsonModule: true,
        isolatedModules: true,
        noEmit: true,
        jsx: "react-jsx",
        strict: true,
        noUnusedLocals: true,
        noUnusedParameters: true,
        noFallthroughCasesInSwitch: true
      },
      include: ["src"],
      references: [{ path: "./tsconfig.node.json" }]
    };

    await this.fileOps.createFile(
      path.join(rootPath, 'tsconfig.json'),
      JSON.stringify(tsConfig, null, 2)
    );

    // Create index.html
    const indexHtml = `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${options.name}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
`;

    await this.fileOps.createFile(
      path.join(rootPath, 'index.html'),
      indexHtml
    );

    // Create src directory and files
    await fs.ensureDir(path.join(rootPath, 'src'));

    // Create main.tsx
    const mainTsx = `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
`;

    await this.fileOps.createFile(
      path.join(rootPath, 'src', 'main.tsx'),
      mainTsx
    );

    // Create App.tsx
    const appTsx = `import { useState } from 'react'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="App">
      <h1>Welcome to ${options.name}</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </div>
  )
}

export default App
`;

    await this.fileOps.createFile(
      path.join(rootPath, 'src', 'App.tsx'),
      appTsx
    );

    // Create CSS files
    if (options.styling === 'tailwind') {
      const indexCss = `@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
`;

      await this.fileOps.createFile(
        path.join(rootPath, 'src', 'index.css'),
        indexCss
      );

      // Create tailwind.config.js
      const tailwindConfig = `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
`;

      await this.fileOps.createFile(
        path.join(rootPath, 'tailwind.config.js'),
        tailwindConfig
      );
    } else {
      // Basic CSS
      const indexCss = `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
`;

      await this.fileOps.createFile(
        path.join(rootPath, 'src', 'index.css'),
        indexCss
      );
    }

    const appCss = `.App {
  text-align: center;
  padding: 2rem;
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
`;

    await this.fileOps.createFile(
      path.join(rootPath, 'src', 'App.css'),
      appCss
    );

    Logger.info('Project structure created successfully');
  }

  /**
   * Check if directory is empty (ignoring common hidden files)
   */
  private async isDirectoryEmpty(dirPath: string): Promise<boolean> {
    try {
      const result = await this.fileOps.listFiles(dirPath);
      if (!result.success) return true;

      const ignoredFiles = ['.git', '.vscode', '.DS_Store', 'Thumbs.db'];
      const significantFiles = result.data.files.filter(
        (file: any) => !ignoredFiles.includes(file.name) && !file.name.startsWith('.')
      );

      return significantFiles.length === 0;

    } catch (error) {
      Logger.warn('Error checking if directory is empty:', error);
      return false;
    }
  }

  /**
   * Create empty context
   */
  private createEmptyContext(): ProjectContext {
    return {
      hasProject: false,
      projectType: 'none',
      framework: 'none',
      styling: 'none',
      packageManager: 'npm',
      components: {},
      patterns: [],
      shouldAutoCreate: true
    };
  }

  /**
   * Convert component map format
   */
  private convertComponentMap(components: any): ComponentMap {
    const converted: ComponentMap = {};
    for (const [name, info] of Object.entries(components)) {
      converted[name] = {
        path: (info as any).path,
        type: (info as any).type
      };
    }
    return converted;
  }

  /**
   * Convert patterns format
   */
  private convertPatterns(patterns: any[]): DesignPattern[] {
    return patterns.map(pattern => ({
      name: pattern.name,
      type: pattern.type,
      description: pattern.description
    }));
  }
}
