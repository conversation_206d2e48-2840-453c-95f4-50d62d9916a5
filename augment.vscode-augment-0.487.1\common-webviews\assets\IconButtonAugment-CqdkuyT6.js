var O=Object.defineProperty;var U=(t,e,r)=>e in t?O(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var $=(t,e,r)=>U(t,typeof e!="symbol"?e+"":e,r);import{W as v,B,s as y}from"./BaseButton-7bccWxEO.js";import{S as E,i as P,s as _,a as g,W as D,E as q,c as I,a3 as z,e as L,F as T,g as F,a4 as j,u as w,t as x,h as Z,I as k,Z as b,j as A,ab as m,$ as R,a0 as N,a1 as S,a2 as W}from"./SpinnerAugment-BJAAUt-n.js";class re{constructor(e,r=1e3){$(this,"_idToPromiseFns",new Map);$(this,"registerPromiseContext",e=>new Promise((r,o)=>{this._idToPromiseFns.set(e.requestId,{resolve:r,reject:o})}));$(this,"resolveAsyncMsg",e=>{if(e.type!==v.asyncWrapper)return!1;const r=e,o=this._idToPromiseFns.get(r.requestId);return!!o&&(this._idToPromiseFns.delete(r.requestId),r.error?o.reject(new Error(r.error)):o.resolve(r),!0)});$(this,"rejectAsyncMsg",(e,r)=>{const o=this._idToPromiseFns.get(e.requestId);o&&(this._idToPromiseFns.delete(e.requestId),console.debug(`AsyncMsgSender: Rejecting request, reason: ${r}`,e),o.reject(r))});$(this,"sendOrTimeout",(e,r=this._timeoutMs)=>{this._postMsgFn(e),r>0&&setTimeout(()=>{var o;return this.rejectAsyncMsg(e,new Error(`Request timed out: ${(o=e==null?void 0:e.baseMsg)==null?void 0:o.type}, id: ${e==null?void 0:e.requestId}`))},r)});$(this,"send",async(e,r=this._timeoutMs)=>{const o=C(e),n=this.registerPromiseContext(o);this.sendOrTimeout(o,r);const i=await n;if(i.error)throw new Error(i.error);if(!i.baseMsg)throw new Error("No response or error message");return i.baseMsg});$(this,"sendToSidecar",async(e,r=this._timeoutMs)=>{const o=C(e,"sidecar"),n=this.registerPromiseContext(o);this.sendOrTimeout(o,r);const i=await n;if(i.error)throw new Error(i.error);if(!i.baseMsg)throw new Error("No response or error message");return i.baseMsg});this._postMsgFn=e,this._timeoutMs=r,window.addEventListener("message",o=>{this.resolveAsyncMsg(o.data)})}async*stream(e,r=this._timeoutMs,o=this._timeoutMs){let n=C(e);n.streamCtx={streamMsgIdx:0,streamNextRequestId:""};let i=0,l=!1;try{let s=this.registerPromiseContext(n);this.sendOrTimeout(n,r);const u=new Promise((d,h)=>{o<=0||setTimeout(()=>h(new Error("Stream timed out")),o)});for(;!l;){const d=await Promise.race([s,u]);if((d==null?void 0:d.type)!==v.asyncWrapper)throw new Error(`Received unexpected message: ${d}`);if(d.error)throw new Error(d.error);if(!d.streamCtx||d.streamCtx.isStreamComplete)return;if(!d.baseMsg)throw new Error("No response or error message");if(d.streamCtx.streamMsgIdx!==i){const h=d.streamCtx.streamMsgIdx;throw new Error(`Received out of order stream chunk. Expected ${i} but got ${h}`)}i=d.streamCtx.streamMsgIdx+1,n={...n,streamCtx:{streamMsgIdx:i,streamNextRequestId:""},requestId:d.streamCtx.streamNextRequestId},s=this.registerPromiseContext(n),yield d.baseMsg}}finally{if(!l){l=!0;try{this._idToPromiseFns.delete(n.requestId)}catch(s){console.warn("Error sending stream cancellation message:",s)}}}}}function C(t,e="host"){return{type:v.asyncWrapper,requestId:crypto.randomUUID(),error:null,baseMsg:t,destination:e}}function G(t){let e;const r=t[8].default,o=R(r,t,t[18],null);return{c(){o&&o.c()},m(n,i){o&&o.m(n,i),e=!0},p(n,i){o&&o.p&&(!e||262144&i)&&N(o,r,n,n[18],e?W(r,n[18],i,null):S(n[18]),null)},i(n){e||(w(o,n),e=!0)},o(n){x(o,n),e=!1},d(n){o&&o.d(n)}}}function H(t){let e,r,o,n;const i=[{size:t[0]===0?.5:t[0]},{variant:t[1]},{color:t[2]},{highContrast:t[3]},{disabled:t[4]},{radius:t[5]},{class:t[7]},t[6]];let l={$$slots:{default:[G]},$$scope:{ctx:t}};for(let s=0;s<i.length;s+=1)l=g(l,i[s]);return r=new B({props:l}),r.$on("click",t[9]),r.$on("keyup",t[10]),r.$on("keydown",t[11]),r.$on("mousedown",t[12]),r.$on("mouseover",t[13]),r.$on("focus",t[14]),r.$on("mouseleave",t[15]),r.$on("blur",t[16]),r.$on("contextmenu",t[17]),{c(){e=D("div"),q(r.$$.fragment),I(e,"class",o=z(`c-icon-btn c-icon-btn--size-${y(t[0])}`)+" svelte-mshpb5")},m(s,u){L(s,e,u),T(r,e,null),n=!0},p(s,[u]){const d=255&u?F(i,[1&u&&{size:s[0]===0?.5:s[0]},2&u&&{variant:s[1]},4&u&&{color:s[2]},8&u&&{highContrast:s[3]},16&u&&{disabled:s[4]},32&u&&{radius:s[5]},128&u&&{class:s[7]},64&u&&j(s[6])]):{};262144&u&&(d.$$scope={dirty:u,ctx:s}),r.$set(d),(!n||1&u&&o!==(o=z(`c-icon-btn c-icon-btn--size-${y(s[0])}`)+" svelte-mshpb5"))&&I(e,"class",o)},i(s){n||(w(r.$$.fragment,s),n=!0)},o(s){x(r.$$.fragment,s),n=!1},d(s){s&&Z(e),k(r)}}}function J(t,e,r){let o,n;const i=["size","variant","color","highContrast","disabled","radius"];let l=b(e,i),{$$slots:s={},$$scope:u}=e,{size:d=2}=e,{variant:h="solid"}=e,{color:f="accent"}=e,{highContrast:p=!1}=e,{disabled:a=!1}=e,{radius:M="medium"}=e;return t.$$set=c=>{e=g(g({},e),A(c)),r(19,l=b(e,i)),"size"in c&&r(0,d=c.size),"variant"in c&&r(1,h=c.variant),"color"in c&&r(2,f=c.color),"highContrast"in c&&r(3,p=c.highContrast),"disabled"in c&&r(4,a=c.disabled),"radius"in c&&r(5,M=c.radius),"$$scope"in c&&r(18,u=c.$$scope)},t.$$.update=()=>{r(7,{class:o,...n}=l,o,(r(6,n),r(19,l)))},[d,h,f,p,a,M,n,o,s,function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},function(c){m.call(this,t,c)},u]}class K extends E{constructor(e){super(),P(this,e,J,H,_,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5})}}function Q(t){let e;const r=t[7].default,o=R(r,t,t[17],null);return{c(){o&&o.c()},m(n,i){o&&o.m(n,i),e=!0},p(n,i){o&&o.p&&(!e||131072&i)&&N(o,r,n,n[17],e?W(r,n[17],i,null):S(n[17]),null)},i(n){e||(w(o,n),e=!0)},o(n){x(o,n),e=!1},d(n){o&&o.d(n)}}}function V(t){let e,r;const o=[{size:t[0]},{variant:t[1]},{color:t[2]},{highContrast:t[3]},{disabled:t[4]},{radius:t[5]},t[6]];let n={$$slots:{default:[Q]},$$scope:{ctx:t}};for(let i=0;i<o.length;i+=1)n=g(n,o[i]);return e=new K({props:n}),e.$on("click",t[8]),e.$on("keyup",t[9]),e.$on("keydown",t[10]),e.$on("mousedown",t[11]),e.$on("mouseover",t[12]),e.$on("focus",t[13]),e.$on("mouseleave",t[14]),e.$on("blur",t[15]),e.$on("contextmenu",t[16]),{c(){q(e.$$.fragment)},m(i,l){T(e,i,l),r=!0},p(i,[l]){const s=127&l?F(o,[1&l&&{size:i[0]},2&l&&{variant:i[1]},4&l&&{color:i[2]},8&l&&{highContrast:i[3]},16&l&&{disabled:i[4]},32&l&&{radius:i[5]},64&l&&j(i[6])]):{};131072&l&&(s.$$scope={dirty:l,ctx:i}),e.$set(s)},i(i){r||(w(e.$$.fragment,i),r=!0)},o(i){x(e.$$.fragment,i),r=!1},d(i){k(e,i)}}}function X(t,e,r){const o=["size","variant","color","highContrast","disabled","radius"];let n=b(e,o),{$$slots:i={},$$scope:l}=e,{size:s=2}=e,{variant:u="solid"}=e,{color:d="accent"}=e,{highContrast:h=!1}=e,{disabled:f=!1}=e,{radius:p="medium"}=e;return t.$$set=a=>{e=g(g({},e),A(a)),r(6,n=b(e,o)),"size"in a&&r(0,s=a.size),"variant"in a&&r(1,u=a.variant),"color"in a&&r(2,d=a.color),"highContrast"in a&&r(3,h=a.highContrast),"disabled"in a&&r(4,f=a.disabled),"radius"in a&&r(5,p=a.radius),"$$scope"in a&&r(17,l=a.$$scope)},[s,u,d,h,f,p,n,i,function(a){m.call(this,t,a)},function(a){m.call(this,t,a)},function(a){m.call(this,t,a)},function(a){m.call(this,t,a)},function(a){m.call(this,t,a)},function(a){m.call(this,t,a)},function(a){m.call(this,t,a)},function(a){m.call(this,t,a)},function(a){m.call(this,t,a)},l]}class oe extends E{constructor(e){super(),P(this,e,X,V,_,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5})}}export{re as A,K as C,oe as I};
