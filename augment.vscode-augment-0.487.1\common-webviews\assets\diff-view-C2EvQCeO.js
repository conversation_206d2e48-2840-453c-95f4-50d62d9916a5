var mg=Object.defineProperty;var Th=o=>{throw TypeError(o)};var _g=(o,t,n)=>t in o?mg(o,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[t]=n;var _=(o,t,n)=>_g(o,typeof t!="symbol"?t+"":t,n),Uc=(o,t,n)=>t.has(o)||Th("Cannot "+n);var x=(o,t,n)=>(Uc(o,t,"read from private field"),n?n.call(o):t.get(o)),Et=(o,t,n)=>t.has(o)?Th("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(o):t.set(o,n),ut=(o,t,n,r)=>(Uc(o,t,"write to private field"),r?r.call(o,n):t.set(o,n),n),Y=(o,t,n)=>(Uc(o,t,"access private method"),n);var na=(o,t,n,r)=>({set _(s){ut(o,t,s,n)},get _(){return x(o,t,r)}});import{O as cn,P as Sd,N as ce,am as ae,aD as Fh,an as Xc,S as gi,i as mi,s as _i,ad as _r,W as Bt,J as qt,E as B,c as jt,Y as yn,ac as Hi,e as nt,f as Vt,F as V,a9 as Id,a8 as Wr,u as F,q as be,t as z,r as xe,h as rt,I as Z,aa as bu,ah as fi,K as he,C as Gi,D as vg,G as yg,A as tr,n as Ht,B as mr,aj as Md,M as Dn,al as Pc,aA as Xi,T as fa,ag as bg,a5 as xu,L as Ed,ak as xg,a6 as Ad,ai as $g,U as wg}from"./SpinnerAugment-BJAAUt-n.js";import"./design-system-init-D2yLRPnY.js";import{g as kg}from"./globals-D0QH3NT1.js";import{S as Cg,W as oe,h as zs,D as Wc,e as pa}from"./BaseButton-7bccWxEO.js";import{A as Sg,I as Ig}from"./IconButtonAugment-CqdkuyT6.js";import{i as bn,a as Bi,b as Ve,c as js,d as Rs,S as Ui,e as Pi,f as Ca,C as Mg,E as Eg,D as Ag,g as Tg,h as Fg,s as Hc,j as $u,k as ga,l as ma,m as wu,n as _a,o as ku,p as va,A as Rg,q as ya,r as Gc,t as Og,u as Lg,v as Ps,w as Td,U as Fd,x as Rd,y as Od,z as Ng}from"./chat-flags-model-t_MCj4l9.js";import{F as Ld,s as Dg,C as zg,a as jg,b as Nd,P as qg}from"./folder-opened-BWTQdsic.js";import{l as Ug,C as Jc,m as Pg,n as Rh,d as Oh,e as Lh}from"./types-xGAhb6Qr.js";import{P as Ae,C as Ne,a as Nn,I as Os,E as Wg}from"./chat-types-D7sox8tw.js";import{C as Hg}from"./types-CGlLNakm.js";import{M as Dd,T as Gg}from"./TextTooltipAugment-DGOJQXY9.js";import{K as vi,A as Nh,R as Bg,B as Vg,P as Zg,T as Qg,a as zd,b as Kg,C as Yg,c as Xg,G as Jg,d as tm,M as tu,e as jd,f as em,g as nm}from"./Keybindings-C-5u2652.js";import{B as Hr}from"./ButtonAugment-CLDnX_Hg.js";import{T as rm}from"./Content-BldOFwN2.js";import{F as Ze}from"./Filespan-D19TbAnP.js";import{D as Bc}from"./index-BAb5fkIe.js";import{M as Ji}from"./MaterialIcon-DkFwt_X2.js";import{a as im,M as sm,b as om,C as am}from"./index-C9A1ZQNk.js";import"./index-Bb_d2FL8.js";import"./file-paths-BcSg4gks.js";import"./CalloutAugment-Bc8HnLJ3.js";import"./exclamation-triangle-BN7hPNzx.js";import"./CardAugment-qFWs8J9b.js";import"./pen-to-square-SVW9AP0k.js";import"./augment-logo-f4Y8aL0S.js";var Dh=NaN,cm="[object Symbol]",um=/^\s+|\s+$/g,lm=/^[-+]0x[0-9a-f]+$/i,hm=/^0b[01]+$/i,dm=/^0o[0-7]+$/i,fm=parseInt,pm=typeof cn=="object"&&cn&&cn.Object===Object&&cn,gm=typeof self=="object"&&self&&self.Object===Object&&self,mm=pm||gm||Function("return this")(),_m=Object.prototype.toString,vm=Math.max,ym=Math.min,Vc=function(){return mm.Date.now()};function eu(o){var t=typeof o;return!!o&&(t=="object"||t=="function")}function zh(o){if(typeof o=="number")return o;if(function(r){return typeof r=="symbol"||function(s){return!!s&&typeof s=="object"}(r)&&_m.call(r)==cm}(o))return Dh;if(eu(o)){var t=typeof o.valueOf=="function"?o.valueOf():o;o=eu(t)?t+"":t}if(typeof o!="string")return o===0?o:+o;o=o.replace(um,"");var n=hm.test(o);return n||dm.test(o)?fm(o.slice(2),n?2:8):lm.test(o)?Dh:+o}const nu=Sd(function(o,t,n){var r,s,a,u,l,d,m=0,p=!1,y=!1,b=!0;if(typeof o!="function")throw new TypeError("Expected a function");function w(E){var G=r,ot=s;return r=s=void 0,m=E,u=o.apply(ot,G)}function T(E){var G=E-d;return d===void 0||G>=t||G<0||y&&E-m>=a}function j(){var E=Vc();if(T(E))return q(E);l=setTimeout(j,function(G){var ot=t-(G-d);return y?ym(ot,a-(G-m)):ot}(E))}function q(E){return l=void 0,b&&r?w(E):(r=s=void 0,u)}function S(){var E=Vc(),G=T(E);if(r=arguments,s=this,d=E,G){if(l===void 0)return function(ot){return m=ot,l=setTimeout(j,t),p?w(ot):u}(d);if(y)return l=setTimeout(j,t),w(d)}return l===void 0&&(l=setTimeout(j,t)),u}return t=zh(t)||0,eu(n)&&(p=!!n.leading,a=(y="maxWait"in n)?vm(zh(n.maxWait)||0,t):a,b="trailing"in n?!!n.trailing:b),S.cancel=function(){l!==void 0&&clearTimeout(l),m=0,r=d=s=l=void 0},S.flush=function(){return l===void 0?u:q(Vc())},S});var ru={exports:{}};(function(o,t){var n="__lodash_hash_undefined__",r=1,s=2,a=9007199254740991,u="[object Arguments]",l="[object Array]",d="[object AsyncFunction]",m="[object Boolean]",p="[object Date]",y="[object Error]",b="[object Function]",w="[object GeneratorFunction]",T="[object Map]",j="[object Number]",q="[object Null]",S="[object Object]",E="[object Promise]",G="[object Proxy]",ot="[object RegExp]",st="[object Set]",Nt="[object String]",pt="[object Symbol]",St="[object Undefined]",mt="[object WeakMap]",Ft="[object ArrayBuffer]",Gt="[object DataView]",ue=/^\[object .+?Constructor\]$/,Ct=/^(?:0|[1-9]\d*)$/,$t={};$t["[object Float32Array]"]=$t["[object Float64Array]"]=$t["[object Int8Array]"]=$t["[object Int16Array]"]=$t["[object Int32Array]"]=$t["[object Uint8Array]"]=$t["[object Uint8ClampedArray]"]=$t["[object Uint16Array]"]=$t["[object Uint32Array]"]=!0,$t[u]=$t[l]=$t[Ft]=$t[m]=$t[Gt]=$t[p]=$t[y]=$t[b]=$t[T]=$t[j]=$t[S]=$t[ot]=$t[st]=$t[Nt]=$t[mt]=!1;var de=typeof cn=="object"&&cn&&cn.Object===Object&&cn,xn=typeof self=="object"&&self&&self.Object===Object&&self,Se=de||xn||Function("return this")(),ft=t&&!t.nodeType&&t,$n=ft&&o&&!o.nodeType&&o,zn=$n&&$n.exports===ft,er=zn&&de.process,yr=function(){try{return er&&er.binding&&er.binding("util")}catch{}}(),bi=yr&&yr.isTypedArray;function ts(C,A){for(var W=-1,et=C==null?0:C.length;++W<et;)if(A(C[W],W,C))return!0;return!1}function Ia(C){var A=-1,W=Array(C.size);return C.forEach(function(et,Pt){W[++A]=[Pt,et]}),W}function Ma(C){var A=-1,W=Array(C.size);return C.forEach(function(et){W[++A]=et}),W}var Vs,es,ns,Ea=Array.prototype,Aa=Function.prototype,xi=Object.prototype,rs=Se["__core-js_shared__"],is=Aa.toString,un=xi.hasOwnProperty,Zs=(Vs=/[^.]+$/.exec(rs&&rs.keys&&rs.keys.IE_PROTO||""))?"Symbol(src)_1."+Vs:"",Qs=xi.toString,ss=RegExp("^"+is.call(un).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ks=zn?Se.Buffer:void 0,br=Se.Symbol,Ys=Se.Uint8Array,Xs=xi.propertyIsEnumerable,Ta=Ea.splice,nr=br?br.toStringTag:void 0,Js=Object.getOwnPropertySymbols,Fa=Ks?Ks.isBuffer:void 0,Ra=(es=Object.keys,ns=Object,function(C){return es(ns(C))}),os=Wn(Se,"DataView"),xr=Wn(Se,"Map"),as=Wn(Se,"Promise"),cs=Wn(Se,"Set"),us=Wn(Se,"WeakMap"),Br=Wn(Object,"create"),Oa=rr(os),La=rr(xr),$i=rr(as),Na=rr(cs),wi=rr(us),ls=br?br.prototype:void 0,ki=ls?ls.valueOf:void 0;function jn(C){var A=-1,W=C==null?0:C.length;for(this.clear();++A<W;){var et=C[A];this.set(et[0],et[1])}}function ln(C){var A=-1,W=C==null?0:C.length;for(this.clear();++A<W;){var et=C[A];this.set(et[0],et[1])}}function qn(C){var A=-1,W=C==null?0:C.length;for(this.clear();++A<W;){var et=C[A];this.set(et[0],et[1])}}function Vr(C){var A=-1,W=C==null?0:C.length;for(this.__data__=new qn;++A<W;)this.add(C[A])}function Un(C){var A=this.__data__=new ln(C);this.size=A.size}function Da(C,A){var W=Qr(C),et=!W&&io(C),Pt=!W&&!et&&Si(C),J=!W&&!et&&!Pt&&ao(C),dt=W||et||Pt||J,te=dt?function(ee,hn){for(var Wt=-1,fe=Array(ee);++Wt<ee;)fe[Wt]=hn(Wt);return fe}(C.length,String):[],Qe=te.length;for(var re in C)!un.call(C,re)||dt&&(re=="length"||Pt&&(re=="offset"||re=="parent")||J&&(re=="buffer"||re=="byteLength"||re=="byteOffset")||ro(re,Qe))||te.push(re);return te}function Zr(C,A){for(var W=C.length;W--;)if(fs(C[W][0],A))return W;return-1}function Pn(C){return C==null?C===void 0?St:q:nr&&nr in Object(C)?function(A){var W=un.call(A,nr),et=A[nr];try{A[nr]=void 0;var Pt=!0}catch{}var J=Qs.call(A);return Pt&&(W?A[nr]=et:delete A[nr]),J}(C):function(A){return Qs.call(A)}(C)}function hs(C){return Kr(C)&&Pn(C)==u}function to(C,A,W,et,Pt){return C===A||(C==null||A==null||!Kr(C)&&!Kr(A)?C!=C&&A!=A:function(J,dt,te,Qe,re,ee){var hn=Qr(J),Wt=Qr(dt),fe=hn?l:wn(J),Ie=Wt?l:wn(dt),ir=(fe=fe==u?S:fe)==S,kr=(Ie=Ie==u?S:Ie)==S,pe=fe==Ie;if(pe&&Si(J)){if(!Si(dt))return!1;hn=!0,ir=!1}if(pe&&!ir)return ee||(ee=new Un),hn||ao(J)?Ci(J,dt,te,Qe,re,ee):function(Rt,At,$e,Hn,Te,Pe,dn){switch($e){case Gt:if(Rt.byteLength!=At.byteLength||Rt.byteOffset!=At.byteOffset)return!1;Rt=Rt.buffer,At=At.buffer;case Ft:return!(Rt.byteLength!=At.byteLength||!Pe(new Ys(Rt),new Ys(At)));case m:case p:case j:return fs(+Rt,+At);case y:return Rt.name==At.name&&Rt.message==At.message;case ot:case Nt:return Rt==At+"";case T:var Me=Ia;case st:var Gn=Hn&r;if(Me||(Me=Ma),Rt.size!=At.size&&!Gn)return!1;var Sr=dn.get(Rt);if(Sr)return Sr==At;Hn|=s,dn.set(Rt,At);var Dt=Ci(Me(Rt),Me(At),Hn,Te,Pe,dn);return dn.delete(Rt),Dt;case pt:if(ki)return ki.call(Rt)==ki.call(At)}return!1}(J,dt,fe,te,Qe,re,ee);if(!(te&r)){var Cr=ir&&un.call(J,"__wrapped__"),ms=kr&&un.call(dt,"__wrapped__");if(Cr||ms){var co=Cr?J.value():J,uo=ms?dt.value():dt;return ee||(ee=new Un),re(co,uo,te,Qe,ee)}}return pe?(ee||(ee=new Un),function(Rt,At,$e,Hn,Te,Pe){var dn=$e&r,Me=ds(Rt),Gn=Me.length,Sr=ds(At),Dt=Sr.length;if(Gn!=Dt&&!dn)return!1;for(var Ke=Gn;Ke--;){var kn=Me[Ke];if(!(dn?kn in At:un.call(At,kn)))return!1}var lo=Pe.get(Rt);if(lo&&Pe.get(At))return lo==At;var Ir=!0;Pe.set(Rt,At),Pe.set(At,Rt);for(var _s=dn;++Ke<Gn;){var Yr=Rt[kn=Me[Ke]],sr=At[kn];if(Hn)var or=dn?Hn(sr,Yr,kn,At,Rt,Pe):Hn(Yr,sr,kn,Rt,At,Pe);if(!(or===void 0?Yr===sr||Te(Yr,sr,$e,Hn,Pe):or)){Ir=!1;break}_s||(_s=kn=="constructor")}if(Ir&&!_s){var Ii=Rt.constructor,Xr=At.constructor;Ii==Xr||!("constructor"in Rt)||!("constructor"in At)||typeof Ii=="function"&&Ii instanceof Ii&&typeof Xr=="function"&&Xr instanceof Xr||(Ir=!1)}return Pe.delete(Rt),Pe.delete(At),Ir}(J,dt,te,Qe,re,ee)):!1}(C,A,W,et,to,Pt))}function eo(C){return!(!oo(C)||function(A){return!!Zs&&Zs in A}(C))&&(ps(C)?ss:ue).test(rr(C))}function no(C){if(W=(A=C)&&A.constructor,et=typeof W=="function"&&W.prototype||xi,A!==et)return Ra(C);var A,W,et,Pt=[];for(var J in Object(C))un.call(C,J)&&J!="constructor"&&Pt.push(J);return Pt}function Ci(C,A,W,et,Pt,J){var dt=W&r,te=C.length,Qe=A.length;if(te!=Qe&&!(dt&&Qe>te))return!1;var re=J.get(C);if(re&&J.get(A))return re==A;var ee=-1,hn=!0,Wt=W&s?new Vr:void 0;for(J.set(C,A),J.set(A,C);++ee<te;){var fe=C[ee],Ie=A[ee];if(et)var ir=dt?et(Ie,fe,ee,A,C,J):et(fe,Ie,ee,C,A,J);if(ir!==void 0){if(ir)continue;hn=!1;break}if(Wt){if(!ts(A,function(kr,pe){if(Cr=pe,!Wt.has(Cr)&&(fe===kr||Pt(fe,kr,W,et,J)))return Wt.push(pe);var Cr})){hn=!1;break}}else if(fe!==Ie&&!Pt(fe,Ie,W,et,J)){hn=!1;break}}return J.delete(C),J.delete(A),hn}function ds(C){return function(A,W,et){var Pt=W(A);return Qr(A)?Pt:function(J,dt){for(var te=-1,Qe=dt.length,re=J.length;++te<Qe;)J[re+te]=dt[te];return J}(Pt,et(A))}(C,gs,wr)}function $r(C,A){var W,et,Pt=C.__data__;return((et=typeof(W=A))=="string"||et=="number"||et=="symbol"||et=="boolean"?W!=="__proto__":W===null)?Pt[typeof A=="string"?"string":"hash"]:Pt.map}function Wn(C,A){var W=function(et,Pt){return et==null?void 0:et[Pt]}(C,A);return eo(W)?W:void 0}jn.prototype.clear=function(){this.__data__=Br?Br(null):{},this.size=0},jn.prototype.delete=function(C){var A=this.has(C)&&delete this.__data__[C];return this.size-=A?1:0,A},jn.prototype.get=function(C){var A=this.__data__;if(Br){var W=A[C];return W===n?void 0:W}return un.call(A,C)?A[C]:void 0},jn.prototype.has=function(C){var A=this.__data__;return Br?A[C]!==void 0:un.call(A,C)},jn.prototype.set=function(C,A){var W=this.__data__;return this.size+=this.has(C)?0:1,W[C]=Br&&A===void 0?n:A,this},ln.prototype.clear=function(){this.__data__=[],this.size=0},ln.prototype.delete=function(C){var A=this.__data__,W=Zr(A,C);return!(W<0)&&(W==A.length-1?A.pop():Ta.call(A,W,1),--this.size,!0)},ln.prototype.get=function(C){var A=this.__data__,W=Zr(A,C);return W<0?void 0:A[W][1]},ln.prototype.has=function(C){return Zr(this.__data__,C)>-1},ln.prototype.set=function(C,A){var W=this.__data__,et=Zr(W,C);return et<0?(++this.size,W.push([C,A])):W[et][1]=A,this},qn.prototype.clear=function(){this.size=0,this.__data__={hash:new jn,map:new(xr||ln),string:new jn}},qn.prototype.delete=function(C){var A=$r(this,C).delete(C);return this.size-=A?1:0,A},qn.prototype.get=function(C){return $r(this,C).get(C)},qn.prototype.has=function(C){return $r(this,C).has(C)},qn.prototype.set=function(C,A){var W=$r(this,C),et=W.size;return W.set(C,A),this.size+=W.size==et?0:1,this},Vr.prototype.add=Vr.prototype.push=function(C){return this.__data__.set(C,n),this},Vr.prototype.has=function(C){return this.__data__.has(C)},Un.prototype.clear=function(){this.__data__=new ln,this.size=0},Un.prototype.delete=function(C){var A=this.__data__,W=A.delete(C);return this.size=A.size,W},Un.prototype.get=function(C){return this.__data__.get(C)},Un.prototype.has=function(C){return this.__data__.has(C)},Un.prototype.set=function(C,A){var W=this.__data__;if(W instanceof ln){var et=W.__data__;if(!xr||et.length<199)return et.push([C,A]),this.size=++W.size,this;W=this.__data__=new qn(et)}return W.set(C,A),this.size=W.size,this};var wr=Js?function(C){return C==null?[]:(C=Object(C),function(A,W){for(var et=-1,Pt=A==null?0:A.length,J=0,dt=[];++et<Pt;){var te=A[et];W(te,et,A)&&(dt[J++]=te)}return dt}(Js(C),function(A){return Xs.call(C,A)}))}:function(){return[]},wn=Pn;function ro(C,A){return!!(A=A??a)&&(typeof C=="number"||Ct.test(C))&&C>-1&&C%1==0&&C<A}function rr(C){if(C!=null){try{return is.call(C)}catch{}try{return C+""}catch{}}return""}function fs(C,A){return C===A||C!=C&&A!=A}(os&&wn(new os(new ArrayBuffer(1)))!=Gt||xr&&wn(new xr)!=T||as&&wn(as.resolve())!=E||cs&&wn(new cs)!=st||us&&wn(new us)!=mt)&&(wn=function(C){var A=Pn(C),W=A==S?C.constructor:void 0,et=W?rr(W):"";if(et)switch(et){case Oa:return Gt;case La:return T;case $i:return E;case Na:return st;case wi:return mt}return A});var io=hs(function(){return arguments}())?hs:function(C){return Kr(C)&&un.call(C,"callee")&&!Xs.call(C,"callee")},Qr=Array.isArray,Si=Fa||function(){return!1};function ps(C){if(!oo(C))return!1;var A=Pn(C);return A==b||A==w||A==d||A==G}function so(C){return typeof C=="number"&&C>-1&&C%1==0&&C<=a}function oo(C){var A=typeof C;return C!=null&&(A=="object"||A=="function")}function Kr(C){return C!=null&&typeof C=="object"}var ao=bi?function(C){return function(A){return C(A)}}(bi):function(C){return Kr(C)&&so(C.length)&&!!$t[Pn(C)]};function gs(C){return(A=C)!=null&&so(A.length)&&!ps(A)?Da(C):no(C);var A}o.exports=function(C,A){return to(C,A)}})(ru,ru.exports);const bm=Sd(ru.exports);function qd(o){return function(t){return"unitOfCodeWork"in t&&!function(n){return n.children.length>0&&"childIds"in n}(t)}(o)?[o]:o.children.flatMap(qd)}function Vi(o){var t;return((t=o.extraData)==null?void 0:t.isAgentConversation)===!0}var ye=(o=>(o[o.active=0]="active",o[o.inactive=1]="inactive",o))(ye||{});function xm(o,t,n=1e3){let r=null,s=0;const a=ce(t),u=()=>{const l=(()=>{const d=Date.now();if(r!==null&&d-s<n)return r;const m=o();return r=m,s=d,m})();a.set(l)};return{subscribe:a.subscribe,resetCache:()=>{r=null,u()},updateStore:u}}var Ud=(o=>(o[o.unset=0]="unset",o[o.positive=1]="positive",o[o.negative=2]="negative",o))(Ud||{}),Ns=(o=>(o[o.unknown=0]="unknown",o[o.new=1]="new",o[o.checkingSafety=2]="checkingSafety",o[o.runnable=3]="runnable",o[o.running=4]="running",o[o.completed=5]="completed",o[o.error=6]="error",o[o.cancelling=7]="cancelling",o[o.cancelled=8]="cancelled",o))(Ns||{});function Zc(o){return o.requestId+";"+o.toolUseId}function jh(o){const[t,n]=o.split(";");return{requestId:t,toolUseId:n}}const tn="__NEW_AGENT__";function $m(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let yi={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function qh(o){yi=o}const Pd=/[&<>"']/,wm=new RegExp(Pd.source,"g"),Wd=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,km=new RegExp(Wd.source,"g"),Cm={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Uh=o=>Cm[o];function on(o,t){if(t){if(Pd.test(o))return o.replace(wm,Uh)}else if(Wd.test(o))return o.replace(km,Uh);return o}const Sm=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Im(o){return o.replace(Sm,(t,n)=>(n=n.toLowerCase())==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):"")}const Mm=/(^|[^\[])\^/g;function Ut(o,t){let n=typeof o=="string"?o:o.source;t=t||"";const r={replace:(s,a)=>{let u=typeof a=="string"?a:a.source;return u=u.replace(Mm,"$1"),n=n.replace(s,u),r},getRegex:()=>new RegExp(n,t)};return r}function Ph(o){try{o=encodeURI(o).replace(/%25/g,"%")}catch{return null}return o}const qs={exec:()=>null};function Wh(o,t){const n=o.replace(/\|/g,(s,a,u)=>{let l=!1,d=a;for(;--d>=0&&u[d]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function ra(o,t,n){const r=o.length;if(r===0)return"";let s=0;for(;s<r;){const a=o.charAt(r-s-1);if(a!==t||n){if(a===t||!n)break;s++}else s++}return o.slice(0,r-s)}function Hh(o,t,n,r){const s=t.href,a=t.title?on(t.title):null,u=o[1].replace(/\\([\[\]])/g,"$1");if(o[0].charAt(0)!=="!"){r.state.inLink=!0;const l={type:"link",raw:n,href:s,title:a,text:u,tokens:r.inlineTokens(u)};return r.state.inLink=!1,l}return{type:"image",raw:n,href:s,title:a,text:on(u)}}class ba{constructor(t){_(this,"options");_(this,"rules");_(this,"lexer");this.options=t||yi}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:ra(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],s=function(a,u){const l=a.match(/^(\s+)(?:```)/);if(l===null)return u;const d=l[1];return u.split(`
`).map(m=>{const p=m.match(/^\s+/);if(p===null)return m;const[y]=p;return y.length>=d.length?m.slice(d.length):m}).join(`
`)}(r,n[3]||"");return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):n[2],text:s}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(/#$/.test(r)){const s=ra(r,"#");this.options.pedantic?r=s.trim():s&&!/ $/.test(s)||(r=s.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:n[0]}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){const r=ra(n[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const a=this.lexer.blockTokens(r);return this.lexer.state.top=s,{type:"blockquote",raw:n[0],tokens:a,text:r}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r=n[1].trim();const s=r.length>1,a={type:"list",raw:"",ordered:s,start:s?+r.slice(0,-1):"",loose:!1,items:[]};r=s?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=s?r:"[*+-]");const u=new RegExp(`^( {0,3}${r})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",d="",m=!1;for(;t;){let p=!1;if(!(n=u.exec(t))||this.rules.block.hr.test(t))break;l=n[0],t=t.substring(l.length);let y=n[2].split(`
`,1)[0].replace(/^\t+/,S=>" ".repeat(3*S.length)),b=t.split(`
`,1)[0],w=0;this.options.pedantic?(w=2,d=y.trimStart()):(w=n[2].search(/[^ ]/),w=w>4?1:w,d=y.slice(w),w+=n[1].length);let T=!1;if(!y&&/^ *$/.test(b)&&(l+=b+`
`,t=t.substring(b.length+1),p=!0),!p){const S=new RegExp(`^ {0,${Math.min(3,w-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),E=new RegExp(`^ {0,${Math.min(3,w-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),G=new RegExp(`^ {0,${Math.min(3,w-1)}}(?:\`\`\`|~~~)`),ot=new RegExp(`^ {0,${Math.min(3,w-1)}}#`);for(;t;){const st=t.split(`
`,1)[0];if(b=st,this.options.pedantic&&(b=b.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),G.test(b)||ot.test(b)||S.test(b)||E.test(t))break;if(b.search(/[^ ]/)>=w||!b.trim())d+=`
`+b.slice(w);else{if(T||y.search(/[^ ]/)>=4||G.test(y)||ot.test(y)||E.test(y))break;d+=`
`+b}T||b.trim()||(T=!0),l+=st+`
`,t=t.substring(st.length+1),y=b.slice(w)}}a.loose||(m?a.loose=!0:/\n *\n *$/.test(l)&&(m=!0));let j,q=null;this.options.gfm&&(q=/^\[[ xX]\] /.exec(d),q&&(j=q[0]!=="[ ] ",d=d.replace(/^\[[ xX]\] +/,""))),a.items.push({type:"list_item",raw:l,task:!!q,checked:j,loose:!1,text:d,tokens:[]}),a.raw+=l}a.items[a.items.length-1].raw=l.trimEnd(),a.items[a.items.length-1].text=d.trimEnd(),a.raw=a.raw.trimEnd();for(let p=0;p<a.items.length;p++)if(this.lexer.state.top=!1,a.items[p].tokens=this.lexer.blockTokens(a.items[p].text,[]),!a.loose){const y=a.items[p].tokens.filter(w=>w.type==="space"),b=y.length>0&&y.some(w=>/\n.*\n/.test(w.raw));a.loose=b}if(a.loose)for(let p=0;p<a.items.length;p++)a.items[p].loose=!0;return a}}html(t){const n=this.rules.block.html.exec(t);if(n)return{type:"html",block:!0,raw:n[0],pre:n[1]==="pre"||n[1]==="script"||n[1]==="style",text:n[0]}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(/\s+/g," "),s=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",a=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:s,title:a}}}table(t){const n=this.rules.block.table.exec(t);if(!n||!/[:|]/.test(n[2]))return;const r=Wh(n[1]),s=n[2].replace(/^\||\| *$/g,"").split("|"),a=n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[],u={type:"table",raw:n[0],header:[],align:[],rows:[]};if(r.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?u.align.push("right"):/^ *:-+: *$/.test(l)?u.align.push("center"):/^ *:-+ *$/.test(l)?u.align.push("left"):u.align.push(null);for(const l of r)u.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of a)u.rows.push(Wh(l,u.header.length).map(d=>({text:d,tokens:this.lexer.inline(d)})));return u}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:on(n[1])}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const u=ra(r.slice(0,-1),"\\");if((r.length-u.length)%2==0)return}else{const u=function(l,d){if(l.indexOf(d[1])===-1)return-1;let m=0;for(let p=0;p<l.length;p++)if(l[p]==="\\")p++;else if(l[p]===d[0])m++;else if(l[p]===d[1]&&(m--,m<0))return p;return-1}(n[2],"()");if(u>-1){const l=(n[0].indexOf("!")===0?5:4)+n[1].length+u;n[2]=n[2].substring(0,u),n[0]=n[0].substring(0,l).trim(),n[3]=""}}let s=n[2],a="";if(this.options.pedantic){const u=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);u&&(s=u[1],a=u[3])}else a=n[3]?n[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(r)?s.slice(1):s.slice(1,-1)),Hh(n,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:a&&a.replace(this.rules.inline.anyPunctuation,"$1")},n[0],this.lexer)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){const s=n[(r[2]||r[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const a=r[0].charAt(0);return{type:"text",raw:a,text:a}}return Hh(r,s,r[0],this.lexer)}}emStrong(t,n,r=""){let s=this.rules.inline.emStrongLDelim.exec(t);if(s&&!(s[3]&&r.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!r||this.rules.inline.punctuation.exec(r))){const a=[...s[0]].length-1;let u,l,d=a,m=0;const p=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(p.lastIndex=0,n=n.slice(-1*t.length+a);(s=p.exec(n))!=null;){if(u=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!u)continue;if(l=[...u].length,s[3]||s[4]){d+=l;continue}if((s[5]||s[6])&&a%3&&!((a+l)%3)){m+=l;continue}if(d-=l,d>0)continue;l=Math.min(l,l+d+m);const y=[...s[0]][0].length,b=t.slice(0,a+s.index+y+l);if(Math.min(a,l)%2){const T=b.slice(1,-1);return{type:"em",raw:b,text:T,tokens:this.lexer.inlineTokens(T)}}const w=b.slice(2,-2);return{type:"strong",raw:b,text:w,tokens:this.lexer.inlineTokens(w)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(/\n/g," ");const s=/[^ ]/.test(r),a=/^ /.test(r)&&/ $/.test(r);return s&&a&&(r=r.substring(1,r.length-1)),r=on(r,!0),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t){const n=this.rules.inline.autolink.exec(t);if(n){let r,s;return n[2]==="@"?(r=on(n[1]),s="mailto:"+r):(r=on(n[1]),s=r),{type:"link",raw:n[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}url(t){var r;let n;if(n=this.rules.inline.url.exec(t)){let s,a;if(n[2]==="@")s=on(n[0]),a="mailto:"+s;else{let u;do u=n[0],n[0]=((r=this.rules.inline._backpedal.exec(n[0]))==null?void 0:r[0])??"";while(u!==n[0]);s=on(n[0]),a=n[1]==="www."?"http://"+n[0]:n[0]}return{type:"link",raw:n[0],text:s,href:a,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(t){const n=this.rules.inline.text.exec(t);if(n){let r;return r=this.lexer.state.inRawBlock?n[0]:on(n[0]),{type:"text",raw:n[0],text:r}}}}const Gs=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Hd=/(?:[*+-]|\d{1,9}[.)])/,Gd=Ut(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Hd).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),Cu=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Su=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Em=Ut(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Su).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Am=Ut(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Hd).getRegex(),Sa="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Iu=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Tm=Ut("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Iu).replace("tag",Sa).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Gh=Ut(Cu).replace("hr",Gs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Sa).getRegex(),Mu={blockquote:Ut(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Gh).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Em,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:Gs,html:Tm,lheading:Gd,list:Am,newline:/^(?: *(?:\n|$))+/,paragraph:Gh,table:qs,text:/^[^\n]+/},Bh=Ut("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Gs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Sa).getRegex(),Fm={...Mu,table:Bh,paragraph:Ut(Cu).replace("hr",Gs).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Bh).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Sa).getRegex()},Rm={...Mu,html:Ut(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Iu).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:qs,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Ut(Cu).replace("hr",Gs).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Gd).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Bd=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Vd=/^( {2,}|\\)\n(?!\s*$)/,Bs="\\p{P}\\p{S}",Om=Ut(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,Bs).getRegex(),Lm=Ut(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,Bs).getRegex(),Nm=Ut("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,Bs).getRegex(),Dm=Ut("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,Bs).getRegex(),zm=Ut(/\\([punct])/,"gu").replace(/punct/g,Bs).getRegex(),jm=Ut(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),qm=Ut(Iu).replace("(?:-->|$)","-->").getRegex(),Um=Ut("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",qm).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),xa=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Pm=Ut(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",xa).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Vh=Ut(/^!?\[(label)\]\[(ref)\]/).replace("label",xa).replace("ref",Su).getRegex(),Zh=Ut(/^!?\[(ref)\](?:\[\])?/).replace("ref",Su).getRegex(),Eu={_backpedal:qs,anyPunctuation:zm,autolink:jm,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:Vd,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:qs,emStrongLDelim:Lm,emStrongRDelimAst:Nm,emStrongRDelimUnd:Dm,escape:Bd,link:Pm,nolink:Zh,punctuation:Om,reflink:Vh,reflinkSearch:Ut("reflink|nolink(?!\\()","g").replace("reflink",Vh).replace("nolink",Zh).getRegex(),tag:Um,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:qs},Wm={...Eu,link:Ut(/^!?\[(label)\]\((.*?)\)/).replace("label",xa).getRegex(),reflink:Ut(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",xa).getRegex()},iu={...Eu,escape:Ut(Bd).replace("])","~|])").getRegex(),url:Ut(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Hm={...iu,br:Ut(Vd).replace("{2,}","*").getRegex(),text:Ut(iu.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ia={normal:Mu,gfm:Fm,pedantic:Rm},Ls={normal:Eu,gfm:iu,breaks:Hm,pedantic:Wm};class Yn{constructor(t){_(this,"tokens");_(this,"options");_(this,"state");_(this,"tokenizer");_(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||yi,this.options.tokenizer=this.options.tokenizer||new ba,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:ia.normal,inline:Ls.normal};this.options.pedantic?(n.block=ia.pedantic,n.inline=Ls.pedantic):this.options.gfm&&(n.block=ia.gfm,this.options.breaks?n.inline=Ls.breaks:n.inline=Ls.gfm),this.tokenizer.rules=n}static get rules(){return{block:ia,inline:Ls}}static lex(t,n){return new Yn(n).lex(t)}static lexInline(t,n){return new Yn(n).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){const r=this.inlineQueue[n];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,n=[]){let r,s,a,u;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(l,d,m)=>d+"    ".repeat(m.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(r=l.call({lexer:this},t,n))&&(t=t.substring(r.raw.length),n.push(r),!0))))if(r=this.tokenizer.space(t))t=t.substring(r.raw.length),r.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(r);else if(r=this.tokenizer.code(t))t=t.substring(r.raw.length),s=n[n.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?n.push(r):(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(r=this.tokenizer.fences(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.heading(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.hr(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.blockquote(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.list(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.html(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.def(t))t=t.substring(r.raw.length),s=n[n.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title}):(s.raw+=`
`+r.raw,s.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(r=this.tokenizer.table(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.lheading(t))t=t.substring(r.raw.length),n.push(r);else{if(a=t,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const d=t.slice(1);let m;this.options.extensions.startBlock.forEach(p=>{m=p.call({lexer:this},d),typeof m=="number"&&m>=0&&(l=Math.min(l,m))}),l<1/0&&l>=0&&(a=t.substring(0,l+1))}if(this.state.top&&(r=this.tokenizer.paragraph(a)))s=n[n.length-1],u&&s.type==="paragraph"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):n.push(r),u=a.length!==t.length,t=t.substring(r.raw.length);else if(r=this.tokenizer.text(t))t=t.substring(r.raw.length),s=n[n.length-1],s&&s.type==="text"?(s.raw+=`
`+r.raw,s.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):n.push(r);else if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r,s,a,u,l,d,m=t;if(this.tokens.links){const p=Object.keys(this.tokens.links);if(p.length>0)for(;(u=this.tokenizer.rules.inline.reflinkSearch.exec(m))!=null;)p.includes(u[0].slice(u[0].lastIndexOf("[")+1,-1))&&(m=m.slice(0,u.index)+"["+"a".repeat(u[0].length-2)+"]"+m.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(u=this.tokenizer.rules.inline.blockSkip.exec(m))!=null;)m=m.slice(0,u.index)+"["+"a".repeat(u[0].length-2)+"]"+m.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(u=this.tokenizer.rules.inline.anyPunctuation.exec(m))!=null;)m=m.slice(0,u.index)+"++"+m.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(l||(d=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(p=>!!(r=p.call({lexer:this},t,n))&&(t=t.substring(r.raw.length),n.push(r),!0))))if(r=this.tokenizer.escape(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.tag(t))t=t.substring(r.raw.length),s=n[n.length-1],s&&r.type==="text"&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);else if(r=this.tokenizer.link(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(r.raw.length),s=n[n.length-1],s&&r.type==="text"&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);else if(r=this.tokenizer.emStrong(t,m,d))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.codespan(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.br(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.del(t))t=t.substring(r.raw.length),n.push(r);else if(r=this.tokenizer.autolink(t))t=t.substring(r.raw.length),n.push(r);else if(this.state.inLink||!(r=this.tokenizer.url(t))){if(a=t,this.options.extensions&&this.options.extensions.startInline){let p=1/0;const y=t.slice(1);let b;this.options.extensions.startInline.forEach(w=>{b=w.call({lexer:this},y),typeof b=="number"&&b>=0&&(p=Math.min(p,b))}),p<1/0&&p>=0&&(a=t.substring(0,p+1))}if(r=this.tokenizer.inlineText(a))t=t.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(d=r.raw.slice(-1)),l=!0,s=n[n.length-1],s&&s.type==="text"?(s.raw+=r.raw,s.text+=r.text):n.push(r);else if(t){const p="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(p);break}throw new Error(p)}}else t=t.substring(r.raw.length),n.push(r);return n}}class $a{constructor(t){_(this,"options");this.options=t||yi}code(t,n,r){var a;const s=(a=(n||"").match(/^\S*/))==null?void 0:a[0];return t=t.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+on(s)+'">'+(r?t:on(t,!0))+`</code></pre>
`:"<pre><code>"+(r?t:on(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,n){return t}heading(t,n,r){return`<h${n}>${t}</h${n}>
`}hr(){return`<hr>
`}list(t,n,r){const s=n?"ol":"ul";return"<"+s+(n&&r!==1?' start="'+r+'"':"")+`>
`+t+"</"+s+`>
`}listitem(t,n,r){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,n){const r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,n,r){const s=Ph(t);if(s===null)return r;let a='<a href="'+(t=s)+'"';return n&&(a+=' title="'+n+'"'),a+=">"+r+"</a>",a}image(t,n,r){const s=Ph(t);if(s===null)return r;let a=`<img src="${t=s}" alt="${r}"`;return n&&(a+=` title="${n}"`),a+=">",a}text(t){return t}}class Au{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,n,r){return""+r}image(t,n,r){return""+r}br(){return""}}class Xn{constructor(t){_(this,"options");_(this,"renderer");_(this,"textRenderer");this.options=t||yi,this.options.renderer=this.options.renderer||new $a,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Au}static parse(t,n){return new Xn(n).parse(t)}static parseInline(t,n){return new Xn(n).parseInline(t)}parse(t,n=!0){let r="";for(let s=0;s<t.length;s++){const a=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const u=a,l=this.options.extensions.renderers[u.type].call({parser:this},u);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(u.type)){r+=l||"";continue}}switch(a.type){case"space":continue;case"hr":r+=this.renderer.hr();continue;case"heading":{const u=a;r+=this.renderer.heading(this.parseInline(u.tokens),u.depth,Im(this.parseInline(u.tokens,this.textRenderer)));continue}case"code":{const u=a;r+=this.renderer.code(u.text,u.lang,!!u.escaped);continue}case"table":{const u=a;let l="",d="";for(let p=0;p<u.header.length;p++)d+=this.renderer.tablecell(this.parseInline(u.header[p].tokens),{header:!0,align:u.align[p]});l+=this.renderer.tablerow(d);let m="";for(let p=0;p<u.rows.length;p++){const y=u.rows[p];d="";for(let b=0;b<y.length;b++)d+=this.renderer.tablecell(this.parseInline(y[b].tokens),{header:!1,align:u.align[b]});m+=this.renderer.tablerow(d)}r+=this.renderer.table(l,m);continue}case"blockquote":{const u=a,l=this.parse(u.tokens);r+=this.renderer.blockquote(l);continue}case"list":{const u=a,l=u.ordered,d=u.start,m=u.loose;let p="";for(let y=0;y<u.items.length;y++){const b=u.items[y],w=b.checked,T=b.task;let j="";if(b.task){const q=this.renderer.checkbox(!!w);m?b.tokens.length>0&&b.tokens[0].type==="paragraph"?(b.tokens[0].text=q+" "+b.tokens[0].text,b.tokens[0].tokens&&b.tokens[0].tokens.length>0&&b.tokens[0].tokens[0].type==="text"&&(b.tokens[0].tokens[0].text=q+" "+b.tokens[0].tokens[0].text)):b.tokens.unshift({type:"text",text:q+" "}):j+=q+" "}j+=this.parse(b.tokens,m),p+=this.renderer.listitem(j,T,!!w)}r+=this.renderer.list(p,l,d);continue}case"html":{const u=a;r+=this.renderer.html(u.text,u.block);continue}case"paragraph":{const u=a;r+=this.renderer.paragraph(this.parseInline(u.tokens));continue}case"text":{let u=a,l=u.tokens?this.parseInline(u.tokens):u.text;for(;s+1<t.length&&t[s+1].type==="text";)u=t[++s],l+=`
`+(u.tokens?this.parseInline(u.tokens):u.text);r+=n?this.renderer.paragraph(l):l;continue}default:{const u='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}}return r}parseInline(t,n){n=n||this.renderer;let r="";for(let s=0;s<t.length;s++){const a=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const u=this.options.extensions.renderers[a.type].call({parser:this},a);if(u!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){r+=u||"";continue}}switch(a.type){case"escape":{const u=a;r+=n.text(u.text);break}case"html":{const u=a;r+=n.html(u.text);break}case"link":{const u=a;r+=n.link(u.href,u.title,this.parseInline(u.tokens,n));break}case"image":{const u=a;r+=n.image(u.href,u.title,u.text);break}case"strong":{const u=a;r+=n.strong(this.parseInline(u.tokens,n));break}case"em":{const u=a;r+=n.em(this.parseInline(u.tokens,n));break}case"codespan":{const u=a;r+=n.codespan(u.text);break}case"br":r+=n.br();break;case"del":{const u=a;r+=n.del(this.parseInline(u.tokens,n));break}case"text":{const u=a;r+=n.text(u.text);break}default:{const u='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}}return r}}class Us{constructor(t){_(this,"options");this.options=t||yi}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}_(Us,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var pi,su,Zd,$d;const ci=new($d=class{constructor(...o){Et(this,pi);_(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});_(this,"options",this.setOptions);_(this,"parse",Y(this,pi,su).call(this,Yn.lex,Xn.parse));_(this,"parseInline",Y(this,pi,su).call(this,Yn.lexInline,Xn.parseInline));_(this,"Parser",Xn);_(this,"Renderer",$a);_(this,"TextRenderer",Au);_(this,"Lexer",Yn);_(this,"Tokenizer",ba);_(this,"Hooks",Us);this.use(...o)}walkTokens(o,t){var r,s;let n=[];for(const a of o)switch(n=n.concat(t.call(this,a)),a.type){case"table":{const u=a;for(const l of u.header)n=n.concat(this.walkTokens(l.tokens,t));for(const l of u.rows)for(const d of l)n=n.concat(this.walkTokens(d.tokens,t));break}case"list":{const u=a;n=n.concat(this.walkTokens(u.items,t));break}default:{const u=a;(s=(r=this.defaults.extensions)==null?void 0:r.childTokens)!=null&&s[u.type]?this.defaults.extensions.childTokens[u.type].forEach(l=>{const d=u[l].flat(1/0);n=n.concat(this.walkTokens(d,t))}):u.tokens&&(n=n.concat(this.walkTokens(u.tokens,t)))}}return n}use(...o){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return o.forEach(n=>{const r={...n};if(r.async=this.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const a=t.renderers[s.name];t.renderers[s.name]=a?function(...u){let l=s.renderer.apply(this,u);return l===!1&&(l=a.apply(this,u)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=t[s.level];a?a.unshift(s.tokenizer):t[s.level]=[s.tokenizer],s.start&&(s.level==="block"?t.startBlock?t.startBlock.push(s.start):t.startBlock=[s.start]:s.level==="inline"&&(t.startInline?t.startInline.push(s.start):t.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(t.childTokens[s.name]=s.childTokens)}),r.extensions=t),n.renderer){const s=this.defaults.renderer||new $a(this.defaults);for(const a in n.renderer){if(!(a in s))throw new Error(`renderer '${a}' does not exist`);if(a==="options")continue;const u=a,l=n.renderer[u],d=s[u];s[u]=(...m)=>{let p=l.apply(s,m);return p===!1&&(p=d.apply(s,m)),p||""}}r.renderer=s}if(n.tokenizer){const s=this.defaults.tokenizer||new ba(this.defaults);for(const a in n.tokenizer){if(!(a in s))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const u=a,l=n.tokenizer[u],d=s[u];s[u]=(...m)=>{let p=l.apply(s,m);return p===!1&&(p=d.apply(s,m)),p}}r.tokenizer=s}if(n.hooks){const s=this.defaults.hooks||new Us;for(const a in n.hooks){if(!(a in s))throw new Error(`hook '${a}' does not exist`);if(a==="options")continue;const u=a,l=n.hooks[u],d=s[u];Us.passThroughHooks.has(a)?s[u]=m=>{if(this.defaults.async)return Promise.resolve(l.call(s,m)).then(y=>d.call(s,y));const p=l.call(s,m);return d.call(s,p)}:s[u]=(...m)=>{let p=l.apply(s,m);return p===!1&&(p=d.apply(s,m)),p}}r.hooks=s}if(n.walkTokens){const s=this.defaults.walkTokens,a=n.walkTokens;r.walkTokens=function(u){let l=[];return l.push(a.call(this,u)),s&&(l=l.concat(s.call(this,u))),l}}this.defaults={...this.defaults,...r}}),this}setOptions(o){return this.defaults={...this.defaults,...o},this}lexer(o,t){return Yn.lex(o,t??this.defaults)}parser(o,t){return Xn.parse(o,t??this.defaults)}},pi=new WeakSet,su=function(o,t){return(n,r)=>{const s={...r},a={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(a.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),a.async=!0);const u=Y(this,pi,Zd).call(this,!!a.silent,!!a.async);if(n==null)return u(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return u(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(a.hooks&&(a.hooks.options=a),a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(n):n).then(l=>o(l,a)).then(l=>a.hooks?a.hooks.processAllTokens(l):l).then(l=>a.walkTokens?Promise.all(this.walkTokens(l,a.walkTokens)).then(()=>l):l).then(l=>t(l,a)).then(l=>a.hooks?a.hooks.postprocess(l):l).catch(u);try{a.hooks&&(n=a.hooks.preprocess(n));let l=o(n,a);a.hooks&&(l=a.hooks.processAllTokens(l)),a.walkTokens&&this.walkTokens(l,a.walkTokens);let d=t(l,a);return a.hooks&&(d=a.hooks.postprocess(d)),d}catch(l){return u(l)}}},Zd=function(o,t){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,o){const r="<p>An error occurred:</p><pre>"+on(n.message+"",!0)+"</pre>";return t?Promise.resolve(r):r}if(t)return Promise.reject(n);throw n}},$d);function zt(o,t){return ci.parse(o,t)}zt.options=zt.setOptions=function(o){return ci.setOptions(o),zt.defaults=ci.defaults,qh(zt.defaults),zt},zt.getDefaults=$m,zt.defaults=yi,zt.use=function(...o){return ci.use(...o),zt.defaults=ci.defaults,qh(zt.defaults),zt},zt.walkTokens=function(o,t){return ci.walkTokens(o,t)},zt.parseInline=ci.parseInline,zt.Parser=Xn,zt.parser=Xn.parse,zt.Renderer=$a,zt.TextRenderer=Au,zt.Lexer=Yn,zt.lexer=Yn.lex,zt.Tokenizer=ba,zt.Hooks=Us,zt.parse=zt,zt.options,zt.setOptions,zt.use,zt.walkTokens,zt.parseInline,Xn.parse,Yn.lex;const Qh=o=>bn(o)&&!!o.request_message;function Gm(o,t){const n=o.customPersonalityPrompts;if(n)switch(t){case Ae.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case Ae.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case Ae.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case Ae.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return Bm[t]}const Bm={[Ae.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[Ae.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[Ae.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[Ae.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};function Vm(o=[]){const t=function(n=[]){let r;for(const s of n){if(s.type===Ne.TOOL_USE)return s;s.type===Ne.TOOL_USE_START&&(r=s)}return r}(o);return t&&t.type===Ne.TOOL_USE?o.filter(n=>n.type!==Ne.TOOL_USE_START):o}class Xt{constructor(t,n,r,s){_(this,"_state");_(this,"_subscribers",new Set);_(this,"_focusModel",new Ld);_(this,"_onSendExchangeListeners",[]);_(this,"_onNewConversationListeners",[]);_(this,"_onHistoryDeleteListeners",[]);_(this,"_onBeforeChangeConversationListeners",[]);_(this,"_totalCharactersCacheThrottleMs",1e3);_(this,"_totalCharactersStore");_(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));_(this,"setConversation",(t,n=!0,r=!0)=>{const s=t.id!==this._state.id;s&&r&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([u,l])=>{if(l.requestId&&l.toolUseId){const{requestId:d,toolUseId:m}=jh(u);return d===l.requestId&&m===l.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",u,"but object has ",Zc(l)),[u,l]}return[u,{...l,...jh(u)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),n&&s&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const a=Xt.isEmpty(t);if(s&&a){const u=this._state.draftExchange;u&&(t.draftExchange=u)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(bn)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(u=>u(this)),this._saveConversation(this._state),s&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(u=>u())),!0});_(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});_(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});_(this,"setName",t=>{this.update({name:t})});_(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});_(this,"updateFeedback",(t,n)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:n}})});_(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Zc(t)]:t}})});_(this,"getToolUseState",(t,n)=>t===void 0||n===void 0||this.toolUseStates===void 0?{phase:Ns.unknown,requestId:t??"",toolUseId:n??""}:this.toolUseStates[Zc({requestId:t,toolUseId:n})]||{phase:Ns.new});_(this,"getLastToolUseState",()=>{var r;const t=this.lastExchange;if(!t)return{phase:Ns.unknown};const n=function(s=[]){let a;for(const u of s){if(u.type===Ne.TOOL_USE)return u;u.type===Ne.TOOL_USE_START&&(a=u)}return a}(t==null?void 0:t.structured_output_nodes);return n?this.getToolUseState(t.request_id,(r=n.tool_use)==null?void 0:r.tool_use_id):{phase:Ns.unknown}});_(this,"addExchange",t=>{const n=[...this._state.chatHistory,t];let r;bn(t)&&(r=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Ud.unset,feedbackNote:""}}:void 0),this.update({chatHistory:n,...r?{feedbackStates:r}:{},lastUrl:void 0})});_(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});_(this,"updateExchangeById",(t,n,r=!1)=>{var l;const s=this.exchangeWithRequestId(n);if(s===null)return console.warn("No exchange with this request ID found."),!1;r&&t.response_text!==void 0&&(t.response_text=(s.response_text??"")+(t.response_text??"")),r&&(t.structured_output_nodes=Vm([...s.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==s.stop_reason&&s.stop_reason&&t.stop_reason===Hg.REASON_UNSPECIFIED&&(t.stop_reason=s.stop_reason),r&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...s.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const a=(l=(t.structured_output_nodes||[]).find(d=>d.type===Ne.MAIN_TEXT_FINISHED))==null?void 0:l.content;a&&a!==t.response_text&&(t.response_text=a);let u=this._state.isShareable||Bi({...s,...t});return this.update({chatHistory:this.chatHistory.map(d=>d.request_id===n?{...d,...t}:d),isShareable:u}),!0});_(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(n=>!n.request_id||!t.has(n.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});_(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});_(this,"clearHistoryFrom",async(t,n=!0)=>{const r=this.historyFrom(t,n),s=r.map(a=>a.request_id).filter(a=>a!==void 0);this.update({chatHistory:this.historyTo(t,!n)}),this._extensionClient.clearMetadataFor({requestIds:s}),r.forEach(a=>{this._onHistoryDeleteListeners.forEach(u=>u(a))})});_(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(n=>n.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});_(this,"historyTo",(t,n=!1)=>{const r=this.chatHistory.findIndex(s=>s.request_id===t);return r===-1?[]:this.chatHistory.slice(0,n?r+1:r)});_(this,"historyFrom",(t,n=!0)=>{const r=this.chatHistory.findIndex(s=>s.request_id===t);return r===-1?[]:this.chatHistory.slice(n?r:r+1)});_(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});_(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:Ve.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id})));_(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(n=>n!==t&&(!t.request_id||n.request_id!==t.request_id))})});_(this,"exchangeWithRequestId",t=>this.chatHistory.find(n=>n.request_id===t)||null);_(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});_(this,"historySummaryVersion",1);_(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(r=>r.request_id===t.request_id))return;const n={seen_state:Ui.seen};this.update({chatHistory:this.chatHistory.map(r=>r.request_id===t.request_id?{...r,...n}:r)})});_(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));_(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const n=t.filter(r=>!r.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:n}})});_(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(n=>n.id);this.update({draftActiveContextIds:t})});_(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),n=this._specialContextInputModel.recentItems.filter(s=>t.has(s.id)||s.recentFile||s.selection||s.sourceFolder),r=this._specialContextInputModel.recentItems.filter(s=>!(t.has(s.id)||s.recentFile||s.selection||s.sourceFolder));this._specialContextInputModel.markItemsActive(n.reverse()),this._specialContextInputModel.markItemsInactive(r.reverse())});_(this,"saveDraftExchange",(t,n)=>{var u,l,d;const r=t!==((u=this.draftExchange)==null?void 0:u.request_message),s=n!==((l=this.draftExchange)==null?void 0:l.rich_text_json_repr);if(!r&&!s)return;const a=(d=this.draftExchange)==null?void 0:d.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:n,mentioned_items:a,status:Ve.draft}})});_(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});_(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const n=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:n,model_id:this.selectedModelId??void 0}).then(()=>{var u,l;const r=!this.name&&this.chatHistory.length===1&&((u=this.firstExchange)==null?void 0:u.request_id)===this.chatHistory[0].request_id,s=Vi(this)&&((l=this._state.extraData)==null?void 0:l.hasAgentOnboarded)&&(a=this.chatHistory,a.filter(d=>Qh(d))).length===2;var a;this._chatFlagModel.summaryTitles&&(r||s)&&this.updateConversationTitle()}).finally(()=>{var r;Vi(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Ug.sentUserMessage,conversationId:this.id,requestId:((r=this.lastExchange)==null?void 0:r.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});_(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:Ve.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});_(this,"sendInstructionExchange",async(t,n)=>{let r=`temp-fe-${crypto.randomUUID()}`;const s={status:Ve.sent,request_id:r,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:Ui.unseen,timestamp:new Date().toISOString()};this.addExchange(s);for await(const a of this._extensionClient.sendInstructionMessage(s,n)){if(!this.updateExchangeById(a,r,!0))return;r=a.request_id||r}});_(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});_(this,"sendSummaryExchange",()=>{const t={status:Ve.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Pi.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});_(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const n={status:Ve.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:Ui.unseen,chatItemType:Pi.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(n);for await(const r of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(r,t,!0))return;t=r.request_id||t}});_(this,"sendExchange",async(t,n=!1)=>{var u;this.updateLastInteraction();let r=`temp-fe-${crypto.randomUUID()}`,s=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&Xt.isNew(this._state)){const l=crypto.randomUUID(),d=this._state.id;try{await this._extensionClient.migrateConversationId(d,l)}catch(m){console.error("Failed to migrate conversation checkpoints:",m)}this._state={...this._state,id:l},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(l),this._subscribers.forEach(m=>m(this))}t=Yh(t);let a={status:Ve.sent,request_id:r,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:s,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:Ui.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(a),this._loadContextFromExchange(a),this._onSendExchangeListeners.forEach(l=>l(a)),this._chatFlagModel.useHistorySummary&&(this._clearStaleHistorySummaryNodes(),await this.maybeAddHistorySummaryNode()),a=await this._addIdeStateNode(a),this.updateExchangeById({structured_request_nodes:a.structured_request_nodes},r,!1);for await(const l of this.sendUserMessage(r,a,n)){if(((u=this.exchangeWithRequestId(r))==null?void 0:u.status)!==Ve.sent||!this.updateExchangeById(l,r,!0))return;r=l.request_id||r}});_(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:Ve.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Jc.chatUseSuggestedQuestion)});_(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});_(this,"recoverExchange",async t=>{var s;if(!t.request_id||t.status!==Ve.sent)return;let n=t.request_id;const r=(s=t.structured_output_nodes)==null?void 0:s.filter(a=>a.type===Ne.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:r??[]},n);for await(const a of this.getChatStream(t)){if(!this.updateExchangeById(a,n,!0))return;n=a.request_id||n}});_(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(n=>{bn(n)&&this._loadContextFromExchange(n)})});_(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});_(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(n=>{bn(n)&&this._unloadContextFromExchange(n)})});_(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});_(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});_(this,"_jsonToStructuredRequest",t=>{const n=[],r=a=>{var l;const u=n.at(-1);if((u==null?void 0:u.type)===Nn.TEXT){const d=((l=u.text_node)==null?void 0:l.content)??"",m={...u,text_node:{content:d+a}};n[n.length-1]=m}else n.push({id:n.length,type:Nn.TEXT,text_node:{content:a}})},s=a=>{var u,l,d,m;if(a.type==="doc"||a.type==="paragraph")for(const p of a.content??[])s(p);else if(a.type==="hardBreak")r(`
`);else if(a.type==="text")r(a.text??"");else if(a.type==="image"){if(typeof((u=a.attrs)==null?void 0:u.src)!="string")return void console.error("Image source is not a string: ",(l=a.attrs)==null?void 0:l.src);if(a.attrs.isLoading)return;const p=(d=a.attrs)==null?void 0:d.title,y=this._fileNameToImageFormat(p);n.push({id:n.length,type:Nn.IMAGE_ID,image_id_node:{image_id:a.attrs.src,format:y}})}else if(a.type==="mention"){const p=(m=a.attrs)==null?void 0:m.data;p&&Ca(p)?n.push({id:n.length,type:Nn.TEXT,text_node:{content:Gm(this._chatFlagModel,p.personality.type)}}):r(`@\`${(p==null?void 0:p.name)??(p==null?void 0:p.id)}\``)}};return s(t),n});this._extensionClient=t,this._chatFlagModel=n,this._specialContextInputModel=r,this._saveConversation=s,this._state={...Xt.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return xm(()=>{let t=0;const n=this._state.chatHistory;return this._convertHistoryToExchanges(n).forEach(r=>{t+=JSON.stringify(r).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((r,s)=>r+s,0))||0)<=4?Ae.PROTOTYPER:Ae.DEFAULT}catch(n){return console.error("Error determining persona type:",n),Ae.DEFAULT}}static create(t={}){const n=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:n,lastInteractedAtIso:n,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Ae.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){var s;if(t.name)return t.name;const n=t.chatHistory.find(bn);return n&&n.request_message?Xt.toSentenceCase(n.request_message):(r=t,((s=r.extraData)==null?void 0:s.isAutofix)===!0?"Autofix Chat":Vi(t)?"New Agent":"New Chat");var r}static isNew(t){return t.id===tn}static isEmpty(t){var n;return!(t.chatHistory.some(bn)||(n=t.draftExchange)!=null&&n.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static isThreadEmpty(t){return!!t.isNew||!t.conversation||Xt.isEmpty(t.conversation)}static getTime(t,n){return n==="lastMessageTimestamp"?Xt.lastMessageTimestamp(t):n==="lastInteractedAt"?Xt.lastInteractedAt(t):Xt.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var r;const n=(r=t.chatHistory.findLast(bn))==null?void 0:r.timestamp;return n?new Date(n):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!Xt.isEmpty(t)||Xt.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(n=>n!==t)}}_notifyBeforeChangeConversation(t,n){let r=n;for(const s of this._onBeforeChangeConversationListeners){const a=s(t,r);a!==void 0&&(r=a)}return r}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return Xt.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Ae.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return Xt.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return Xt.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var r;const t=(((r=this.draftExchange)==null?void 0:r.request_message)??"").trim()!=="",n=this.hasImagesInDraft();return t||n}hasImagesInDraft(){var r;const t=(r=this.draftExchange)==null?void 0:r.rich_text_json_repr;if(!t)return!1;const n=s=>Array.isArray(s)?s.some(n):!!s&&(s.type==="image"||!(!s.content||!Array.isArray(s.content))&&s.content.some(n));return n(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(bn)??null}get lastExchange(){return this.chatHistory.findLast(bn)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>bn(t)&&t.status===Ve.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Bi(t)||js(t)||Rs(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const n=(t=t.filter(s=>!Rs(s)||s.summaryVersion===this.historySummaryVersion)).findLastIndex(s=>Rs(s));this._chatFlagModel.useHistorySummary&&n>0&&(console.info("Using history summary node found at index %d",n),t=t.slice(n));const r=[];for(const s of t)if(Bi(s))r.push(Kh(s));else if(js(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0){if(s.revertTarget){const a=Zm(s,1),u={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[a],response_nodes:[]};r.push(u)}}else this._chatFlagModel.useHistorySummary&&Rs(s)&&r.push(Kh(s));return r}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===Ve.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const n=crypto.randomUUID();let r,s="";const a=await this._addIdeStateNode(Yh({...t,request_id:n,status:Ve.sent,timestamp:new Date().toISOString()}));for await(const u of this.sendUserMessage(n,a,!0))u.response_text&&(s+=u.response_text),u.request_id&&(r=u.request_id);return{responseText:s,requestId:r}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,n,r){return[]}async*sendUserMessage(t,n,r){var p;const s=this._specialContextInputModel.chatActiveContext;let a;if(n.chatHistory!==void 0)a=n.chatHistory;else{let y=this.successfulMessages;if(n.chatItemType===Pi.summaryTitle){const b=y.findIndex(w=>w.chatItemType!==Pi.agentOnboarding&&Qh(w));b!==-1&&(y=y.slice(b))}a=this._convertHistoryToExchanges(y)}let u=this.personaType;if(n.structured_request_nodes){const y=n.structured_request_nodes.find(b=>b.type===Nn.CHANGE_PERSONALITY);y&&y.change_personality_node&&(u=y.change_personality_node.personality_type)}const l={text:n.request_message,chatHistory:a,silent:r,modelId:n.model_id,context:s,userSpecifiedFiles:s.userSpecifiedFiles,externalSourceIds:(p=s.externalSources)==null?void 0:p.map(y=>y.id),disableRetrieval:n.disableRetrieval??!1,disableSelectedCodeDetails:n.disableSelectedCodeDetails??!1,nodes:n.structured_request_nodes,memoriesInfo:n.memoriesInfo,personaType:u,conversationId:this.id,createdTimestamp:Date.now()},d=this._createStreamStateHandlers(t,l,{flags:this._chatFlagModel}),m=this._extensionClient.startChatStreamWithRetry(t,l,{flags:this._chatFlagModel});for await(const y of m){let b=y;for(const w of d)b=w.handleChunk(b)??b;yield b}for(const y of d)yield*y.handleComplete()}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(n=>n!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(n=>n!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(n=>n!==t)}}updateChatItem(t,n){return this.chatHistory.find(r=>r.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(r=>r.request_id===t?{...r,...n}:r)}),!0)}_fileNameToImageFormat(t){var r;switch((r=t.split(".").at(-1))==null?void 0:r.toLowerCase()){case"jpeg":case"jpg":return Os.JPEG;case"png":return Os.PNG;case"gif":return Os.GIF;case"webp":return Os.WEBP;default:return Os.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let n,r=(t.structured_request_nodes??[]).filter(s=>s.type!==Nn.IDE_STATE);try{n=await this._extensionClient.getChatRequestIdeState()}catch(s){console.error("Failed to add IDE state to exchange:",s)}return n?(r=[...r,{id:Qd(r)+1,type:Nn.IDE_STATE,ide_state_node:n}],{...t,structured_request_nodes:r}):t}async maybeAddHistorySummaryNode(){var b;const t=this._chatFlagModel.historySummaryPrompt;if(!t||t.trim()==="")return!1;const n=this._convertHistoryToExchanges(this.chatHistory),[r,s]=Dg(n,this._chatFlagModel.historySummaryLowerChars,this._chatFlagModel.historySummaryMaxChars);if(r.length===0)return!1;let a=((b=r.at(-1))==null?void 0:b.response_nodes)??[],u=a.filter(w=>w.type===Ne.TOOL_USE);u.length>0&&(r.at(-1).response_nodes=a.filter(w=>w.type!==Ne.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",r.length);const{responseText:l,requestId:d}=await this.sendSilentExchange({request_message:t,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:r}),m={chatItemType:Pi.historySummary,summaryVersion:this.historySummaryVersion,request_id:d,request_message:t,response_text:l,structured_output_nodes:[{id:u.map(w=>w.id).reduce((w,T)=>Math.max(w,T),-1)+1,type:Ne.RAW_RESPONSE,content:l},...u],status:Ve.success,seen_state:Ui.seen,timestamp:new Date().toISOString()},p=this.chatHistory.findIndex(w=>w.request_id===r.at(-1).request_id)+1;console.info("Adding a history summary node at index %d",p);const y=[...this._state.chatHistory];return y.splice(p,0,m),this.update({chatHistory:y}),!0}_clearStaleHistorySummaryNodes(){this.update({chatHistory:this.chatHistory.filter(t=>!Rs(t)||t.summaryVersion===this.historySummaryVersion)})}}function Zm(o,t){const n=(js(o),o.fromTimestamp),r=(js(o),o.toTimestamp),s=js(o)&&o.revertTarget!==void 0;return{id:t,type:Nn.CHECKPOINT_REF,checkpoint_ref_node:{request_id:o.request_id||"",from_timestamp:n,to_timestamp:r,source:s?Wg.CHECKPOINT_REVERT:void 0}}}function Kh(o){const t=(o.structured_output_nodes??[]).filter(n=>n.type===Ne.RAW_RESPONSE||n.type===Ne.TOOL_USE||n.type===Ne.TOOL_USE_START).map(n=>n.type===Ne.TOOL_USE_START?{...n,tool_use:{...n.tool_use,input_json:"{}"},type:Ne.TOOL_USE}:n);return{request_message:o.request_message,response_text:o.response_text??"",request_id:o.request_id||"",request_nodes:o.structured_request_nodes??[],response_nodes:t}}function Qd(o){return o.length>0?Math.max(...o.map(t=>t.id)):0}function Yh(o){var t;if(o.request_message.length>0&&!((t=o.structured_request_nodes)!=null&&t.some(n=>n.type===Nn.TEXT))){let n=o.structured_request_nodes??[];return n=[...n,{id:Qd(n)+1,type:Nn.TEXT,text_node:{content:o.request_message}}],{...o,structured_request_nodes:n}}return o}class Qm{constructor(t=!0,n=setTimeout){_(this,"_notify",new Set);_(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});_(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const n=this._setTimeout;t.timeoutId=n(this._handle,t.timeout,t)});_(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});_(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=n}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,n){return this._register(t,n,!0)}interval(t,n){return this._register(t,n,!1)}at(t,n){return this._register(0,n,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,n,r,s){if(!t&&!s)return()=>{};const a={timeout:t,notify:n,once:r,date:s};return this._notify.add(a),this._schedule(a),()=>{this._clearTimeout(a),this._notify.delete(a)}}}class Km{constructor(t=0,n=0,r=new Qm,s=ce("busy"),a=ce(!1)){_(this,"unsubNotify");_(this,"unsubMessage");_(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});_(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=n,this.idleScheduler=r,this.idleStatus=s,this.focusAfterIdle=a,this.idleNotifyTimeout=t,this.idleMessageTimeout=n}set idleMessageTimeout(t){var n;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(n=this.unsubMessage)==null||n.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var n;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(n=this.unsubNotify)==null||n.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,n;(t=this.unsubNotify)==null||t.call(this),(n=this.unsubMessage)==null||n.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var ua=(o=>(o.send="send",o.addTask="addTask",o))(ua||{});class Ym{constructor(){_(this,"_mode",ce(ua.send));_(this,"_currentMode",ua.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(ua).includes(t)&&this._mode.set(t)}}const sa=ce("idle");class Tu{constructor(t,n,r,s={}){_(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});_(this,"extensionClient");_(this,"_chatFlagsModel");_(this,"_currConversationModel");_(this,"_chatModeModel");_(this,"_sendModeModel");_(this,"_currentChatMode");_(this,"_flagsLoaded",ce(!1));_(this,"subscribers",new Set);_(this,"idleMessageModel",new Km);_(this,"isPanelCollapsed");_(this,"agentExecutionMode");_(this,"sortConversationsBy");_(this,"displayedAnnouncements");_(this,"onLoaded",async()=>{var r,s;const t=await this.extensionClient.getChatInitData(),n=!this._chatFlagsModel.doUseNewDraftFunctionality&&(t.enableBackgroundAgents||t.enableNewThreadsList);this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??Ag,bigSyncThreshold:t.bigSyncThreshold??Tg,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Fg,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??Cg.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:(t.enableBackgroundAgents??!1)||(t.enableNewThreadsList??!1),enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryMaxChars:t.historySummaryMaxChars??0,historySummaryLowerChars:t.historySummaryLowerChars??0,historySummaryPrompt:t.historySummaryPrompt??""}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._currentChatMode=t.currentChatMode,n&&this.onDoUseNewDraftFunctionalityChanged(),this._flagsLoaded.set(!0),(s=(r=this.options).onLoaded)==null||s.call(r),this.notifySubscribers()});_(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));_(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==Hc&&this.currentConversationId!==Hc||(delete this._state.conversations[Hc],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(n=>{this.idleMessageModel.idleNotifyTimeout=n.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=n.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([n,r])=>n===tn||Xt.isValid(r))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});_(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[n,r]of Object.entries(t)){if(r.isShareable)continue;const s=r.chatHistory.some(a=>Bi(a));t[n]={...r,isShareable:s}}this._state.conversations=t});_(this,"updateChatState",t=>{this._state={...this._state,...t};const n=this._state.conversations,r=new Set;for(const[s,a]of Object.entries(n))a.isPinned&&r.add(s);this.setState(this._state),this.notifySubscribers()});_(this,"saveImmediate",()=>{this._host.setState(this._state)});_(this,"setState",nu(t=>{this._host.setState({...t,isPanelCollapsed:ae(this.isPanelCollapsed),agentExecutionMode:ae(this.agentExecutionMode),sortConversationsBy:ae(this.sortConversationsBy),displayedAnnouncements:ae(this.displayedAnnouncements),sendMode:this._sendModeModel.getCurrentMode()})},1e3,{maxWait:15e3}));_(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});_(this,"withWebviewClientEvent",(t,n)=>(...r)=>(this.extensionClient.reportWebviewClientEvent(t),n(...r)));_(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[tn];if(this.currentConversationId&&this.currentConversationId!==tn&&this._state.conversations[this.currentConversationId]&&Xt.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const n={...this._state.conversations[this.currentConversationId],id:tn};this._state.conversations[tn]=n,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=tn,this._currConversationModel.setConversation(n)}});_(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Pi.educateFeatures,request_id:crypto.randomUUID(),seen_state:Ui.seen})});_(this,"popCurrentConversation",async()=>{var n,r;const t=this.currentConversationId;t&&await this.deleteConversation(t,((n=this.nextConversation)==null?void 0:n.id)??((r=this.previousConversation)==null?void 0:r.id))});_(this,"setCurrentConversation",async(t,n=!0,r)=>{if(t===this.currentConversationId&&(r!=null&&r.noopIfSameConversation))return;let s;this.flags.doUseNewDraftFunctionality?(t===void 0&&(t=tn),s=this._state.conversations[t]??Xt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:r==null?void 0:r.newTaskUuid}),t===tn&&(s.id=tn),r!=null&&r.newTaskUuid&&(s.rootTaskUuid=r.newTaskUuid)):t===void 0?(this.deleteInvalidConversations(Vi(this._currConversationModel)?"agent":"chat"),s=Xt.create({personaType:await this._currConversationModel.decidePersonaType()})):s=this._state.conversations[t]??Xt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:r==null?void 0:r.newTaskUuid});const a=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(s,!a,n),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});_(this,"saveConversation",(t,n)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),n&&delete this._state.conversations[tn]});_(this,"isConversationShareable",t=>{var n;return((n=this._state.conversations[t])==null?void 0:n.isShareable)??!0});_(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});_(this,"getConversationUrl",async t=>{const n=this._state.conversations[t];if(n.lastUrl)return n.lastUrl;sa.set("copying");const r=n==null?void 0:n.chatHistory,s=r.reduce((l,d)=>(Bi(d)&&l.push({request_id:d.request_id||"",request_message:d.request_message,response_text:d.response_text||""}),l),[]);if(s.length===0)throw new Error("No chat history to share");const a=Xt.getDisplayName(n),u=await this.extensionClient.saveChat(t,s,a);if(u.data){let l=u.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...n,lastUrl:l}}}),l}throw new Error("Failed to create URL")});_(this,"shareConversation",async t=>{if(t!==void 0)try{const n=await this.getConversationUrl(t);if(!n)return void sa.set("idle");navigator.clipboard.writeText(n),sa.set("copied")}catch{sa.set("failed")}});_(this,"deleteConversations",async(t,n=void 0,r=[],s)=>{const a=t.length+r.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${a>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const u=new Set(t);this.deleteConversationIds(u)}if(r.length>0&&s)for(const u of r)try{await s.deleteAgent(u,!0)}catch(l){console.error(`Failed to delete remote agent ${u}:`,l)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(n)}});_(this,"deleteConversation",async(t,n=void 0)=>{await this.deleteConversations([t],n)});_(this,"deleteConversationIds",async t=>{var r;const n=[];for(const s of t){const a=((r=this._state.conversations[s])==null?void 0:r.requestIds)??[];n.push(...a)}for(const s of Object.values(this._state.conversations))if(t.has(s.id)){for(const u of s.chatHistory)bn(u)&&this.deleteImagesInExchange(u);const a=s.draftExchange;a&&this.deleteImagesInExchange(a)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([s])=>!t.has(s)))}),this.extensionClient.clearMetadataFor({requestIds:n,conversationIds:Array.from(t)})});_(this,"deleteImagesInExchange",t=>{const n=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const r of n)this.deleteImage(r)});_(this,"findImagesInJson",t=>{const n=[],r=s=>{var a;if(s.type==="image"&&((a=s.attrs)!=null&&a.src))n.push(s.attrs.src);else if((s.type==="doc"||s.type==="paragraph")&&s.content)for(const u of s.content)r(u)};return r(t),n});_(this,"findImagesInStructuredRequest",t=>t.reduce((n,r)=>(r.type===Nn.IMAGE_ID&&r.image_id_node&&n.push(r.image_id_node.image_id),n),[]));_(this,"toggleConversationPinned",t=>{const n=this._state.conversations[t],r={...n,isPinned:!n.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:r}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});_(this,"renameConversation",(t,n)=>{const r={...this._state.conversations[t],name:n};this.updateChatState({conversations:{...this._state.conversations,[t]:r}}),t===this.currentConversationId&&this._currConversationModel.setName(n)});_(this,"smartPaste",(t,n,r,s)=>{const a=this._currConversationModel.historyTo(t,!0).filter(u=>Bi(u)).map(u=>({request_message:u.request_message,response_text:u.response_text||"",request_id:u.request_id||""}));this.extensionClient.smartPaste({generatedCode:n,chatHistory:a,targetFile:r??void 0,options:s})});_(this,"saveImage",async t=>await this.extensionClient.saveImage(t));_(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));_(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=n,this._specialContextInputModel=r,this.options=s,this._chatFlagsModel=new Mg(s.initialFlags),this.extensionClient=new Eg(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new Xt(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new Ym,this.initialize(s.initialConversation);const a=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=ce(a),this.agentExecutionMode=ce(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=ce(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=ce(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get currentChatMode(){return this._currentChatMode}setCurrentChatMode(t){this._currentChatMode=t,this.extensionClient.setLastUsedChatMode(t)}get flagsLoaded(){return this._flagsLoaded}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}orderedConversations(t,n="desc",r){const s=t||this._state.sortConversationsBy||"lastMessageTimestamp";let a=Object.values(this._state.conversations);return r&&(a=a.filter(r)),a.sort((u,l)=>{const d=Xt.getTime(u,s).getTime(),m=Xt.getTime(l,s).getTime();return n==="asc"?d-m:m-d})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),n=t.findIndex(r=>r.id===this.currentConversationId);return t.length>n+1?t[n+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),n=t.findIndex(r=>r.id===this.currentConversationId);return n>0?t[n-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const n=Object.keys(this.conversations).filter(r=>{if(r===tn)return!1;const s=!Xt.isValid(this.conversations[r]),a=Vi(this.conversations[r]);return s&&(t==="agent"&&a||t==="chat"&&!a||t==="all")});n.length&&this.deleteConversationIds(new Set(n))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const n=t.data;if(n.type===oe.newThread){if("data"in n&&n.data){const r=n.data.mode;(async()=>(await this.setCurrentConversation(),r&&this._chatModeModel?r.toLowerCase()==="agent"?await this._chatModeModel.setToAgent("manual"):r.toLowerCase()==="chat"?this._chatModeModel.setToChat():console.warn("Unknown chat mode:",r):r&&console.warn("ChatModeModel not available, cannot set mode:",r)))()}else this.setCurrentConversation();return!0}return!1}}_(Tu,"NEW_AGENT_KEY",tn);function Xh(o,t){let n,r,s=t;const a=()=>s.editor.getModifiedEditor(),u=()=>{const{afterLineNumber:l}=s,d=a();if(l===void 0)return void d.changeViewZones(p=>{n&&d&&r&&p.removeZone(r)});const m={...s,afterLineNumber:l,domNode:o,suppressMouseDown:!0};d==null||d.changeViewZones(p=>{n&&r&&p.removeZone(r),r=p.addZone(m),n=m})};return u(),{update:l=>{s=l,u()},destroy:()=>{const l=a();l.changeViewZones(d=>{if(n&&l&&r)try{d.removeZone(r)}catch(m){if(m instanceof Error){if(m.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${m}`)}})}}}var Gr=(o=>(o.edit="edit",o.instruction="instruction",o))(Gr||{}),ou=(o=>(o[o.instructionDrawer=0]="instructionDrawer",o[o.chunkActionPanel=1]="chunkActionPanel",o))(ou||{});const qi=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Jh=new Set,au=typeof process=="object"&&process?process:{},Kd=(o,t,n,r)=>{typeof au.emitWarning=="function"?au.emitWarning(o,t,n,r):console.error(`[${n}] ${t}: ${o}`)};let wa=globalThis.AbortController,td=globalThis.AbortSignal;var wd;if(wa===void 0){td=class{constructor(){_(this,"onabort");_(this,"_onabort",[]);_(this,"reason");_(this,"aborted",!1)}addEventListener(n,r){this._onabort.push(r)}},wa=class{constructor(){_(this,"signal",new td);t()}abort(n){var r,s;if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(const a of this.signal._onabort)a(n);(s=(r=this.signal).onabort)==null||s.call(r,n)}}};let o=((wd=au.env)==null?void 0:wd.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{o&&(o=!1,Kd("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const Nr=o=>o&&o===Math.floor(o)&&o>0&&isFinite(o),Yd=o=>Nr(o)?o<=Math.pow(2,8)?Uint8Array:o<=Math.pow(2,16)?Uint16Array:o<=Math.pow(2,32)?Uint32Array:o<=Number.MAX_SAFE_INTEGER?la:null:null;class la extends Array{constructor(t){super(t),this.fill(0)}}var Zi;const li=class li{constructor(t,n){_(this,"heap");_(this,"length");if(!x(li,Zi))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(t),this.length=0}static create(t){const n=Yd(t);if(!n)return[];ut(li,Zi,!0);const r=new li(t,n);return ut(li,Zi,!1),r}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};Zi=new WeakMap,Et(li,Zi,!1);let cu=li;var kd,Cd,Mn,en,En,An,Qi,Ki,ve,Tn,le,Yt,bt,qe,nn,Le,ke,Fn,Ce,Rn,On,rn,Ln,Pr,Ue,H,lu,hi,gr,Ws,sn,Xd,di,Yi,Hs,Dr,zr,hu,ha,da,Kt,du,Ds,jr,fu;const Ou=class Ou{constructor(t){Et(this,H);Et(this,Mn);Et(this,en);Et(this,En);Et(this,An);Et(this,Qi);Et(this,Ki);_(this,"ttl");_(this,"ttlResolution");_(this,"ttlAutopurge");_(this,"updateAgeOnGet");_(this,"updateAgeOnHas");_(this,"allowStale");_(this,"noDisposeOnSet");_(this,"noUpdateTTL");_(this,"maxEntrySize");_(this,"sizeCalculation");_(this,"noDeleteOnFetchRejection");_(this,"noDeleteOnStaleGet");_(this,"allowStaleOnFetchAbort");_(this,"allowStaleOnFetchRejection");_(this,"ignoreFetchAbort");Et(this,ve);Et(this,Tn);Et(this,le);Et(this,Yt);Et(this,bt);Et(this,qe);Et(this,nn);Et(this,Le);Et(this,ke);Et(this,Fn);Et(this,Ce);Et(this,Rn);Et(this,On);Et(this,rn);Et(this,Ln);Et(this,Pr);Et(this,Ue);Et(this,hi,()=>{});Et(this,gr,()=>{});Et(this,Ws,()=>{});Et(this,sn,()=>!1);Et(this,di,t=>{});Et(this,Yi,(t,n,r)=>{});Et(this,Hs,(t,n,r,s)=>{if(r||s)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});_(this,kd,"LRUCache");const{max:n=0,ttl:r,ttlResolution:s=1,ttlAutopurge:a,updateAgeOnGet:u,updateAgeOnHas:l,allowStale:d,dispose:m,disposeAfter:p,noDisposeOnSet:y,noUpdateTTL:b,maxSize:w=0,maxEntrySize:T=0,sizeCalculation:j,fetchMethod:q,memoMethod:S,noDeleteOnFetchRejection:E,noDeleteOnStaleGet:G,allowStaleOnFetchRejection:ot,allowStaleOnFetchAbort:st,ignoreFetchAbort:Nt}=t;if(n!==0&&!Nr(n))throw new TypeError("max option must be a nonnegative integer");const pt=n?Yd(n):Array;if(!pt)throw new Error("invalid max value: "+n);if(ut(this,Mn,n),ut(this,en,w),this.maxEntrySize=T||x(this,en),this.sizeCalculation=j,this.sizeCalculation){if(!x(this,en)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(S!==void 0&&typeof S!="function")throw new TypeError("memoMethod must be a function if defined");if(ut(this,Ki,S),q!==void 0&&typeof q!="function")throw new TypeError("fetchMethod must be a function if specified");if(ut(this,Qi,q),ut(this,Pr,!!q),ut(this,le,new Map),ut(this,Yt,new Array(n).fill(void 0)),ut(this,bt,new Array(n).fill(void 0)),ut(this,qe,new pt(n)),ut(this,nn,new pt(n)),ut(this,Le,0),ut(this,ke,0),ut(this,Fn,cu.create(n)),ut(this,ve,0),ut(this,Tn,0),typeof m=="function"&&ut(this,En,m),typeof p=="function"?(ut(this,An,p),ut(this,Ce,[])):(ut(this,An,void 0),ut(this,Ce,void 0)),ut(this,Ln,!!x(this,En)),ut(this,Ue,!!x(this,An)),this.noDisposeOnSet=!!y,this.noUpdateTTL=!!b,this.noDeleteOnFetchRejection=!!E,this.allowStaleOnFetchRejection=!!ot,this.allowStaleOnFetchAbort=!!st,this.ignoreFetchAbort=!!Nt,this.maxEntrySize!==0){if(x(this,en)!==0&&!Nr(x(this,en)))throw new TypeError("maxSize must be a positive integer if specified");if(!Nr(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");Y(this,H,Xd).call(this)}if(this.allowStale=!!d,this.noDeleteOnStaleGet=!!G,this.updateAgeOnGet=!!u,this.updateAgeOnHas=!!l,this.ttlResolution=Nr(s)||s===0?s:1,this.ttlAutopurge=!!a,this.ttl=r||0,this.ttl){if(!Nr(this.ttl))throw new TypeError("ttl must be a positive integer if specified");Y(this,H,lu).call(this)}if(x(this,Mn)===0&&this.ttl===0&&x(this,en)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!x(this,Mn)&&!x(this,en)){const St="LRU_CACHE_UNBOUNDED";(mt=>!Jh.has(mt))(St)&&(Jh.add(St),Kd("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",St,Ou))}}static unsafeExposeInternals(t){return{starts:x(t,On),ttls:x(t,rn),sizes:x(t,Rn),keyMap:x(t,le),keyList:x(t,Yt),valList:x(t,bt),next:x(t,qe),prev:x(t,nn),get head(){return x(t,Le)},get tail(){return x(t,ke)},free:x(t,Fn),isBackgroundFetch:n=>{var r;return Y(r=t,H,Kt).call(r,n)},backgroundFetch:(n,r,s,a)=>{var u;return Y(u=t,H,da).call(u,n,r,s,a)},moveToTail:n=>{var r;return Y(r=t,H,Ds).call(r,n)},indexes:n=>{var r;return Y(r=t,H,Dr).call(r,n)},rindexes:n=>{var r;return Y(r=t,H,zr).call(r,n)},isStale:n=>{var r;return x(r=t,sn).call(r,n)}}}get max(){return x(this,Mn)}get maxSize(){return x(this,en)}get calculatedSize(){return x(this,Tn)}get size(){return x(this,ve)}get fetchMethod(){return x(this,Qi)}get memoMethod(){return x(this,Ki)}get dispose(){return x(this,En)}get disposeAfter(){return x(this,An)}getRemainingTTL(t){return x(this,le).has(t)?1/0:0}*entries(){for(const t of Y(this,H,Dr).call(this))x(this,bt)[t]===void 0||x(this,Yt)[t]===void 0||Y(this,H,Kt).call(this,x(this,bt)[t])||(yield[x(this,Yt)[t],x(this,bt)[t]])}*rentries(){for(const t of Y(this,H,zr).call(this))x(this,bt)[t]===void 0||x(this,Yt)[t]===void 0||Y(this,H,Kt).call(this,x(this,bt)[t])||(yield[x(this,Yt)[t],x(this,bt)[t]])}*keys(){for(const t of Y(this,H,Dr).call(this)){const n=x(this,Yt)[t];n===void 0||Y(this,H,Kt).call(this,x(this,bt)[t])||(yield n)}}*rkeys(){for(const t of Y(this,H,zr).call(this)){const n=x(this,Yt)[t];n===void 0||Y(this,H,Kt).call(this,x(this,bt)[t])||(yield n)}}*values(){for(const t of Y(this,H,Dr).call(this))x(this,bt)[t]===void 0||Y(this,H,Kt).call(this,x(this,bt)[t])||(yield x(this,bt)[t])}*rvalues(){for(const t of Y(this,H,zr).call(this))x(this,bt)[t]===void 0||Y(this,H,Kt).call(this,x(this,bt)[t])||(yield x(this,bt)[t])}[(Cd=Symbol.iterator,kd=Symbol.toStringTag,Cd)](){return this.entries()}find(t,n={}){for(const r of Y(this,H,Dr).call(this)){const s=x(this,bt)[r],a=Y(this,H,Kt).call(this,s)?s.__staleWhileFetching:s;if(a!==void 0&&t(a,x(this,Yt)[r],this))return this.get(x(this,Yt)[r],n)}}forEach(t,n=this){for(const r of Y(this,H,Dr).call(this)){const s=x(this,bt)[r],a=Y(this,H,Kt).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&t.call(n,a,x(this,Yt)[r],this)}}rforEach(t,n=this){for(const r of Y(this,H,zr).call(this)){const s=x(this,bt)[r],a=Y(this,H,Kt).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&t.call(n,a,x(this,Yt)[r],this)}}purgeStale(){let t=!1;for(const n of Y(this,H,zr).call(this,{allowStale:!0}))x(this,sn).call(this,n)&&(Y(this,H,jr).call(this,x(this,Yt)[n],"expire"),t=!0);return t}info(t){const n=x(this,le).get(t);if(n===void 0)return;const r=x(this,bt)[n],s=Y(this,H,Kt).call(this,r)?r.__staleWhileFetching:r;if(s===void 0)return;const a={value:s};if(x(this,rn)&&x(this,On)){const u=x(this,rn)[n],l=x(this,On)[n];if(u&&l){const d=u-(qi.now()-l);a.ttl=d,a.start=Date.now()}}return x(this,Rn)&&(a.size=x(this,Rn)[n]),a}dump(){const t=[];for(const n of Y(this,H,Dr).call(this,{allowStale:!0})){const r=x(this,Yt)[n],s=x(this,bt)[n],a=Y(this,H,Kt).call(this,s)?s.__staleWhileFetching:s;if(a===void 0||r===void 0)continue;const u={value:a};if(x(this,rn)&&x(this,On)){u.ttl=x(this,rn)[n];const l=qi.now()-x(this,On)[n];u.start=Math.floor(Date.now()-l)}x(this,Rn)&&(u.size=x(this,Rn)[n]),t.unshift([r,u])}return t}load(t){this.clear();for(const[n,r]of t){if(r.start){const s=Date.now()-r.start;r.start=qi.now()-s}this.set(n,r.value,r)}}set(t,n,r={}){var b,w,T,j,q;if(n===void 0)return this.delete(t),this;const{ttl:s=this.ttl,start:a,noDisposeOnSet:u=this.noDisposeOnSet,sizeCalculation:l=this.sizeCalculation,status:d}=r;let{noUpdateTTL:m=this.noUpdateTTL}=r;const p=x(this,Hs).call(this,t,n,r.size||0,l);if(this.maxEntrySize&&p>this.maxEntrySize)return d&&(d.set="miss",d.maxEntrySizeExceeded=!0),Y(this,H,jr).call(this,t,"set"),this;let y=x(this,ve)===0?void 0:x(this,le).get(t);if(y===void 0)y=x(this,ve)===0?x(this,ke):x(this,Fn).length!==0?x(this,Fn).pop():x(this,ve)===x(this,Mn)?Y(this,H,ha).call(this,!1):x(this,ve),x(this,Yt)[y]=t,x(this,bt)[y]=n,x(this,le).set(t,y),x(this,qe)[x(this,ke)]=y,x(this,nn)[y]=x(this,ke),ut(this,ke,y),na(this,ve)._++,x(this,Yi).call(this,y,p,d),d&&(d.set="add"),m=!1;else{Y(this,H,Ds).call(this,y);const S=x(this,bt)[y];if(n!==S){if(x(this,Pr)&&Y(this,H,Kt).call(this,S)){S.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:E}=S;E===void 0||u||(x(this,Ln)&&((b=x(this,En))==null||b.call(this,E,t,"set")),x(this,Ue)&&((w=x(this,Ce))==null||w.push([E,t,"set"])))}else u||(x(this,Ln)&&((T=x(this,En))==null||T.call(this,S,t,"set")),x(this,Ue)&&((j=x(this,Ce))==null||j.push([S,t,"set"])));if(x(this,di).call(this,y),x(this,Yi).call(this,y,p,d),x(this,bt)[y]=n,d){d.set="replace";const E=S&&Y(this,H,Kt).call(this,S)?S.__staleWhileFetching:S;E!==void 0&&(d.oldValue=E)}}else d&&(d.set="update")}if(s===0||x(this,rn)||Y(this,H,lu).call(this),x(this,rn)&&(m||x(this,Ws).call(this,y,s,a),d&&x(this,gr).call(this,d,y)),!u&&x(this,Ue)&&x(this,Ce)){const S=x(this,Ce);let E;for(;E=S==null?void 0:S.shift();)(q=x(this,An))==null||q.call(this,...E)}return this}pop(){var t;try{for(;x(this,ve);){const n=x(this,bt)[x(this,Le)];if(Y(this,H,ha).call(this,!0),Y(this,H,Kt).call(this,n)){if(n.__staleWhileFetching)return n.__staleWhileFetching}else if(n!==void 0)return n}}finally{if(x(this,Ue)&&x(this,Ce)){const n=x(this,Ce);let r;for(;r=n==null?void 0:n.shift();)(t=x(this,An))==null||t.call(this,...r)}}}has(t,n={}){const{updateAgeOnHas:r=this.updateAgeOnHas,status:s}=n,a=x(this,le).get(t);if(a!==void 0){const u=x(this,bt)[a];if(Y(this,H,Kt).call(this,u)&&u.__staleWhileFetching===void 0)return!1;if(!x(this,sn).call(this,a))return r&&x(this,hi).call(this,a),s&&(s.has="hit",x(this,gr).call(this,s,a)),!0;s&&(s.has="stale",x(this,gr).call(this,s,a))}else s&&(s.has="miss");return!1}peek(t,n={}){const{allowStale:r=this.allowStale}=n,s=x(this,le).get(t);if(s===void 0||!r&&x(this,sn).call(this,s))return;const a=x(this,bt)[s];return Y(this,H,Kt).call(this,a)?a.__staleWhileFetching:a}async fetch(t,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,ttl:u=this.ttl,noDisposeOnSet:l=this.noDisposeOnSet,size:d=0,sizeCalculation:m=this.sizeCalculation,noUpdateTTL:p=this.noUpdateTTL,noDeleteOnFetchRejection:y=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:b=this.allowStaleOnFetchRejection,ignoreFetchAbort:w=this.ignoreFetchAbort,allowStaleOnFetchAbort:T=this.allowStaleOnFetchAbort,context:j,forceRefresh:q=!1,status:S,signal:E}=n;if(!x(this,Pr))return S&&(S.fetch="get"),this.get(t,{allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,status:S});const G={allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,ttl:u,noDisposeOnSet:l,size:d,sizeCalculation:m,noUpdateTTL:p,noDeleteOnFetchRejection:y,allowStaleOnFetchRejection:b,allowStaleOnFetchAbort:T,ignoreFetchAbort:w,status:S,signal:E};let ot=x(this,le).get(t);if(ot===void 0){S&&(S.fetch="miss");const st=Y(this,H,da).call(this,t,ot,G,j);return st.__returned=st}{const st=x(this,bt)[ot];if(Y(this,H,Kt).call(this,st)){const mt=r&&st.__staleWhileFetching!==void 0;return S&&(S.fetch="inflight",mt&&(S.returnedStale=!0)),mt?st.__staleWhileFetching:st.__returned=st}const Nt=x(this,sn).call(this,ot);if(!q&&!Nt)return S&&(S.fetch="hit"),Y(this,H,Ds).call(this,ot),s&&x(this,hi).call(this,ot),S&&x(this,gr).call(this,S,ot),st;const pt=Y(this,H,da).call(this,t,ot,G,j),St=pt.__staleWhileFetching!==void 0&&r;return S&&(S.fetch=Nt?"stale":"refresh",St&&Nt&&(S.returnedStale=!0)),St?pt.__staleWhileFetching:pt.__returned=pt}}async forceFetch(t,n={}){const r=await this.fetch(t,n);if(r===void 0)throw new Error("fetch() returned undefined");return r}memo(t,n={}){const r=x(this,Ki);if(!r)throw new Error("no memoMethod provided to constructor");const{context:s,forceRefresh:a,...u}=n,l=this.get(t,u);if(!a&&l!==void 0)return l;const d=r(t,l,{options:u,context:s});return this.set(t,d,u),d}get(t,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,status:u}=n,l=x(this,le).get(t);if(l!==void 0){const d=x(this,bt)[l],m=Y(this,H,Kt).call(this,d);return u&&x(this,gr).call(this,u,l),x(this,sn).call(this,l)?(u&&(u.get="stale"),m?(u&&r&&d.__staleWhileFetching!==void 0&&(u.returnedStale=!0),r?d.__staleWhileFetching:void 0):(a||Y(this,H,jr).call(this,t,"expire"),u&&r&&(u.returnedStale=!0),r?d:void 0)):(u&&(u.get="hit"),m?d.__staleWhileFetching:(Y(this,H,Ds).call(this,l),s&&x(this,hi).call(this,l),d))}u&&(u.get="miss")}delete(t){return Y(this,H,jr).call(this,t,"delete")}clear(){return Y(this,H,fu).call(this,"delete")}};Mn=new WeakMap,en=new WeakMap,En=new WeakMap,An=new WeakMap,Qi=new WeakMap,Ki=new WeakMap,ve=new WeakMap,Tn=new WeakMap,le=new WeakMap,Yt=new WeakMap,bt=new WeakMap,qe=new WeakMap,nn=new WeakMap,Le=new WeakMap,ke=new WeakMap,Fn=new WeakMap,Ce=new WeakMap,Rn=new WeakMap,On=new WeakMap,rn=new WeakMap,Ln=new WeakMap,Pr=new WeakMap,Ue=new WeakMap,H=new WeakSet,lu=function(){const t=new la(x(this,Mn)),n=new la(x(this,Mn));ut(this,rn,t),ut(this,On,n),ut(this,Ws,(a,u,l=qi.now())=>{if(n[a]=u!==0?l:0,t[a]=u,u!==0&&this.ttlAutopurge){const d=setTimeout(()=>{x(this,sn).call(this,a)&&Y(this,H,jr).call(this,x(this,Yt)[a],"expire")},u+1);d.unref&&d.unref()}}),ut(this,hi,a=>{n[a]=t[a]!==0?qi.now():0}),ut(this,gr,(a,u)=>{if(t[u]){const l=t[u],d=n[u];if(!l||!d)return;a.ttl=l,a.start=d,a.now=r||s();const m=a.now-d;a.remainingTTL=l-m}});let r=0;const s=()=>{const a=qi.now();if(this.ttlResolution>0){r=a;const u=setTimeout(()=>r=0,this.ttlResolution);u.unref&&u.unref()}return a};this.getRemainingTTL=a=>{const u=x(this,le).get(a);if(u===void 0)return 0;const l=t[u],d=n[u];return!l||!d?1/0:l-((r||s())-d)},ut(this,sn,a=>{const u=n[a],l=t[a];return!!l&&!!u&&(r||s())-u>l})},hi=new WeakMap,gr=new WeakMap,Ws=new WeakMap,sn=new WeakMap,Xd=function(){const t=new la(x(this,Mn));ut(this,Tn,0),ut(this,Rn,t),ut(this,di,n=>{ut(this,Tn,x(this,Tn)-t[n]),t[n]=0}),ut(this,Hs,(n,r,s,a)=>{if(Y(this,H,Kt).call(this,r))return 0;if(!Nr(s)){if(!a)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof a!="function")throw new TypeError("sizeCalculation must be a function");if(s=a(r,n),!Nr(s))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return s}),ut(this,Yi,(n,r,s)=>{if(t[n]=r,x(this,en)){const a=x(this,en)-t[n];for(;x(this,Tn)>a;)Y(this,H,ha).call(this,!0)}ut(this,Tn,x(this,Tn)+t[n]),s&&(s.entrySize=r,s.totalCalculatedSize=x(this,Tn))})},di=new WeakMap,Yi=new WeakMap,Hs=new WeakMap,Dr=function*({allowStale:t=this.allowStale}={}){if(x(this,ve))for(let n=x(this,ke);Y(this,H,hu).call(this,n)&&(!t&&x(this,sn).call(this,n)||(yield n),n!==x(this,Le));)n=x(this,nn)[n]},zr=function*({allowStale:t=this.allowStale}={}){if(x(this,ve))for(let n=x(this,Le);Y(this,H,hu).call(this,n)&&(!t&&x(this,sn).call(this,n)||(yield n),n!==x(this,ke));)n=x(this,qe)[n]},hu=function(t){return t!==void 0&&x(this,le).get(x(this,Yt)[t])===t},ha=function(t){var a,u;const n=x(this,Le),r=x(this,Yt)[n],s=x(this,bt)[n];return x(this,Pr)&&Y(this,H,Kt).call(this,s)?s.__abortController.abort(new Error("evicted")):(x(this,Ln)||x(this,Ue))&&(x(this,Ln)&&((a=x(this,En))==null||a.call(this,s,r,"evict")),x(this,Ue)&&((u=x(this,Ce))==null||u.push([s,r,"evict"]))),x(this,di).call(this,n),t&&(x(this,Yt)[n]=void 0,x(this,bt)[n]=void 0,x(this,Fn).push(n)),x(this,ve)===1?(ut(this,Le,ut(this,ke,0)),x(this,Fn).length=0):ut(this,Le,x(this,qe)[n]),x(this,le).delete(r),na(this,ve)._--,n},da=function(t,n,r,s){const a=n===void 0?void 0:x(this,bt)[n];if(Y(this,H,Kt).call(this,a))return a;const u=new wa,{signal:l}=r;l==null||l.addEventListener("abort",()=>u.abort(l.reason),{signal:u.signal});const d={signal:u.signal,options:r,context:s},m=(w,T=!1)=>{const{aborted:j}=u.signal,q=r.ignoreFetchAbort&&w!==void 0;if(r.status&&(j&&!T?(r.status.fetchAborted=!0,r.status.fetchError=u.signal.reason,q&&(r.status.fetchAbortIgnored=!0)):r.status.fetchResolved=!0),j&&!q&&!T)return p(u.signal.reason);const S=y;return x(this,bt)[n]===y&&(w===void 0?S.__staleWhileFetching?x(this,bt)[n]=S.__staleWhileFetching:Y(this,H,jr).call(this,t,"fetch"):(r.status&&(r.status.fetchUpdated=!0),this.set(t,w,d.options))),w},p=w=>{const{aborted:T}=u.signal,j=T&&r.allowStaleOnFetchAbort,q=j||r.allowStaleOnFetchRejection,S=q||r.noDeleteOnFetchRejection,E=y;if(x(this,bt)[n]===y&&(!S||E.__staleWhileFetching===void 0?Y(this,H,jr).call(this,t,"fetch"):j||(x(this,bt)[n]=E.__staleWhileFetching)),q)return r.status&&E.__staleWhileFetching!==void 0&&(r.status.returnedStale=!0),E.__staleWhileFetching;if(E.__returned===E)throw w};r.status&&(r.status.fetchDispatched=!0);const y=new Promise((w,T)=>{var q;const j=(q=x(this,Qi))==null?void 0:q.call(this,t,a,d);j&&j instanceof Promise&&j.then(S=>w(S===void 0?void 0:S),T),u.signal.addEventListener("abort",()=>{r.ignoreFetchAbort&&!r.allowStaleOnFetchAbort||(w(void 0),r.allowStaleOnFetchAbort&&(w=S=>m(S,!0)))})}).then(m,w=>(r.status&&(r.status.fetchRejected=!0,r.status.fetchError=w),p(w))),b=Object.assign(y,{__abortController:u,__staleWhileFetching:a,__returned:void 0});return n===void 0?(this.set(t,b,{...d.options,status:void 0}),n=x(this,le).get(t)):x(this,bt)[n]=b,b},Kt=function(t){if(!x(this,Pr))return!1;const n=t;return!!n&&n instanceof Promise&&n.hasOwnProperty("__staleWhileFetching")&&n.__abortController instanceof wa},du=function(t,n){x(this,nn)[n]=t,x(this,qe)[t]=n},Ds=function(t){t!==x(this,ke)&&(t===x(this,Le)?ut(this,Le,x(this,qe)[t]):Y(this,H,du).call(this,x(this,nn)[t],x(this,qe)[t]),Y(this,H,du).call(this,x(this,ke),t),ut(this,ke,t))},jr=function(t,n){var s,a,u,l;let r=!1;if(x(this,ve)!==0){const d=x(this,le).get(t);if(d!==void 0)if(r=!0,x(this,ve)===1)Y(this,H,fu).call(this,n);else{x(this,di).call(this,d);const m=x(this,bt)[d];if(Y(this,H,Kt).call(this,m)?m.__abortController.abort(new Error("deleted")):(x(this,Ln)||x(this,Ue))&&(x(this,Ln)&&((s=x(this,En))==null||s.call(this,m,t,n)),x(this,Ue)&&((a=x(this,Ce))==null||a.push([m,t,n]))),x(this,le).delete(t),x(this,Yt)[d]=void 0,x(this,bt)[d]=void 0,d===x(this,ke))ut(this,ke,x(this,nn)[d]);else if(d===x(this,Le))ut(this,Le,x(this,qe)[d]);else{const p=x(this,nn)[d];x(this,qe)[p]=x(this,qe)[d];const y=x(this,qe)[d];x(this,nn)[y]=x(this,nn)[d]}na(this,ve)._--,x(this,Fn).push(d)}}if(x(this,Ue)&&((u=x(this,Ce))!=null&&u.length)){const d=x(this,Ce);let m;for(;m=d==null?void 0:d.shift();)(l=x(this,An))==null||l.call(this,...m)}return r},fu=function(t){var n,r,s;for(const a of Y(this,H,zr).call(this,{allowStale:!0})){const u=x(this,bt)[a];if(Y(this,H,Kt).call(this,u))u.__abortController.abort(new Error("deleted"));else{const l=x(this,Yt)[a];x(this,Ln)&&((n=x(this,En))==null||n.call(this,u,l,t)),x(this,Ue)&&((r=x(this,Ce))==null||r.push([u,l,t]))}}if(x(this,le).clear(),x(this,bt).fill(void 0),x(this,Yt).fill(void 0),x(this,rn)&&x(this,On)&&(x(this,rn).fill(0),x(this,On).fill(0)),x(this,Rn)&&x(this,Rn).fill(0),ut(this,Le,0),ut(this,ke,0),x(this,Fn).length=0,ut(this,Tn,0),ut(this,ve,0),x(this,Ue)&&x(this,Ce)){const a=x(this,Ce);let u;for(;u=a==null?void 0:a.shift();)(s=x(this,An))==null||s.call(this,...u)}};let uu=Ou;class Jd{constructor(){_(this,"_syncStatus",{status:Pg.done,foldersProgress:[]});_(this,"_syncEnabledState",Rh.initializing);_(this,"_workspaceGuidelines",[]);_(this,"_openUserGuidelinesInput",!1);_(this,"_userGuidelines");_(this,"_contextStore",new Xm);_(this,"_prevOpenFiles",[]);_(this,"_disableContext",!1);_(this,"_enableAgentMemories",!1);_(this,"subscribers",new Set);_(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));_(this,"handleMessageFromExtension",t=>{const n=t.data;switch(n.type){case oe.sourceFoldersUpdated:this.onSourceFoldersUpdated(n.data.sourceFolders);break;case oe.sourceFoldersSyncStatus:this.onSyncStatusUpdated(n.data);break;case oe.fileRangesSelected:this.updateSelections(n.data);break;case oe.currentlyOpenFiles:this.setCurrentlyOpenFiles(n.data);break;case oe.syncEnabledState:this.onSyncEnabledStateUpdate(n.data);break;case oe.updateGuidelinesState:this.onGuidelinesStateUpdate(n.data);break;default:return!1}return!0});_(this,"onSourceFoldersUpdated",t=>{const n=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(r=>({sourceFolder:r,status:ye.active,label:r.folderRoot,showWarning:r.guidelinesOverLimit,id:r.folderRoot+String(r.guidelinesEnabled)+String(r.guidelinesOverLimit)})),n,r=>r.id),this.notifySubscribers()});_(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});_(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});_(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});_(this,"addFile",t=>{this.addFiles([t])});_(this,"addFiles",t=>{this.updateFiles(t,[])});_(this,"removeFile",t=>{this.removeFiles([t])});_(this,"removeFiles",t=>{this.updateFiles([],t)});_(this,"updateItems",(t,n)=>{this.updateItemsInplace(t,n),this.notifySubscribers()});_(this,"updateItemsInplace",(t,n)=>{this._contextStore.update(t,n,r=>r.id)});_(this,"updateFiles",(t,n)=>{const r=u=>({file:u,...Gc(u)}),s=t.map(r),a=n.map(r);this._contextStore.update(s,a,u=>u.id),this.notifySubscribers()});_(this,"updateRules",(t,n)=>{const r=u=>({rule:u,...Og(u)}),s=t.map(r),a=n.map(r);this._contextStore.update(s,a,u=>u.id),this.notifySubscribers()});_(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});_(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});_(this,"setCurrentlyOpenFiles",t=>{const n=t.map(s=>({recentFile:s,...Gc(s)})),r=this._prevOpenFiles;this._prevOpenFiles=n,this._contextStore.update(n,r,s=>s.id),r.forEach(s=>{const a=this._contextStore.peekKey(s.id);a!=null&&a.recentFile&&(a.file=a.recentFile,delete a.recentFile)}),n.forEach(s=>{const a=this._contextStore.peekKey(s.id);a!=null&&a.file&&(a.recentFile=a.file,delete a.file)}),this.notifySubscribers()});_(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});_(this,"updateUserGuidelines",(t,n)=>{const r=this.userGuidelines,s=t.overLimit||((n==null?void 0:n.overLimit)??!1),a={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:ye.active,referenceCount:1,showWarning:s,rulesAndGuidelinesState:n};this._contextStore.update([a],r,u=>u.id),this.notifySubscribers()});_(this,"onGuidelinesStateUpdate",t=>{var s;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const n=t.userGuidelines,r=this.userGuidelines;if(n||t.rulesAndGuidelines||r.length>0){const a=n||{enabled:!1,overLimit:!1,contents:"",lengthLimit:((s=t.rulesAndGuidelines)==null?void 0:s.lengthLimit)??2e3};this.updateUserGuidelines(a,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(a=>a.sourceFolder))});_(this,"updateSourceFoldersWithGuidelines",t=>t.map(n=>{const r=this._workspaceGuidelines.find(s=>s.workspaceFolder===n.folderRoot);return{...n,guidelinesEnabled:(r==null?void 0:r.enabled)??!1,guidelinesOverLimit:(r==null?void 0:r.overLimit)??!1,guidelinesLengthLimit:(r==null?void 0:r.lengthLimit)??2e3}}));_(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});_(this,"updateExternalSources",(t,n)=>{this._contextStore.update(t,n,r=>r.id),this.notifySubscribers()});_(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});_(this,"updateSelections",t=>{const n=this._contextStore.values.filter(ma),r=t.map(s=>({selection:s,...Gc(s)}));this._contextStore.update([],n,s=>s.id),this._contextStore.update(r,[],s=>s.id),this.notifySubscribers()});_(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const n=this.recentActiveItems[0];return this.markInactive(n),!0}return!1});_(this,"markInactive",t=>{this.markItemsInactive([t])});_(this,"markItemsInactive",t=>{t.forEach(n=>{this._contextStore.setStatus(n.id,ye.inactive)}),this.notifySubscribers()});_(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});_(this,"markActive",t=>{this.markItemsActive([t])});_(this,"markItemsActive",t=>{t.forEach(n=>{this._contextStore.setStatus(n.id,ye.active)}),this.notifySubscribers()});_(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});_(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});_(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});_(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>$u(t)&&!ga(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(ga)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(ma)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(wu)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(_a)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(ku)}get userGuidelines(){return this._contextStore.values.filter(va)}get agentMemories(){return[{...Rg,status:this._enableAgentMemories?ye.active:ye.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>ya(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===ye.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===ye.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===ye.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===ye.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===ye.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===ye.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var d;if(this.syncEnabledState===Rh.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(m=>m.progress!==void 0);if(t.length===0)return;const n=t.reduce((m,p)=>{var y;return m+(((y=p==null?void 0:p.progress)==null?void 0:y.trackedFiles)??0)},0),r=t.reduce((m,p)=>{var y;return m+(((y=p==null?void 0:p.progress)==null?void 0:y.backlogSize)??0)},0),s=Math.max(n,0),a=Math.min(Math.max(r,0),s),u=s-a,l=[];for(const m of t)(d=m==null?void 0:m.progress)!=null&&d.newlyTracked&&l.push(m.folderRoot);return{status:this._syncStatus.status,totalFiles:s,syncedCount:u,backlogSize:a,newlyTrackedFolders:l}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(_a(t)||va(t)||Ca(t)||ya(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===ye.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===ye.inactive)}get isContextDisabled(){return this._disableContext}}class Xm{constructor(){_(this,"_cache",new uu({max:1e3}));_(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));_(this,"clear",()=>{this._cache.clear()});_(this,"update",(t,n,r)=>{t.forEach(s=>this.addInPlace(s,r)),n.forEach(s=>this.removeInPlace(s,r))});_(this,"removeFromStore",(t,n)=>{const r=n(t);this._cache.delete(r)});_(this,"addInPlace",(t,n)=>{const r=n(t),s=t.referenceCount??1,a=this._cache.get(r),u=t.status??(a==null?void 0:a.status)??ye.active;a?(a.referenceCount+=s,a.status=u,a.pinned=t.pinned??a.pinned,a.showWarning=t.showWarning??a.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in a&&(a.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in a&&(a.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(r,{...t,pinned:void 0,referenceCount:s,status:u})});_(this,"removeInPlace",(t,n)=>{const r=n(t),s=this._cache.get(r);s&&(s.referenceCount-=1,s.referenceCount===0&&this._cache.delete(r))});_(this,"setStatus",(t,n)=>{const r=this._cache.get(t);r&&(r.status=n)});_(this,"togglePinned",t=>{const n=this._cache.peek(t);n&&(n.pinned?this.unpin(t):this.pin(t))});_(this,"pin",t=>{const n=this._cache.peek(t);n&&!n.pinned&&(n.pinned=!0,n.referenceCount+=1)});_(this,"unpin",t=>{const n=this._cache.peek(t);n&&n.pinned&&(n.pinned=!1,n.referenceCount-=1,n.referenceCount===0&&this._cache.delete(t))});_(this,"toggleStatus",t=>{const n=this._cache.get(t);n&&(n.status=n.status===ye.active?ye.inactive:ye.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Jm{constructor(t,n,r){_(this,"_originalModel");_(this,"_modifiedModel");_(this,"_fullEdits",[]);_(this,"_currEdit");_(this,"_currOriginalEdit");_(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(n=>{this._modifiedModel.applyEdits([n])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});_(this,"finish",()=>this._completeCurrEdit());_(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);_(this,"_completeCurrEdit",t=>{const n={resetOriginal:[],original:[],modified:[]};if(!t)return n;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const r=this._nextModifiedInsertPosition(),s=t.stagedEndLine-t.stagedStartLine,a={range:new this._monaco.Range(r.lineNumber,0,r.lineNumber+s,0),text:""};n.modified.push(a),this._modifiedModel.applyEdits([a]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return n});_(this,"_startNewEdit",t=>{const n={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},n.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),n});_(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const n=this._nextModifiedInsertPosition(),r={...this._currEdit,text:t.newText,range:new this._monaco.Range(n.lineNumber,n.column,n.lineNumber,n.column)};return this._modifiedModel.applyEdits([r]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[r]:[]}});_(this,"_nextModifiedInsertPosition",()=>{var n;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((n=this._currEdit.text)==null?void 0:n.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=n,this._monaco=r,this._originalModel=this._monaco.editor.createModel(n),this._modifiedModel=this._monaco.editor.createModel(n)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class t_{constructor(t,n,r){_(this,"_asyncMsgSender");_(this,"_editor");_(this,"_chatModel");_(this,"_focusModel",new Ld);_(this,"_hasScrolledOnInit",!1);_(this,"_markHasScrolledOnInit",nu(()=>{this._hasScrolledOnInit=!0},200));_(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});_(this,"_subscribers",new Set);_(this,"_disposables",[]);_(this,"_rootChunk");_(this,"_keybindings",ce({}));_(this,"_requestId",ce(void 0));_(this,"requestId",this._requestId);_(this,"_disableResolution",ce(!1));_(this,"disableResolution",Fh(this._disableResolution));_(this,"_disableApply",ce(!1));_(this,"disableApply",Fh(this._disableApply));_(this,"_currStream");_(this,"_isLoadingDiffChunks",ce(!1));_(this,"_selectionLines",ce(void 0));_(this,"_mode",ce(Gr.edit));_(this,"initializeEditor",t=>{var n,r,s,a,u,l,d,m,p,y,b,w;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new Tu(new Dd(zs),zs,new Jd),(r=(n=this._monaco.editor).registerCommand)==null||r.call(n,"acceptFocusedChunk",this.acceptFocusedChunk),(a=(s=this._monaco.editor).registerCommand)==null||a.call(s,"rejectFocusedChunk",this.rejectFocusedChunk),(l=(u=this._monaco.editor).registerCommand)==null||l.call(u,"acceptAllChunks",this.acceptAllChunks),(m=(d=this._monaco.editor).registerCommand)==null||m.call(d,"rejectAllChunks",this.rejectAllChunks),(y=(p=this._monaco.editor).registerCommand)==null||y.call(p,"focusNextChunk",this.focusNextChunk),(w=(b=this._monaco.editor).registerCommand)==null||w.call(b,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(T=>this.notifySubscribers())}),this.initialize()});_(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));_(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});_(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});_(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const n=this.leaves[0];this.revealChunk(n)}this.notifyDiffViewUpdated(),this.notifySubscribers()});_(this,"onMouseMoveModified",t=>{var s,a,u,l,d,m;if(((s=t.target.position)==null?void 0:s.lineNumber)===void 0||this.leaves===void 0)return;const n=this.editorOffset,r=(a=t.target.position)==null?void 0:a.lineNumber;for(let p=0;p<this.leaves.length;p++){const y=this.leaves[p],b=(u=y.unitOfCodeWork.lineChanges)==null?void 0:u.lineChanges[0].modifiedStart,w=(l=y.unitOfCodeWork.lineChanges)==null?void 0:l.lineChanges[0].modifiedEnd,T=(d=y.unitOfCodeWork.lineChanges)==null?void 0:d.lineChanges[0].originalStart,j=(m=y.unitOfCodeWork.lineChanges)==null?void 0:m.lineChanges[0].originalEnd;if(b!==void 0&&w!==void 0&&T!==void 0&&j!==void 0){if(b!==w||r!==b){if(b<=r&&r<w){this.setCurrFocusedChunkIdx(p,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const q=this._editor.getOriginalEditor(),S=q.getOption(this._monaco.editor.EditorOption.lineHeight),E=q.getScrolledVisiblePosition({lineNumber:T,column:0}),G=q.getScrolledVisiblePosition({lineNumber:j+1,column:0});if(E===null||G===null)continue;const ot=E.top-S/2+n,st=G.top-S/2+n;if(t.event.posy>=ot&&t.event.posy<=st){this.setCurrFocusedChunkIdx(p,!1);break}break}}}});_(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:oe.diffViewWindowFocusChange,data:t})});_(this,"setCurrFocusedChunkIdx",(t,n=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),n&&this.revealCurrFocusedChunk(),this.notifySubscribers())});_(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});_(this,"revealChunk",t=>{var s;const n=(s=t.unitOfCodeWork.lineChanges)==null?void 0:s.lineChanges[0],r=n==null?void 0:n.modifiedStart;r!==void 0&&this._editor.revealLineNearTop(r-1)});_(this,"renderCentralOverlayWidget",t=>{const n=()=>({editor:this._editor,id:"central-overlay-widget"}),r=function(s,a,u){let l,d=a;const m=()=>d.editor.getModifiedEditor(),p=()=>{const y=m();if(!y)return;const b={getDomNode:()=>s,getId:()=>d.id,getPosition:()=>({preference:u.monaco.editor.OverlayWidgetPositionPreference.TOP_CENTER})};l&&y.removeOverlayWidget(l),y.addOverlayWidget(b),l=b};return p(),{update:y=>{d=y,p()},destroy:()=>{const y=m();y&&l&&y.removeOverlayWidget(l)}}}(t,n(),{monaco:this._monaco});return{update:()=>{r.update(n())},destroy:r.destroy}});_(this,"renderInstructionsDrawerViewZone",(t,n)=>{let r=!1,s=n;const a=n.autoFocus??!0,u=p=>{a&&!r&&(this._editor.revealLineNearTop(p),r=!0)},l=p=>({...p,ordinal:ou.instructionDrawer,editor:this._editor,afterLineNumber:p.line}),d=Xh(t,l(n)),m=[];return a&&m.push(this._editor.onDidUpdateDiff(()=>{u(s.line)})),{update:p=>{const y={...s,...p};bm(y,s)||(d.update(l(y)),s=y,u(y.line))},destroy:()=>{d.destroy(),m.forEach(p=>p.dispose())}}});_(this,"renderActionsViewZone",(t,n)=>{const r=a=>{var l;let u;return u=a.chunk?(l=a.chunk.unitOfCodeWork.lineChanges)==null?void 0:l.lineChanges[0].modifiedStart:1,{...a,ordinal:ou.chunkActionPanel,editor:this._editor,afterLineNumber:u?u-1:void 0}},s=Xh(t,r(n));return{update:a=>{s.update(r(a))},destroy:s.destroy}});_(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});_(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});_(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});_(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});_(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});_(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});_(this,"initialize",async()=>{var d;const t=await this._asyncMsgSender.send({type:oe.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:n,instruction:r,keybindings:s,editable:a}=t.data;this._editor.updateOptions({readOnly:!a});const u=ae(this._keybindings);this._keybindings.set(s??u);const l=r==null?void 0:r.selection;l&&(l.start.line===l.end.line&&l.start.character===l.end.character&&this._mode.set(Gr.instruction),ae(this.selectionLines)===void 0&&this._selectionLines.set({start:l.start.line,end:l.end.line})),this.updateModels(n.originalCode??"",n.modifiedCode??"",{rootPath:n.repoRoot,relPath:n.pathName}),(d=this._currStream)==null||d.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});_(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:oe.disposeDiffView})});_(this,"_tryFetchStream",async()=>{var n,r,s;const t=this._asyncMsgSender.stream({type:oe.diffViewFetchPendingStream},15e3,6e4);for await(const a of t)switch(a.type){case oe.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(a.data.requestId);const u=this._editor.getOriginalEditor().getValue();this._currStream=new Jm(a.data.streamId,u,this._monaco),this._syncStreamToModels();break}case oe.diffViewDiffStreamEnded:if(((n=this._currStream)==null?void 0:n.id)!==a.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case oe.diffViewDiffStreamChunk:{if(((r=this._currStream)==null?void 0:r.id)!==a.data.streamId)return;const u=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!u)return this.setLoading(!1),void this._cleanupStream();const l=(s=this._currStream)==null?void 0:s.onReceiveChunk(a);l&&(this._applyDeltaDiff(l),ae(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});_(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case oe.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case oe.diffViewAcceptAllChunks:this.acceptAllChunks();break;case oe.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case oe.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case oe.diffViewFocusPrevChunk:this.focusPrevChunk();break;case oe.diffViewFocusNextChunk:this.focusNextChunk()}});_(this,"_applyDeltaDiff",t=>{const n=this._editor.getOriginalEditor().getModel(),r=this._editor.getModifiedEditor().getModel();n&&r&&(n.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(s=>{n.pushEditOperations([],[s],()=>[])}),t.modified.forEach(s=>{r.pushEditOperations([],[s],()=>[])}))});_(this,"_cleanupStream",()=>{var t;if(this._currStream){const n=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(n),this._currStream=void 0,this._resetScrollOnInit()}});_(this,"_syncStreamToModels",()=>{var r,s;const t=(r=this._currStream)==null?void 0:r.originalValue,n=(s=this._currStream)==null?void 0:s.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),n&&n!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(n)});_(this,"acceptChunk",async t=>{ae(this._disableApply)||this.acceptChunks([t])});_(this,"acceptChunks",async(t,n=!1)=>{ae(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,Wc.accept,n),await Xc(),this.areModelsEqual()&&!ae(this.isLoading)&&this.disposeDiffViewPanel())});_(this,"areModelsEqual",()=>{var r,s;const t=(r=this._editor.getModel())==null?void 0:r.original,n=(s=this._editor.getModel())==null?void 0:s.modified;return(t==null?void 0:t.getValue())===(n==null?void 0:n.getValue())});_(this,"rejectChunk",async t=>{this.rejectChunks([t])});_(this,"rejectChunks",async(t,n=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,Wc.reject,n),await Xc(),this.areModelsEqual()&&!ae(this.isLoading)&&this.disposeDiffViewPanel()});_(this,"notifyDiffViewUpdated",nu(()=>{this.notifyResolvedChunks([],Wc.accept)},1e3));_(this,"notifyResolvedChunks",async(t,n,r=!1)=>{var a;const s=(a=this._editor.getModel())==null?void 0:a.original.uri.path;s&&await this._asyncMsgSender.send({type:oe.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:s,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(u=>u.unitOfCodeWork),resolveType:n,shouldApplyToAll:r}},2e3)});_(this,"executeDiffChunks",(t,n)=>{var p,y,b;if(ae(this._disableResolution)||n&&ae(this._disableApply))return;const r=(p=this._editor.getModel())==null?void 0:p.original,s=(y=this._editor.getModel())==null?void 0:y.modified;if(!r||!s||this._currStream!==void 0)return;const a=[],u=[];for(const w of t){const T=(b=w.unitOfCodeWork.lineChanges)==null?void 0:b.lineChanges[0];if(!T||w.unitOfCodeWork.originalCode===void 0||w.unitOfCodeWork.modifiedCode===void 0)continue;let j={startLineNumber:T.originalStart,startColumn:1,endLineNumber:T.originalEnd,endColumn:1},q={startLineNumber:T.modifiedStart,startColumn:1,endLineNumber:T.modifiedEnd,endColumn:1};const S=n?w.unitOfCodeWork.modifiedCode:w.unitOfCodeWork.originalCode;S!==void 0&&(a.push({range:j,text:S}),u.push({range:q,text:S}))}r.pushEditOperations([],a,()=>[]),s.pushEditOperations([],u,()=>[]);const l=this._focusModel.nextIdx({nowrap:!0});if(l===void 0)return;const d=l===this._focusModel.focusedItemIdx?l-1:l,m=this._focusModel.items[d];m&&this.revealChunk(m)});_(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});_(this,"handleInstructionSubmit",t=>{const n=this._editor.getModifiedEditor(),r=this.getSelectedCodeDetails(n);if(!r)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,r)});_(this,"updateModels",(t,n,r)=>{var u,l;const s=(l=(u=this._editor.getModel())==null?void 0:u.original)==null?void 0:l.uri,a=(r&&this._monaco.Uri.file(r.relPath))??s;if(a)if((s==null?void 0:s.fsPath)!==a.fsPath||(s==null?void 0:s.authority)!==a.authority){const d=a.with({fragment:crypto.randomUUID()}),m=a.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,d),modified:this._monaco.editor.createModel(n??"",void 0,m)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==n&&this.getModifiedEditor().setValue(n??"");else console.warn("No URI found for diff view. Not updating models.")});_(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=r,this._asyncMsgSender=new Sg(s=>zs.postMessage(s)),this.initializeEditor(n)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return qd(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var a,u;const t=[],n=this._editor.getLineChanges(),r=(a=this._editor.getModel())==null?void 0:a.original,s=(u=this._editor.getModel())==null?void 0:u.modified;if(n&&r&&s){for(const l of n){const d=ed({startLineNumber:l.originalStartLineNumber,startColumn:1,endLineNumber:l.originalEndLineNumber,endColumn:1}),m=ed({startLineNumber:l.modifiedStartLineNumber,startColumn:1,endLineNumber:l.modifiedEndLineNumber,endColumn:1}),p=e_(this._editor,d,m);t.push(p)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(l=>l.id)}}}getSelectedCodeDetails(t){const n=t.getModel();if(!n)return null;const r=n.getLanguageId(),s=1,a=1,u={lineNumber:n.getLineCount(),column:n.getLineMaxColumn(n.getLineCount())},l=ae(this._selectionLines);if(!l)throw new Error("No selection lines found");const d=Math.min(l.end+1,u.lineNumber),m=new this._monaco.Range(l.start+1,1,d,n.getLineMaxColumn(d));let p=n.getValueInRange(m);d<n.getLineCount()&&(p+=n.getEOL());const y=new this._monaco.Range(s,a,m.startLineNumber,m.startColumn),b=Math.min(m.endLineNumber+1,u.lineNumber),w=new this._monaco.Range(b,1,u.lineNumber,u.column);return{selectedCode:p,prefix:n.getValueInRange(y),suffix:n.getValueInRange(w),path:n.uri.path,language:r,prefixBegin:y.startLineNumber-1,suffixEnd:w.endLineNumber-1}}}function e_(o,t,n){var a,u;const r=(a=o.getModel())==null?void 0:a.original,s=(u=o.getModel())==null?void 0:u.modified;if(!r||!s)throw new Error("No models found");return function(l,d,m,p){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:l,modifiedCode:d,lineChanges:{lineChanges:[{originalStart:m.startLineNumber,originalEnd:m.endLineNumber,modifiedStart:p.startLineNumber,modifiedEnd:p.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(r.getValueInRange(t),s.getValueInRange(n),t,n)}function ed(o){return o.endLineNumber===0?{startLineNumber:o.startLineNumber+1,startColumn:1,endLineNumber:o.startLineNumber+1,endColumn:1}:{startLineNumber:o.startLineNumber,startColumn:1,endLineNumber:o.endLineNumber+1,endColumn:1}}function nd(o){let t,n;return t=new Hr({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[n_]},$$scope:{ctx:o}}}),t.$on("click",function(){_r(o[4])&&o[4].apply(this,arguments)}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){o=r;const a={};132096&s&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function n_(o){let t,n,r;return t=new vi({props:{keybinding:o[10].acceptFocusedChunk}}),{c(){B(t.$$.fragment),n=he(`
        Accept`)},m(s,a){V(t,s,a),nt(s,n,a),r=!0},p(s,a){const u={};1024&a&&(u.keybinding=s[10].acceptFocusedChunk),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&rt(n),Z(t,s)}}}function r_(o){let t,n,r;return t=new vi({props:{keybinding:o[10].rejectFocusedChunk}}),{c(){B(t.$$.fragment),n=he(`
      Reject`)},m(s,a){V(t,s,a),nt(s,n,a),r=!0},p(s,a){const u={};1024&a&&(u.keybinding=s[10].rejectFocusedChunk),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&rt(n),Z(t,s)}}}function i_(o){let t,n,r,s,a,u,l,d,m,p,y=!o[3]&&nd(o);return l=new Hr({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[r_]},$$scope:{ctx:o}}}),l.$on("click",function(){_r(o[5])&&o[5].apply(this,arguments)}),{c(){t=Bt("div"),r=qt(),s=Bt("div"),a=Bt("div"),y&&y.c(),u=qt(),B(l.$$.fragment),jt(t,"class","svelte-zm1705"),yn(t,"c-chunk-diff-border--focused",!!o[7]&&o[1]),jt(a,"class","c-button-container svelte-zm1705"),yn(a,"c-button-container--focused",o[1]),yn(a,"c-button-container--transparent",o[9]),jt(s,"class","c-chunk-action-panel-anchor svelte-zm1705"),Hi(s,"top",o[8]+"px"),yn(s,"c-chunk-action-panel-anchor--left",o[0]==="left"),yn(s,"c-chunk-action-panel-anchor--right",o[0]==="right"),yn(s,"c-chunk-action-panel-anchor--focused",o[1])},m(b,w){nt(b,t,w),nt(b,r,w),nt(b,s,w),Vt(s,a),y&&y.m(a,null),Vt(a,u),V(l,a,null),d=!0,m||(p=[Id(n=o[6].renderActionsViewZone(t,{chunk:o[7],heightInPx:o[2],onDomNodeTop:o[12]})),Wr(s,"mouseenter",o[13]),Wr(s,"mousemove",o[13]),Wr(s,"mouseleave",o[13])],m=!0)},p(b,[w]){o=b,n&&_r(n.update)&&132&w&&n.update.call(null,{chunk:o[7],heightInPx:o[2],onDomNodeTop:o[12]}),(!d||130&w)&&yn(t,"c-chunk-diff-border--focused",!!o[7]&&o[1]),o[3]?y&&(be(),z(y,1,1,()=>{y=null}),xe()):y?(y.p(o,w),8&w&&F(y,1)):(y=nd(o),y.c(),F(y,1),y.m(a,u));const T={};132096&w&&(T.$$scope={dirty:w,ctx:o}),l.$set(T),(!d||2&w)&&yn(a,"c-button-container--focused",o[1]),(!d||512&w)&&yn(a,"c-button-container--transparent",o[9]),(!d||256&w)&&Hi(s,"top",o[8]+"px"),(!d||1&w)&&yn(s,"c-chunk-action-panel-anchor--left",o[0]==="left"),(!d||1&w)&&yn(s,"c-chunk-action-panel-anchor--right",o[0]==="right"),(!d||2&w)&&yn(s,"c-chunk-action-panel-anchor--focused",o[1])},i(b){d||(F(y),F(l.$$.fragment,b),d=!0)},o(b){z(y),z(l.$$.fragment,b),d=!1},d(b){b&&(rt(t),rt(r),rt(s)),y&&y.d(),Z(l),m=!1,bu(p)}}}function s_(o,t,n){let r,{align:s="right"}=t,{isFocused:a}=t,{heightInPx:u=1}=t,{disableApply:l=!1}=t,{onAccept:d}=t,{onReject:m}=t,{diffViewModel:p}=t,{leaf:y}=t;const b=p.keybindings;fi(o,b,S=>n(10,r=S));let w=0,T,j=!1;function q(){T&&(clearTimeout(T),T=void 0),n(9,j=!1)}return o.$$set=S=>{"align"in S&&n(0,s=S.align),"isFocused"in S&&n(1,a=S.isFocused),"heightInPx"in S&&n(2,u=S.heightInPx),"disableApply"in S&&n(3,l=S.disableApply),"onAccept"in S&&n(4,d=S.onAccept),"onReject"in S&&n(5,m=S.onReject),"diffViewModel"in S&&n(6,p=S.diffViewModel),"leaf"in S&&n(7,y=S.leaf)},[s,a,u,l,d,m,p,y,w,j,r,b,S=>{n(8,w=S)},function(S){S.target.closest(".c-button-container")?q():S.type==="mouseenter"||S.type==="mousemove"?(q(),T=setTimeout(()=>{n(9,j=!0)},400)):S.type==="mouseleave"&&q()}]}class o_ extends gi{constructor(t){super(),mi(this,t,s_,i_,_i,{align:0,isFocused:1,heightInPx:2,disableApply:3,onAccept:4,onReject:5,diffViewModel:6,leaf:7})}}function rd(o){let t,n,r;function s(u){o[18](u)}let a={onOpenChange:o[16],content:o[3],triggerOn:[rm.Hover],$$slots:{default:[c_]},$$scope:{ctx:o}};return o[4]!==void 0&&(a.requestClose=o[4]),t=new Gg({props:a}),Gi.push(()=>vg(t,"requestClose",s)),{c(){B(t.$$.fragment)},m(u,l){V(t,u,l),r=!0},p(u,l){const d={};8&l&&(d.content=u[3]),1048576&l&&(d.$$scope={dirty:l,ctx:u}),!n&&16&l&&(n=!0,d.requestClose=u[4],yg(()=>n=!1)),t.$set(d)},i(u){r||(F(t.$$.fragment,u),r=!0)},o(u){z(t.$$.fragment,u),r=!1},d(u){Z(t,u)}}}function a_(o){let t,n;return t=new zg({}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function c_(o){let t,n;return t=new Ig({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[a_]},$$scope:{ctx:o}}}),t.$on("click",o[17]),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};1048576&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function u_(o){let t;return{c(){t=Bt("span"),t.textContent="No changes",jt(t,"class","c-diff-page-counter svelte-4zjwll")},m(n,r){nt(n,t,r)},p:Ht,i:Ht,o:Ht,d(n){n&&rt(t)}}}function l_(o){var m,p;let t,n,r,s,a,u,l,d=((p=(m=o[1])==null?void 0:m.leaves)==null?void 0:p.length)+"";return u=new Md({props:{size:1,loading:o[10]}}),{c(){t=Bt("span"),n=he(o[2]),r=he(" of "),s=he(d),a=qt(),B(u.$$.fragment),jt(t,"class","c-diff-page-counter svelte-4zjwll")},m(y,b){nt(y,t,b),Vt(t,n),Vt(t,r),Vt(t,s),Vt(t,a),V(u,t,null),l=!0},p(y,b){var T,j;(!l||4&b)&&Dn(n,y[2]),(!l||2&b)&&d!==(d=((j=(T=y[1])==null?void 0:T.leaves)==null?void 0:j.length)+"")&&Dn(s,d);const w={};1024&b&&(w.loading=y[10]),u.$set(w)},i(y){l||(F(u.$$.fragment,y),l=!0)},o(y){z(u.$$.fragment,y),l=!1},d(y){y&&rt(t),Z(u)}}}function h_(o){let t,n,r,s;return r=new Md({props:{size:1,loading:o[10]}}),{c(){t=Bt("span"),n=he(`Generating changes
        `),B(r.$$.fragment),jt(t,"class","c-diff-page-counter svelte-4zjwll")},m(a,u){nt(a,t,u),Vt(t,n),V(r,t,null),s=!0},p(a,u){const l={};1024&u&&(l.loading=a[10]),r.$set(l)},i(a){s||(F(r.$$.fragment,a),s=!0)},o(a){z(r.$$.fragment,a),s=!1},d(a){a&&rt(t),Z(r)}}}function id(o){let t,n,r,s,a,u;t=new Hr({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[d_]},$$scope:{ctx:o}}}),t.$on("click",function(){_r(o[0].focusPrevChunk)&&o[0].focusPrevChunk.apply(this,arguments)}),r=new Hr({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[f_]},$$scope:{ctx:o}}}),r.$on("click",function(){_r(o[0].focusNextChunk)&&o[0].focusNextChunk.apply(this,arguments)});let l=!o[12]&&sd(o);return{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment),s=qt(),l&&l.c(),a=tr()},m(d,m){V(t,d,m),nt(d,n,m),V(r,d,m),nt(d,s,m),l&&l.m(d,m),nt(d,a,m),u=!0},p(d,m){o=d;const p={};1050624&m&&(p.$$scope={dirty:m,ctx:o}),t.$set(p);const y={};1050624&m&&(y.$$scope={dirty:m,ctx:o}),r.$set(y),o[12]?l&&(be(),z(l,1,1,()=>{l=null}),xe()):l?(l.p(o,m),4096&m&&F(l,1)):(l=sd(o),l.c(),F(l,1),l.m(a.parentNode,a))},i(d){u||(F(t.$$.fragment,d),F(r.$$.fragment,d),F(l),u=!0)},o(d){z(t.$$.fragment,d),z(r.$$.fragment,d),z(l),u=!1},d(d){d&&(rt(n),rt(s),rt(a)),Z(t,d),Z(r,d),l&&l.d(d)}}}function d_(o){let t,n,r;return t=new vi({props:{keybinding:o[11].focusPrevChunk}}),{c(){B(t.$$.fragment),n=he(`
        Back`)},m(s,a){V(t,s,a),nt(s,n,a),r=!0},p(s,a){const u={};2048&a&&(u.keybinding=s[11].focusPrevChunk),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&rt(n),Z(t,s)}}}function f_(o){let t,n,r;return t=new vi({props:{keybinding:o[11].focusNextChunk}}),{c(){B(t.$$.fragment),n=he(`
        Next`)},m(s,a){V(t,s,a),nt(s,n,a),r=!0},p(s,a){const u={};2048&a&&(u.keybinding=s[11].focusNextChunk),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&rt(n),Z(t,s)}}}function sd(o){let t,n,r,s=!o[13]&&od(o);return n=new Hr({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[g_]},$$scope:{ctx:o}}}),n.$on("click",function(){_r(o[0].rejectAllChunks)&&o[0].rejectAllChunks.apply(this,arguments)}),{c(){s&&s.c(),t=qt(),B(n.$$.fragment)},m(a,u){s&&s.m(a,u),nt(a,t,u),V(n,a,u),r=!0},p(a,u){(o=a)[13]?s&&(be(),z(s,1,1,()=>{s=null}),xe()):s?(s.p(o,u),8192&u&&F(s,1)):(s=od(o),s.c(),F(s,1),s.m(t.parentNode,t));const l={};1050624&u&&(l.$$scope={dirty:u,ctx:o}),n.$set(l)},i(a){r||(F(s),F(n.$$.fragment,a),r=!0)},o(a){z(s),z(n.$$.fragment,a),r=!1},d(a){a&&rt(t),s&&s.d(a),Z(n,a)}}}function od(o){let t,n;return t=new Hr({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[p_]},$$scope:{ctx:o}}}),t.$on("click",function(){_r(o[0].acceptAllChunks)&&o[0].acceptAllChunks.apply(this,arguments)}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){o=r;const a={};1050624&s&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function p_(o){let t,n,r;return t=new vi({props:{keybinding:o[11].acceptAllChunks}}),{c(){B(t.$$.fragment),n=he(`
            Accept All`)},m(s,a){V(t,s,a),nt(s,n,a),r=!0},p(s,a){const u={};2048&a&&(u.keybinding=s[11].acceptAllChunks),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&rt(n),Z(t,s)}}}function g_(o){let t,n,r;return t=new vi({props:{keybinding:o[11].rejectAllChunks}}),{c(){B(t.$$.fragment),n=he(`
          Reject All`)},m(s,a){V(t,s,a),nt(s,n,a),r=!0},p(s,a){const u={};2048&a&&(u.keybinding=s[11].rejectAllChunks),t.$set(u)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){z(t.$$.fragment,s),r=!1},d(s){s&&rt(n),Z(t,s)}}}function m_(o){let t,n,r,s,a,u,l,d=o[9]&&rd(o);const m=[h_,l_,u_],p=[];function y(w,T){return!w[5]&&w[10]?0:w[5]?1:2}s=y(o),a=p[s]=m[s](o);let b=o[5]&&id(o);return{c(){t=Bt("div"),n=Bt("div"),d&&d.c(),r=qt(),a.c(),u=qt(),b&&b.c(),jt(n,"class","c-button-container svelte-4zjwll"),jt(t,"class","c-top-action-panel-anchor svelte-4zjwll")},m(w,T){nt(w,t,T),Vt(t,n),d&&d.m(n,null),Vt(n,r),p[s].m(n,null),Vt(n,u),b&&b.m(n,null),l=!0},p(w,[T]){w[9]?d?(d.p(w,T),512&T&&F(d,1)):(d=rd(w),d.c(),F(d,1),d.m(n,r)):d&&(be(),z(d,1,1,()=>{d=null}),xe());let j=s;s=y(w),s===j?p[s].p(w,T):(be(),z(p[j],1,1,()=>{p[j]=null}),xe(),a=p[s],a?a.p(w,T):(a=p[s]=m[s](w),a.c()),F(a,1),a.m(n,u)),w[5]?b?(b.p(w,T),32&T&&F(b,1)):(b=id(w),b.c(),F(b,1),b.m(n,null)):b&&(be(),z(b,1,1,()=>{b=null}),xe())},i(w){l||(F(d),F(a),F(b),l=!0)},o(w){z(d),z(a),z(b),l=!1},d(w){w&&rt(t),d&&d.d(),p[s].d(),b&&b.d()}}}function __(o,t,n){let r,s,a,u,l,d,m,p,y,b,w=Ht,T=()=>(w(),w=mr(E,mt=>n(1,d=mt)),E),j=Ht,q=Ht,S=Ht;o.$$.on_destroy.push(()=>w()),o.$$.on_destroy.push(()=>j()),o.$$.on_destroy.push(()=>q()),o.$$.on_destroy.push(()=>S());let{diffViewModel:E}=t;T();const G=E.keybindings;fi(o,G,mt=>n(11,p=mt));const ot=E.requestId;fi(o,ot,mt=>n(9,l=mt));let st,Nt="x",pt="Copy request ID",St=()=>{};return o.$$set=mt=>{"diffViewModel"in mt&&T(n(0,E=mt.diffViewModel))},o.$$.update=()=>{var mt;2&o.$$.dirty&&(n(8,r=d.disableResolution),q(),q=mr(r,Ft=>n(12,y=Ft))),2&o.$$.dirty&&(n(7,s=d.disableApply),S(),S=mr(s,Ft=>n(13,b=Ft))),2&o.$$.dirty&&(d.currFocusedChunkIdx!==void 0?n(2,Nt=(d.currFocusedChunkIdx+1).toString()):n(2,Nt="x")),2&o.$$.dirty&&(n(6,a=d.isLoading),j(),j=mr(a,Ft=>n(10,m=Ft))),2&o.$$.dirty&&n(5,u=!!((mt=d.leaves)!=null&&mt.length))},[E,d,Nt,pt,St,u,a,s,r,l,m,p,y,b,G,ot,function(mt){mt||(clearTimeout(st),st=void 0,n(3,pt="Copy request ID"))},async function(){l&&(await navigator.clipboard.writeText(l),n(3,pt="Copied!"),clearTimeout(st),st=setTimeout(St,1500))},function(mt){St=mt,n(4,St)}]}class v_ extends gi{constructor(t){super(),mi(this,t,__,m_,_i,{diffViewModel:0})}}var oa,aa,pu={exports:{}};oa=pu,aa=pu.exports,(function(){var o,t="Expected a function",n="__lodash_hash_undefined__",r="__lodash_placeholder__",s=16,a=32,u=64,l=128,d=256,m=1/0,p=9007199254740991,y=NaN,b=4294967295,w=[["ary",l],["bind",1],["bindKey",2],["curry",8],["curryRight",s],["flip",512],["partial",a],["partialRight",u],["rearg",d]],T="[object Arguments]",j="[object Array]",q="[object Boolean]",S="[object Date]",E="[object Error]",G="[object Function]",ot="[object GeneratorFunction]",st="[object Map]",Nt="[object Number]",pt="[object Object]",St="[object Promise]",mt="[object RegExp]",Ft="[object Set]",Gt="[object String]",ue="[object Symbol]",Ct="[object WeakMap]",$t="[object ArrayBuffer]",de="[object DataView]",xn="[object Float32Array]",Se="[object Float64Array]",ft="[object Int8Array]",$n="[object Int16Array]",zn="[object Int32Array]",er="[object Uint8Array]",yr="[object Uint8ClampedArray]",bi="[object Uint16Array]",ts="[object Uint32Array]",Ia=/\b__p \+= '';/g,Ma=/\b(__p \+=) '' \+/g,Vs=/(__e\(.*?\)|\b__t\)) \+\n'';/g,es=/&(?:amp|lt|gt|quot|#39);/g,ns=/[&<>"']/g,Ea=RegExp(es.source),Aa=RegExp(ns.source),xi=/<%-([\s\S]+?)%>/g,rs=/<%([\s\S]+?)%>/g,is=/<%=([\s\S]+?)%>/g,un=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Zs=/^\w*$/,Qs=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ss=/[\\^$.*+?()[\]{}|]/g,Ks=RegExp(ss.source),br=/^\s+/,Ys=/\s/,Xs=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Ta=/\{\n\/\* \[wrapped with (.+)\] \*/,nr=/,? & /,Js=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Fa=/[()=,{}\[\]\/\s]/,Ra=/\\(\\)?/g,os=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,xr=/\w*$/,as=/^[-+]0x[0-9a-f]+$/i,cs=/^0b[01]+$/i,us=/^\[object .+?Constructor\]$/,Br=/^0o[0-7]+$/i,Oa=/^(?:0|[1-9]\d*)$/,La=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,$i=/($^)/,Na=/['\n\r\u2028\u2029\\]/g,wi="\\ud800-\\udfff",ls="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",ki="\\u2700-\\u27bf",jn="a-z\\xdf-\\xf6\\xf8-\\xff",ln="A-Z\\xc0-\\xd6\\xd8-\\xde",qn="\\ufe0e\\ufe0f",Vr="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Un="['’]",Da="["+wi+"]",Zr="["+Vr+"]",Pn="["+ls+"]",hs="\\d+",to="["+ki+"]",eo="["+jn+"]",no="[^"+wi+Vr+hs+ki+jn+ln+"]",Ci="\\ud83c[\\udffb-\\udfff]",ds="[^"+wi+"]",$r="(?:\\ud83c[\\udde6-\\uddff]){2}",Wn="[\\ud800-\\udbff][\\udc00-\\udfff]",wr="["+ln+"]",wn="\\u200d",ro="(?:"+eo+"|"+no+")",rr="(?:"+wr+"|"+no+")",fs="(?:['’](?:d|ll|m|re|s|t|ve))?",io="(?:['’](?:D|LL|M|RE|S|T|VE))?",Qr="(?:"+Pn+"|"+Ci+")?",Si="["+qn+"]?",ps=Si+Qr+"(?:"+wn+"(?:"+[ds,$r,Wn].join("|")+")"+Si+Qr+")*",so="(?:"+[to,$r,Wn].join("|")+")"+ps,oo="(?:"+[ds+Pn+"?",Pn,$r,Wn,Da].join("|")+")",Kr=RegExp(Un,"g"),ao=RegExp(Pn,"g"),gs=RegExp(Ci+"(?="+Ci+")|"+oo+ps,"g"),C=RegExp([wr+"?"+eo+"+"+fs+"(?="+[Zr,wr,"$"].join("|")+")",rr+"+"+io+"(?="+[Zr,wr+ro,"$"].join("|")+")",wr+"?"+ro+"+"+fs,wr+"+"+io,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",hs,so].join("|"),"g"),A=RegExp("["+wn+wi+ls+qn+"]"),W=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,et=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Pt=-1,J={};J[xn]=J[Se]=J[ft]=J[$n]=J[zn]=J[er]=J[yr]=J[bi]=J[ts]=!0,J[T]=J[j]=J[$t]=J[q]=J[de]=J[S]=J[E]=J[G]=J[st]=J[Nt]=J[pt]=J[mt]=J[Ft]=J[Gt]=J[Ct]=!1;var dt={};dt[T]=dt[j]=dt[$t]=dt[de]=dt[q]=dt[S]=dt[xn]=dt[Se]=dt[ft]=dt[$n]=dt[zn]=dt[st]=dt[Nt]=dt[pt]=dt[mt]=dt[Ft]=dt[Gt]=dt[ue]=dt[er]=dt[yr]=dt[bi]=dt[ts]=!0,dt[E]=dt[G]=dt[Ct]=!1;var te={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Qe=parseFloat,re=parseInt,ee=typeof cn=="object"&&cn&&cn.Object===Object&&cn,hn=typeof self=="object"&&self&&self.Object===Object&&self,Wt=ee||hn||Function("return this")(),fe=aa&&!aa.nodeType&&aa,Ie=fe&&oa&&!oa.nodeType&&oa,ir=Ie&&Ie.exports===fe,kr=ir&&ee.process,pe=function(){try{var M=Ie&&Ie.require&&Ie.require("util").types;return M||kr&&kr.binding&&kr.binding("util")}catch{}}(),Cr=pe&&pe.isArrayBuffer,ms=pe&&pe.isDate,co=pe&&pe.isMap,uo=pe&&pe.isRegExp,Rt=pe&&pe.isSet,At=pe&&pe.isTypedArray;function $e(M,N,D){switch(D.length){case 0:return M.call(N);case 1:return M.call(N,D[0]);case 2:return M.call(N,D[0],D[1]);case 3:return M.call(N,D[0],D[1],D[2])}return M.apply(N,D)}function Hn(M,N,D,K){for(var _t=-1,Mt=M==null?0:M.length;++_t<Mt;){var ge=M[_t];N(K,ge,D(ge),M)}return K}function Te(M,N){for(var D=-1,K=M==null?0:M.length;++D<K&&N(M[D],D,M)!==!1;);return M}function Pe(M,N){for(var D=M==null?0:M.length;D--&&N(M[D],D,M)!==!1;);return M}function dn(M,N){for(var D=-1,K=M==null?0:M.length;++D<K;)if(!N(M[D],D,M))return!1;return!0}function Me(M,N){for(var D=-1,K=M==null?0:M.length,_t=0,Mt=[];++D<K;){var ge=M[D];N(ge,D,M)&&(Mt[_t++]=ge)}return Mt}function Gn(M,N){return!(M==null||!M.length)&&or(M,N,0)>-1}function Sr(M,N,D){for(var K=-1,_t=M==null?0:M.length;++K<_t;)if(D(N,M[K]))return!0;return!1}function Dt(M,N){for(var D=-1,K=M==null?0:M.length,_t=Array(K);++D<K;)_t[D]=N(M[D],D,M);return _t}function Ke(M,N){for(var D=-1,K=N.length,_t=M.length;++D<K;)M[_t+D]=N[D];return M}function kn(M,N,D,K){var _t=-1,Mt=M==null?0:M.length;for(K&&Mt&&(D=M[++_t]);++_t<Mt;)D=N(D,M[_t],_t,M);return D}function lo(M,N,D,K){var _t=M==null?0:M.length;for(K&&_t&&(D=M[--_t]);_t--;)D=N(D,M[_t],_t,M);return D}function Ir(M,N){for(var D=-1,K=M==null?0:M.length;++D<K;)if(N(M[D],D,M))return!0;return!1}var _s=za("length");function Yr(M,N,D){var K;return D(M,function(_t,Mt,ge){if(N(_t,Mt,ge))return K=Mt,!1}),K}function sr(M,N,D,K){for(var _t=M.length,Mt=D+(K?1:-1);K?Mt--:++Mt<_t;)if(N(M[Mt],Mt,M))return Mt;return-1}function or(M,N,D){return N==N?function(K,_t,Mt){for(var ge=Mt-1,Bn=K.length;++ge<Bn;)if(K[ge]===_t)return ge;return-1}(M,N,D):sr(M,Xr,D)}function Ii(M,N,D,K){for(var _t=D-1,Mt=M.length;++_t<Mt;)if(K(M[_t],N))return _t;return-1}function Xr(M){return M!=M}function Lu(M,N){var D=M==null?0:M.length;return D?qa(M,N)/D:y}function za(M){return function(N){return N==null?o:N[M]}}function ja(M){return function(N){return M==null?o:M[N]}}function Nu(M,N,D,K,_t){return _t(M,function(Mt,ge,Bn){D=K?(K=!1,Mt):N(D,Mt,ge,Bn)}),D}function qa(M,N){for(var D,K=-1,_t=M.length;++K<_t;){var Mt=N(M[K]);Mt!==o&&(D=D===o?Mt:D+Mt)}return D}function Ua(M,N){for(var D=-1,K=Array(M);++D<M;)K[D]=N(D);return K}function Du(M){return M&&M.slice(0,Uu(M)+1).replace(br,"")}function Ye(M){return function(N){return M(N)}}function Pa(M,N){return Dt(N,function(D){return M[D]})}function vs(M,N){return M.has(N)}function zu(M,N){for(var D=-1,K=M.length;++D<K&&or(N,M[D],0)>-1;);return D}function ju(M,N){for(var D=M.length;D--&&or(N,M[D],0)>-1;);return D}var hf=ja({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),df=ja({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function ff(M){return"\\"+te[M]}function Mi(M){return A.test(M)}function Wa(M){var N=-1,D=Array(M.size);return M.forEach(function(K,_t){D[++N]=[_t,K]}),D}function qu(M,N){return function(D){return M(N(D))}}function Mr(M,N){for(var D=-1,K=M.length,_t=0,Mt=[];++D<K;){var ge=M[D];ge!==N&&ge!==r||(M[D]=r,Mt[_t++]=D)}return Mt}function ho(M){var N=-1,D=Array(M.size);return M.forEach(function(K){D[++N]=K}),D}function pf(M){var N=-1,D=Array(M.size);return M.forEach(function(K){D[++N]=[K,K]}),D}function Ei(M){return Mi(M)?function(N){for(var D=gs.lastIndex=0;gs.test(N);)++D;return D}(M):_s(M)}function Cn(M){return Mi(M)?function(N){return N.match(gs)||[]}(M):function(N){return N.split("")}(M)}function Uu(M){for(var N=M.length;N--&&Ys.test(M.charAt(N)););return N}var gf=ja({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),Ai=function M(N){var D,K=(N=N==null?Wt:Ai.defaults(Wt.Object(),N,Ai.pick(Wt,et))).Array,_t=N.Date,Mt=N.Error,ge=N.Function,Bn=N.Math,Zt=N.Object,Ha=N.RegExp,mf=N.String,fn=N.TypeError,fo=K.prototype,_f=ge.prototype,Ti=Zt.prototype,po=N["__core-js_shared__"],go=_f.toString,Lt=Ti.hasOwnProperty,vf=0,Pu=(D=/[^.]+$/.exec(po&&po.keys&&po.keys.IE_PROTO||""))?"Symbol(src)_1."+D:"",mo=Ti.toString,yf=go.call(Zt),bf=Wt._,xf=Ha("^"+go.call(Lt).replace(ss,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),_o=ir?N.Buffer:o,Er=N.Symbol,vo=N.Uint8Array,Wu=_o?_o.allocUnsafe:o,yo=qu(Zt.getPrototypeOf,Zt),Hu=Zt.create,Gu=Ti.propertyIsEnumerable,bo=fo.splice,Bu=Er?Er.isConcatSpreadable:o,ys=Er?Er.iterator:o,Jr=Er?Er.toStringTag:o,xo=function(){try{var e=ii(Zt,"defineProperty");return e({},"",{}),e}catch{}}(),$f=N.clearTimeout!==Wt.clearTimeout&&N.clearTimeout,wf=_t&&_t.now!==Wt.Date.now&&_t.now,kf=N.setTimeout!==Wt.setTimeout&&N.setTimeout,$o=Bn.ceil,wo=Bn.floor,Ga=Zt.getOwnPropertySymbols,Cf=_o?_o.isBuffer:o,Vu=N.isFinite,Sf=fo.join,If=qu(Zt.keys,Zt),me=Bn.max,Fe=Bn.min,Mf=_t.now,Ef=N.parseInt,Zu=Bn.random,Af=fo.reverse,Ba=ii(N,"DataView"),bs=ii(N,"Map"),Va=ii(N,"Promise"),Fi=ii(N,"Set"),xs=ii(N,"WeakMap"),$s=ii(Zt,"create"),ko=xs&&new xs,Ri={},Tf=si(Ba),Ff=si(bs),Rf=si(Va),Of=si(Fi),Lf=si(xs),Co=Er?Er.prototype:o,ws=Co?Co.valueOf:o,Qu=Co?Co.toString:o;function g(e){if(ne(e)&&!xt(e)&&!(e instanceof It)){if(e instanceof pn)return e;if(Lt.call(e,"__wrapped__"))return Kl(e)}return new pn(e)}var Oi=function(){function e(){}return function(i){if(!Jt(i))return{};if(Hu)return Hu(i);e.prototype=i;var c=new e;return e.prototype=o,c}}();function So(){}function pn(e,i){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!i,this.__index__=0,this.__values__=o}function It(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=b,this.__views__=[]}function ti(e){var i=-1,c=e==null?0:e.length;for(this.clear();++i<c;){var h=e[i];this.set(h[0],h[1])}}function ar(e){var i=-1,c=e==null?0:e.length;for(this.clear();++i<c;){var h=e[i];this.set(h[0],h[1])}}function cr(e){var i=-1,c=e==null?0:e.length;for(this.clear();++i<c;){var h=e[i];this.set(h[0],h[1])}}function ei(e){var i=-1,c=e==null?0:e.length;for(this.__data__=new cr;++i<c;)this.add(e[i])}function Sn(e){var i=this.__data__=new ar(e);this.size=i.size}function Ku(e,i){var c=xt(e),h=!c&&oi(e),f=!c&&!h&&Or(e),v=!c&&!h&&!f&&zi(e),$=c||h||f||v,k=$?Ua(e.length,mf):[],I=k.length;for(var O in e)!i&&!Lt.call(e,O)||$&&(O=="length"||f&&(O=="offset"||O=="parent")||v&&(O=="buffer"||O=="byteLength"||O=="byteOffset")||dr(O,I))||k.push(O);return k}function Yu(e){var i=e.length;return i?e[ic(0,i-1)]:o}function Nf(e,i){return qo(We(e),ni(i,0,e.length))}function Df(e){return qo(We(e))}function Za(e,i,c){(c!==o&&!In(e[i],c)||c===o&&!(i in e))&&ur(e,i,c)}function ks(e,i,c){var h=e[i];Lt.call(e,i)&&In(h,c)&&(c!==o||i in e)||ur(e,i,c)}function Io(e,i){for(var c=e.length;c--;)if(In(e[c][0],i))return c;return-1}function zf(e,i,c,h){return Ar(e,function(f,v,$){i(h,f,c(f),$)}),h}function Xu(e,i){return e&&Zn(i,we(i),e)}function ur(e,i,c){i=="__proto__"&&xo?xo(e,i,{configurable:!0,enumerable:!0,value:c,writable:!0}):e[i]=c}function Qa(e,i){for(var c=-1,h=i.length,f=K(h),v=e==null;++c<h;)f[c]=v?o:Ec(e,i[c]);return f}function ni(e,i,c){return e==e&&(c!==o&&(e=e<=c?e:c),i!==o&&(e=e>=i?e:i)),e}function gn(e,i,c,h,f,v){var $,k=1&i,I=2&i,O=4&i;if(c&&($=f?c(e,h,f,v):c(e)),$!==o)return $;if(!Jt(e))return e;var R=xt(e);if(R){if($=function(L){var P=L.length,lt=new L.constructor(P);return P&&typeof L[0]=="string"&&Lt.call(L,"index")&&(lt.index=L.index,lt.input=L.input),lt}(e),!k)return We(e,$)}else{var U=Re(e),X=U==G||U==ot;if(Or(e))return $l(e,k);if(U==pt||U==T||X&&!f){if($=I||X?{}:Ul(e),!k)return I?function(L,P){return Zn(L,jl(L),P)}(e,function(L,P){return L&&Zn(P,Ge(P),L)}($,e)):function(L,P){return Zn(L,vc(L),P)}(e,Xu($,e))}else{if(!dt[U])return f?e:{};$=function(L,P,lt){var Q,yt=L.constructor;switch(P){case $t:return hc(L);case q:case S:return new yt(+L);case de:return function(gt,Tt){var it=Tt?hc(gt.buffer):gt.buffer;return new gt.constructor(it,gt.byteOffset,gt.byteLength)}(L,lt);case xn:case Se:case ft:case $n:case zn:case er:case yr:case bi:case ts:return wl(L,lt);case st:return new yt;case Nt:case Gt:return new yt(L);case mt:return function(gt){var Tt=new gt.constructor(gt.source,xr.exec(gt));return Tt.lastIndex=gt.lastIndex,Tt}(L);case Ft:return new yt;case ue:return Q=L,ws?Zt(ws.call(Q)):{}}}(e,U,k)}}v||(v=new Sn);var tt=v.get(e);if(tt)return tt;v.set(e,$),ph(e)?e.forEach(function(L){$.add(gn(L,i,c,L,e,v))}):dh(e)&&e.forEach(function(L,P){$.set(P,gn(L,i,c,P,e,v))});var at=R?o:(O?I?gc:pc:I?Ge:we)(e);return Te(at||e,function(L,P){at&&(L=e[P=L]),ks($,P,gn(L,i,c,P,e,v))}),$}function Ju(e,i,c){var h=c.length;if(e==null)return!h;for(e=Zt(e);h--;){var f=c[h],v=i[f],$=e[f];if($===o&&!(f in e)||!v($))return!1}return!0}function tl(e,i,c){if(typeof e!="function")throw new fn(t);return Ts(function(){e.apply(o,c)},i)}function Cs(e,i,c,h){var f=-1,v=Gn,$=!0,k=e.length,I=[],O=i.length;if(!k)return I;c&&(i=Dt(i,Ye(c))),h?(v=Sr,$=!1):i.length>=200&&(v=vs,$=!1,i=new ei(i));t:for(;++f<k;){var R=e[f],U=c==null?R:c(R);if(R=h||R!==0?R:0,$&&U==U){for(var X=O;X--;)if(i[X]===U)continue t;I.push(R)}else v(i,U,h)||I.push(R)}return I}g.templateSettings={escape:xi,evaluate:rs,interpolate:is,variable:"",imports:{_:g}},g.prototype=So.prototype,g.prototype.constructor=g,pn.prototype=Oi(So.prototype),pn.prototype.constructor=pn,It.prototype=Oi(So.prototype),It.prototype.constructor=It,ti.prototype.clear=function(){this.__data__=$s?$s(null):{},this.size=0},ti.prototype.delete=function(e){var i=this.has(e)&&delete this.__data__[e];return this.size-=i?1:0,i},ti.prototype.get=function(e){var i=this.__data__;if($s){var c=i[e];return c===n?o:c}return Lt.call(i,e)?i[e]:o},ti.prototype.has=function(e){var i=this.__data__;return $s?i[e]!==o:Lt.call(i,e)},ti.prototype.set=function(e,i){var c=this.__data__;return this.size+=this.has(e)?0:1,c[e]=$s&&i===o?n:i,this},ar.prototype.clear=function(){this.__data__=[],this.size=0},ar.prototype.delete=function(e){var i=this.__data__,c=Io(i,e);return!(c<0||(c==i.length-1?i.pop():bo.call(i,c,1),--this.size,0))},ar.prototype.get=function(e){var i=this.__data__,c=Io(i,e);return c<0?o:i[c][1]},ar.prototype.has=function(e){return Io(this.__data__,e)>-1},ar.prototype.set=function(e,i){var c=this.__data__,h=Io(c,e);return h<0?(++this.size,c.push([e,i])):c[h][1]=i,this},cr.prototype.clear=function(){this.size=0,this.__data__={hash:new ti,map:new(bs||ar),string:new ti}},cr.prototype.delete=function(e){var i=jo(this,e).delete(e);return this.size-=i?1:0,i},cr.prototype.get=function(e){return jo(this,e).get(e)},cr.prototype.has=function(e){return jo(this,e).has(e)},cr.prototype.set=function(e,i){var c=jo(this,e),h=c.size;return c.set(e,i),this.size+=c.size==h?0:1,this},ei.prototype.add=ei.prototype.push=function(e){return this.__data__.set(e,n),this},ei.prototype.has=function(e){return this.__data__.has(e)},Sn.prototype.clear=function(){this.__data__=new ar,this.size=0},Sn.prototype.delete=function(e){var i=this.__data__,c=i.delete(e);return this.size=i.size,c},Sn.prototype.get=function(e){return this.__data__.get(e)},Sn.prototype.has=function(e){return this.__data__.has(e)},Sn.prototype.set=function(e,i){var c=this.__data__;if(c instanceof ar){var h=c.__data__;if(!bs||h.length<199)return h.push([e,i]),this.size=++c.size,this;c=this.__data__=new cr(h)}return c.set(e,i),this.size=c.size,this};var Ar=Il(Vn),el=Il(Ya,!0);function jf(e,i){var c=!0;return Ar(e,function(h,f,v){return c=!!i(h,f,v)}),c}function Mo(e,i,c){for(var h=-1,f=e.length;++h<f;){var v=e[h],$=i(v);if($!=null&&(k===o?$==$&&!Je($):c($,k)))var k=$,I=v}return I}function nl(e,i){var c=[];return Ar(e,function(h,f,v){i(h,f,v)&&c.push(h)}),c}function Ee(e,i,c,h,f){var v=-1,$=e.length;for(c||(c=Yf),f||(f=[]);++v<$;){var k=e[v];i>0&&c(k)?i>1?Ee(k,i-1,c,h,f):Ke(f,k):h||(f[f.length]=k)}return f}var Ka=Ml(),rl=Ml(!0);function Vn(e,i){return e&&Ka(e,i,we)}function Ya(e,i){return e&&rl(e,i,we)}function Eo(e,i){return Me(i,function(c){return fr(e[c])})}function ri(e,i){for(var c=0,h=(i=Fr(i,e)).length;e!=null&&c<h;)e=e[Qn(i[c++])];return c&&c==h?e:o}function il(e,i,c){var h=i(e);return xt(e)?h:Ke(h,c(e))}function De(e){return e==null?e===o?"[object Undefined]":"[object Null]":Jr&&Jr in Zt(e)?function(i){var c=Lt.call(i,Jr),h=i[Jr];try{i[Jr]=o;var f=!0}catch{}var v=mo.call(i);return f&&(c?i[Jr]=h:delete i[Jr]),v}(e):function(i){return mo.call(i)}(e)}function Xa(e,i){return e>i}function qf(e,i){return e!=null&&Lt.call(e,i)}function Uf(e,i){return e!=null&&i in Zt(e)}function Ja(e,i,c){for(var h=c?Sr:Gn,f=e[0].length,v=e.length,$=v,k=K(v),I=1/0,O=[];$--;){var R=e[$];$&&i&&(R=Dt(R,Ye(i))),I=Fe(R.length,I),k[$]=!c&&(i||f>=120&&R.length>=120)?new ei($&&R):o}R=e[0];var U=-1,X=k[0];t:for(;++U<f&&O.length<I;){var tt=R[U],at=i?i(tt):tt;if(tt=c||tt!==0?tt:0,!(X?vs(X,at):h(O,at,c))){for($=v;--$;){var L=k[$];if(!(L?vs(L,at):h(e[$],at,c)))continue t}X&&X.push(at),O.push(tt)}}return O}function Ss(e,i,c){var h=(e=Gl(e,i=Fr(i,e)))==null?e:e[Qn(_n(i))];return h==null?o:$e(h,e,c)}function sl(e){return ne(e)&&De(e)==T}function Is(e,i,c,h,f){return e===i||(e==null||i==null||!ne(e)&&!ne(i)?e!=e&&i!=i:function(v,$,k,I,O,R){var U=xt(v),X=xt($),tt=U?j:Re(v),at=X?j:Re($),L=(tt=tt==T?pt:tt)==pt,P=(at=at==T?pt:at)==pt,lt=tt==at;if(lt&&Or(v)){if(!Or($))return!1;U=!0,L=!1}if(lt&&!L)return R||(R=new Sn),U||zi(v)?zl(v,$,k,I,O,R):function(it,ht,_e,se,je,Qt,Oe){switch(_e){case de:if(it.byteLength!=ht.byteLength||it.byteOffset!=ht.byteOffset)return!1;it=it.buffer,ht=ht.buffer;case $t:return!(it.byteLength!=ht.byteLength||!Qt(new vo(it),new vo(ht)));case q:case S:case Nt:return In(+it,+ht);case E:return it.name==ht.name&&it.message==ht.message;case mt:case Gt:return it==ht+"";case st:var Kn=Wa;case Ft:var Lr=1&se;if(Kn||(Kn=ho),it.size!=ht.size&&!Lr)return!1;var Qo=Oe.get(it);if(Qo)return Qo==ht;se|=2,Oe.set(it,ht);var jc=zl(Kn(it),Kn(ht),se,je,Qt,Oe);return Oe.delete(it),jc;case ue:if(ws)return ws.call(it)==ws.call(ht)}return!1}(v,$,tt,k,I,O,R);if(!(1&k)){var Q=L&&Lt.call(v,"__wrapped__"),yt=P&&Lt.call($,"__wrapped__");if(Q||yt){var gt=Q?v.value():v,Tt=yt?$.value():$;return R||(R=new Sn),O(gt,Tt,k,I,R)}}return!!lt&&(R||(R=new Sn),function(it,ht,_e,se,je,Qt){var Oe=1&_e,Kn=pc(it),Lr=Kn.length,Qo=pc(ht),jc=Qo.length;if(Lr!=jc&&!Oe)return!1;for(var Ko=Lr;Ko--;){var ai=Kn[Ko];if(!(Oe?ai in ht:Lt.call(ht,ai)))return!1}var Mh=Qt.get(it),Eh=Qt.get(ht);if(Mh&&Eh)return Mh==ht&&Eh==it;var Yo=!0;Qt.set(it,ht),Qt.set(ht,it);for(var qc=Oe;++Ko<Lr;){var Xo=it[ai=Kn[Ko]],Jo=ht[ai];if(se)var Ah=Oe?se(Jo,Xo,ai,ht,it,Qt):se(Xo,Jo,ai,it,ht,Qt);if(!(Ah===o?Xo===Jo||je(Xo,Jo,_e,se,Qt):Ah)){Yo=!1;break}qc||(qc=ai=="constructor")}if(Yo&&!qc){var ta=it.constructor,ea=ht.constructor;ta==ea||!("constructor"in it)||!("constructor"in ht)||typeof ta=="function"&&ta instanceof ta&&typeof ea=="function"&&ea instanceof ea||(Yo=!1)}return Qt.delete(it),Qt.delete(ht),Yo}(v,$,k,I,O,R))}(e,i,c,h,Is,f))}function tc(e,i,c,h){var f=c.length,v=f,$=!h;if(e==null)return!v;for(e=Zt(e);f--;){var k=c[f];if($&&k[2]?k[1]!==e[k[0]]:!(k[0]in e))return!1}for(;++f<v;){var I=(k=c[f])[0],O=e[I],R=k[1];if($&&k[2]){if(O===o&&!(I in e))return!1}else{var U=new Sn;if(h)var X=h(O,R,I,e,i,U);if(!(X===o?Is(R,O,3,h,U):X))return!1}}return!0}function ol(e){return!(!Jt(e)||(i=e,Pu&&Pu in i))&&(fr(e)?xf:us).test(si(e));var i}function al(e){return typeof e=="function"?e:e==null?Be:typeof e=="object"?xt(e)?ll(e[0],e[1]):ul(e):Ih(e)}function ec(e){if(!As(e))return If(e);var i=[];for(var c in Zt(e))Lt.call(e,c)&&c!="constructor"&&i.push(c);return i}function Pf(e){if(!Jt(e))return function(f){var v=[];if(f!=null)for(var $ in Zt(f))v.push($);return v}(e);var i=As(e),c=[];for(var h in e)(h!="constructor"||!i&&Lt.call(e,h))&&c.push(h);return c}function nc(e,i){return e<i}function cl(e,i){var c=-1,h=He(e)?K(e.length):[];return Ar(e,function(f,v,$){h[++c]=i(f,v,$)}),h}function ul(e){var i=_c(e);return i.length==1&&i[0][2]?Wl(i[0][0],i[0][1]):function(c){return c===e||tc(c,e,i)}}function ll(e,i){return yc(e)&&Pl(i)?Wl(Qn(e),i):function(c){var h=Ec(c,e);return h===o&&h===i?Ac(c,e):Is(i,h,3)}}function Ao(e,i,c,h,f){e!==i&&Ka(i,function(v,$){if(f||(f=new Sn),Jt(v))(function(I,O,R,U,X,tt,at){var L=xc(I,R),P=xc(O,R),lt=at.get(P);if(lt)Za(I,R,lt);else{var Q=tt?tt(L,P,R+"",I,O,at):o,yt=Q===o;if(yt){var gt=xt(P),Tt=!gt&&Or(P),it=!gt&&!Tt&&zi(P);Q=P,gt||Tt||it?xt(L)?Q=L:ie(L)?Q=We(L):Tt?(yt=!1,Q=$l(P,!0)):it?(yt=!1,Q=wl(P,!0)):Q=[]:Fs(P)||oi(P)?(Q=L,oi(L)?Q=_h(L):Jt(L)&&!fr(L)||(Q=Ul(P))):yt=!1}yt&&(at.set(P,Q),X(Q,P,U,tt,at),at.delete(P)),Za(I,R,Q)}})(e,i,$,c,Ao,h,f);else{var k=h?h(xc(e,$),v,$+"",e,i,f):o;k===o&&(k=v),Za(e,$,k)}},Ge)}function hl(e,i){var c=e.length;if(c)return dr(i+=i<0?c:0,c)?e[i]:o}function dl(e,i,c){i=i.length?Dt(i,function(v){return xt(v)?function($){return ri($,v.length===1?v[0]:v)}:v}):[Be];var h=-1;i=Dt(i,Ye(ct()));var f=cl(e,function(v,$,k){var I=Dt(i,function(O){return O(v)});return{criteria:I,index:++h,value:v}});return function(v,$){var k=v.length;for(v.sort($);k--;)v[k]=v[k].value;return v}(f,function(v,$){return function(k,I,O){for(var R=-1,U=k.criteria,X=I.criteria,tt=U.length,at=O.length;++R<tt;){var L=kl(U[R],X[R]);if(L)return R>=at?L:L*(O[R]=="desc"?-1:1)}return k.index-I.index}(v,$,c)})}function fl(e,i,c){for(var h=-1,f=i.length,v={};++h<f;){var $=i[h],k=ri(e,$);c(k,$)&&Ms(v,Fr($,e),k)}return v}function rc(e,i,c,h){var f=h?Ii:or,v=-1,$=i.length,k=e;for(e===i&&(i=We(i)),c&&(k=Dt(e,Ye(c)));++v<$;)for(var I=0,O=i[v],R=c?c(O):O;(I=f(k,R,I,h))>-1;)k!==e&&bo.call(k,I,1),bo.call(e,I,1);return e}function pl(e,i){for(var c=e?i.length:0,h=c-1;c--;){var f=i[c];if(c==h||f!==v){var v=f;dr(f)?bo.call(e,f,1):ac(e,f)}}return e}function ic(e,i){return e+wo(Zu()*(i-e+1))}function sc(e,i){var c="";if(!e||i<1||i>p)return c;do i%2&&(c+=e),(i=wo(i/2))&&(e+=e);while(i);return c}function kt(e,i){return $c(Hl(e,i,Be),e+"")}function Wf(e){return Yu(ji(e))}function Hf(e,i){var c=ji(e);return qo(c,ni(i,0,c.length))}function Ms(e,i,c,h){if(!Jt(e))return e;for(var f=-1,v=(i=Fr(i,e)).length,$=v-1,k=e;k!=null&&++f<v;){var I=Qn(i[f]),O=c;if(I==="__proto__"||I==="constructor"||I==="prototype")return e;if(f!=$){var R=k[I];(O=h?h(R,I,k):o)===o&&(O=Jt(R)?R:dr(i[f+1])?[]:{})}ks(k,I,O),k=k[I]}return e}var gl=ko?function(e,i){return ko.set(e,i),e}:Be,Gf=xo?function(e,i){return xo(e,"toString",{configurable:!0,enumerable:!1,value:Fc(i),writable:!0})}:Be;function Bf(e){return qo(ji(e))}function mn(e,i,c){var h=-1,f=e.length;i<0&&(i=-i>f?0:f+i),(c=c>f?f:c)<0&&(c+=f),f=i>c?0:c-i>>>0,i>>>=0;for(var v=K(f);++h<f;)v[h]=e[h+i];return v}function Vf(e,i){var c;return Ar(e,function(h,f,v){return!(c=i(h,f,v))}),!!c}function To(e,i,c){var h=0,f=e==null?h:e.length;if(typeof i=="number"&&i==i&&f<=2147483647){for(;h<f;){var v=h+f>>>1,$=e[v];$!==null&&!Je($)&&(c?$<=i:$<i)?h=v+1:f=v}return f}return oc(e,i,Be,c)}function oc(e,i,c,h){var f=0,v=e==null?0:e.length;if(v===0)return 0;for(var $=(i=c(i))!=i,k=i===null,I=Je(i),O=i===o;f<v;){var R=wo((f+v)/2),U=c(e[R]),X=U!==o,tt=U===null,at=U==U,L=Je(U);if($)var P=h||at;else P=O?at&&(h||X):k?at&&X&&(h||!tt):I?at&&X&&!tt&&(h||!L):!tt&&!L&&(h?U<=i:U<i);P?f=R+1:v=R}return Fe(v,4294967294)}function ml(e,i){for(var c=-1,h=e.length,f=0,v=[];++c<h;){var $=e[c],k=i?i($):$;if(!c||!In(k,I)){var I=k;v[f++]=$===0?0:$}}return v}function _l(e){return typeof e=="number"?e:Je(e)?y:+e}function Xe(e){if(typeof e=="string")return e;if(xt(e))return Dt(e,Xe)+"";if(Je(e))return Qu?Qu.call(e):"";var i=e+"";return i=="0"&&1/e==-1/0?"-0":i}function Tr(e,i,c){var h=-1,f=Gn,v=e.length,$=!0,k=[],I=k;if(c)$=!1,f=Sr;else if(v>=200){var O=i?null:Qf(e);if(O)return ho(O);$=!1,f=vs,I=new ei}else I=i?[]:k;t:for(;++h<v;){var R=e[h],U=i?i(R):R;if(R=c||R!==0?R:0,$&&U==U){for(var X=I.length;X--;)if(I[X]===U)continue t;i&&I.push(U),k.push(R)}else f(I,U,c)||(I!==k&&I.push(U),k.push(R))}return k}function ac(e,i){return(e=Gl(e,i=Fr(i,e)))==null||delete e[Qn(_n(i))]}function vl(e,i,c,h){return Ms(e,i,c(ri(e,i)),h)}function Fo(e,i,c,h){for(var f=e.length,v=h?f:-1;(h?v--:++v<f)&&i(e[v],v,e););return c?mn(e,h?0:v,h?v+1:f):mn(e,h?v+1:0,h?f:v)}function yl(e,i){var c=e;return c instanceof It&&(c=c.value()),kn(i,function(h,f){return f.func.apply(f.thisArg,Ke([h],f.args))},c)}function cc(e,i,c){var h=e.length;if(h<2)return h?Tr(e[0]):[];for(var f=-1,v=K(h);++f<h;)for(var $=e[f],k=-1;++k<h;)k!=f&&(v[f]=Cs(v[f]||$,e[k],i,c));return Tr(Ee(v,1),i,c)}function bl(e,i,c){for(var h=-1,f=e.length,v=i.length,$={};++h<f;){var k=h<v?i[h]:o;c($,e[h],k)}return $}function uc(e){return ie(e)?e:[]}function lc(e){return typeof e=="function"?e:Be}function Fr(e,i){return xt(e)?e:yc(e,i)?[e]:Ql(Ot(e))}var Zf=kt;function Rr(e,i,c){var h=e.length;return c=c===o?h:c,!i&&c>=h?e:mn(e,i,c)}var xl=$f||function(e){return Wt.clearTimeout(e)};function $l(e,i){if(i)return e.slice();var c=e.length,h=Wu?Wu(c):new e.constructor(c);return e.copy(h),h}function hc(e){var i=new e.constructor(e.byteLength);return new vo(i).set(new vo(e)),i}function wl(e,i){var c=i?hc(e.buffer):e.buffer;return new e.constructor(c,e.byteOffset,e.length)}function kl(e,i){if(e!==i){var c=e!==o,h=e===null,f=e==e,v=Je(e),$=i!==o,k=i===null,I=i==i,O=Je(i);if(!k&&!O&&!v&&e>i||v&&$&&I&&!k&&!O||h&&$&&I||!c&&I||!f)return 1;if(!h&&!v&&!O&&e<i||O&&c&&f&&!h&&!v||k&&c&&f||!$&&f||!I)return-1}return 0}function Cl(e,i,c,h){for(var f=-1,v=e.length,$=c.length,k=-1,I=i.length,O=me(v-$,0),R=K(I+O),U=!h;++k<I;)R[k]=i[k];for(;++f<$;)(U||f<v)&&(R[c[f]]=e[f]);for(;O--;)R[k++]=e[f++];return R}function Sl(e,i,c,h){for(var f=-1,v=e.length,$=-1,k=c.length,I=-1,O=i.length,R=me(v-k,0),U=K(R+O),X=!h;++f<R;)U[f]=e[f];for(var tt=f;++I<O;)U[tt+I]=i[I];for(;++$<k;)(X||f<v)&&(U[tt+c[$]]=e[f++]);return U}function We(e,i){var c=-1,h=e.length;for(i||(i=K(h));++c<h;)i[c]=e[c];return i}function Zn(e,i,c,h){var f=!c;c||(c={});for(var v=-1,$=i.length;++v<$;){var k=i[v],I=h?h(c[k],e[k],k,c,e):o;I===o&&(I=e[k]),f?ur(c,k,I):ks(c,k,I)}return c}function Ro(e,i){return function(c,h){var f=xt(c)?Hn:zf,v=i?i():{};return f(c,e,ct(h,2),v)}}function Li(e){return kt(function(i,c){var h=-1,f=c.length,v=f>1?c[f-1]:o,$=f>2?c[2]:o;for(v=e.length>3&&typeof v=="function"?(f--,v):o,$&&ze(c[0],c[1],$)&&(v=f<3?o:v,f=1),i=Zt(i);++h<f;){var k=c[h];k&&e(i,k,h,v)}return i})}function Il(e,i){return function(c,h){if(c==null)return c;if(!He(c))return e(c,h);for(var f=c.length,v=i?f:-1,$=Zt(c);(i?v--:++v<f)&&h($[v],v,$)!==!1;);return c}}function Ml(e){return function(i,c,h){for(var f=-1,v=Zt(i),$=h(i),k=$.length;k--;){var I=$[e?k:++f];if(c(v[I],I,v)===!1)break}return i}}function El(e){return function(i){var c=Mi(i=Ot(i))?Cn(i):o,h=c?c[0]:i.charAt(0),f=c?Rr(c,1).join(""):i.slice(1);return h[e]()+f}}function Ni(e){return function(i){return kn(Ch(kh(i).replace(Kr,"")),e,"")}}function Es(e){return function(){var i=arguments;switch(i.length){case 0:return new e;case 1:return new e(i[0]);case 2:return new e(i[0],i[1]);case 3:return new e(i[0],i[1],i[2]);case 4:return new e(i[0],i[1],i[2],i[3]);case 5:return new e(i[0],i[1],i[2],i[3],i[4]);case 6:return new e(i[0],i[1],i[2],i[3],i[4],i[5]);case 7:return new e(i[0],i[1],i[2],i[3],i[4],i[5],i[6])}var c=Oi(e.prototype),h=e.apply(c,i);return Jt(h)?h:c}}function Al(e){return function(i,c,h){var f=Zt(i);if(!He(i)){var v=ct(c,3);i=we(i),c=function(k){return v(f[k],k,f)}}var $=e(i,c,h);return $>-1?f[v?i[$]:$]:o}}function Tl(e){return hr(function(i){var c=i.length,h=c,f=pn.prototype.thru;for(e&&i.reverse();h--;){var v=i[h];if(typeof v!="function")throw new fn(t);if(f&&!$&&zo(v)=="wrapper")var $=new pn([],!0)}for(h=$?h:c;++h<c;){var k=zo(v=i[h]),I=k=="wrapper"?mc(v):o;$=I&&bc(I[0])&&I[1]==424&&!I[4].length&&I[9]==1?$[zo(I[0])].apply($,I[3]):v.length==1&&bc(v)?$[k]():$.thru(v)}return function(){var O=arguments,R=O[0];if($&&O.length==1&&xt(R))return $.plant(R).value();for(var U=0,X=c?i[U].apply(this,O):R;++U<c;)X=i[U].call(this,X);return X}})}function Oo(e,i,c,h,f,v,$,k,I,O){var R=i&l,U=1&i,X=2&i,tt=24&i,at=512&i,L=X?o:Es(e);return function P(){for(var lt=arguments.length,Q=K(lt),yt=lt;yt--;)Q[yt]=arguments[yt];if(tt)var gt=Di(P),Tt=function(se,je){for(var Qt=se.length,Oe=0;Qt--;)se[Qt]===je&&++Oe;return Oe}(Q,gt);if(h&&(Q=Cl(Q,h,f,tt)),v&&(Q=Sl(Q,v,$,tt)),lt-=Tt,tt&&lt<O){var it=Mr(Q,gt);return Ol(e,i,Oo,P.placeholder,c,Q,it,k,I,O-lt)}var ht=U?c:this,_e=X?ht[e]:e;return lt=Q.length,k?Q=function(se,je){for(var Qt=se.length,Oe=Fe(je.length,Qt),Kn=We(se);Oe--;){var Lr=je[Oe];se[Oe]=dr(Lr,Qt)?Kn[Lr]:o}return se}(Q,k):at&&lt>1&&Q.reverse(),R&&I<lt&&(Q.length=I),this&&this!==Wt&&this instanceof P&&(_e=L||Es(_e)),_e.apply(ht,Q)}}function Fl(e,i){return function(c,h){return function(f,v,$,k){return Vn(f,function(I,O,R){v(k,$(I),O,R)}),k}(c,e,i(h),{})}}function Lo(e,i){return function(c,h){var f;if(c===o&&h===o)return i;if(c!==o&&(f=c),h!==o){if(f===o)return h;typeof c=="string"||typeof h=="string"?(c=Xe(c),h=Xe(h)):(c=_l(c),h=_l(h)),f=e(c,h)}return f}}function dc(e){return hr(function(i){return i=Dt(i,Ye(ct())),kt(function(c){var h=this;return e(i,function(f){return $e(f,h,c)})})})}function No(e,i){var c=(i=i===o?" ":Xe(i)).length;if(c<2)return c?sc(i,e):i;var h=sc(i,$o(e/Ei(i)));return Mi(i)?Rr(Cn(h),0,e).join(""):h.slice(0,e)}function Rl(e){return function(i,c,h){return h&&typeof h!="number"&&ze(i,c,h)&&(c=h=o),i=pr(i),c===o?(c=i,i=0):c=pr(c),function(f,v,$,k){for(var I=-1,O=me($o((v-f)/($||1)),0),R=K(O);O--;)R[k?O:++I]=f,f+=$;return R}(i,c,h=h===o?i<c?1:-1:pr(h),e)}}function Do(e){return function(i,c){return typeof i=="string"&&typeof c=="string"||(i=vn(i),c=vn(c)),e(i,c)}}function Ol(e,i,c,h,f,v,$,k,I,O){var R=8&i;i|=R?a:u,4&(i&=~(R?u:a))||(i&=-4);var U=[e,i,f,R?v:o,R?$:o,R?o:v,R?o:$,k,I,O],X=c.apply(o,U);return bc(e)&&Bl(X,U),X.placeholder=h,Vl(X,e,i)}function fc(e){var i=Bn[e];return function(c,h){if(c=vn(c),(h=h==null?0:Fe(wt(h),292))&&Vu(c)){var f=(Ot(c)+"e").split("e");return+((f=(Ot(i(f[0]+"e"+(+f[1]+h)))+"e").split("e"))[0]+"e"+(+f[1]-h))}return i(c)}}var Qf=Fi&&1/ho(new Fi([,-0]))[1]==m?function(e){return new Fi(e)}:Lc;function Ll(e){return function(i){var c=Re(i);return c==st?Wa(i):c==Ft?pf(i):function(h,f){return Dt(f,function(v){return[v,h[v]]})}(i,e(i))}}function lr(e,i,c,h,f,v,$,k){var I=2&i;if(!I&&typeof e!="function")throw new fn(t);var O=h?h.length:0;if(O||(i&=-97,h=f=o),$=$===o?$:me(wt($),0),k=k===o?k:wt(k),O-=f?f.length:0,i&u){var R=h,U=f;h=f=o}var X=I?o:mc(e),tt=[e,i,c,h,f,R,U,v,$,k];if(X&&function(L,P){var lt=L[1],Q=P[1],yt=lt|Q,gt=yt<131,Tt=Q==l&&lt==8||Q==l&&lt==d&&L[7].length<=P[8]||Q==384&&P[7].length<=P[8]&&lt==8;if(!gt&&!Tt)return L;1&Q&&(L[2]=P[2],yt|=1&lt?0:4);var it=P[3];if(it){var ht=L[3];L[3]=ht?Cl(ht,it,P[4]):it,L[4]=ht?Mr(L[3],r):P[4]}(it=P[5])&&(ht=L[5],L[5]=ht?Sl(ht,it,P[6]):it,L[6]=ht?Mr(L[5],r):P[6]),(it=P[7])&&(L[7]=it),Q&l&&(L[8]=L[8]==null?P[8]:Fe(L[8],P[8])),L[9]==null&&(L[9]=P[9]),L[0]=P[0],L[1]=yt}(tt,X),e=tt[0],i=tt[1],c=tt[2],h=tt[3],f=tt[4],!(k=tt[9]=tt[9]===o?I?0:e.length:me(tt[9]-O,0))&&24&i&&(i&=-25),i&&i!=1)at=i==8||i==s?function(L,P,lt){var Q=Es(L);return function yt(){for(var gt=arguments.length,Tt=K(gt),it=gt,ht=Di(yt);it--;)Tt[it]=arguments[it];var _e=gt<3&&Tt[0]!==ht&&Tt[gt-1]!==ht?[]:Mr(Tt,ht);return(gt-=_e.length)<lt?Ol(L,P,Oo,yt.placeholder,o,Tt,_e,o,o,lt-gt):$e(this&&this!==Wt&&this instanceof yt?Q:L,this,Tt)}}(e,i,k):i!=a&&i!=33||f.length?Oo.apply(o,tt):function(L,P,lt,Q){var yt=1&P,gt=Es(L);return function Tt(){for(var it=-1,ht=arguments.length,_e=-1,se=Q.length,je=K(se+ht),Qt=this&&this!==Wt&&this instanceof Tt?gt:L;++_e<se;)je[_e]=Q[_e];for(;ht--;)je[_e++]=arguments[++it];return $e(Qt,yt?lt:this,je)}}(e,i,c,h);else var at=function(L,P,lt){var Q=1&P,yt=Es(L);return function gt(){return(this&&this!==Wt&&this instanceof gt?yt:L).apply(Q?lt:this,arguments)}}(e,i,c);return Vl((X?gl:Bl)(at,tt),e,i)}function Nl(e,i,c,h){return e===o||In(e,Ti[c])&&!Lt.call(h,c)?i:e}function Dl(e,i,c,h,f,v){return Jt(e)&&Jt(i)&&(v.set(i,e),Ao(e,i,o,Dl,v),v.delete(i)),e}function Kf(e){return Fs(e)?o:e}function zl(e,i,c,h,f,v){var $=1&c,k=e.length,I=i.length;if(k!=I&&!($&&I>k))return!1;var O=v.get(e),R=v.get(i);if(O&&R)return O==i&&R==e;var U=-1,X=!0,tt=2&c?new ei:o;for(v.set(e,i),v.set(i,e);++U<k;){var at=e[U],L=i[U];if(h)var P=$?h(L,at,U,i,e,v):h(at,L,U,e,i,v);if(P!==o){if(P)continue;X=!1;break}if(tt){if(!Ir(i,function(lt,Q){if(!vs(tt,Q)&&(at===lt||f(at,lt,c,h,v)))return tt.push(Q)})){X=!1;break}}else if(at!==L&&!f(at,L,c,h,v)){X=!1;break}}return v.delete(e),v.delete(i),X}function hr(e){return $c(Hl(e,o,Jl),e+"")}function pc(e){return il(e,we,vc)}function gc(e){return il(e,Ge,jl)}var mc=ko?function(e){return ko.get(e)}:Lc;function zo(e){for(var i=e.name+"",c=Ri[i],h=Lt.call(Ri,i)?c.length:0;h--;){var f=c[h],v=f.func;if(v==null||v==e)return f.name}return i}function Di(e){return(Lt.call(g,"placeholder")?g:e).placeholder}function ct(){var e=g.iteratee||Rc;return e=e===Rc?al:e,arguments.length?e(arguments[0],arguments[1]):e}function jo(e,i){var c,h,f=e.__data__;return((h=typeof(c=i))=="string"||h=="number"||h=="symbol"||h=="boolean"?c!=="__proto__":c===null)?f[typeof i=="string"?"string":"hash"]:f.map}function _c(e){for(var i=we(e),c=i.length;c--;){var h=i[c],f=e[h];i[c]=[h,f,Pl(f)]}return i}function ii(e,i){var c=function(h,f){return h==null?o:h[f]}(e,i);return ol(c)?c:o}var vc=Ga?function(e){return e==null?[]:(e=Zt(e),Me(Ga(e),function(i){return Gu.call(e,i)}))}:Nc,jl=Ga?function(e){for(var i=[];e;)Ke(i,vc(e)),e=yo(e);return i}:Nc,Re=De;function ql(e,i,c){for(var h=-1,f=(i=Fr(i,e)).length,v=!1;++h<f;){var $=Qn(i[h]);if(!(v=e!=null&&c(e,$)))break;e=e[$]}return v||++h!=f?v:!!(f=e==null?0:e.length)&&Bo(f)&&dr($,f)&&(xt(e)||oi(e))}function Ul(e){return typeof e.constructor!="function"||As(e)?{}:Oi(yo(e))}function Yf(e){return xt(e)||oi(e)||!!(Bu&&e&&e[Bu])}function dr(e,i){var c=typeof e;return!!(i=i??p)&&(c=="number"||c!="symbol"&&Oa.test(e))&&e>-1&&e%1==0&&e<i}function ze(e,i,c){if(!Jt(c))return!1;var h=typeof i;return!!(h=="number"?He(c)&&dr(i,c.length):h=="string"&&i in c)&&In(c[i],e)}function yc(e,i){if(xt(e))return!1;var c=typeof e;return!(c!="number"&&c!="symbol"&&c!="boolean"&&e!=null&&!Je(e))||Zs.test(e)||!un.test(e)||i!=null&&e in Zt(i)}function bc(e){var i=zo(e),c=g[i];if(typeof c!="function"||!(i in It.prototype))return!1;if(e===c)return!0;var h=mc(c);return!!h&&e===h[0]}(Ba&&Re(new Ba(new ArrayBuffer(1)))!=de||bs&&Re(new bs)!=st||Va&&Re(Va.resolve())!=St||Fi&&Re(new Fi)!=Ft||xs&&Re(new xs)!=Ct)&&(Re=function(e){var i=De(e),c=i==pt?e.constructor:o,h=c?si(c):"";if(h)switch(h){case Tf:return de;case Ff:return st;case Rf:return St;case Of:return Ft;case Lf:return Ct}return i});var Xf=po?fr:Dc;function As(e){var i=e&&e.constructor;return e===(typeof i=="function"&&i.prototype||Ti)}function Pl(e){return e==e&&!Jt(e)}function Wl(e,i){return function(c){return c!=null&&c[e]===i&&(i!==o||e in Zt(c))}}function Hl(e,i,c){return i=me(i===o?e.length-1:i,0),function(){for(var h=arguments,f=-1,v=me(h.length-i,0),$=K(v);++f<v;)$[f]=h[i+f];f=-1;for(var k=K(i+1);++f<i;)k[f]=h[f];return k[i]=c($),$e(e,this,k)}}function Gl(e,i){return i.length<2?e:ri(e,mn(i,0,-1))}function xc(e,i){if((i!=="constructor"||typeof e[i]!="function")&&i!="__proto__")return e[i]}var Bl=Zl(gl),Ts=kf||function(e,i){return Wt.setTimeout(e,i)},$c=Zl(Gf);function Vl(e,i,c){var h=i+"";return $c(e,function(f,v){var $=v.length;if(!$)return f;var k=$-1;return v[k]=($>1?"& ":"")+v[k],v=v.join($>2?", ":" "),f.replace(Xs,`{
/* [wrapped with `+v+`] */
`)}(h,function(f,v){return Te(w,function($){var k="_."+$[0];v&$[1]&&!Gn(f,k)&&f.push(k)}),f.sort()}(function(f){var v=f.match(Ta);return v?v[1].split(nr):[]}(h),c)))}function Zl(e){var i=0,c=0;return function(){var h=Mf(),f=16-(h-c);if(c=h,f>0){if(++i>=800)return arguments[0]}else i=0;return e.apply(o,arguments)}}function qo(e,i){var c=-1,h=e.length,f=h-1;for(i=i===o?h:i;++c<i;){var v=ic(c,f),$=e[v];e[v]=e[c],e[c]=$}return e.length=i,e}var Ql=function(e){var i=Ho(e,function(h){return c.size===500&&c.clear(),h}),c=i.cache;return i}(function(e){var i=[];return e.charCodeAt(0)===46&&i.push(""),e.replace(Qs,function(c,h,f,v){i.push(f?v.replace(Ra,"$1"):h||c)}),i});function Qn(e){if(typeof e=="string"||Je(e))return e;var i=e+"";return i=="0"&&1/e==-1/0?"-0":i}function si(e){if(e!=null){try{return go.call(e)}catch{}try{return e+""}catch{}}return""}function Kl(e){if(e instanceof It)return e.clone();var i=new pn(e.__wrapped__,e.__chain__);return i.__actions__=We(e.__actions__),i.__index__=e.__index__,i.__values__=e.__values__,i}var Jf=kt(function(e,i){return ie(e)?Cs(e,Ee(i,1,ie,!0)):[]}),tp=kt(function(e,i){var c=_n(i);return ie(c)&&(c=o),ie(e)?Cs(e,Ee(i,1,ie,!0),ct(c,2)):[]}),ep=kt(function(e,i){var c=_n(i);return ie(c)&&(c=o),ie(e)?Cs(e,Ee(i,1,ie,!0),o,c):[]});function Yl(e,i,c){var h=e==null?0:e.length;if(!h)return-1;var f=c==null?0:wt(c);return f<0&&(f=me(h+f,0)),sr(e,ct(i,3),f)}function Xl(e,i,c){var h=e==null?0:e.length;if(!h)return-1;var f=h-1;return c!==o&&(f=wt(c),f=c<0?me(h+f,0):Fe(f,h-1)),sr(e,ct(i,3),f,!0)}function Jl(e){return e!=null&&e.length?Ee(e,1):[]}function th(e){return e&&e.length?e[0]:o}var np=kt(function(e){var i=Dt(e,uc);return i.length&&i[0]===e[0]?Ja(i):[]}),rp=kt(function(e){var i=_n(e),c=Dt(e,uc);return i===_n(c)?i=o:c.pop(),c.length&&c[0]===e[0]?Ja(c,ct(i,2)):[]}),ip=kt(function(e){var i=_n(e),c=Dt(e,uc);return(i=typeof i=="function"?i:o)&&c.pop(),c.length&&c[0]===e[0]?Ja(c,o,i):[]});function _n(e){var i=e==null?0:e.length;return i?e[i-1]:o}var sp=kt(eh);function eh(e,i){return e&&e.length&&i&&i.length?rc(e,i):e}var op=hr(function(e,i){var c=e==null?0:e.length,h=Qa(e,i);return pl(e,Dt(i,function(f){return dr(f,c)?+f:f}).sort(kl)),h});function wc(e){return e==null?e:Af.call(e)}var ap=kt(function(e){return Tr(Ee(e,1,ie,!0))}),cp=kt(function(e){var i=_n(e);return ie(i)&&(i=o),Tr(Ee(e,1,ie,!0),ct(i,2))}),up=kt(function(e){var i=_n(e);return i=typeof i=="function"?i:o,Tr(Ee(e,1,ie,!0),o,i)});function kc(e){if(!e||!e.length)return[];var i=0;return e=Me(e,function(c){if(ie(c))return i=me(c.length,i),!0}),Ua(i,function(c){return Dt(e,za(c))})}function nh(e,i){if(!e||!e.length)return[];var c=kc(e);return i==null?c:Dt(c,function(h){return $e(i,o,h)})}var lp=kt(function(e,i){return ie(e)?Cs(e,i):[]}),hp=kt(function(e){return cc(Me(e,ie))}),dp=kt(function(e){var i=_n(e);return ie(i)&&(i=o),cc(Me(e,ie),ct(i,2))}),fp=kt(function(e){var i=_n(e);return i=typeof i=="function"?i:o,cc(Me(e,ie),o,i)}),pp=kt(kc),gp=kt(function(e){var i=e.length,c=i>1?e[i-1]:o;return c=typeof c=="function"?(e.pop(),c):o,nh(e,c)});function rh(e){var i=g(e);return i.__chain__=!0,i}function Uo(e,i){return i(e)}var mp=hr(function(e){var i=e.length,c=i?e[0]:0,h=this.__wrapped__,f=function(v){return Qa(v,e)};return!(i>1||this.__actions__.length)&&h instanceof It&&dr(c)?((h=h.slice(c,+c+(i?1:0))).__actions__.push({func:Uo,args:[f],thisArg:o}),new pn(h,this.__chain__).thru(function(v){return i&&!v.length&&v.push(o),v})):this.thru(f)}),_p=Ro(function(e,i,c){Lt.call(e,c)?++e[c]:ur(e,c,1)}),vp=Al(Yl),yp=Al(Xl);function ih(e,i){return(xt(e)?Te:Ar)(e,ct(i,3))}function sh(e,i){return(xt(e)?Pe:el)(e,ct(i,3))}var bp=Ro(function(e,i,c){Lt.call(e,c)?e[c].push(i):ur(e,c,[i])}),xp=kt(function(e,i,c){var h=-1,f=typeof i=="function",v=He(e)?K(e.length):[];return Ar(e,function($){v[++h]=f?$e(i,$,c):Ss($,i,c)}),v}),$p=Ro(function(e,i,c){ur(e,c,i)});function Po(e,i){return(xt(e)?Dt:cl)(e,ct(i,3))}var wp=Ro(function(e,i,c){e[c?0:1].push(i)},function(){return[[],[]]}),kp=kt(function(e,i){if(e==null)return[];var c=i.length;return c>1&&ze(e,i[0],i[1])?i=[]:c>2&&ze(i[0],i[1],i[2])&&(i=[i[0]]),dl(e,Ee(i,1),[])}),Wo=wf||function(){return Wt.Date.now()};function oh(e,i,c){return i=c?o:i,i=e&&i==null?e.length:i,lr(e,l,o,o,o,o,i)}function ah(e,i){var c;if(typeof i!="function")throw new fn(t);return e=wt(e),function(){return--e>0&&(c=i.apply(this,arguments)),e<=1&&(i=o),c}}var Cc=kt(function(e,i,c){var h=1;if(c.length){var f=Mr(c,Di(Cc));h|=a}return lr(e,h,i,c,f)}),ch=kt(function(e,i,c){var h=3;if(c.length){var f=Mr(c,Di(ch));h|=a}return lr(i,h,e,c,f)});function uh(e,i,c){var h,f,v,$,k,I,O=0,R=!1,U=!1,X=!0;if(typeof e!="function")throw new fn(t);function tt(Q){var yt=h,gt=f;return h=f=o,O=Q,$=e.apply(gt,yt)}function at(Q){var yt=Q-I;return I===o||yt>=i||yt<0||U&&Q-O>=v}function L(){var Q=Wo();if(at(Q))return P(Q);k=Ts(L,function(yt){var gt=i-(yt-I);return U?Fe(gt,v-(yt-O)):gt}(Q))}function P(Q){return k=o,X&&h?tt(Q):(h=f=o,$)}function lt(){var Q=Wo(),yt=at(Q);if(h=arguments,f=this,I=Q,yt){if(k===o)return function(gt){return O=gt,k=Ts(L,i),R?tt(gt):$}(I);if(U)return xl(k),k=Ts(L,i),tt(I)}return k===o&&(k=Ts(L,i)),$}return i=vn(i)||0,Jt(c)&&(R=!!c.leading,v=(U="maxWait"in c)?me(vn(c.maxWait)||0,i):v,X="trailing"in c?!!c.trailing:X),lt.cancel=function(){k!==o&&xl(k),O=0,h=I=f=k=o},lt.flush=function(){return k===o?$:P(Wo())},lt}var Cp=kt(function(e,i){return tl(e,1,i)}),Sp=kt(function(e,i,c){return tl(e,vn(i)||0,c)});function Ho(e,i){if(typeof e!="function"||i!=null&&typeof i!="function")throw new fn(t);var c=function(){var h=arguments,f=i?i.apply(this,h):h[0],v=c.cache;if(v.has(f))return v.get(f);var $=e.apply(this,h);return c.cache=v.set(f,$)||v,$};return c.cache=new(Ho.Cache||cr),c}function Go(e){if(typeof e!="function")throw new fn(t);return function(){var i=arguments;switch(i.length){case 0:return!e.call(this);case 1:return!e.call(this,i[0]);case 2:return!e.call(this,i[0],i[1]);case 3:return!e.call(this,i[0],i[1],i[2])}return!e.apply(this,i)}}Ho.Cache=cr;var Ip=Zf(function(e,i){var c=(i=i.length==1&&xt(i[0])?Dt(i[0],Ye(ct())):Dt(Ee(i,1),Ye(ct()))).length;return kt(function(h){for(var f=-1,v=Fe(h.length,c);++f<v;)h[f]=i[f].call(this,h[f]);return $e(e,this,h)})}),Sc=kt(function(e,i){var c=Mr(i,Di(Sc));return lr(e,a,o,i,c)}),lh=kt(function(e,i){var c=Mr(i,Di(lh));return lr(e,u,o,i,c)}),Mp=hr(function(e,i){return lr(e,d,o,o,o,i)});function In(e,i){return e===i||e!=e&&i!=i}var Ep=Do(Xa),Ap=Do(function(e,i){return e>=i}),oi=sl(function(){return arguments}())?sl:function(e){return ne(e)&&Lt.call(e,"callee")&&!Gu.call(e,"callee")},xt=K.isArray,Tp=Cr?Ye(Cr):function(e){return ne(e)&&De(e)==$t};function He(e){return e!=null&&Bo(e.length)&&!fr(e)}function ie(e){return ne(e)&&He(e)}var Or=Cf||Dc,Fp=ms?Ye(ms):function(e){return ne(e)&&De(e)==S};function Ic(e){if(!ne(e))return!1;var i=De(e);return i==E||i=="[object DOMException]"||typeof e.message=="string"&&typeof e.name=="string"&&!Fs(e)}function fr(e){if(!Jt(e))return!1;var i=De(e);return i==G||i==ot||i=="[object AsyncFunction]"||i=="[object Proxy]"}function hh(e){return typeof e=="number"&&e==wt(e)}function Bo(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=p}function Jt(e){var i=typeof e;return e!=null&&(i=="object"||i=="function")}function ne(e){return e!=null&&typeof e=="object"}var dh=co?Ye(co):function(e){return ne(e)&&Re(e)==st};function fh(e){return typeof e=="number"||ne(e)&&De(e)==Nt}function Fs(e){if(!ne(e)||De(e)!=pt)return!1;var i=yo(e);if(i===null)return!0;var c=Lt.call(i,"constructor")&&i.constructor;return typeof c=="function"&&c instanceof c&&go.call(c)==yf}var Mc=uo?Ye(uo):function(e){return ne(e)&&De(e)==mt},ph=Rt?Ye(Rt):function(e){return ne(e)&&Re(e)==Ft};function Vo(e){return typeof e=="string"||!xt(e)&&ne(e)&&De(e)==Gt}function Je(e){return typeof e=="symbol"||ne(e)&&De(e)==ue}var zi=At?Ye(At):function(e){return ne(e)&&Bo(e.length)&&!!J[De(e)]},Rp=Do(nc),Op=Do(function(e,i){return e<=i});function gh(e){if(!e)return[];if(He(e))return Vo(e)?Cn(e):We(e);if(ys&&e[ys])return function(c){for(var h,f=[];!(h=c.next()).done;)f.push(h.value);return f}(e[ys]());var i=Re(e);return(i==st?Wa:i==Ft?ho:ji)(e)}function pr(e){return e?(e=vn(e))===m||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:e===0?e:0}function wt(e){var i=pr(e),c=i%1;return i==i?c?i-c:i:0}function mh(e){return e?ni(wt(e),0,b):0}function vn(e){if(typeof e=="number")return e;if(Je(e))return y;if(Jt(e)){var i=typeof e.valueOf=="function"?e.valueOf():e;e=Jt(i)?i+"":i}if(typeof e!="string")return e===0?e:+e;e=Du(e);var c=cs.test(e);return c||Br.test(e)?re(e.slice(2),c?2:8):as.test(e)?y:+e}function _h(e){return Zn(e,Ge(e))}function Ot(e){return e==null?"":Xe(e)}var Lp=Li(function(e,i){if(As(i)||He(i))Zn(i,we(i),e);else for(var c in i)Lt.call(i,c)&&ks(e,c,i[c])}),vh=Li(function(e,i){Zn(i,Ge(i),e)}),Zo=Li(function(e,i,c,h){Zn(i,Ge(i),e,h)}),Np=Li(function(e,i,c,h){Zn(i,we(i),e,h)}),Dp=hr(Qa),zp=kt(function(e,i){e=Zt(e);var c=-1,h=i.length,f=h>2?i[2]:o;for(f&&ze(i[0],i[1],f)&&(h=1);++c<h;)for(var v=i[c],$=Ge(v),k=-1,I=$.length;++k<I;){var O=$[k],R=e[O];(R===o||In(R,Ti[O])&&!Lt.call(e,O))&&(e[O]=v[O])}return e}),jp=kt(function(e){return e.push(o,Dl),$e(yh,o,e)});function Ec(e,i,c){var h=e==null?o:ri(e,i);return h===o?c:h}function Ac(e,i){return e!=null&&ql(e,i,Uf)}var qp=Fl(function(e,i,c){i!=null&&typeof i.toString!="function"&&(i=mo.call(i)),e[i]=c},Fc(Be)),Up=Fl(function(e,i,c){i!=null&&typeof i.toString!="function"&&(i=mo.call(i)),Lt.call(e,i)?e[i].push(c):e[i]=[c]},ct),Pp=kt(Ss);function we(e){return He(e)?Ku(e):ec(e)}function Ge(e){return He(e)?Ku(e,!0):Pf(e)}var Wp=Li(function(e,i,c){Ao(e,i,c)}),yh=Li(function(e,i,c,h){Ao(e,i,c,h)}),Hp=hr(function(e,i){var c={};if(e==null)return c;var h=!1;i=Dt(i,function(v){return v=Fr(v,e),h||(h=v.length>1),v}),Zn(e,gc(e),c),h&&(c=gn(c,7,Kf));for(var f=i.length;f--;)ac(c,i[f]);return c}),Gp=hr(function(e,i){return e==null?{}:function(c,h){return fl(c,h,function(f,v){return Ac(c,v)})}(e,i)});function bh(e,i){if(e==null)return{};var c=Dt(gc(e),function(h){return[h]});return i=ct(i),fl(e,c,function(h,f){return i(h,f[0])})}var xh=Ll(we),$h=Ll(Ge);function ji(e){return e==null?[]:Pa(e,we(e))}var Bp=Ni(function(e,i,c){return i=i.toLowerCase(),e+(c?wh(i):i)});function wh(e){return Tc(Ot(e).toLowerCase())}function kh(e){return(e=Ot(e))&&e.replace(La,hf).replace(ao,"")}var Vp=Ni(function(e,i,c){return e+(c?"-":"")+i.toLowerCase()}),Zp=Ni(function(e,i,c){return e+(c?" ":"")+i.toLowerCase()}),Qp=El("toLowerCase"),Kp=Ni(function(e,i,c){return e+(c?"_":"")+i.toLowerCase()}),Yp=Ni(function(e,i,c){return e+(c?" ":"")+Tc(i)}),Xp=Ni(function(e,i,c){return e+(c?" ":"")+i.toUpperCase()}),Tc=El("toUpperCase");function Ch(e,i,c){return e=Ot(e),(i=c?o:i)===o?function(h){return W.test(h)}(e)?function(h){return h.match(C)||[]}(e):function(h){return h.match(Js)||[]}(e):e.match(i)||[]}var Sh=kt(function(e,i){try{return $e(e,o,i)}catch(c){return Ic(c)?c:new Mt(c)}}),Jp=hr(function(e,i){return Te(i,function(c){c=Qn(c),ur(e,c,Cc(e[c],e))}),e});function Fc(e){return function(){return e}}var tg=Tl(),eg=Tl(!0);function Be(e){return e}function Rc(e){return al(typeof e=="function"?e:gn(e,1))}var ng=kt(function(e,i){return function(c){return Ss(c,e,i)}}),rg=kt(function(e,i){return function(c){return Ss(e,c,i)}});function Oc(e,i,c){var h=we(i),f=Eo(i,h);c!=null||Jt(i)&&(f.length||!h.length)||(c=i,i=e,e=this,f=Eo(i,we(i)));var v=!(Jt(c)&&"chain"in c&&!c.chain),$=fr(e);return Te(f,function(k){var I=i[k];e[k]=I,$&&(e.prototype[k]=function(){var O=this.__chain__;if(v||O){var R=e(this.__wrapped__);return(R.__actions__=We(this.__actions__)).push({func:I,args:arguments,thisArg:e}),R.__chain__=O,R}return I.apply(e,Ke([this.value()],arguments))})}),e}function Lc(){}var ig=dc(Dt),sg=dc(dn),og=dc(Ir);function Ih(e){return yc(e)?za(Qn(e)):function(i){return function(c){return ri(c,i)}}(e)}var ag=Rl(),cg=Rl(!0);function Nc(){return[]}function Dc(){return!1}var zc,ug=Lo(function(e,i){return e+i},0),lg=fc("ceil"),hg=Lo(function(e,i){return e/i},1),dg=fc("floor"),fg=Lo(function(e,i){return e*i},1),pg=fc("round"),gg=Lo(function(e,i){return e-i},0);return g.after=function(e,i){if(typeof i!="function")throw new fn(t);return e=wt(e),function(){if(--e<1)return i.apply(this,arguments)}},g.ary=oh,g.assign=Lp,g.assignIn=vh,g.assignInWith=Zo,g.assignWith=Np,g.at=Dp,g.before=ah,g.bind=Cc,g.bindAll=Jp,g.bindKey=ch,g.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return xt(e)?e:[e]},g.chain=rh,g.chunk=function(e,i,c){i=(c?ze(e,i,c):i===o)?1:me(wt(i),0);var h=e==null?0:e.length;if(!h||i<1)return[];for(var f=0,v=0,$=K($o(h/i));f<h;)$[v++]=mn(e,f,f+=i);return $},g.compact=function(e){for(var i=-1,c=e==null?0:e.length,h=0,f=[];++i<c;){var v=e[i];v&&(f[h++]=v)}return f},g.concat=function(){var e=arguments.length;if(!e)return[];for(var i=K(e-1),c=arguments[0],h=e;h--;)i[h-1]=arguments[h];return Ke(xt(c)?We(c):[c],Ee(i,1))},g.cond=function(e){var i=e==null?0:e.length,c=ct();return e=i?Dt(e,function(h){if(typeof h[1]!="function")throw new fn(t);return[c(h[0]),h[1]]}):[],kt(function(h){for(var f=-1;++f<i;){var v=e[f];if($e(v[0],this,h))return $e(v[1],this,h)}})},g.conforms=function(e){return function(i){var c=we(i);return function(h){return Ju(h,i,c)}}(gn(e,1))},g.constant=Fc,g.countBy=_p,g.create=function(e,i){var c=Oi(e);return i==null?c:Xu(c,i)},g.curry=function e(i,c,h){var f=lr(i,8,o,o,o,o,o,c=h?o:c);return f.placeholder=e.placeholder,f},g.curryRight=function e(i,c,h){var f=lr(i,s,o,o,o,o,o,c=h?o:c);return f.placeholder=e.placeholder,f},g.debounce=uh,g.defaults=zp,g.defaultsDeep=jp,g.defer=Cp,g.delay=Sp,g.difference=Jf,g.differenceBy=tp,g.differenceWith=ep,g.drop=function(e,i,c){var h=e==null?0:e.length;return h?mn(e,(i=c||i===o?1:wt(i))<0?0:i,h):[]},g.dropRight=function(e,i,c){var h=e==null?0:e.length;return h?mn(e,0,(i=h-(i=c||i===o?1:wt(i)))<0?0:i):[]},g.dropRightWhile=function(e,i){return e&&e.length?Fo(e,ct(i,3),!0,!0):[]},g.dropWhile=function(e,i){return e&&e.length?Fo(e,ct(i,3),!0):[]},g.fill=function(e,i,c,h){var f=e==null?0:e.length;return f?(c&&typeof c!="number"&&ze(e,i,c)&&(c=0,h=f),function(v,$,k,I){var O=v.length;for((k=wt(k))<0&&(k=-k>O?0:O+k),(I=I===o||I>O?O:wt(I))<0&&(I+=O),I=k>I?0:mh(I);k<I;)v[k++]=$;return v}(e,i,c,h)):[]},g.filter=function(e,i){return(xt(e)?Me:nl)(e,ct(i,3))},g.flatMap=function(e,i){return Ee(Po(e,i),1)},g.flatMapDeep=function(e,i){return Ee(Po(e,i),m)},g.flatMapDepth=function(e,i,c){return c=c===o?1:wt(c),Ee(Po(e,i),c)},g.flatten=Jl,g.flattenDeep=function(e){return e!=null&&e.length?Ee(e,m):[]},g.flattenDepth=function(e,i){return e!=null&&e.length?Ee(e,i=i===o?1:wt(i)):[]},g.flip=function(e){return lr(e,512)},g.flow=tg,g.flowRight=eg,g.fromPairs=function(e){for(var i=-1,c=e==null?0:e.length,h={};++i<c;){var f=e[i];h[f[0]]=f[1]}return h},g.functions=function(e){return e==null?[]:Eo(e,we(e))},g.functionsIn=function(e){return e==null?[]:Eo(e,Ge(e))},g.groupBy=bp,g.initial=function(e){return e!=null&&e.length?mn(e,0,-1):[]},g.intersection=np,g.intersectionBy=rp,g.intersectionWith=ip,g.invert=qp,g.invertBy=Up,g.invokeMap=xp,g.iteratee=Rc,g.keyBy=$p,g.keys=we,g.keysIn=Ge,g.map=Po,g.mapKeys=function(e,i){var c={};return i=ct(i,3),Vn(e,function(h,f,v){ur(c,i(h,f,v),h)}),c},g.mapValues=function(e,i){var c={};return i=ct(i,3),Vn(e,function(h,f,v){ur(c,f,i(h,f,v))}),c},g.matches=function(e){return ul(gn(e,1))},g.matchesProperty=function(e,i){return ll(e,gn(i,1))},g.memoize=Ho,g.merge=Wp,g.mergeWith=yh,g.method=ng,g.methodOf=rg,g.mixin=Oc,g.negate=Go,g.nthArg=function(e){return e=wt(e),kt(function(i){return hl(i,e)})},g.omit=Hp,g.omitBy=function(e,i){return bh(e,Go(ct(i)))},g.once=function(e){return ah(2,e)},g.orderBy=function(e,i,c,h){return e==null?[]:(xt(i)||(i=i==null?[]:[i]),xt(c=h?o:c)||(c=c==null?[]:[c]),dl(e,i,c))},g.over=ig,g.overArgs=Ip,g.overEvery=sg,g.overSome=og,g.partial=Sc,g.partialRight=lh,g.partition=wp,g.pick=Gp,g.pickBy=bh,g.property=Ih,g.propertyOf=function(e){return function(i){return e==null?o:ri(e,i)}},g.pull=sp,g.pullAll=eh,g.pullAllBy=function(e,i,c){return e&&e.length&&i&&i.length?rc(e,i,ct(c,2)):e},g.pullAllWith=function(e,i,c){return e&&e.length&&i&&i.length?rc(e,i,o,c):e},g.pullAt=op,g.range=ag,g.rangeRight=cg,g.rearg=Mp,g.reject=function(e,i){return(xt(e)?Me:nl)(e,Go(ct(i,3)))},g.remove=function(e,i){var c=[];if(!e||!e.length)return c;var h=-1,f=[],v=e.length;for(i=ct(i,3);++h<v;){var $=e[h];i($,h,e)&&(c.push($),f.push(h))}return pl(e,f),c},g.rest=function(e,i){if(typeof e!="function")throw new fn(t);return kt(e,i=i===o?i:wt(i))},g.reverse=wc,g.sampleSize=function(e,i,c){return i=(c?ze(e,i,c):i===o)?1:wt(i),(xt(e)?Nf:Hf)(e,i)},g.set=function(e,i,c){return e==null?e:Ms(e,i,c)},g.setWith=function(e,i,c,h){return h=typeof h=="function"?h:o,e==null?e:Ms(e,i,c,h)},g.shuffle=function(e){return(xt(e)?Df:Bf)(e)},g.slice=function(e,i,c){var h=e==null?0:e.length;return h?(c&&typeof c!="number"&&ze(e,i,c)?(i=0,c=h):(i=i==null?0:wt(i),c=c===o?h:wt(c)),mn(e,i,c)):[]},g.sortBy=kp,g.sortedUniq=function(e){return e&&e.length?ml(e):[]},g.sortedUniqBy=function(e,i){return e&&e.length?ml(e,ct(i,2)):[]},g.split=function(e,i,c){return c&&typeof c!="number"&&ze(e,i,c)&&(i=c=o),(c=c===o?b:c>>>0)?(e=Ot(e))&&(typeof i=="string"||i!=null&&!Mc(i))&&!(i=Xe(i))&&Mi(e)?Rr(Cn(e),0,c):e.split(i,c):[]},g.spread=function(e,i){if(typeof e!="function")throw new fn(t);return i=i==null?0:me(wt(i),0),kt(function(c){var h=c[i],f=Rr(c,0,i);return h&&Ke(f,h),$e(e,this,f)})},g.tail=function(e){var i=e==null?0:e.length;return i?mn(e,1,i):[]},g.take=function(e,i,c){return e&&e.length?mn(e,0,(i=c||i===o?1:wt(i))<0?0:i):[]},g.takeRight=function(e,i,c){var h=e==null?0:e.length;return h?mn(e,(i=h-(i=c||i===o?1:wt(i)))<0?0:i,h):[]},g.takeRightWhile=function(e,i){return e&&e.length?Fo(e,ct(i,3),!1,!0):[]},g.takeWhile=function(e,i){return e&&e.length?Fo(e,ct(i,3)):[]},g.tap=function(e,i){return i(e),e},g.throttle=function(e,i,c){var h=!0,f=!0;if(typeof e!="function")throw new fn(t);return Jt(c)&&(h="leading"in c?!!c.leading:h,f="trailing"in c?!!c.trailing:f),uh(e,i,{leading:h,maxWait:i,trailing:f})},g.thru=Uo,g.toArray=gh,g.toPairs=xh,g.toPairsIn=$h,g.toPath=function(e){return xt(e)?Dt(e,Qn):Je(e)?[e]:We(Ql(Ot(e)))},g.toPlainObject=_h,g.transform=function(e,i,c){var h=xt(e),f=h||Or(e)||zi(e);if(i=ct(i,4),c==null){var v=e&&e.constructor;c=f?h?new v:[]:Jt(e)&&fr(v)?Oi(yo(e)):{}}return(f?Te:Vn)(e,function($,k,I){return i(c,$,k,I)}),c},g.unary=function(e){return oh(e,1)},g.union=ap,g.unionBy=cp,g.unionWith=up,g.uniq=function(e){return e&&e.length?Tr(e):[]},g.uniqBy=function(e,i){return e&&e.length?Tr(e,ct(i,2)):[]},g.uniqWith=function(e,i){return i=typeof i=="function"?i:o,e&&e.length?Tr(e,o,i):[]},g.unset=function(e,i){return e==null||ac(e,i)},g.unzip=kc,g.unzipWith=nh,g.update=function(e,i,c){return e==null?e:vl(e,i,lc(c))},g.updateWith=function(e,i,c,h){return h=typeof h=="function"?h:o,e==null?e:vl(e,i,lc(c),h)},g.values=ji,g.valuesIn=function(e){return e==null?[]:Pa(e,Ge(e))},g.without=lp,g.words=Ch,g.wrap=function(e,i){return Sc(lc(i),e)},g.xor=hp,g.xorBy=dp,g.xorWith=fp,g.zip=pp,g.zipObject=function(e,i){return bl(e||[],i||[],ks)},g.zipObjectDeep=function(e,i){return bl(e||[],i||[],Ms)},g.zipWith=gp,g.entries=xh,g.entriesIn=$h,g.extend=vh,g.extendWith=Zo,Oc(g,g),g.add=ug,g.attempt=Sh,g.camelCase=Bp,g.capitalize=wh,g.ceil=lg,g.clamp=function(e,i,c){return c===o&&(c=i,i=o),c!==o&&(c=(c=vn(c))==c?c:0),i!==o&&(i=(i=vn(i))==i?i:0),ni(vn(e),i,c)},g.clone=function(e){return gn(e,4)},g.cloneDeep=function(e){return gn(e,5)},g.cloneDeepWith=function(e,i){return gn(e,5,i=typeof i=="function"?i:o)},g.cloneWith=function(e,i){return gn(e,4,i=typeof i=="function"?i:o)},g.conformsTo=function(e,i){return i==null||Ju(e,i,we(i))},g.deburr=kh,g.defaultTo=function(e,i){return e==null||e!=e?i:e},g.divide=hg,g.endsWith=function(e,i,c){e=Ot(e),i=Xe(i);var h=e.length,f=c=c===o?h:ni(wt(c),0,h);return(c-=i.length)>=0&&e.slice(c,f)==i},g.eq=In,g.escape=function(e){return(e=Ot(e))&&Aa.test(e)?e.replace(ns,df):e},g.escapeRegExp=function(e){return(e=Ot(e))&&Ks.test(e)?e.replace(ss,"\\$&"):e},g.every=function(e,i,c){var h=xt(e)?dn:jf;return c&&ze(e,i,c)&&(i=o),h(e,ct(i,3))},g.find=vp,g.findIndex=Yl,g.findKey=function(e,i){return Yr(e,ct(i,3),Vn)},g.findLast=yp,g.findLastIndex=Xl,g.findLastKey=function(e,i){return Yr(e,ct(i,3),Ya)},g.floor=dg,g.forEach=ih,g.forEachRight=sh,g.forIn=function(e,i){return e==null?e:Ka(e,ct(i,3),Ge)},g.forInRight=function(e,i){return e==null?e:rl(e,ct(i,3),Ge)},g.forOwn=function(e,i){return e&&Vn(e,ct(i,3))},g.forOwnRight=function(e,i){return e&&Ya(e,ct(i,3))},g.get=Ec,g.gt=Ep,g.gte=Ap,g.has=function(e,i){return e!=null&&ql(e,i,qf)},g.hasIn=Ac,g.head=th,g.identity=Be,g.includes=function(e,i,c,h){e=He(e)?e:ji(e),c=c&&!h?wt(c):0;var f=e.length;return c<0&&(c=me(f+c,0)),Vo(e)?c<=f&&e.indexOf(i,c)>-1:!!f&&or(e,i,c)>-1},g.indexOf=function(e,i,c){var h=e==null?0:e.length;if(!h)return-1;var f=c==null?0:wt(c);return f<0&&(f=me(h+f,0)),or(e,i,f)},g.inRange=function(e,i,c){return i=pr(i),c===o?(c=i,i=0):c=pr(c),function(h,f,v){return h>=Fe(f,v)&&h<me(f,v)}(e=vn(e),i,c)},g.invoke=Pp,g.isArguments=oi,g.isArray=xt,g.isArrayBuffer=Tp,g.isArrayLike=He,g.isArrayLikeObject=ie,g.isBoolean=function(e){return e===!0||e===!1||ne(e)&&De(e)==q},g.isBuffer=Or,g.isDate=Fp,g.isElement=function(e){return ne(e)&&e.nodeType===1&&!Fs(e)},g.isEmpty=function(e){if(e==null)return!0;if(He(e)&&(xt(e)||typeof e=="string"||typeof e.splice=="function"||Or(e)||zi(e)||oi(e)))return!e.length;var i=Re(e);if(i==st||i==Ft)return!e.size;if(As(e))return!ec(e).length;for(var c in e)if(Lt.call(e,c))return!1;return!0},g.isEqual=function(e,i){return Is(e,i)},g.isEqualWith=function(e,i,c){var h=(c=typeof c=="function"?c:o)?c(e,i):o;return h===o?Is(e,i,o,c):!!h},g.isError=Ic,g.isFinite=function(e){return typeof e=="number"&&Vu(e)},g.isFunction=fr,g.isInteger=hh,g.isLength=Bo,g.isMap=dh,g.isMatch=function(e,i){return e===i||tc(e,i,_c(i))},g.isMatchWith=function(e,i,c){return c=typeof c=="function"?c:o,tc(e,i,_c(i),c)},g.isNaN=function(e){return fh(e)&&e!=+e},g.isNative=function(e){if(Xf(e))throw new Mt("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return ol(e)},g.isNil=function(e){return e==null},g.isNull=function(e){return e===null},g.isNumber=fh,g.isObject=Jt,g.isObjectLike=ne,g.isPlainObject=Fs,g.isRegExp=Mc,g.isSafeInteger=function(e){return hh(e)&&e>=-9007199254740991&&e<=p},g.isSet=ph,g.isString=Vo,g.isSymbol=Je,g.isTypedArray=zi,g.isUndefined=function(e){return e===o},g.isWeakMap=function(e){return ne(e)&&Re(e)==Ct},g.isWeakSet=function(e){return ne(e)&&De(e)=="[object WeakSet]"},g.join=function(e,i){return e==null?"":Sf.call(e,i)},g.kebabCase=Vp,g.last=_n,g.lastIndexOf=function(e,i,c){var h=e==null?0:e.length;if(!h)return-1;var f=h;return c!==o&&(f=(f=wt(c))<0?me(h+f,0):Fe(f,h-1)),i==i?function(v,$,k){for(var I=k+1;I--;)if(v[I]===$)return I;return I}(e,i,f):sr(e,Xr,f,!0)},g.lowerCase=Zp,g.lowerFirst=Qp,g.lt=Rp,g.lte=Op,g.max=function(e){return e&&e.length?Mo(e,Be,Xa):o},g.maxBy=function(e,i){return e&&e.length?Mo(e,ct(i,2),Xa):o},g.mean=function(e){return Lu(e,Be)},g.meanBy=function(e,i){return Lu(e,ct(i,2))},g.min=function(e){return e&&e.length?Mo(e,Be,nc):o},g.minBy=function(e,i){return e&&e.length?Mo(e,ct(i,2),nc):o},g.stubArray=Nc,g.stubFalse=Dc,g.stubObject=function(){return{}},g.stubString=function(){return""},g.stubTrue=function(){return!0},g.multiply=fg,g.nth=function(e,i){return e&&e.length?hl(e,wt(i)):o},g.noConflict=function(){return Wt._===this&&(Wt._=bf),this},g.noop=Lc,g.now=Wo,g.pad=function(e,i,c){e=Ot(e);var h=(i=wt(i))?Ei(e):0;if(!i||h>=i)return e;var f=(i-h)/2;return No(wo(f),c)+e+No($o(f),c)},g.padEnd=function(e,i,c){e=Ot(e);var h=(i=wt(i))?Ei(e):0;return i&&h<i?e+No(i-h,c):e},g.padStart=function(e,i,c){e=Ot(e);var h=(i=wt(i))?Ei(e):0;return i&&h<i?No(i-h,c)+e:e},g.parseInt=function(e,i,c){return c||i==null?i=0:i&&(i=+i),Ef(Ot(e).replace(br,""),i||0)},g.random=function(e,i,c){if(c&&typeof c!="boolean"&&ze(e,i,c)&&(i=c=o),c===o&&(typeof i=="boolean"?(c=i,i=o):typeof e=="boolean"&&(c=e,e=o)),e===o&&i===o?(e=0,i=1):(e=pr(e),i===o?(i=e,e=0):i=pr(i)),e>i){var h=e;e=i,i=h}if(c||e%1||i%1){var f=Zu();return Fe(e+f*(i-e+Qe("1e-"+((f+"").length-1))),i)}return ic(e,i)},g.reduce=function(e,i,c){var h=xt(e)?kn:Nu,f=arguments.length<3;return h(e,ct(i,4),c,f,Ar)},g.reduceRight=function(e,i,c){var h=xt(e)?lo:Nu,f=arguments.length<3;return h(e,ct(i,4),c,f,el)},g.repeat=function(e,i,c){return i=(c?ze(e,i,c):i===o)?1:wt(i),sc(Ot(e),i)},g.replace=function(){var e=arguments,i=Ot(e[0]);return e.length<3?i:i.replace(e[1],e[2])},g.result=function(e,i,c){var h=-1,f=(i=Fr(i,e)).length;for(f||(f=1,e=o);++h<f;){var v=e==null?o:e[Qn(i[h])];v===o&&(h=f,v=c),e=fr(v)?v.call(e):v}return e},g.round=pg,g.runInContext=M,g.sample=function(e){return(xt(e)?Yu:Wf)(e)},g.size=function(e){if(e==null)return 0;if(He(e))return Vo(e)?Ei(e):e.length;var i=Re(e);return i==st||i==Ft?e.size:ec(e).length},g.snakeCase=Kp,g.some=function(e,i,c){var h=xt(e)?Ir:Vf;return c&&ze(e,i,c)&&(i=o),h(e,ct(i,3))},g.sortedIndex=function(e,i){return To(e,i)},g.sortedIndexBy=function(e,i,c){return oc(e,i,ct(c,2))},g.sortedIndexOf=function(e,i){var c=e==null?0:e.length;if(c){var h=To(e,i);if(h<c&&In(e[h],i))return h}return-1},g.sortedLastIndex=function(e,i){return To(e,i,!0)},g.sortedLastIndexBy=function(e,i,c){return oc(e,i,ct(c,2),!0)},g.sortedLastIndexOf=function(e,i){if(e!=null&&e.length){var c=To(e,i,!0)-1;if(In(e[c],i))return c}return-1},g.startCase=Yp,g.startsWith=function(e,i,c){return e=Ot(e),c=c==null?0:ni(wt(c),0,e.length),i=Xe(i),e.slice(c,c+i.length)==i},g.subtract=gg,g.sum=function(e){return e&&e.length?qa(e,Be):0},g.sumBy=function(e,i){return e&&e.length?qa(e,ct(i,2)):0},g.template=function(e,i,c){var h=g.templateSettings;c&&ze(e,i,c)&&(i=o),e=Ot(e),i=Zo({},i,h,Nl);var f,v,$=Zo({},i.imports,h.imports,Nl),k=we($),I=Pa($,k),O=0,R=i.interpolate||$i,U="__p += '",X=Ha((i.escape||$i).source+"|"+R.source+"|"+(R===is?os:$i).source+"|"+(i.evaluate||$i).source+"|$","g"),tt="//# sourceURL="+(Lt.call(i,"sourceURL")?(i.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Pt+"]")+`
`;e.replace(X,function(P,lt,Q,yt,gt,Tt){return Q||(Q=yt),U+=e.slice(O,Tt).replace(Na,ff),lt&&(f=!0,U+=`' +
__e(`+lt+`) +
'`),gt&&(v=!0,U+=`';
`+gt+`;
__p += '`),Q&&(U+=`' +
((__t = (`+Q+`)) == null ? '' : __t) +
'`),O=Tt+P.length,P}),U+=`';
`;var at=Lt.call(i,"variable")&&i.variable;if(at){if(Fa.test(at))throw new Mt("Invalid `variable` option passed into `_.template`")}else U=`with (obj) {
`+U+`
}
`;U=(v?U.replace(Ia,""):U).replace(Ma,"$1").replace(Vs,"$1;"),U="function("+(at||"obj")+`) {
`+(at?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(f?", __e = _.escape":"")+(v?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+U+`return __p
}`;var L=Sh(function(){return ge(k,tt+"return "+U).apply(o,I)});if(L.source=U,Ic(L))throw L;return L},g.times=function(e,i){if((e=wt(e))<1||e>p)return[];var c=b,h=Fe(e,b);i=ct(i),e-=b;for(var f=Ua(h,i);++c<e;)i(c);return f},g.toFinite=pr,g.toInteger=wt,g.toLength=mh,g.toLower=function(e){return Ot(e).toLowerCase()},g.toNumber=vn,g.toSafeInteger=function(e){return e?ni(wt(e),-9007199254740991,p):e===0?e:0},g.toString=Ot,g.toUpper=function(e){return Ot(e).toUpperCase()},g.trim=function(e,i,c){if((e=Ot(e))&&(c||i===o))return Du(e);if(!e||!(i=Xe(i)))return e;var h=Cn(e),f=Cn(i);return Rr(h,zu(h,f),ju(h,f)+1).join("")},g.trimEnd=function(e,i,c){if((e=Ot(e))&&(c||i===o))return e.slice(0,Uu(e)+1);if(!e||!(i=Xe(i)))return e;var h=Cn(e);return Rr(h,0,ju(h,Cn(i))+1).join("")},g.trimStart=function(e,i,c){if((e=Ot(e))&&(c||i===o))return e.replace(br,"");if(!e||!(i=Xe(i)))return e;var h=Cn(e);return Rr(h,zu(h,Cn(i))).join("")},g.truncate=function(e,i){var c=30,h="...";if(Jt(i)){var f="separator"in i?i.separator:f;c="length"in i?wt(i.length):c,h="omission"in i?Xe(i.omission):h}var v=(e=Ot(e)).length;if(Mi(e)){var $=Cn(e);v=$.length}if(c>=v)return e;var k=c-Ei(h);if(k<1)return h;var I=$?Rr($,0,k).join(""):e.slice(0,k);if(f===o)return I+h;if($&&(k+=I.length-k),Mc(f)){if(e.slice(k).search(f)){var O,R=I;for(f.global||(f=Ha(f.source,Ot(xr.exec(f))+"g")),f.lastIndex=0;O=f.exec(R);)var U=O.index;I=I.slice(0,U===o?k:U)}}else if(e.indexOf(Xe(f),k)!=k){var X=I.lastIndexOf(f);X>-1&&(I=I.slice(0,X))}return I+h},g.unescape=function(e){return(e=Ot(e))&&Ea.test(e)?e.replace(es,gf):e},g.uniqueId=function(e){var i=++vf;return Ot(e)+i},g.upperCase=Xp,g.upperFirst=Tc,g.each=ih,g.eachRight=sh,g.first=th,Oc(g,(zc={},Vn(g,function(e,i){Lt.call(g.prototype,i)||(zc[i]=e)}),zc),{chain:!1}),g.VERSION="4.17.21",Te(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){g[e].placeholder=g}),Te(["drop","take"],function(e,i){It.prototype[e]=function(c){c=c===o?1:me(wt(c),0);var h=this.__filtered__&&!i?new It(this):this.clone();return h.__filtered__?h.__takeCount__=Fe(c,h.__takeCount__):h.__views__.push({size:Fe(c,b),type:e+(h.__dir__<0?"Right":"")}),h},It.prototype[e+"Right"]=function(c){return this.reverse()[e](c).reverse()}}),Te(["filter","map","takeWhile"],function(e,i){var c=i+1,h=c==1||c==3;It.prototype[e]=function(f){var v=this.clone();return v.__iteratees__.push({iteratee:ct(f,3),type:c}),v.__filtered__=v.__filtered__||h,v}}),Te(["head","last"],function(e,i){var c="take"+(i?"Right":"");It.prototype[e]=function(){return this[c](1).value()[0]}}),Te(["initial","tail"],function(e,i){var c="drop"+(i?"":"Right");It.prototype[e]=function(){return this.__filtered__?new It(this):this[c](1)}}),It.prototype.compact=function(){return this.filter(Be)},It.prototype.find=function(e){return this.filter(e).head()},It.prototype.findLast=function(e){return this.reverse().find(e)},It.prototype.invokeMap=kt(function(e,i){return typeof e=="function"?new It(this):this.map(function(c){return Ss(c,e,i)})}),It.prototype.reject=function(e){return this.filter(Go(ct(e)))},It.prototype.slice=function(e,i){e=wt(e);var c=this;return c.__filtered__&&(e>0||i<0)?new It(c):(e<0?c=c.takeRight(-e):e&&(c=c.drop(e)),i!==o&&(c=(i=wt(i))<0?c.dropRight(-i):c.take(i-e)),c)},It.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},It.prototype.toArray=function(){return this.take(b)},Vn(It.prototype,function(e,i){var c=/^(?:filter|find|map|reject)|While$/.test(i),h=/^(?:head|last)$/.test(i),f=g[h?"take"+(i=="last"?"Right":""):i],v=h||/^find/.test(i);f&&(g.prototype[i]=function(){var $=this.__wrapped__,k=h?[1]:arguments,I=$ instanceof It,O=k[0],R=I||xt($),U=function(lt){var Q=f.apply(g,Ke([lt],k));return h&&X?Q[0]:Q};R&&c&&typeof O=="function"&&O.length!=1&&(I=R=!1);var X=this.__chain__,tt=!!this.__actions__.length,at=v&&!X,L=I&&!tt;if(!v&&R){$=L?$:new It(this);var P=e.apply($,k);return P.__actions__.push({func:Uo,args:[U],thisArg:o}),new pn(P,X)}return at&&L?e.apply(this,k):(P=this.thru(U),at?h?P.value()[0]:P.value():P)})}),Te(["pop","push","shift","sort","splice","unshift"],function(e){var i=fo[e],c=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",h=/^(?:pop|shift)$/.test(e);g.prototype[e]=function(){var f=arguments;if(h&&!this.__chain__){var v=this.value();return i.apply(xt(v)?v:[],f)}return this[c](function($){return i.apply(xt($)?$:[],f)})}}),Vn(It.prototype,function(e,i){var c=g[i];if(c){var h=c.name+"";Lt.call(Ri,h)||(Ri[h]=[]),Ri[h].push({name:i,func:c})}}),Ri[Oo(o,2).name]=[{name:"wrapper",func:o}],It.prototype.clone=function(){var e=new It(this.__wrapped__);return e.__actions__=We(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=We(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=We(this.__views__),e},It.prototype.reverse=function(){if(this.__filtered__){var e=new It(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},It.prototype.value=function(){var e=this.__wrapped__.value(),i=this.__dir__,c=xt(e),h=i<0,f=c?e.length:0,v=function(Tt,it,ht){for(var _e=-1,se=ht.length;++_e<se;){var je=ht[_e],Qt=je.size;switch(je.type){case"drop":Tt+=Qt;break;case"dropRight":it-=Qt;break;case"take":it=Fe(it,Tt+Qt);break;case"takeRight":Tt=me(Tt,it-Qt)}}return{start:Tt,end:it}}(0,f,this.__views__),$=v.start,k=v.end,I=k-$,O=h?k:$-1,R=this.__iteratees__,U=R.length,X=0,tt=Fe(I,this.__takeCount__);if(!c||!h&&f==I&&tt==I)return yl(e,this.__actions__);var at=[];t:for(;I--&&X<tt;){for(var L=-1,P=e[O+=i];++L<U;){var lt=R[L],Q=lt.iteratee,yt=lt.type,gt=Q(P);if(yt==2)P=gt;else if(!gt){if(yt==1)continue t;break t}}at[X++]=P}return at},g.prototype.at=mp,g.prototype.chain=function(){return rh(this)},g.prototype.commit=function(){return new pn(this.value(),this.__chain__)},g.prototype.next=function(){this.__values__===o&&(this.__values__=gh(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?o:this.__values__[this.__index__++]}},g.prototype.plant=function(e){for(var i,c=this;c instanceof So;){var h=Kl(c);h.__index__=0,h.__values__=o,i?f.__wrapped__=h:i=h;var f=h;c=c.__wrapped__}return f.__wrapped__=e,i},g.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof It){var i=e;return this.__actions__.length&&(i=new It(this)),(i=i.reverse()).__actions__.push({func:Uo,args:[wc],thisArg:o}),new pn(i,this.__chain__)}return this.thru(wc)},g.prototype.toJSON=g.prototype.valueOf=g.prototype.value=function(){return yl(this.__wrapped__,this.__actions__)},g.prototype.first=g.prototype.head,ys&&(g.prototype[ys]=function(){return this}),g}();Ie?((Ie.exports=Ai)._=Ai,fe._=Ai):Wt._=Ai}).call(cn);var y_=pu.exports;function vr(o){return Array.isArray?Array.isArray(o):nf(o)==="[object Array]"}const b_=1/0;function x_(o){return o==null?"":function(t){if(typeof t=="string")return t;let n=t+"";return n=="0"&&1/t==-b_?"-0":n}(o)}function Jn(o){return typeof o=="string"}function tf(o){return typeof o=="number"}function $_(o){return o===!0||o===!1||function(t){return ef(t)&&t!==null}(o)&&nf(o)=="[object Boolean]"}function ef(o){return typeof o=="object"}function an(o){return o!=null}function Qc(o){return!o.trim().length}function nf(o){return o==null?o===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(o)}const w_=o=>`Missing ${o} property in key`,k_=o=>`Property 'weight' in key '${o}' must be a positive integer`,ad=Object.prototype.hasOwnProperty;class C_{constructor(t){this._keys=[],this._keyMap={};let n=0;t.forEach(r=>{let s=rf(r);this._keys.push(s),this._keyMap[s.id]=s,n+=s.weight}),this._keys.forEach(r=>{r.weight/=n})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function rf(o){let t=null,n=null,r=null,s=1,a=null;if(Jn(o)||vr(o))r=o,t=cd(o),n=gu(o);else{if(!ad.call(o,"name"))throw new Error(w_("name"));const u=o.name;if(r=u,ad.call(o,"weight")&&(s=o.weight,s<=0))throw new Error(k_(u));t=cd(u),n=gu(u),a=o.getFn}return{path:t,id:n,weight:s,src:r,getFn:a}}function cd(o){return vr(o)?o:o.split(".")}function gu(o){return vr(o)?o.join("."):o}const S_={useExtendedSearch:!1,getFn:function(o,t){let n=[],r=!1;const s=(a,u,l)=>{if(an(a))if(u[l]){const d=a[u[l]];if(!an(d))return;if(l===u.length-1&&(Jn(d)||tf(d)||$_(d)))n.push(x_(d));else if(vr(d)){r=!0;for(let m=0,p=d.length;m<p;m+=1)s(d[m],u,l+1)}else u.length&&s(d,u,l+1)}else n.push(a)};return s(o,Jn(t)?t.split("."):t,0),r?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var vt={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(o,t)=>o.score===t.score?o.idx<t.idx?-1:1:o.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...S_};const I_=/[^ ]+/g;class Fu{constructor({getFn:t=vt.getFn,fieldNormWeight:n=vt.fieldNormWeight}={}){this.norm=function(r=1,s=3){const a=new Map,u=Math.pow(10,s);return{get(l){const d=l.match(I_).length;if(a.has(d))return a.get(d);const m=1/Math.pow(d,.5*r),p=parseFloat(Math.round(m*u)/u);return a.set(d,p),p},clear(){a.clear()}}}(n,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((n,r)=>{this._keysMap[n.id]=r})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,Jn(this.docs[0])?this.docs.forEach((t,n)=>{this._addString(t,n)}):this.docs.forEach((t,n)=>{this._addObject(t,n)}),this.norm.clear())}add(t){const n=this.size();Jn(t)?this._addString(t,n):this._addObject(t,n)}removeAt(t){this.records.splice(t,1);for(let n=t,r=this.size();n<r;n+=1)this.records[n].i-=1}getValueForItemAtKeyId(t,n){return t[this._keysMap[n]]}size(){return this.records.length}_addString(t,n){if(!an(t)||Qc(t))return;let r={v:t,i:n,n:this.norm.get(t)};this.records.push(r)}_addObject(t,n){let r={i:n,$:{}};this.keys.forEach((s,a)=>{let u=s.getFn?s.getFn(t):this.getFn(t,s.path);if(an(u)){if(vr(u)){let l=[];const d=[{nestedArrIndex:-1,value:u}];for(;d.length;){const{nestedArrIndex:m,value:p}=d.pop();if(an(p))if(Jn(p)&&!Qc(p)){let y={v:p,i:m,n:this.norm.get(p)};l.push(y)}else vr(p)&&p.forEach((y,b)=>{d.push({nestedArrIndex:b,value:y})})}r.$[a]=l}else if(Jn(u)&&!Qc(u)){let l={v:u,n:this.norm.get(u)};r.$[a]=l}}}),this.records.push(r)}toJSON(){return{keys:this.keys,records:this.records}}}function sf(o,t,{getFn:n=vt.getFn,fieldNormWeight:r=vt.fieldNormWeight}={}){const s=new Fu({getFn:n,fieldNormWeight:r});return s.setKeys(o.map(rf)),s.setSources(t),s.create(),s}function ca(o,{errors:t=0,currentLocation:n=0,expectedLocation:r=0,distance:s=vt.distance,ignoreLocation:a=vt.ignoreLocation}={}){const u=t/o.length;if(a)return u;const l=Math.abs(r-n);return s?u+l/s:l?1:u}const ui=32;function M_(o,t,n,{location:r=vt.location,distance:s=vt.distance,threshold:a=vt.threshold,findAllMatches:u=vt.findAllMatches,minMatchCharLength:l=vt.minMatchCharLength,includeMatches:d=vt.includeMatches,ignoreLocation:m=vt.ignoreLocation}={}){if(t.length>ui)throw new Error(`Pattern length exceeds max of ${ui}.`);const p=t.length,y=o.length,b=Math.max(0,Math.min(r,y));let w=a,T=b;const j=l>1||d,q=j?Array(y):[];let S;for(;(S=o.indexOf(t,T))>-1;){let pt=ca(t,{currentLocation:S,expectedLocation:b,distance:s,ignoreLocation:m});if(w=Math.min(pt,w),T=S+p,j){let St=0;for(;St<p;)q[S+St]=1,St+=1}}T=-1;let E=[],G=1,ot=p+y;const st=1<<p-1;for(let pt=0;pt<p;pt+=1){let St=0,mt=ot;for(;St<mt;)ca(t,{errors:pt,currentLocation:b+mt,expectedLocation:b,distance:s,ignoreLocation:m})<=w?St=mt:ot=mt,mt=Math.floor((ot-St)/2+St);ot=mt;let Ft=Math.max(1,b-mt+1),Gt=u?y:Math.min(b+mt,y)+p,ue=Array(Gt+2);ue[Gt+1]=(1<<pt)-1;for(let Ct=Gt;Ct>=Ft;Ct-=1){let $t=Ct-1,de=n[o.charAt($t)];if(j&&(q[$t]=+!!de),ue[Ct]=(ue[Ct+1]<<1|1)&de,pt&&(ue[Ct]|=(E[Ct+1]|E[Ct])<<1|1|E[Ct+1]),ue[Ct]&st&&(G=ca(t,{errors:pt,currentLocation:$t,expectedLocation:b,distance:s,ignoreLocation:m}),G<=w)){if(w=G,T=$t,T<=b)break;Ft=Math.max(1,2*b-T)}}if(ca(t,{errors:pt+1,currentLocation:b,expectedLocation:b,distance:s,ignoreLocation:m})>w)break;E=ue}const Nt={isMatch:T>=0,score:Math.max(.001,G)};if(j){const pt=function(St=[],mt=vt.minMatchCharLength){let Ft=[],Gt=-1,ue=-1,Ct=0;for(let $t=St.length;Ct<$t;Ct+=1){let de=St[Ct];de&&Gt===-1?Gt=Ct:de||Gt===-1||(ue=Ct-1,ue-Gt+1>=mt&&Ft.push([Gt,ue]),Gt=-1)}return St[Ct-1]&&Ct-Gt>=mt&&Ft.push([Gt,Ct-1]),Ft}(q,l);pt.length?d&&(Nt.indices=pt):Nt.isMatch=!1}return Nt}function E_(o){let t={};for(let n=0,r=o.length;n<r;n+=1){const s=o.charAt(n);t[s]=(t[s]||0)|1<<r-n-1}return t}class of{constructor(t,{location:n=vt.location,threshold:r=vt.threshold,distance:s=vt.distance,includeMatches:a=vt.includeMatches,findAllMatches:u=vt.findAllMatches,minMatchCharLength:l=vt.minMatchCharLength,isCaseSensitive:d=vt.isCaseSensitive,ignoreLocation:m=vt.ignoreLocation}={}){if(this.options={location:n,threshold:r,distance:s,includeMatches:a,findAllMatches:u,minMatchCharLength:l,isCaseSensitive:d,ignoreLocation:m},this.pattern=d?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const p=(b,w)=>{this.chunks.push({pattern:b,alphabet:E_(b),startIndex:w})},y=this.pattern.length;if(y>ui){let b=0;const w=y%ui,T=y-w;for(;b<T;)p(this.pattern.substr(b,ui),b),b+=ui;if(w){const j=y-ui;p(this.pattern.substr(j),j)}}else p(this.pattern,0)}searchIn(t){const{isCaseSensitive:n,includeMatches:r}=this.options;if(n||(t=t.toLowerCase()),this.pattern===t){let T={isMatch:!0,score:0};return r&&(T.indices=[[0,t.length-1]]),T}const{location:s,distance:a,threshold:u,findAllMatches:l,minMatchCharLength:d,ignoreLocation:m}=this.options;let p=[],y=0,b=!1;this.chunks.forEach(({pattern:T,alphabet:j,startIndex:q})=>{const{isMatch:S,score:E,indices:G}=M_(t,T,j,{location:s+q,distance:a,threshold:u,findAllMatches:l,minMatchCharLength:d,includeMatches:r,ignoreLocation:m});S&&(b=!0),y+=E,S&&G&&(p=[...p,...G])});let w={isMatch:b,score:b?y/this.chunks.length:1};return b&&r&&(w.indices=p),w}}class qr{constructor(t){this.pattern=t}static isMultiMatch(t){return ud(t,this.multiRegex)}static isSingleMatch(t){return ud(t,this.singleRegex)}search(){}}function ud(o,t){const n=o.match(t);return n?n[1]:null}class af extends qr{constructor(t,{location:n=vt.location,threshold:r=vt.threshold,distance:s=vt.distance,includeMatches:a=vt.includeMatches,findAllMatches:u=vt.findAllMatches,minMatchCharLength:l=vt.minMatchCharLength,isCaseSensitive:d=vt.isCaseSensitive,ignoreLocation:m=vt.ignoreLocation}={}){super(t),this._bitapSearch=new of(t,{location:n,threshold:r,distance:s,includeMatches:a,findAllMatches:u,minMatchCharLength:l,isCaseSensitive:d,ignoreLocation:m})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class cf extends qr{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let n,r=0;const s=[],a=this.pattern.length;for(;(n=t.indexOf(this.pattern,r))>-1;)r=n+a,s.push([n,r-1]);const u=!!s.length;return{isMatch:u,score:u?0:1,indices:s}}}const mu=[class extends qr{constructor(o){super(o)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(o){const t=o===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},cf,class extends qr{constructor(o){super(o)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(o){const t=o.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends qr{constructor(o){super(o)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(o){const t=!o.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},class extends qr{constructor(o){super(o)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(o){const t=!o.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},class extends qr{constructor(o){super(o)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(o){const t=o.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[o.length-this.pattern.length,o.length-1]}}},class extends qr{constructor(o){super(o)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(o){const t=o.indexOf(this.pattern)===-1;return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},af],ld=mu.length,A_=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,T_=new Set([af.type,cf.type]);class F_{constructor(t,{isCaseSensitive:n=vt.isCaseSensitive,includeMatches:r=vt.includeMatches,minMatchCharLength:s=vt.minMatchCharLength,ignoreLocation:a=vt.ignoreLocation,findAllMatches:u=vt.findAllMatches,location:l=vt.location,threshold:d=vt.threshold,distance:m=vt.distance}={}){this.query=null,this.options={isCaseSensitive:n,includeMatches:r,minMatchCharLength:s,findAllMatches:u,ignoreLocation:a,location:l,threshold:d,distance:m},this.pattern=n?t:t.toLowerCase(),this.query=function(p,y={}){return p.split("|").map(b=>{let w=b.trim().split(A_).filter(j=>j&&!!j.trim()),T=[];for(let j=0,q=w.length;j<q;j+=1){const S=w[j];let E=!1,G=-1;for(;!E&&++G<ld;){const ot=mu[G];let st=ot.isMultiMatch(S);st&&(T.push(new ot(st,y)),E=!0)}if(!E)for(G=-1;++G<ld;){const ot=mu[G];let st=ot.isSingleMatch(S);if(st){T.push(new ot(st,y));break}}}return T})}(this.pattern,this.options)}static condition(t,n){return n.useExtendedSearch}searchIn(t){const n=this.query;if(!n)return{isMatch:!1,score:1};const{includeMatches:r,isCaseSensitive:s}=this.options;t=s?t:t.toLowerCase();let a=0,u=[],l=0;for(let d=0,m=n.length;d<m;d+=1){const p=n[d];u.length=0,a=0;for(let y=0,b=p.length;y<b;y+=1){const w=p[y],{isMatch:T,indices:j,score:q}=w.search(t);if(!T){l=0,a=0,u.length=0;break}if(a+=1,l+=q,r){const S=w.constructor.type;T_.has(S)?u=[...u,...j]:u.push(j)}}if(a){let y={isMatch:!0,score:l/a};return r&&(y.indices=u),y}}return{isMatch:!1,score:1}}}const _u=[];function vu(o,t){for(let n=0,r=_u.length;n<r;n+=1){let s=_u[n];if(s.condition(o,t))return new s(o,t)}return new of(o,t)}const Ru="$and",R_="$or",hd="$path",O_="$val",Kc=o=>!(!o[Ru]&&!o[R_]),dd=o=>({[Ru]:Object.keys(o).map(t=>({[t]:o[t]}))});function uf(o,t,{auto:n=!0}={}){const r=s=>{let a=Object.keys(s);const u=(d=>!!d[hd])(s);if(!u&&a.length>1&&!Kc(s))return r(dd(s));if((d=>!vr(d)&&ef(d)&&!Kc(d))(s)){const d=u?s[hd]:a[0],m=u?s[O_]:s[d];if(!Jn(m))throw new Error((y=>`Invalid value for key ${y}`)(d));const p={keyId:gu(d),pattern:m};return n&&(p.searcher=vu(m,t)),p}let l={children:[],operator:a[0]};return a.forEach(d=>{const m=s[d];vr(m)&&m.forEach(p=>{l.children.push(r(p))})}),l};return Kc(o)||(o=dd(o)),r(o)}function L_(o,t){const n=o.matches;t.matches=[],an(n)&&n.forEach(r=>{if(!an(r.indices)||!r.indices.length)return;const{indices:s,value:a}=r;let u={indices:s,value:a};r.key&&(u.key=r.key.src),r.idx>-1&&(u.refIndex=r.idx),t.matches.push(u)})}function N_(o,t){t.score=o.score}class Wi{constructor(t,n={},r){this.options={...vt,...n},this.options.useExtendedSearch,this._keyStore=new C_(this.options.keys),this.setCollection(t,r)}setCollection(t,n){if(this._docs=t,n&&!(n instanceof Fu))throw new Error("Incorrect 'index' type");this._myIndex=n||sf(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){an(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const n=[];for(let r=0,s=this._docs.length;r<s;r+=1){const a=this._docs[r];t(a,r)&&(this.removeAt(r),r-=1,s-=1,n.push(a))}return n}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:n=-1}={}){const{includeMatches:r,includeScore:s,shouldSort:a,sortFn:u,ignoreFieldNorm:l}=this.options;let d=Jn(t)?Jn(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(m,{ignoreFieldNorm:p=vt.ignoreFieldNorm}){m.forEach(y=>{let b=1;y.matches.forEach(({key:w,norm:T,score:j})=>{const q=w?w.weight:null;b*=Math.pow(j===0&&q?Number.EPSILON:j,(q||1)*(p?1:T))}),y.score=b})}(d,{ignoreFieldNorm:l}),a&&d.sort(u),tf(n)&&n>-1&&(d=d.slice(0,n)),function(m,p,{includeMatches:y=vt.includeMatches,includeScore:b=vt.includeScore}={}){const w=[];return y&&w.push(L_),b&&w.push(N_),m.map(T=>{const{idx:j}=T,q={item:p[j],refIndex:j};return w.length&&w.forEach(S=>{S(T,q)}),q})}(d,this._docs,{includeMatches:r,includeScore:s})}_searchStringList(t){const n=vu(t,this.options),{records:r}=this._myIndex,s=[];return r.forEach(({v:a,i:u,n:l})=>{if(!an(a))return;const{isMatch:d,score:m,indices:p}=n.searchIn(a);d&&s.push({item:a,idx:u,matches:[{score:m,value:a,norm:l,indices:p}]})}),s}_searchLogical(t){const n=uf(t,this.options),r=(l,d,m)=>{if(!l.children){const{keyId:y,searcher:b}=l,w=this._findMatches({key:this._keyStore.get(y),value:this._myIndex.getValueForItemAtKeyId(d,y),searcher:b});return w&&w.length?[{idx:m,item:d,matches:w}]:[]}const p=[];for(let y=0,b=l.children.length;y<b;y+=1){const w=l.children[y],T=r(w,d,m);if(T.length)p.push(...T);else if(l.operator===Ru)return[]}return p},s=this._myIndex.records,a={},u=[];return s.forEach(({$:l,i:d})=>{if(an(l)){let m=r(n,l,d);m.length&&(a[d]||(a[d]={idx:d,item:l,matches:[]},u.push(a[d])),m.forEach(({matches:p})=>{a[d].matches.push(...p)}))}}),u}_searchObjectList(t){const n=vu(t,this.options),{keys:r,records:s}=this._myIndex,a=[];return s.forEach(({$:u,i:l})=>{if(!an(u))return;let d=[];r.forEach((m,p)=>{d.push(...this._findMatches({key:m,value:u[p],searcher:n}))}),d.length&&a.push({idx:l,item:u,matches:d})}),a}_findMatches({key:t,value:n,searcher:r}){if(!an(n))return[];let s=[];if(vr(n))n.forEach(({v:a,i:u,n:l})=>{if(!an(a))return;const{isMatch:d,score:m,indices:p}=r.searchIn(a);d&&s.push({score:m,key:t,value:a,idx:u,norm:l,indices:p})});else{const{v:a,n:u}=n,{isMatch:l,score:d,indices:m}=r.searchIn(a);l&&s.push({score:d,key:t,value:a,norm:u,indices:m})}return s}}Wi.version="7.0.0",Wi.createIndex=sf,Wi.parseIndex=function(o,{getFn:t=vt.getFn,fieldNormWeight:n=vt.fieldNormWeight}={}){const{keys:r,records:s}=o,a=new Fu({getFn:t,fieldNormWeight:n});return a.setKeys(r),a.setIndexRecords(s),a},Wi.config=vt,Wi.parseQuery=uf,function(...o){_u.push(...o)}(F_);const Ur=class Ur{constructor(t,n){_(this,"_disposers",[]);_(this,"_allMentionables",ce([]));_(this,"_breadcrumbIds",ce([]));_(this,"_userQuery",ce(""));_(this,"_active",ce(!1));_(this,"_allGroups",Pc([this._active,this._allMentionables],([t,n])=>t?Lg(n):[]));_(this,"_currentGroup",Pc([this._breadcrumbIds,this._allGroups],([t,n])=>{if(t.length===0)return;const r=t[t.length-1];return n.find(s=>Ps(s)&&s.id===r)}));_(this,"dispose",()=>{for(const t of this._disposers)t()});_(this,"openDropdown",()=>{this._active.set(!0)});_(this,"closeDropdown",()=>{this._active.set(!1),this._resetState()});_(this,"toggleDropdown",()=>ae(this._active)?(this.closeDropdown(),!1):(this.openDropdown(),!0));_(this,"pushBreadcrumb",t=>{ae(this._active)&&this._breadcrumbIds.update(n=>[...n,t.id])});_(this,"popBreadcrumb",()=>{ae(this._active)&&this._breadcrumbIds.update(t=>t.slice(0,-1))});_(this,"selectMentionable",t=>{var s;const n=this._chatModel.extensionClient,r=this._chatModel.specialContextInputModel;return Ps(t)&&t.type==="breadcrumb"?(this.pushBreadcrumb(t),!0):t.type==="breadcrumb-back"?(this.popBreadcrumb(),!0):Td(t)?(r.markAllActive(),this.closeDropdown(),n.reportWebviewClientEvent(Jc.chatRestoreDefaultContext),!0):t.clearContext?(r.markAllInactive(),this.closeDropdown(),n.reportWebviewClientEvent(Jc.chatClearContext),!0):t.userGuidelines?(n.openSettingsPage("guidelines"),this.closeDropdown(),!0):((s=this._insertMentionNode)==null||s.call(this,t),this.closeDropdown(),!0)});_(this,"_displayItems",Pc([this._active,this._breadcrumbIds,this._userQuery,this._currentGroup,this.allGroups],([t,n,r,s,a])=>{if(!t)return[];if(n.length>0&&s)return[{...s,type:"breadcrumb-back"},...s.group.items.slice(0,Ur.SINGLE_GROUP_MAX_ITEMS).map(u=>({...u,type:"item"}))];if(r.length>0){const u=D_(ae(this._userQuery)).map(l=>({...l,type:"item"}));return a.flatMap(l=>[{...l,type:"breadcrumb"},...l.group.items.slice(0,Ur.MULTI_GROUP_MAX_ITEMS).map(d=>({...d,type:"item"}))]).concat(u)}return[{...Fd,type:"item"},...a.map(u=>({...u,type:"breadcrumb"})),{...Rd,type:"item"},{...Od,type:"item"}]}));_(this,"_refreshSeqNum",0);_(this,"_refreshMentionables",y_.throttle(async()=>{if(!ae(this._active))return;this._refreshSeqNum++;const t=this._refreshSeqNum,n=this._chatModel.currentConversationModel&&Vi(this._chatModel.currentConversationModel),r=ae(this._userQuery),s=await this._chatModel.extensionClient.getSuggestions(r,n);t===this._refreshSeqNum&&this._allMentionables.set(lf({query:r,mentionables:s}))},Ur.REFRESH_THROTTLE_MS,{leading:!0,trailing:!0}));this._chatModel=t,this._insertMentionNode=n,this._disposers.push(this._userQuery.subscribe(this._refreshMentionables)),this._disposers.push(this._active.subscribe(this._refreshMentionables))}get allGroups(){return this._allGroups}get currentGroup(){return this._currentGroup}get breadcrumbIds(){return this._breadcrumbIds}get displayItems(){return this._displayItems}get active(){return this._active}get userQuery(){return this._userQuery}_resetState(){this._breadcrumbIds.set([]),this._userQuery.set("")}};_(Ur,"REFRESH_THROTTLE_MS",600),_(Ur,"SINGLE_GROUP_MAX_ITEMS",12),_(Ur,"MULTI_GROUP_MAX_ITEMS",6);let yu=Ur;const lf=({query:o,mentionables:t,returnAllIfNoResults:n=!0,threshold:r=1})=>{if(o.length<=1)return t;const s=new Wi(t,{keys:["label"],threshold:r,minMatchCharLength:0,ignoreLocation:!0,includeScore:!0,useExtendedSearch:!1,shouldSort:!0,findAllMatches:!0}).search(o);return s.length===0&&n?t:s.map(a=>a.item)},D_=o=>lf({query:o,mentionables:[Fd,Rd,Od],returnAllIfNoResults:!1,threshold:.6});function ka(o){switch(o){case Ae.DEFAULT:return Nh;case Ae.PROTOTYPER:return Zg;case Ae.BRAINSTORM:return Vg;case Ae.REVIEWER:return Bg;default:return Nh}}function z_(o){let t,n,r,s=o[0].label+"";return{c(){t=Bt("span"),n=Bt("span"),r=he(s),jt(n,"class","c-mentionable-group-label__text right"),jt(t,"class","c-mentionable-group-label")},m(a,u){nt(a,t,u),Vt(t,n),Vt(n,r)},p(a,u){1&u&&s!==(s=a[0].label+"")&&Dn(r,s)},i:Ht,o:Ht,d(a){a&&rt(t)}}}function j_(o){let t,n;return t=new Qg({props:{$$slots:{text:[Q_],leftIcon:[Z_]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};17&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function q_(o){let t,n=o[0].label+"";return{c(){t=he(n)},m(r,s){nt(r,t,s)},p(r,s){1&s&&n!==(n=r[0].label+"")&&Dn(t,n)},i:Ht,o:Ht,d(r){r&&rt(t)}}}function U_(o){let t,n;return t=new Ze({props:{filepath:o[0].rule.path,$$slots:{leftIcon:[K_]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].rule.path),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function P_(o){let t,n;return t=new Ze({props:{filepath:o[0].recentFile.pathName,$$slots:{leftIcon:[Y_]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].recentFile.pathName),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function W_(o){let t,n;return t=new Ze({props:{filepath:o[0].selection.pathName,$$slots:{leftIcon:[X_]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].selection.pathName),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function H_(o){let t,n;return t=new Ze({props:{filepath:o[0].sourceFolder.folderRoot,$$slots:{leftIcon:[J_]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].sourceFolder.folderRoot),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function G_(o){let t,n;return t=new Ze({props:{filepath:o[0].externalSource.name,$$slots:{leftIcon:[t0]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].externalSource.name),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function B_(o){let t,n;return t=new Ze({props:{filepath:o[0].folder.pathName,$$slots:{leftIcon:[e0]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].folder.pathName),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function V_(o){let t,n;return t=new Ze({props:{filepath:o[0].file.pathName,$$slots:{leftIcon:[n0]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};1&s&&(a.filepath=r[0].file.pathName),16&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function Z_(o){let t,n,r;var s=ka(o[0].personality.type);return s&&(n=Xi(s,{})),{c(){t=Bt("span"),n&&B(n.$$.fragment),jt(t,"slot","leftIcon"),jt(t,"class","c-context-menu-item__icon svelte-1a2w9oo")},m(a,u){nt(a,t,u),n&&V(n,t,null),r=!0},p(a,u){if(1&u&&s!==(s=ka(a[0].personality.type))){if(n){be();const l=n;z(l.$$.fragment,1,0,()=>{Z(l,1)}),xe()}s?(n=Xi(s,{}),B(n.$$.fragment),F(n.$$.fragment,1),V(n,t,null)):n=null}},i(a){r||(n&&F(n.$$.fragment,a),r=!0)},o(a){n&&z(n.$$.fragment,a),r=!1},d(a){a&&rt(t),n&&Z(n)}}}function Q_(o){let t,n,r=o[0].label+"";return{c(){t=Bt("span"),n=he(r),jt(t,"slot","text")},m(s,a){nt(s,t,a),Vt(t,n)},p(s,a){1&a&&r!==(r=s[0].label+"")&&Dn(n,r)},d(s){s&&rt(t)}}}function K_(o){let t,n;return t=new zd({props:{slot:"leftIcon",iconName:"rule"}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p:Ht,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function Y_(o){let t,n;return t=new Ji({props:{slot:"leftIcon",iconName:"description"}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p:Ht,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function X_(o){let t,n;return t=new Ji({props:{slot:"leftIcon",iconName:"text_select_start"}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p:Ht,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function J_(o){let t,n;return t=new Ji({props:{slot:"leftIcon",iconName:"folder_managed"}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p:Ht,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function t0(o){let t,n;return t=new Ji({props:{slot:"leftIcon",iconName:"import_contacts"}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p:Ht,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function e0(o){let t,n;return t=new Ji({props:{slot:"leftIcon",iconName:"folder_open"}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p:Ht,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function n0(o){let t,n;return t=new Ji({props:{slot:"leftIcon",iconName:"description"}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p:Ht,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function r0(o){let t,n,r,s,a,u,l,d,m,p,y,b,w,T;const j=[V_,B_,G_,H_,W_,P_,U_,q_,j_,z_],q=[];function S(E,G){return 1&G&&(t=null),1&G&&(n=null),1&G&&(r=null),1&G&&(s=null),1&G&&(a=null),1&G&&(u=null),1&G&&(l=null),1&G&&(d=null),1&G&&(m=null),1&G&&(p=null),t==null&&(t=!!$u(E[0])),t?0:(n==null&&(n=!!wu(E[0])),n?1:(r==null&&(r=!!ku(E[0])),r?2:(s==null&&(s=!!_a(E[0])),s?3:(a==null&&(a=!!ma(E[0])),a?4:(u==null&&(u=!!ga(E[0])),u?5:(l==null&&(l=!!ya(E[0])),l?6:(d==null&&(d=!!Ps(E[0])),d?7:(m==null&&(m=!!Ca(E[0])),m?8:(p==null&&(p=!!(Td(E[0])||Ng(E[0])||va(E[0]))),p?9:-1)))))))))}return~(y=S(o,-1))&&(b=q[y]=j[y](o)),{c(){b&&b.c(),w=tr()},m(E,G){~y&&q[y].m(E,G),nt(E,w,G),T=!0},p(E,G){let ot=y;y=S(E,G),y===ot?~y&&q[y].p(E,G):(b&&(be(),z(q[ot],1,1,()=>{q[ot]=null}),xe()),~y?(b=q[y],b?b.p(E,G):(b=q[y]=j[y](E),b.c()),F(b,1),b.m(w.parentNode,w)):b=null)},i(E){T||(F(b),T=!0)},o(E){z(b),T=!1},d(E){E&&rt(w),~y&&q[y].d(E)}}}function i0(o){let t,n,r;var s=o[3];function a(u,l){return{props:{highlight:u[2],onSelect:u[1],$$slots:{default:[r0]},$$scope:{ctx:u}}}}return s&&(t=Xi(s,a(o))),{c(){t&&B(t.$$.fragment),n=tr()},m(u,l){t&&V(t,u,l),nt(u,n,l),r=!0},p(u,[l]){if(8&l&&s!==(s=u[3])){if(t){be();const d=t;z(d.$$.fragment,1,0,()=>{Z(d,1)}),xe()}s?(t=Xi(s,a(u)),B(t.$$.fragment),F(t.$$.fragment,1),V(t,n.parentNode,n)):t=null}else if(s){const d={};4&l&&(d.highlight=u[2]),2&l&&(d.onSelect=u[1]),17&l&&(d.$$scope={dirty:l,ctx:u}),t.$set(d)}},i(u){r||(t&&F(t.$$.fragment,u),r=!0)},o(u){t&&z(t.$$.fragment,u),r=!1},d(u){u&&rt(n),t&&Z(t,u)}}}function s0(o,t,n){let r,{item:s}=t,{onSelect:a}=t,{highlight:u}=t;return o.$$set=l=>{"item"in l&&n(0,s=l.item),"onSelect"in l&&n(1,a=l.onSelect),"highlight"in l&&n(2,u=l.highlight)},o.$$.update=()=>{1&o.$$.dirty&&(s.type==="breadcrumb-back"?n(3,r=Bc.BreadcrumbBackItem):s.type==="breadcrumb"&&Ps(s)?n(3,r=Bc.BreadcrumbItem):s.type!=="item"||Ps(s)||n(3,r=Bc.Item))},[s,a,u,r]}class o0 extends gi{constructor(t){super(),mi(this,t,s0,i0,_i,{item:0,onSelect:1,highlight:2})}}function a0(o){let t,n=o[0].label+"";return{c(){t=he(n)},m(r,s){nt(r,t,s)},p(r,s){1&s&&n!==(n=r[0].label+"")&&Dn(t,n)},i:Ht,o:Ht,d(r){r&&rt(t)}}}function c0(o){let t,n,r,s;return t=new zd({}),r=new Ze({props:{filepath:`${Oh}/${Lh}/${o[0].rule.path}`}}),{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment)},m(a,u){V(t,a,u),nt(a,n,u),V(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=`${Oh}/${Lh}/${a[0].rule.path}`),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&rt(n),Z(t,a),Z(r,a)}}}function u0(o){let t,n,r,s,a,u,l,d;return n=new Kg({props:{heightPx:32,floatHeight:4,animationDuration:2.25,$$slots:{default:[_0]},$$scope:{ctx:o}}}),a=new fa({props:{size:2,weight:"medium",$$slots:{default:[v0]},$$scope:{ctx:o}}}),l=new fa({props:{size:1,$$slots:{default:[y0]},$$scope:{ctx:o}}}),{c(){t=Bt("div"),B(n.$$.fragment),r=qt(),s=Bt("div"),B(a.$$.fragment),u=qt(),B(l.$$.fragment),jt(t,"class","c-mention-hover-contents__personality-icon svelte-11069rs"),jt(s,"class","c-mention-hover-contents__personality svelte-11069rs")},m(m,p){nt(m,t,p),V(n,t,null),nt(m,r,p),nt(m,s,p),V(a,s,null),Vt(s,u),V(l,s,null),d=!0},p(m,p){const y={};3&p&&(y.$$scope={dirty:p,ctx:m}),n.$set(y);const b={};3&p&&(b.$$scope={dirty:p,ctx:m}),a.$set(b);const w={};3&p&&(w.$$scope={dirty:p,ctx:m}),l.$set(w)},i(m){d||(F(n.$$.fragment,m),F(a.$$.fragment,m),F(l.$$.fragment,m),d=!0)},o(m){z(n.$$.fragment,m),z(a.$$.fragment,m),z(l.$$.fragment,m),d=!1},d(m){m&&(rt(t),rt(r),rt(s)),Z(n),Z(a),Z(l)}}}function l0(o){var a,u;let t,n,r,s;return t=new Yg({}),r=new Ze({props:{filepath:`${o[0].selection.pathName}:L${(a=o[0].selection.fullRange)==null?void 0:a.startLineNumber}-${(u=o[0].selection.fullRange)==null?void 0:u.endLineNumber}`}}),{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment)},m(l,d){V(t,l,d),nt(l,n,d),V(r,l,d),s=!0},p(l,d){var p,y;const m={};1&d&&(m.filepath=`${l[0].selection.pathName}:L${(p=l[0].selection.fullRange)==null?void 0:p.startLineNumber}-${(y=l[0].selection.fullRange)==null?void 0:y.endLineNumber}`),r.$set(m)},i(l){s||(F(t.$$.fragment,l),F(r.$$.fragment,l),s=!0)},o(l){z(t.$$.fragment,l),z(r.$$.fragment,l),s=!1},d(l){l&&rt(n),Z(t,l),Z(r,l)}}}function h0(o){var s;let t,n,r=(o[0].userGuidelines.overLimit||((s=o[0].rulesAndGuidelinesState)==null?void 0:s.overLimit))&&fd(o);return{c(){r&&r.c(),t=tr()},m(a,u){r&&r.m(a,u),nt(a,t,u),n=!0},p(a,u){var l;a[0].userGuidelines.overLimit||(l=a[0].rulesAndGuidelinesState)!=null&&l.overLimit?r?(r.p(a,u),1&u&&F(r,1)):(r=fd(a),r.c(),F(r,1),r.m(t.parentNode,t)):r&&(be(),z(r,1,1,()=>{r=null}),xe())},i(a){n||(F(r),n=!0)},o(a){z(r),n=!1},d(a){a&&rt(t),r&&r.d(a)}}}function d0(o){let t,n,r,s,a,u,l,d;return r=new Xg({}),a=new Ze({props:{class:"c-source-folder-item",filepath:o[0].sourceFolder.folderRoot}}),l=new Jg({props:{class:"guidelines-filespan",sourceFolder:o[0].sourceFolder}}),{c(){t=Bt("div"),n=Bt("div"),B(r.$$.fragment),s=qt(),B(a.$$.fragment),u=qt(),B(l.$$.fragment),jt(n,"class","l-source-folder-name svelte-11069rs"),jt(t,"class","l-mention-hover-contents__source-folder")},m(m,p){nt(m,t,p),Vt(t,n),V(r,n,null),Vt(n,s),V(a,n,null),Vt(t,u),V(l,t,null),d=!0},p(m,p){const y={};1&p&&(y.filepath=m[0].sourceFolder.folderRoot),a.$set(y);const b={};1&p&&(b.sourceFolder=m[0].sourceFolder),l.$set(b)},i(m){d||(F(r.$$.fragment,m),F(a.$$.fragment,m),F(l.$$.fragment,m),d=!0)},o(m){z(r.$$.fragment,m),z(a.$$.fragment,m),z(l.$$.fragment,m),d=!1},d(m){m&&rt(t),Z(r),Z(a),Z(l)}}}function f0(o){let t,n,r,s;return t=new tm({}),r=new Ze({props:{filepath:o[0].externalSource.name}}),{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment)},m(a,u){V(t,a,u),nt(a,n,u),V(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=a[0].externalSource.name),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&rt(n),Z(t,a),Z(r,a)}}}function p0(o){let t,n,r,s;return t=new jg({}),r=new Ze({props:{filepath:o[0].folder.pathName}}),{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment)},m(a,u){V(t,a,u),nt(a,n,u),V(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=a[0].folder.pathName),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&rt(n),Z(t,a),Z(r,a)}}}function g0(o){let t,n,r,s;return t=new Nd({}),r=new Ze({props:{filepath:o[0].recentFile.pathName}}),{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment)},m(a,u){V(t,a,u),nt(a,n,u),V(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=a[0].recentFile.pathName),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&rt(n),Z(t,a),Z(r,a)}}}function m0(o){let t,n,r,s;return t=new Nd({}),r=new Ze({props:{filepath:o[0].file.pathName}}),{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment)},m(a,u){V(t,a,u),nt(a,n,u),V(r,a,u),s=!0},p(a,u){const l={};1&u&&(l.filepath=a[0].file.pathName),r.$set(l)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&rt(n),Z(t,a),Z(r,a)}}}function _0(o){let t,n,r;var s=ka(o[0].personality.type);return s&&(t=Xi(s,{})),{c(){t&&B(t.$$.fragment),n=tr()},m(a,u){t&&V(t,a,u),nt(a,n,u),r=!0},p(a,u){if(1&u&&s!==(s=ka(a[0].personality.type))){if(t){be();const l=t;z(l.$$.fragment,1,0,()=>{Z(l,1)}),xe()}s?(t=Xi(s,{}),B(t.$$.fragment),F(t.$$.fragment,1),V(t,n.parentNode,n)):t=null}},i(a){r||(t&&F(t.$$.fragment,a),r=!0)},o(a){t&&z(t.$$.fragment,a),r=!1},d(a){a&&rt(n),t&&Z(t,a)}}}function v0(o){let t,n=o[0].label+"";return{c(){t=he(n)},m(r,s){nt(r,t,s)},p(r,s){1&s&&n!==(n=r[0].label+"")&&Dn(t,n)},d(r){r&&rt(t)}}}function y0(o){let t,n=o[0].personality.description+"";return{c(){t=he(n)},m(r,s){nt(r,t,s)},p(r,s){1&s&&n!==(n=r[0].personality.description+"")&&Dn(t,n)},d(r){r&&rt(t)}}}function fd(o){let t,n,r,s;const a=[x0,b0],u=[];function l(d,m){var p;return(p=d[0].rulesAndGuidelinesState)!=null&&p.overLimit?0:d[0].userGuidelines.overLimit?1:-1}return~(t=l(o))&&(n=u[t]=a[t](o)),{c(){n&&n.c(),r=tr()},m(d,m){~t&&u[t].m(d,m),nt(d,r,m),s=!0},p(d,m){let p=t;t=l(d),t===p?~t&&u[t].p(d,m):(n&&(be(),z(u[p],1,1,()=>{u[p]=null}),xe()),~t?(n=u[t],n?n.p(d,m):(n=u[t]=a[t](d),n.c()),F(n,1),n.m(r.parentNode,r)):n=null)},i(d){s||(F(n),s=!0)},o(d){z(n),s=!1},d(d){d&&rt(r),~t&&u[t].d(d)}}}function b0(o){let t,n;return t=new fa({props:{size:1,$$slots:{default:[$0]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};3&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function x0(o){let t,n;return t=new fa({props:{size:1,$$slots:{default:[w0]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};3&s&&(a.$$scope={dirty:s,ctx:r}),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function $0(o){let t,n=`Guidelines exceeded length limit of ${o[0].userGuidelines.lengthLimit} characters`;return{c(){t=he(n)},m(r,s){nt(r,t,s)},p(r,s){1&s&&n!==(n=`Guidelines exceeded length limit of ${r[0].userGuidelines.lengthLimit} characters`)&&Dn(t,n)},d(r){r&&rt(t)}}}function w0(o){let t,n=`Rules and workspace guidelines (${o[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${o[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`;return{c(){t=he(n)},m(r,s){nt(r,t,s)},p(r,s){1&s&&n!==(n=`Rules and workspace guidelines (${r[0].rulesAndGuidelinesState.totalCharacterCount} chars)
          exceeded limit of ${r[0].rulesAndGuidelinesState.lengthLimit} characters, remove some rules
          or reduce the length of your guidelines.`)&&Dn(t,n)},d(r){r&&rt(t)}}}function k0(o){let t,n,r,s,a,u,l,d,m,p,y,b,w;const T=[m0,g0,p0,f0,d0,h0,l0,u0,c0,a0],j=[];function q(S,E){return 1&E&&(n=null),1&E&&(r=null),1&E&&(s=null),1&E&&(a=null),1&E&&(u=null),1&E&&(l=null),1&E&&(d=null),1&E&&(m=null),1&E&&(p=null),n==null&&(n=!(!S[0]||!$u(S[0]))),n?0:(r==null&&(r=!(!S[0]||!ga(S[0]))),r?1:(s==null&&(s=!(!S[0]||!wu(S[0]))),s?2:(a==null&&(a=!(!S[0]||!ku(S[0]))),a?3:(u==null&&(u=!(!S[0]||!_a(S[0]))),u?4:(l==null&&(l=!!(S[0]&&va(S[0])&&S[0].userGuidelines.enabled)),l?5:(d==null&&(d=!(!S[0]||!ma(S[0]))),d?6:(m==null&&(m=!(!S[0]||!Ca(S[0]))),m?7:(p==null&&(p=!(!S[0]||!ya(S[0]))),p?8:9))))))))}return y=q(o,-1),b=j[y]=T[y](o),{c(){t=Bt("div"),b.c(),jt(t,"class","c-mention-hover-contents svelte-11069rs")},m(S,E){nt(S,t,E),j[y].m(t,null),w=!0},p(S,[E]){let G=y;y=q(S,E),y===G?j[y].p(S,E):(be(),z(j[G],1,1,()=>{j[G]=null}),xe(),b=j[y],b?b.p(S,E):(b=j[y]=T[y](S),b.c()),F(b,1),b.m(t,null))},i(S){w||(F(b),w=!0)},o(S){z(b),w=!1},d(S){S&&rt(t),j[y].d()}}}function C0(o,t,n){let{option:r}=t;return o.$$set=s=>{"option"in s&&n(0,r=s.option)},[r]}class S0 extends gi{constructor(t){super(),mi(this,t,C0,k0,_i,{option:0})}}function pd(o,t,n){const r=o.slice();return r[15]=t[n],r}function gd(o){let t,n;function r(){return o[8](o[15])}return t=new o0({props:{item:o[15],highlight:o[15]===o[14],onSelect:r}}),{c(){B(t.$$.fragment)},m(s,a){V(t,s,a),n=!0},p(s,a){o=s;const u={};4&a&&(u.item=o[15]),16388&a&&(u.highlight=o[15]===o[14]),4&a&&(u.onSelect=r),t.$set(u)},i(s){n||(F(t.$$.fragment,s),n=!0)},o(s){z(t.$$.fragment,s),n=!1},d(s){Z(t,s)}}}function I0(o){let t,n,r=pa(o[2]),s=[];for(let u=0;u<r.length;u+=1)s[u]=gd(pd(o,r,u));const a=u=>z(s[u],1,1,()=>{s[u]=null});return{c(){for(let u=0;u<s.length;u+=1)s[u].c();t=tr()},m(u,l){for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(u,l);nt(u,t,l),n=!0},p(u,l){if(16420&l){let d;for(r=pa(u[2]),d=0;d<r.length;d+=1){const m=pd(u,r,d);s[d]?(s[d].p(m,l),F(s[d],1)):(s[d]=gd(m),s[d].c(),F(s[d],1),s[d].m(t.parentNode,t))}for(be(),d=r.length;d<s.length;d+=1)a(d);xe()}},i(u){if(!n){for(let l=0;l<r.length;l+=1)F(s[l]);n=!0}},o(u){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)z(s[l]);n=!1},d(u){u&&rt(t),Ed(s,u)}}}function M0(o){let t,n;return t=new S0({props:{slot:"mentionable",option:o[13]}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p(r,s){const a={};8192&s&&(a.option=r[13]),t.$set(a)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function E0(o){let t,n,r,s;return t=new tu.Menu.Root({props:{mentionables:o[2],onQueryUpdate:o[4],onSelectMentionable:o[5],$$slots:{default:[I0,({activeItem:a})=>({14:a}),({activeItem:a})=>a?16384:0]},$$scope:{ctx:o}}}),r=new tu.ChipTooltip({props:{$$slots:{mentionable:[M0,({mentionable:a})=>({13:a}),({mentionable:a})=>a?8192:0]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment)},m(a,u){V(t,a,u),nt(a,n,u),V(r,a,u),s=!0},p(a,u){const l={};4&u&&(l.mentionables=a[2]),278532&u&&(l.$$scope={dirty:u,ctx:a}),t.$set(l);const d={};270336&u&&(d.$$scope={dirty:u,ctx:a}),r.$set(d)},i(a){s||(F(t.$$.fragment,a),F(r.$$.fragment,a),s=!0)},o(a){z(t.$$.fragment,a),z(r.$$.fragment,a),s=!1},d(a){a&&rt(n),Z(t,a),Z(r,a)}}}function A0(o){let t,n,r={triggerCharacter:"@",onMentionItemsUpdated:o[0],$$slots:{default:[E0]},$$scope:{ctx:o}};return t=new tu.Root({props:r}),o[9](t),{c(){B(t.$$.fragment)},m(s,a){V(t,s,a),n=!0},p(s,[a]){const u={};1&a&&(u.onMentionItemsUpdated=s[0]),262148&a&&(u.$$scope={dirty:a,ctx:s}),t.$set(u)},i(s){n||(F(t.$$.fragment,s),n=!0)},o(s){z(t.$$.fragment,s),n=!1},d(s){o[9](null),Z(t,s)}}}function T0(o,t,n){let r,{requestEditorFocus:s}=t,{onMentionItemsUpdated:a}=t;const u=bg("chatModel");if(!u)throw new Error("ChatModel not found in context");const l=new yu(u,p),d=l.displayItems;let m;function p(b){return!!m&&(m.insertMention(b),l.closeDropdown(),!0)}function y(b){const w=l.selectMentionable(b);return s(),w}return fi(o,d,b=>n(2,r=b)),xu(()=>{l.dispose()}),o.$$set=b=>{"requestEditorFocus"in b&&n(6,s=b.requestEditorFocus),"onMentionItemsUpdated"in b&&n(0,a=b.onMentionItemsUpdated)},[a,m,r,d,function(b){b===void 0?l.closeDropdown():(l.openDropdown(),l.userQuery.set(b))},y,s,b=>p(b),b=>y(b),function(b){Gi[b?"unshift":"push"](()=>{m=b,n(1,m)})}]}class F0 extends gi{constructor(t){super(),mi(this,t,T0,A0,_i,{requestEditorFocus:6,onMentionItemsUpdated:0,insertMentionNode:7})}get insertMentionNode(){return this.$$.ctx[7]}}function md(o){let t,n,r,s,a,u,l,d,m,p,y,b,w,T,j={focusOnInit:!0,$$slots:{default:[R0]},$$scope:{ctx:o}};return u=new jd.Root({props:j}),o[25](u),m=new Hr({props:{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$slots:{default:[O0]},$$scope:{ctx:o}}}),m.$on("click",function(){_r(o[0].disposeDiffViewPanel)&&o[0].disposeDiffViewPanel.apply(this,arguments)}),y=new Hr({props:{id:"send",size:1,variant:"solid",color:"accent",title:o[3]===Gr.instruction?"Instruct Augment":"Edit with Augment",disabled:!o[4].trim()||o[11],$$slots:{iconRight:[N0],default:[L0]},$$scope:{ctx:o}}}),y.$on("click",o[14]),{c(){t=Bt("div"),n=qt(),r=Bt("div"),s=Bt("div"),a=Bt("div"),B(u.$$.fragment),l=qt(),d=Bt("div"),B(m.$$.fragment),p=qt(),B(y.$$.fragment),jt(a,"class","l-input-area__input svelte-1cxscce"),jt(d,"class","c-instruction-drawer-panel__btn-container svelte-1cxscce"),jt(s,"class","instruction-drawer-panel__contents svelte-1cxscce"),jt(s,"tabindex","0"),jt(s,"role","button"),jt(r,"class","instruction-drawer-panel svelte-1cxscce"),Hi(r,"top",o[5]+"px"),Hi(r,"height",o[6]+"px")},m(q,S){nt(q,t,S),nt(q,n,S),nt(q,r,S),Vt(r,s),Vt(s,a),V(u,a,null),o[26](a),Vt(s,l),Vt(s,d),V(m,d,null),Vt(d,p),V(y,d,null),b=!0,w||(T=[Id(o[15].call(null,t)),Wr(s,"click",o[17]),Wr(s,"keydown",o[27])],w=!0)},p(q,S){o=q;const E={};1296&S[0]|256&S[1]&&(E.$$scope={dirty:S,ctx:o}),u.$set(E);const G={};256&S[1]&&(G.$$scope={dirty:S,ctx:o}),m.$set(G);const ot={};8&S[0]&&(ot.title=o[3]===Gr.instruction?"Instruct Augment":"Edit with Augment"),2064&S[0]&&(ot.disabled=!o[4].trim()||o[11]),8&S[0]|256&S[1]&&(ot.$$scope={dirty:S,ctx:o}),y.$set(ot),(!b||32&S[0])&&Hi(r,"top",o[5]+"px"),(!b||64&S[0])&&Hi(r,"height",o[6]+"px")},i(q){b||(F(u.$$.fragment,q),F(m.$$.fragment,q),F(y.$$.fragment,q),b=!0)},o(q){z(u.$$.fragment,q),z(m.$$.fragment,q),z(y.$$.fragment,q),b=!1},d(q){q&&(rt(t),rt(n),rt(r)),o[25](null),Z(u),o[26](null),Z(m),Z(y),w=!1,bu(T)}}}function R0(o){let t,n,r,s,a,u,l,d;t=new em({props:{shortcuts:{Enter:o[23]}}});let m={requestEditorFocus:o[16],onMentionItemsUpdated:o[18]};return r=new F0({props:m}),o[24](r),a=new jd.Content({props:{content:o[4],onContentChanged:o[19]}}),l=new nm({props:{placeholder:o[10]}}),{c(){B(t.$$.fragment),n=qt(),B(r.$$.fragment),s=qt(),B(a.$$.fragment),u=qt(),B(l.$$.fragment)},m(p,y){V(t,p,y),nt(p,n,y),V(r,p,y),nt(p,s,y),V(a,p,y),nt(p,u,y),V(l,p,y),d=!0},p(p,y){r.$set({});const b={};16&y[0]&&(b.content=p[4]),a.$set(b);const w={};1024&y[0]&&(w.placeholder=p[10]),l.$set(w)},i(p){d||(F(t.$$.fragment,p),F(r.$$.fragment,p),F(a.$$.fragment,p),F(l.$$.fragment,p),d=!0)},o(p){z(t.$$.fragment,p),z(r.$$.fragment,p),z(a.$$.fragment,p),z(l.$$.fragment,p),d=!1},d(p){p&&(rt(n),rt(s),rt(u)),Z(t,p),o[24](null),Z(r,p),Z(a,p),Z(l,p)}}}function O0(o){let t,n,r;return n=new vi({props:{keybinding:"esc"}}),{c(){t=he(`Close
          `),B(n.$$.fragment)},m(s,a){nt(s,t,a),V(n,s,a),r=!0},p:Ht,i(s){r||(F(n.$$.fragment,s),r=!0)},o(s){z(n.$$.fragment,s),r=!1},d(s){s&&rt(t),Z(n,s)}}}function L0(o){let t,n=o[3]===Gr.instruction?"Instruct":"Edit";return{c(){t=he(n)},m(r,s){nt(r,t,s)},p(r,s){8&s[0]&&n!==(n=r[3]===Gr.instruction?"Instruct":"Edit")&&Dn(t,n)},d(r){r&&rt(t)}}}function N0(o){let t,n;return t=new qg({props:{slot:"iconRight"}}),{c(){B(t.$$.fragment)},m(r,s){V(t,r,s),n=!0},p:Ht,i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){z(t.$$.fragment,r),n=!1},d(r){Z(t,r)}}}function D0(o){let t,n,r=o[2]&&md(o);return{c(){r&&r.c(),t=tr()},m(s,a){r&&r.m(s,a),nt(s,t,a),n=!0},p(s,a){s[2]?r?(r.p(s,a),4&a[0]&&F(r,1)):(r=md(s),r.c(),F(r,1),r.m(t.parentNode,t)):r&&(be(),z(r,1,1,()=>{r=null}),xe())},i(s){n||(F(r),n=!0)},o(s){z(r),n=!1},d(s){s&&rt(t),r&&r.d(s)}}}function z0(o,t,n){let r,s,a,u,l,d,m=Ht,p=()=>(m(),m=mr(b,ft=>n(22,u=ft)),b),y=Ht;o.$$.on_destroy.push(()=>m()),o.$$.on_destroy.push(()=>y());let{diffViewModel:b}=t;p();let{initialConversation:w}=t,{initialFlags:T}=t;const j=im.getContext().monaco,q={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},S=new Dd(zs);let E=new Jd;S.registerConsumer(E);let G=new Tu(S,zs,E,{initialConversation:w,initialFlags:T});const ot=G.currentConversationModel;let st,Nt;S.registerConsumer(G),function(ft){xg("chatModel",ft)}(G);let pt,St="";const mt=b.mode;fi(o,mt,ft=>n(3,l=ft));const Ft=b.selectionLines;function Gt(){const ft=b.getModifiedEditor(),$n=ae(j);if(!ft||!$n||(pt==null||pt.clear(),!a))return;const zn=a.start,er=a.end,yr={range:new $n.Range(zn+1,1,er+1,1),options:q};pt||(pt=ft.createDecorationsCollection()),pt.set([yr])}function ue(){return!!(St!=null&&St.trim())&&(b.handleInstructionSubmit(St),!0)}fi(o,Ft,ft=>n(2,a=ft)),Ad(async()=>{await Xc(),Se(),n(5,de=b.editorOffset)}),xu(()=>{st==null||st.destroy(),pt==null||pt.clear()});let Ct,$t,de=0,xn=57;const Se=()=>Ct==null?void 0:Ct.forceFocus();return o.$$set=ft=>{"diffViewModel"in ft&&p(n(0,b=ft.diffViewModel)),"initialConversation"in ft&&n(20,w=ft.initialConversation),"initialFlags"in ft&&n(21,T=ft.initialFlags)},o.$$.update=()=>{if(8&o.$$.dirty[0]&&n(10,r=(l===Gr.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs"),4194304&o.$$.dirty[0]&&(n(9,s=u.isLoading),y(),y=mr(s,ft=>n(11,d=ft))),6&o.$$.dirty[0]&&Nt){if(a==null)n(6,xn=0);else{const ft=Nt.scrollHeight;n(6,xn=Math.min(40+ft,108))}st==null||st.update({heightInPx:xn}),Gt()}},[b,Nt,a,l,St,de,xn,Ct,$t,s,r,d,mt,Ft,ue,function(ft){if(ft){const $n=a?a.start:1;st=b.renderInstructionsDrawerViewZone(ft,{line:$n,heightInPx:xn,onDomNodeTop:zn=>{n(5,de=b.editorOffset+zn)},autoFocus:!0}),Gt()}},()=>Ct==null?void 0:Ct.requestFocus(),Se,ft=>{ot.saveDraftMentions(ft.current)},function(ft){n(4,St=ft.rawText)},w,T,u,()=>ue(),function(ft){Gi[ft?"unshift":"push"](()=>{$t=ft,n(8,$t)})},function(ft){Gi[ft?"unshift":"push"](()=>{Ct=ft,n(7,Ct)})},function(ft){Gi[ft?"unshift":"push"](()=>{Nt=ft,n(1,Nt)})},ft=>{ft.key==="Enter"&&(Se(),ft.stopPropagation(),ft.preventDefault())}]}class j0 extends gi{constructor(t){super(),mi(this,t,z0,D0,_i,{diffViewModel:0,initialConversation:20,initialFlags:21},null,[-1,-1])}}const{window:Yc}=kg;function _d(o,t,n){const r=o.slice();return r[17]=t[n],r[19]=n,r}function vd(o){let t,n,r,s,a;return n=new v_({props:{diffViewModel:o[3]}}),s=new j0({props:{diffViewModel:o[3]}}),{c(){t=Bt("div"),B(n.$$.fragment),r=qt(),B(s.$$.fragment),jt(t,"class","sticky-top svelte-453n6i")},m(u,l){nt(u,t,l),V(n,t,null),nt(u,r,l),V(s,u,l),a=!0},p(u,l){const d={};8&l&&(d.diffViewModel=u[3]),n.$set(d);const m={};8&l&&(m.diffViewModel=u[3]),s.$set(m)},i(u){a||(F(n.$$.fragment,u),F(s.$$.fragment,u),a=!0)},o(u){z(n.$$.fragment,u),z(s.$$.fragment,u),a=!1},d(u){u&&(rt(t),rt(r)),Z(n),Z(s,u)}}}function yd(o){let t,n,r=pa(o[4]),s=[];for(let u=0;u<r.length;u+=1)s[u]=xd(_d(o,r,u));const a=u=>z(s[u],1,1,()=>{s[u]=null});return{c(){for(let u=0;u<s.length;u+=1)s[u].c();t=tr()},m(u,l){for(let d=0;d<s.length;d+=1)s[d]&&s[d].m(u,l);nt(u,t,l),n=!0},p(u,l){if(280&l){let d;for(r=pa(u[4]),d=0;d<r.length;d+=1){const m=_d(u,r,d);s[d]?(s[d].p(m,l),F(s[d],1)):(s[d]=xd(m),s[d].c(),F(s[d],1),s[d].m(t.parentNode,t))}for(be(),d=r.length;d<s.length;d+=1)a(d);xe()}},i(u){if(!n){for(let l=0;l<r.length;l+=1)F(s[l]);n=!0}},o(u){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)z(s[l]);n=!1},d(u){u&&rt(t),Ed(s,u)}}}function bd(o){var a;let t,n;function r(){return o[14](o[17])}function s(){return o[15](o[17])}return t=new o_({props:{isFocused:((a=o[3])==null?void 0:a.currFocusedChunkIdx)===o[19],onAccept:r,onReject:s,diffViewModel:o[3],leaf:o[17],align:"right",disableApply:o[8]}}),{c(){B(t.$$.fragment)},m(u,l){V(t,u,l),n=!0},p(u,l){var m;o=u;const d={};8&l&&(d.isFocused=((m=o[3])==null?void 0:m.currFocusedChunkIdx)===o[19]),24&l&&(d.onAccept=r),24&l&&(d.onReject=s),8&l&&(d.diffViewModel=o[3]),16&l&&(d.leaf=o[17]),256&l&&(d.disableApply=o[8]),t.$set(d)},i(u){n||(F(t.$$.fragment,u),n=!0)},o(u){z(t.$$.fragment,u),n=!1},d(u){Z(t,u)}}}function xd(o){let t,n,r=o[17].unitOfCodeWork.modifiedCode!==o[17].unitOfCodeWork.originalCode&&bd(o);return{c(){r&&r.c(),t=tr()},m(s,a){r&&r.m(s,a),nt(s,t,a),n=!0},p(s,a){s[17].unitOfCodeWork.modifiedCode!==s[17].unitOfCodeWork.originalCode?r?(r.p(s,a),16&a&&F(r,1)):(r=bd(s),r.c(),F(r,1),r.m(t.parentNode,t)):r&&(be(),z(r,1,1,()=>{r=null}),xe())},i(s){n||(F(r),n=!0)},o(s){z(r),n=!1},d(s){s&&rt(t),r&&r.d(s)}}}function q0(o){var m;let t,n,r,s,a,u,l=o[3]&&vd(o),d=o[3]&&((m=o[4])==null?void 0:m.length)&&!o[7]&&yd(o);return{c(){t=Bt("div"),l&&l.c(),n=qt(),r=Bt("div"),s=Bt("div"),a=qt(),d&&d.c(),jt(s,"class","editor svelte-453n6i"),jt(r,"class","editor-container svelte-453n6i"),jt(t,"class","diff-view-container svelte-453n6i")},m(p,y){nt(p,t,y),l&&l.m(t,null),Vt(t,n),Vt(t,r),Vt(r,s),o[13](s),Vt(r,a),d&&d.m(r,null),u=!0},p(p,y){var b;p[3]?l?(l.p(p,y),8&y&&F(l,1)):(l=vd(p),l.c(),F(l,1),l.m(t,n)):l&&(be(),z(l,1,1,()=>{l=null}),xe()),p[3]&&((b=p[4])!=null&&b.length)&&!p[7]?d?(d.p(p,y),152&y&&F(d,1)):(d=yd(p),d.c(),F(d,1),d.m(r,null)):d&&(be(),z(d,1,1,()=>{d=null}),xe())},i(p){u||(F(l),F(d),u=!0)},o(p){z(l),z(d),u=!1},d(p){p&&rt(t),l&&l.d(),o[13](null),d&&d.d()}}}function U0(o){let t,n,r,s;return t=new sm.Root({props:{$$slots:{default:[q0]},$$scope:{ctx:o}}}),{c(){B(t.$$.fragment)},m(a,u){V(t,a,u),n=!0,r||(s=[Wr(Yc,"message",function(){var l,d;_r((l=o[0])==null?void 0:l.handleMessageFromExtension)&&((d=o[0])==null||d.handleMessageFromExtension.apply(this,arguments))}),Wr(Yc,"focus",o[11]),Wr(Yc,"blur",o[12])],r=!0)},p(a,[u]){o=a;const l={};1048986&u&&(l.$$scope={dirty:u,ctx:o}),t.$set(l)},i(a){n||(F(t.$$.fragment,a),n=!0)},o(a){z(t.$$.fragment,a),n=!1},d(a){Z(t,a),r=!1,bu(s)}}}function P0(o,t,n){let r,s,a,u,l,d,m,p,y,b,w=Ht,T=Ht,j=Ht;function q(E){const G=wg.dark;return om((E==null?void 0:E.category)||G,E==null?void 0:E.intensity)??am.get(G)}fi(o,$g,E=>n(10,l=E)),o.$$.on_destroy.push(()=>w()),o.$$.on_destroy.push(()=>T()),o.$$.on_destroy.push(()=>j()),Ad(async()=>{n(9,b=await window.augmentDeps.monaco),b||console.error("Monaco not loaded. Diff view cannot be initialized.")}),xu(()=>{p==null||p.dispose()});let S=!1;return o.$$.update=()=>{if(1539&o.$$.dirty&&b&&y&&!p&&(n(0,p=new t_(y,q(l),b)),w(),w=mr(p,E=>n(3,u=E))),1&o.$$.dirty&&(n(6,r=p==null?void 0:p.disableApply),j(),j=mr(r,E=>n(8,m=E))),1&o.$$.dirty&&(n(5,s=p==null?void 0:p.disableResolution),T(),T=mr(s,E=>n(7,d=E))),1025&o.$$.dirty){const E=l;p&&(p==null||p.updateTheme(q(E)))}8&o.$$.dirty&&n(4,a=u==null?void 0:u.leaves),5&o.$$.dirty&&(p==null||p.updateIsWebviewFocused(S))},[p,y,S,u,a,s,r,d,m,b,l,()=>n(2,S=!0),()=>n(2,S=!1),function(E){Gi[E?"unshift":"push"](()=>{y=E,n(1,y)})},E=>u==null?void 0:u.acceptChunk(E),E=>u==null?void 0:u.rejectChunk(E)]}new class extends gi{constructor(o){super(),mi(this,o,P0,U0,_i,{})}}({target:document.getElementById("app")});
