/**
 * Mock implementation of Logger for unit testing
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export class Logger {
  private static logLevel: LogLevel = LogLevel.INFO;
  private static logs: Array<{ level: LogLevel; message: string; args: any[] }> = [];

  static initialize(): void {
    // Mock implementation - do nothing
  }

  static debug(message: string, ...args: any[]): void {
    this.log(LogLevel.DEBUG, message, ...args);
  }

  static info(message: string, ...args: any[]): void {
    this.log(LogLevel.INFO, message, ...args);
  }

  static warn(message: string, ...args: any[]): void {
    this.log(LogLevel.WARN, message, ...args);
  }

  static error(message: string, error?: any): void {
    let errorMessage = message;
    
    if (error) {
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
      } else {
        errorMessage += `: ${JSON.stringify(error)}`;
      }
    }

    this.log(LogLevel.ERROR, errorMessage);
  }

  private static log(level: LogLevel, message: string, ...args: any[]): void {
    if (level >= this.logLevel) {
      this.logs.push({ level, message, args });
      
      // Also log to console for debugging tests if needed
      if (process.env.NODE_ENV === 'test' && process.env.DEBUG_TESTS) {
        const levelStr = LogLevel[level];
        console.log(`[${levelStr}] ${message}`, ...args);
      }
    }
  }

  static show(): void {
    // Mock implementation - do nothing
  }

  static clear(): void {
    this.logs = [];
  }

  static setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  static getLogLevel(): LogLevel {
    return this.logLevel;
  }

  static dispose(): void {
    this.logs = [];
  }

  // Test utilities
  static getLogs(): Array<{ level: LogLevel; message: string; args: any[] }> {
    return [...this.logs];
  }

  static getLogsByLevel(level: LogLevel): Array<{ level: LogLevel; message: string; args: any[] }> {
    return this.logs.filter(log => log.level === level);
  }

  static clearLogs(): void {
    this.logs = [];
  }

  static hasLogWithMessage(message: string): boolean {
    return this.logs.some(log => log.message.includes(message));
  }
}
