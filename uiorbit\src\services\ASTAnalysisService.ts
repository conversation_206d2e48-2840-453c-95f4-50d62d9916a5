import * as vscode from 'vscode';
import * as ts from 'typescript';
import * as parser from '@babel/parser';
import traverse from '@babel/traverse';
import * as t from '@babel/types';
import { Logger } from '../utils/Logger';

export interface ASTNode {
  type: string;
  name?: string;
  start: number;
  end: number;
  line: number;
  column: number;
  children?: ASTNode[];
  metadata?: any;
}

export interface ComponentInfo {
  name: string;
  type: 'function' | 'class' | 'arrow';
  props?: string[];
  hooks?: string[];
  imports?: string[];
  exports?: string[];
  location: {
    start: number;
    end: number;
    line: number;
  };
}

export interface FileAnalysis {
  filePath: string;
  language: string;
  components: ComponentInfo[];
  functions: ASTNode[];
  classes: ASTNode[];
  imports: ASTNode[];
  exports: ASTNode[];
  variables: ASTNode[];
  types: ASTNode[];
  interfaces: ASTNode[];
  ast: any;
  errors: string[];
}

/**
 * AST Analysis Service for parsing and analyzing code files
 */
export class ASTAnalysisService {
  private typeChecker?: ts.TypeChecker;
  private program?: ts.Program;

  constructor() {
    Logger.info('AST Analysis Service initialized');
  }

  /**
   * Analyze a file and return comprehensive AST information
   */
  async analyzeFile(filePath: string, content?: string): Promise<FileAnalysis> {
    try {
      const fileContent = content || await this.readFile(filePath);
      const language = this.detectLanguage(filePath);

      Logger.info(`Analyzing file: ${filePath} (${language})`);

      switch (language) {
        case 'typescript':
        case 'typescriptreact':
          return await this.analyzeTypeScript(filePath, fileContent);
        case 'javascript':
        case 'javascriptreact':
          return await this.analyzeJavaScript(filePath, fileContent);
        case 'css':
        case 'scss':
        case 'less':
          return await this.analyzeCSS(filePath, fileContent);
        case 'html':
          return await this.analyzeHTML(filePath, fileContent);
        case 'json':
          return await this.analyzeJSON(filePath, fileContent);
        default:
          return this.createEmptyAnalysis(filePath, language);
      }
    } catch (error) {
      Logger.error(`Error analyzing file ${filePath}:`, error);
      return this.createErrorAnalysis(filePath, error);
    }
  }

  /**
   * Analyze TypeScript/TSX files
   */
  private async analyzeTypeScript(filePath: string, content: string): Promise<FileAnalysis> {
    const analysis: FileAnalysis = {
      filePath,
      language: 'typescript',
      components: [],
      functions: [],
      classes: [],
      imports: [],
      exports: [],
      variables: [],
      types: [],
      interfaces: [],
      ast: null,
      errors: []
    };

    try {
      // Create TypeScript source file
      const sourceFile = ts.createSourceFile(
        filePath,
        content,
        ts.ScriptTarget.Latest,
        true,
        filePath.endsWith('.tsx') ? ts.ScriptKind.TSX : ts.ScriptKind.TS
      );

      analysis.ast = sourceFile;

      // Traverse AST and extract information
      this.traverseTypeScriptAST(sourceFile, analysis);

      // Extract React components if TSX
      if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
        this.extractReactComponents(sourceFile, analysis);
      }

    } catch (error) {
      analysis.errors.push(`TypeScript parsing error: ${error.message}`);
      Logger.error('TypeScript parsing error:', error);
    }

    return analysis;
  }

  /**
   * Analyze JavaScript/JSX files using Babel
   */
  private async analyzeJavaScript(filePath: string, content: string): Promise<FileAnalysis> {
    const analysis: FileAnalysis = {
      filePath,
      language: 'javascript',
      components: [],
      functions: [],
      classes: [],
      imports: [],
      exports: [],
      variables: [],
      types: [],
      interfaces: [],
      ast: null,
      errors: []
    };

    try {
      // Parse with Babel
      const ast = parser.parse(content, {
        sourceType: 'module',
        allowImportExportEverywhere: true,
        allowReturnOutsideFunction: true,
        plugins: [
          'jsx',
          'typescript',
          'decorators-legacy',
          'classProperties',
          'objectRestSpread',
          'asyncGenerators',
          'functionBind',
          'exportDefaultFrom',
          'exportNamespaceFrom',
          'dynamicImport',
          'nullishCoalescingOperator',
          'optionalChaining'
        ]
      });

      analysis.ast = ast;

      // Traverse Babel AST
      traverse(ast, {
        // Import declarations
        ImportDeclaration: (path) => {
          analysis.imports.push(this.createASTNode(path.node, 'import'));
        },

        // Export declarations
        ExportNamedDeclaration: (path) => {
          analysis.exports.push(this.createASTNode(path.node, 'export'));
        },

        ExportDefaultDeclaration: (path) => {
          analysis.exports.push(this.createASTNode(path.node, 'export-default'));
        },

        // Function declarations
        FunctionDeclaration: (path) => {
          analysis.functions.push(this.createASTNode(path.node, 'function'));
        },

        // Arrow functions
        ArrowFunctionExpression: (path) => {
          if (t.isVariableDeclarator(path.parent) && t.isIdentifier(path.parent.id)) {
            analysis.functions.push(this.createASTNode(path.node, 'arrow-function', path.parent.id.name));
          }
        },

        // Class declarations
        ClassDeclaration: (path) => {
          analysis.classes.push(this.createASTNode(path.node, 'class'));
        },

        // Variable declarations
        VariableDeclaration: (path) => {
          path.node.declarations.forEach(declarator => {
            if (t.isIdentifier(declarator.id)) {
              analysis.variables.push(this.createASTNode(declarator, 'variable', declarator.id.name));
            }
          });
        },

        // TypeScript interfaces
        TSInterfaceDeclaration: (path) => {
          analysis.interfaces.push(this.createASTNode(path.node, 'interface'));
        },

        // TypeScript type aliases
        TSTypeAliasDeclaration: (path) => {
          analysis.types.push(this.createASTNode(path.node, 'type'));
        }
      });

      // Extract React components for JSX files
      if (filePath.endsWith('.jsx') || filePath.endsWith('.tsx')) {
        this.extractReactComponentsBabel(ast, analysis);
      }

    } catch (error) {
      analysis.errors.push(`JavaScript parsing error: ${error.message}`);
      Logger.error('JavaScript parsing error:', error);
    }

    return analysis;
  }

  /**
   * Extract React components from TypeScript AST
   */
  private extractReactComponents(sourceFile: ts.SourceFile, analysis: FileAnalysis): void {
    const visit = (node: ts.Node) => {
      // Function components
      if (ts.isFunctionDeclaration(node) && node.name) {
        if (this.isReactComponent(node)) {
          const component: ComponentInfo = {
            name: node.name.text,
            type: 'function',
            props: this.extractProps(node),
            hooks: this.extractHooks(node),
            location: {
              start: node.getStart(),
              end: node.getEnd(),
              line: sourceFile.getLineAndCharacterOfPosition(node.getStart()).line + 1
            }
          };
          analysis.components.push(component);
        }
      }

      // Arrow function components
      if (ts.isVariableStatement(node)) {
        node.declarationList.declarations.forEach(declaration => {
          if (ts.isIdentifier(declaration.name) && 
              declaration.initializer && 
              ts.isArrowFunction(declaration.initializer)) {
            if (this.isReactComponent(declaration.initializer)) {
              const component: ComponentInfo = {
                name: declaration.name.text,
                type: 'arrow',
                props: this.extractProps(declaration.initializer),
                hooks: this.extractHooks(declaration.initializer),
                location: {
                  start: declaration.getStart(),
                  end: declaration.getEnd(),
                  line: sourceFile.getLineAndCharacterOfPosition(declaration.getStart()).line + 1
                }
              };
              analysis.components.push(component);
            }
          }
        });
      }

      // Class components
      if (ts.isClassDeclaration(node) && node.name) {
        if (this.isReactClassComponent(node)) {
          const component: ComponentInfo = {
            name: node.name.text,
            type: 'class',
            location: {
              start: node.getStart(),
              end: node.getEnd(),
              line: sourceFile.getLineAndCharacterOfPosition(node.getStart()).line + 1
            }
          };
          analysis.components.push(component);
        }
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
  }

  /**
   * Extract React components from Babel AST
   */
  private extractReactComponentsBabel(ast: any, analysis: FileAnalysis): void {
    traverse(ast, {
      FunctionDeclaration: (path) => {
        if (this.isReactComponentBabel(path.node)) {
          const component: ComponentInfo = {
            name: path.node.id?.name || 'Anonymous',
            type: 'function',
            props: this.extractPropsBabel(path.node),
            hooks: this.extractHooksBabel(path.node),
            location: {
              start: path.node.start || 0,
              end: path.node.end || 0,
              line: path.node.loc?.start.line || 0
            }
          };
          analysis.components.push(component);
        }
      },

      VariableDeclarator: (path) => {
        if (t.isIdentifier(path.node.id) && 
            t.isArrowFunctionExpression(path.node.init) &&
            this.isReactComponentBabel(path.node.init)) {
          const component: ComponentInfo = {
            name: path.node.id.name,
            type: 'arrow',
            props: this.extractPropsBabel(path.node.init),
            hooks: this.extractHooksBabel(path.node.init),
            location: {
              start: path.node.start || 0,
              end: path.node.end || 0,
              line: path.node.loc?.start.line || 0
            }
          };
          analysis.components.push(component);
        }
      }
    });
  }

  /**
   * Create AST node representation
   */
  private createASTNode(node: any, type: string, name?: string): ASTNode {
    return {
      type,
      name: name || (node.id?.name) || (node.name?.text) || 'anonymous',
      start: node.start || 0,
      end: node.end || 0,
      line: node.loc?.start?.line || 0,
      column: node.loc?.start?.column || 0,
      metadata: {
        kind: node.kind,
        modifiers: node.modifiers
      }
    };
  }

  /**
   * Helper methods for React component detection and analysis
   */
  private isReactComponent(node: any): boolean {
    // Check if function returns JSX
    return this.containsJSX(node);
  }

  private isReactComponentBabel(node: any): boolean {
    return this.containsJSXBabel(node);
  }

  private isReactClassComponent(node: ts.ClassDeclaration): boolean {
    // Check if extends React.Component or Component
    return node.heritageClauses?.some(clause => 
      clause.types.some(type => 
        ts.isIdentifier(type.expression) && 
        (type.expression.text === 'Component' || type.expression.text === 'PureComponent')
      )
    ) || false;
  }

  private containsJSX(node: any): boolean {
    // Simplified JSX detection - would need more sophisticated logic
    return true; // Placeholder
  }

  private containsJSXBabel(node: any): boolean {
    // Simplified JSX detection for Babel AST
    return true; // Placeholder
  }

  private extractProps(node: any): string[] {
    // Extract prop names from function parameters
    return []; // Placeholder
  }

  private extractPropsBabel(node: any): string[] {
    return []; // Placeholder
  }

  private extractHooks(node: any): string[] {
    // Extract React hooks usage
    return []; // Placeholder
  }

  private extractHooksBabel(node: any): string[] {
    return []; // Placeholder
  }

  /**
   * Utility methods
   */
  private async readFile(filePath: string): Promise<string> {
    const uri = vscode.Uri.file(filePath);
    const document = await vscode.workspace.openTextDocument(uri);
    return document.getText();
  }

  private detectLanguage(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'ts': 'typescript',
      'tsx': 'typescriptreact',
      'js': 'javascript',
      'jsx': 'javascriptreact',
      'css': 'css',
      'scss': 'scss',
      'less': 'less',
      'html': 'html',
      'json': 'json'
    };
    return languageMap[ext || ''] || 'text';
  }

  private traverseTypeScriptAST(node: ts.Node, analysis: FileAnalysis): void {
    // Traverse TypeScript AST and extract symbols
    // Implementation would go here
  }

  private async analyzeCSS(filePath: string, content: string): Promise<FileAnalysis> {
    // CSS analysis implementation
    return this.createEmptyAnalysis(filePath, 'css');
  }

  private async analyzeHTML(filePath: string, content: string): Promise<FileAnalysis> {
    // HTML analysis implementation
    return this.createEmptyAnalysis(filePath, 'html');
  }

  private async analyzeJSON(filePath: string, content: string): Promise<FileAnalysis> {
    // JSON analysis implementation
    return this.createEmptyAnalysis(filePath, 'json');
  }

  private createEmptyAnalysis(filePath: string, language: string): FileAnalysis {
    return {
      filePath,
      language,
      components: [],
      functions: [],
      classes: [],
      imports: [],
      exports: [],
      variables: [],
      types: [],
      interfaces: [],
      ast: null,
      errors: []
    };
  }

  private createErrorAnalysis(filePath: string, error: any): FileAnalysis {
    return {
      filePath,
      language: 'unknown',
      components: [],
      functions: [],
      classes: [],
      imports: [],
      exports: [],
      variables: [],
      types: [],
      interfaces: [],
      ast: null,
      errors: [error.message || 'Unknown error']
    };
  }
}
