import * as path from 'path';
import { Logger } from '../utils/Logger';
import { ProjectContext } from './ProjectDetectionService';

export interface ComponentTemplate {
  id: string;
  name: string;
  description: string;
  category: 'ui' | 'layout' | 'form' | 'navigation' | 'data' | 'utility';
  framework: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components' | 'emotion';
  complexity: 'simple' | 'medium' | 'advanced';
  files: TemplateFile[];
  variables: TemplateVariable[];
  dependencies?: string[];
  tags: string[];
}

export interface TemplateFile {
  path: string;
  content: string;
  type: 'component' | 'style' | 'test' | 'story' | 'types' | 'config';
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'boolean' | 'number' | 'array' | 'object';
  description: string;
  defaultValue?: any;
  required: boolean;
  options?: string[];
}

export interface TemplateRenderOptions {
  componentName: string;
  variables: Record<string, any>;
  targetPath?: string;
  framework?: string;
  styling?: string;
}

/**
 * Template Engine Service for UIOrbit
 * Manages and renders component templates with modern UI patterns
 */
export class TemplateEngineService {
  private templates: Map<string, ComponentTemplate> = new Map();

  constructor() {
    this.initializeBuiltInTemplates();
  }

  /**
   * Get all available templates
   */
  getTemplates(filters?: {
    category?: string;
    framework?: string;
    styling?: string;
    complexity?: string;
  }): ComponentTemplate[] {
    let templates = Array.from(this.templates.values());

    if (filters) {
      if (filters.category) {
        templates = templates.filter(t => t.category === filters.category);
      }
      if (filters.framework) {
        templates = templates.filter(t => t.framework === filters.framework);
      }
      if (filters.styling) {
        templates = templates.filter(t => t.styling === filters.styling);
      }
      if (filters.complexity) {
        templates = templates.filter(t => t.complexity === filters.complexity);
      }
    }

    return templates;
  }

  /**
   * Get template by ID
   */
  getTemplate(id: string): ComponentTemplate | null {
    return this.templates.get(id) || null;
  }

  /**
   * Render template with variables
   */
  renderTemplate(templateId: string, options: TemplateRenderOptions): TemplateFile[] {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    Logger.info(`Rendering template: ${templateId} for component: ${options.componentName}`);

    const renderedFiles: TemplateFile[] = [];

    for (const file of template.files) {
      const renderedContent = this.processTemplate(file.content, {
        componentName: options.componentName,
        ...options.variables
      });

      const renderedPath = this.processTemplate(file.path, {
        componentName: options.componentName,
        ...options.variables
      });

      renderedFiles.push({
        path: renderedPath,
        content: renderedContent,
        type: file.type
      });
    }

    return renderedFiles;
  }

  /**
   * Add custom template
   */
  addTemplate(template: ComponentTemplate): void {
    this.templates.set(template.id, template);
    Logger.info(`Added template: ${template.id}`);
  }

  /**
   * Process template string with variables
   */
  private processTemplate(template: string, variables: Record<string, any>): string {
    let processed = template;

    // Replace variables in format {{variableName}}
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      processed = processed.replace(regex, String(value));
    }

    // Handle conditional blocks {{#if condition}}...{{/if}}
    processed = this.processConditionals(processed, variables);

    // Handle loops {{#each array}}...{{/each}}
    processed = this.processLoops(processed, variables);

    // Handle helper functions
    processed = this.processHelpers(processed, variables);

    return processed;
  }

  /**
   * Process conditional blocks
   */
  private processConditionals(template: string, variables: Record<string, any>): string {
    const conditionalRegex = /{{#if\s+(\w+)}}([\s\S]*?){{\/if}}/g;
    
    return template.replace(conditionalRegex, (match, condition, content) => {
      const value = variables[condition];
      return value ? content : '';
    });
  }

  /**
   * Process loop blocks
   */
  private processLoops(template: string, variables: Record<string, any>): string {
    const loopRegex = /{{#each\s+(\w+)}}([\s\S]*?){{\/each}}/g;
    
    return template.replace(loopRegex, (match, arrayName, content) => {
      const array = variables[arrayName];
      if (!Array.isArray(array)) {
        return '';
      }

      return array.map((item, index) => {
        let itemContent = content;
        // Replace {{this}} with current item
        itemContent = itemContent.replace(/{{this}}/g, String(item));
        // Replace {{@index}} with current index
        itemContent = itemContent.replace(/{{@index}}/g, String(index));
        return itemContent;
      }).join('');
    });
  }

  /**
   * Process helper functions
   */
  private processHelpers(template: string, variables: Record<string, any>): string {
    // {{pascalCase variableName}}
    template = template.replace(/{{pascalCase\s+(\w+)}}/g, (match, varName) => {
      const value = variables[varName];
      return this.toPascalCase(String(value));
    });

    // {{camelCase variableName}}
    template = template.replace(/{{camelCase\s+(\w+)}}/g, (match, varName) => {
      const value = variables[varName];
      return this.toCamelCase(String(value));
    });

    // {{kebabCase variableName}}
    template = template.replace(/{{kebabCase\s+(\w+)}}/g, (match, varName) => {
      const value = variables[varName];
      return this.toKebabCase(String(value));
    });

    return template;
  }

  /**
   * Initialize built-in templates
   */
  private initializeBuiltInTemplates(): void {
    // Modern Button Component
    this.addTemplate({
      id: 'modern-button',
      name: 'Modern Button',
      description: 'A modern, accessible button component with multiple variants',
      category: 'ui',
      framework: 'react',
      styling: 'tailwind',
      complexity: 'simple',
      tags: ['button', 'ui', 'interactive', 'accessible'],
      files: [
        {
          path: '{{componentName}}.tsx',
          content: `import React from 'react';
import { cn } from '@/lib/utils';

export interface {{componentName}}Props {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

const {{componentName}}: React.FC<{{componentName}}Props> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  className,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';
  
  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground'
  };
  
  const sizeClasses = {
    sm: 'h-9 px-3 text-sm',
    md: 'h-10 px-4 py-2',
    lg: 'h-11 px-8 text-lg'
  };

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {{#if loading}}
      <svg className="mr-2 h-4 w-4 animate-spin" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      {{/if}}
      {children}
    </button>
  );
};

export default {{componentName}};`,
          type: 'component'
        },
        {
          path: '{{componentName}}.stories.tsx',
          content: `import type { Meta, StoryObj } from '@storybook/react';
import {{componentName}} from './{{componentName}}';

const meta: Meta<typeof {{componentName}}> = {
  title: 'UI/{{componentName}}',
  component: {{componentName}},
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'outline', 'ghost'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    children: 'Button',
    variant: 'primary',
  },
};

export const Secondary: Story = {
  args: {
    children: 'Button',
    variant: 'secondary',
  },
};

export const Outline: Story = {
  args: {
    children: 'Button',
    variant: 'outline',
  },
};

export const Loading: Story = {
  args: {
    children: 'Please wait',
    loading: true,
  },
};`,
          type: 'story'
        }
      ],
      variables: [
        {
          name: 'componentName',
          type: 'string',
          description: 'Name of the button component',
          defaultValue: 'Button',
          required: true
        },
        {
          name: 'loading',
          type: 'boolean',
          description: 'Include loading state',
          defaultValue: true,
          required: false
        }
      ]
    });

    // Modern Card Component
    this.addTemplate({
      id: 'modern-card',
      name: 'Modern Card',
      description: 'A flexible card component with glassmorphism design',
      category: 'ui',
      framework: 'react',
      styling: 'tailwind',
      complexity: 'medium',
      tags: ['card', 'container', 'glassmorphism', 'modern'],
      files: [
        {
          path: '{{componentName}}.tsx',
          content: `import React from 'react';
import { cn } from '@/lib/utils';

export interface {{componentName}}Props {
  children: React.ReactNode;
  variant?: 'default' | 'glass' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

const {{componentName}}: React.FC<{{componentName}}Props> = ({
  children,
  variant = 'default',
  padding = 'md',
  className,
  header,
  footer,
  ...props
}) => {
  const baseClasses = 'rounded-lg transition-all duration-200';
  
  const variantClasses = {
    default: 'bg-card text-card-foreground border border-border',
    glass: 'bg-white/10 backdrop-blur-md border border-white/20 shadow-lg',
    elevated: 'bg-card text-card-foreground shadow-lg hover:shadow-xl',
    outlined: 'bg-transparent border-2 border-border hover:border-primary/50'
  };
  
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        paddingClasses[padding],
        className
      )}
      {...props}
    >
      {header && (
        <div className="mb-4 border-b border-border pb-4">
          {header}
        </div>
      )}
      
      <div className="flex-1">
        {children}
      </div>
      
      {footer && (
        <div className="mt-4 border-t border-border pt-4">
          {footer}
        </div>
      )}
    </div>
  );
};

export default {{componentName}};`,
          type: 'component'
        }
      ],
      variables: [
        {
          name: 'componentName',
          type: 'string',
          description: 'Name of the card component',
          defaultValue: 'Card',
          required: true
        }
      ]
    });

    Logger.info('Built-in templates initialized');
  }

  /**
   * Utility functions for case conversion
   */
  private toPascalCase(str: string): string {
    return str.replace(/(?:^|[\s-_])+(.)/g, (match, char) => char.toUpperCase());
  }

  private toCamelCase(str: string): string {
    const pascal = this.toPascalCase(str);
    return pascal.charAt(0).toLowerCase() + pascal.slice(1);
  }

  private toKebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase();
  }
}
