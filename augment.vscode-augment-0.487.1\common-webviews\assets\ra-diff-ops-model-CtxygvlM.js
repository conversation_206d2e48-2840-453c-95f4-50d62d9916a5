var l=Object.defineProperty;var d=(r,e,a)=>e in r?l(r,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[e]=a;var p=(r,e,a)=>d(r,typeof e!="symbol"?e+"":e,a);import{W as n}from"./BaseButton-7bccWxEO.js";import{N as o}from"./SpinnerAugment-BJAAUt-n.js";class c{constructor(e){p(this,"_applyingFilePaths",o([]));p(this,"_appliedFilePaths",o([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(a=>{e=a})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(a=>{e=a})(),e}async getDiffExplanation(e,a,t=3e4){try{return(await this._asyncMsgSender.send({type:n.diffExplanationRequest,data:{changedFiles:e,apikey:a}},t)).data.explanation}catch(s){return console.error("Failed to get diff explanation:",s),[]}}async groupChanges(e,a=!1,t){try{return(await this._asyncMsgSender.send({type:n.diffGroupChangesRequest,data:{changedFiles:e,changesById:a,apikey:t}})).data.groupedChanges}catch(s){return console.error("Failed to group changes:",s),[]}}async getDescriptions(e,a){try{const t=await this._asyncMsgSender.send({type:n.diffDescriptionsRequest,data:{groupedChanges:e,apikey:a}},1e5);return{explanation:t.data.explanation,error:t.data.error}}catch(t){return console.error("Failed to get descriptions:",t),{explanation:[],error:`Failed to get descriptions: ${t instanceof Error?t.message:String(t)}`}}}async applyChanges(e,a,t){this._applyingFilePaths.update(s=>[...s.filter(i=>i!==e),e]);try{(await this._asyncMsgSender.send({type:n.applyChangesRequest,data:{path:e,originalCode:a,newCode:t}},3e4)).data.success&&this._appliedFilePaths.update(s=>[...s.filter(i=>i!==e),e])}catch(s){console.error("applyChanges error",s)}finally{this._applyingFilePaths.update(s=>s.filter(i=>i!==e))}}async openFile(e){try{const a=await this._asyncMsgSender.send({type:n.openFileRequest,data:{path:e}},1e4);return a.data.success||console.error("Failed to open file:",a.data.error),a.data.success}catch(a){console.error("openFile error",a)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:n.reportAgentChangesApplied})}}p(c,"key","remoteAgentsDiffOpsModel");export{c as R};
