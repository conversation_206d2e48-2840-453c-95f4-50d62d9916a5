var lt=Object.defineProperty;var ut=(o,e,t)=>e in o?lt(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var ne=(o,e,t)=>ut(o,typeof e!="symbol"?e+"":e,t);import{S as Gn,T as Ue,R as qe}from"./check-DWGOhZOn.js";import{R as Je,b as pe,a as Se}from"./types-DDm27S8B.js";import{g as Ye,a as dt,h as $t,b as mt,c as pt,d as gt,e as ft,f as ht,j as Ge,k as Xe,m as jn,o as Me,E as oe,p as Qe,S as vt,q as Le}from"./arrow-up-right-from-square-CdEOBPRR.js";import{S as j,i as Z,s as W,A as Q,e as v,u as $,q as N,t as p,r as H,h,ag as ce,ah as le,a as xe,V as en,W as M,E as C,J as B,K as F,c as x,X as nn,Y as V,F as _,f as R,a8 as te,g as je,a4 as wt,M as K,I as k,aa as ze,ax as xt,$ as ye,a0 as Ce,a1 as _e,a2 as ke,n as U,B as ge,an as Zn,ab as yt,C as re,a5 as Wn,D as Be,G as Pe,aE as Ne,a3 as tn,a9 as Kn,ad as be,a6 as fe,aj as he,T as ee,b as me,ap as Ct,L as Yn,aA as sn,H as _t,w as kt,x as bt,y as It,d as on,z as St,j as rn,ae as cn,af as Te}from"./SpinnerAugment-BJAAUt-n.js";import{M as Tt}from"./MaterialIcon-DkFwt_X2.js";import{o as Xn}from"./keypress-DD1aQVr0.js";import{A as an,a as Y}from"./autofix-state-d-ymFdyn.js";import{t as Rt}from"./index-Bb_d2FL8.js";import{bc as Ke,bd as Et,aq as Mt,be as Lt,bf as Qn,bg as At,bh as Ft,bi as qt,bj as Ve,bk as zt}from"./AugmentMessage-B1RETs6x.js";import{T as He}from"./Content-BldOFwN2.js";import{E as Bt,a as et,b as Pt}from"./folder-BB1rR2Vr.js";import{e as De,f as Nt,g as Ht,E as Dt,h as Ot,T as nt}from"./Keybindings-C-5u2652.js";import{c as tt,I as Ut,R as Vt,D as Jt,C as Gt,d as st,e as jt,f as Ae,g as Zt}from"./main-panel-Bd7m3n0i.js";import{B as Oe}from"./ButtonAugment-CLDnX_Hg.js";import{P as Wt,C as Kt}from"./folder-opened-BWTQdsic.js";import{E as Yt}from"./expand-CRxF6TiY.js";import{P as Xt}from"./pen-to-square-SVW9AP0k.js";import{T as Ie}from"./TextTooltipAugment-DGOJQXY9.js";import{I as ve}from"./IconButtonAugment-CqdkuyT6.js";import{C as Qt,a as es,M as ns,O as ts}from"./diff-utils-RpWUB_Gw.js";import{B as ot}from"./layer-group-CeTgJtYd.js";import{e as Fe}from"./BaseButton-7bccWxEO.js";import{C as ss}from"./CardAugment-qFWs8J9b.js";import{C as rt,a as ct}from"./CollapseButtonAugment-BPhovcAJ.js";import{M as os,S as rs}from"./index-C9A1ZQNk.js";import{C as cs}from"./github-DDehkTJf.js";import{a as Re}from"./index-CGH5qOQn.js";import{s as is}from"./utils-BW_yYq2f.js";const as=(o,e,t,n)=>{const s={retryMessage:void 0,showGeneratingResponse:!1,showResumingRemoteAgent:!1,showAwaitingUserInput:!1,showRunningSpacer:!1,showStopped:!1,remoteAgentErrorConfig:void 0,showPaused:!1};if(e===Me.running){const r=o==null?void 0:o.lastExchange;if(r!=null&&r.isRetriable&&(r!=null&&r.display_error_message))s.retryMessage=r.display_error_message;else if(t||n.isActive){const c=n.isActive?n.getLastToolUseState():o.getLastToolUseState();if(n.isActive){const i=n.currentAgent;(i==null?void 0:i.workspace_status)===Je.workspaceResuming?s.showResumingRemoteAgent=!0:c.phase!==Ue.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0}else c.phase!==Ue.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0}else s.showGeneratingResponse=!0}else e===Me.awaitingUserAction?(s.showAwaitingUserInput=!0,s.showRunningSpacer=!0):((r,c)=>{var d;const i=(d=r==null?void 0:r.lastExchange)==null?void 0:d.status,a=i===oe.cancelled,l=r==null?void 0:r.getLastToolUseState().phase,u=l===Ue.cancelled;return!c.isActive&&(a||u)})(o,n)&&(s.showStopped=!0);if(n.isActive){const r=n.currentAgent;(r==null?void 0:r.workspace_status)===Je.workspacePaused&&(s.showPaused=!0)}return s},xc=(o,e,t,n)=>{const s=o.currentConversationModel,r=((d,m)=>m.isActive?m.getCurrentChatHistory():d.chatHistory.filter(g=>Ye(g)||dt(g)||$t(g)||mt(g)||pt(g)||gt(g)||ft(g)||ht(g)||Ge(g)||Xe(g)))(s,n),c=(d=>d.reduce((m,g,w)=>(Ye(g)&&jn(g)&&m.length>0||Xe(g)&&m.length>0?m[m.length-1].push({turn:g,idx:w}):m.push([{turn:g,idx:w}]),m),[]))(r),i=((d,m)=>{if(m.isActive){const g=m.currentAgent;return(g==null?void 0:g.workspace_status)===Je.workspaceResuming||m.isCurrentAgentRunning?Me.running:Me.notRunning}return d})(e,n),a=as(s,i,t,n),l=!n.isActive,u=!!n.isActive;if(n.isActive){if(n.sendMessageError&&n.currentAgentId){const d=n.currentAgentId,m=n.sendMessageError;a.remoteAgentErrorConfig={error:m,onRetry:m.canRetry&&m.failedExchangeId?()=>n.retryFailedMessage(d,m.failedExchangeId):void 0,onDelete:m.type===Gn.agentFailed?()=>n.deleteAgent(d):void 0}}else if(n.agentChatHistoryError&&n.currentAgentId){const d=n.currentAgentId;a.remoteAgentErrorConfig={error:n.agentChatHistoryError,onRetry:()=>n.refreshAgentChatHistory(d)}}}return{chatHistory:r,groupedChatHistory:c,lastGroupConfig:a,doShowFloatingButtons:l,doShowAgentSetupLogs:u}};function ln(o){let e,t,n,s,r,c,i,a;const l=[o[4][o[1]]];let u={};for(let g=0;g<l.length;g+=1)u=xe(u,l[g]);t=new Tt({props:u});let d=[{class:"stage-container"},o[1]?en(o[3][o[1]]):{},{role:"button"},{tabindex:"0"}],m={};for(let g=0;g<d.length;g+=1)m=xe(m,d[g]);return{c(){e=M("div"),C(t.$$.fragment),n=B(),s=M("div"),r=F(o[1]),x(s,"class","message svelte-1etsput"),nn(e,m),V(e,"active",o[0]),V(e,"svelte-1etsput",!0)},m(g,w){v(g,e,w),_(t,e,null),R(e,n),R(e,s),R(s,r),c=!0,i||(a=[te(e,"click",o[5]),te(e,"keydown",Xn("Enter",o[5]))],i=!0)},p(g,w){const L=18&w?je(l,[wt(g[4][g[1]])]):{};t.$set(L),(!c||2&w)&&K(r,g[1]),nn(e,m=je(d,[{class:"stage-container"},2&w&&(g[1]?en(g[3][g[1]]):{}),{role:"button"},{tabindex:"0"}])),V(e,"active",g[0]),V(e,"svelte-1etsput",!0)},i(g){c||($(t.$$.fragment,g),c=!0)},o(g){p(t.$$.fragment,g),c=!1},d(g){g&&h(e),k(t),i=!1,ze(a)}}}function ls(o){let e,t,n=o[1]&&ln(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,[r]){s[1]?n?(n.p(s,r),2&r&&$(n,1)):(n=ln(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(N(),p(n,1,1,()=>{n=null}),H())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function us(o,e,t){let n,s,r,c,{stage:i}=e,{iterationId:a}=e,{stageCount:l}=e;const u=ce("autofixConversationModel");le(o,u,g=>t(10,c=g));const d={[Y.retesting]:"info",[Y.testRunning]:"info",[Y.testFailed]:"error",[Y.testPassed]:"success",[Y.generatingSolutions]:"info",[Y.suggestedSolutions]:"warning",[Y.selectedSolutions]:"success"},m={[Y.retesting]:{iconName:"cached",color:"#FFFFFF"},[Y.testRunning]:{iconName:"cached",color:"#FFFFFF"},[Y.testFailed]:{iconName:"error",color:"#DB3B4B"},[Y.testPassed]:{iconName:"check_circle",color:"#388A34"},[Y.generatingSolutions]:{iconName:"cached",color:"#FFFFFF"},[Y.suggestedSolutions]:{iconName:"edit",color:"#FFFFFF"},[Y.selectedSolutions]:{iconName:"edit",color:"#FFFFFF"}};return o.$$set=g=>{"stage"in g&&t(6,i=g.stage),"iterationId"in g&&t(7,a=g.iterationId),"stageCount"in g&&t(8,l=g.stageCount)},o.$$.update=()=>{var g,w,L;1152&o.$$.dirty&&t(9,n=c==null?void 0:c.getAutofixIteration(a)),1600&o.$$.dirty&&t(0,s=n&&((L=(w=(g=c.extraData)==null?void 0:g.autofixIterations)==null?void 0:w.at(-1))==null?void 0:L.id)===n.id&&n.currentStage===i),833&o.$$.dirty&&t(1,r=function(f,I,A,P){var E;return f?I===an.runTest?f.commandFailed===void 0&&P?f.isFirstIteration?Y.testRunning:Y.retesting:f.commandFailed===!0?Y.testFailed:Y.testPassed:I===an.applyFix?A===(((E=f.suggestedSolutions)==null?void 0:E.length)||0)?f.selectedSolutions?Y.selectedSolutions:Y.generatingSolutions:Y.suggestedSolutions:null:null}(n,i,l,s))},[s,r,u,d,m,()=>{r!==Y.generatingSolutions&&u.launchAutofixPanel(a,i)},i,a,l,n,c]}class yc extends j{constructor(e){super(),Z(this,e,us,ls,W,{stage:6,iterationId:7,stageCount:8})}}function Cc(o,e){const t=Math.abs(o);let n=200,s=500;typeof e=="number"?n=e:e&&(n=e.baseThreshold??200,s=e.predictTime??500);const r=t*s/1e3;return Math.max(n,r)}function _c(o,e=10){const t=Math.abs(o);return t>1e3?2*e:t>500?1.5*e:t>200?e:.5*e}function un(o){const{scrollTop:e,clientHeight:t,scrollHeight:n}=o;return n-e-t}function kc(o,e={}){let t=e,n={scrollTop:0,scrollBottom:0,scrollHeight:0,scrolledIntoBottom:!0,scrolledAwayFromBottom:!0};const s=()=>{var f,I,A;const{scrollTop:r,scrollHeight:c,offsetHeight:i}=o,a=un(o),l=r>n.scrollTop+1,u=c-n.scrollHeight,d=!(u<0&&n.scrollBottom<-u)&&r<n.scrollTop-1&&a>n.scrollBottom+1,m=c>i,g=((P,E=40)=>un(P)<=E)(o),w=g&&m&&l,L=d||!m;w&&!n.scrolledIntoBottom?(f=t.onScrollIntoBottom)==null||f.call(t):L&&!n.scrolledAwayFromBottom&&((I=t.onScrollAwayFromBottom)==null||I.call(t)),n={scrollTop:r,scrollBottom:a,scrolledIntoBottom:w,scrolledAwayFromBottom:L,scrollHeight:c},(A=t.onScroll)==null||A.call(t,r)};return o.addEventListener("scroll",s),{update(r){t=r},destroy(){o.removeEventListener("scroll",s)}}}function bc(o,e){var r;let t=o.offsetHeight,n=e;const s=new ResizeObserver(c=>{var a;const i=c[0].contentRect.height;c.length===1?i!==t&&((a=n.onHeightChange)==null||a.call(n,i),t=i):console.warn("Unexpected number of resize entries: ",c)});return s.observe(o),(r=n==null?void 0:n.onHeightChange)==null||r.call(n,t),{update(c){n=c},destroy:()=>s.disconnect()}}function Ic(o,e){let t=e;const n=Rt(()=>{t.onSeen()},1e3,{leading:!0,trailing:!0}),s=new IntersectionObserver(c=>{c.length===1?c[0].isIntersecting&&n():console.warn("Unexpected number of intersection entries: ",c)},{threshold:.5}),r=()=>{s.disconnect(),t.track&&s.observe(o)};return r(),{update(c){const i=t;t=c,t.track!==i.track&&r()},destroy:()=>{s.disconnect(),t.onSeen()}}}function dn(o){let e,t,n,s=o[6]&&$n();return t=new Vt({props:{changeImageMode:o[32],saveImage:o[9].saveImage,deleteImage:o[9].deleteImage,renderImage:o[9].renderImage,isEditable:o[33]}}),{c(){s&&s.c(),e=B(),C(t.$$.fragment)},m(r,c){s&&s.m(r,c),v(r,e,c),_(t,r,c),n=!0},p(r,c){r[6]?s?64&c[0]&&$(s,1):(s=$n(),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(N(),p(s,1,1,()=>{s=null}),H());const i={};258&c[0]&&(i.changeImageMode=r[32]),512&c[0]&&(i.saveImage=r[9].saveImage),512&c[0]&&(i.deleteImage=r[9].deleteImage),512&c[0]&&(i.renderImage=r[9].renderImage),64&c[0]&&(i.isEditable=r[33]),t.$set(i)},i(r){n||($(s),$(t.$$.fragment,r),n=!0)},o(r){p(s),p(t.$$.fragment,r),n=!1},d(r){r&&h(e),s&&s.d(r),k(t,r)}}}function $n(o){let e,t;return e=new Jt({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ds(o){var L;let e,t,n,s,r,c,i,a,l,u,d=o[9].flags.enableChatMultimodal&&dn(o);t=new Nt({props:{shortcuts:o[13]}});let m={requestEditorFocus:o[22],onMentionItemsUpdated:o[21]};s=new tt({props:m}),o[34](s),c=new Ht({props:{placeholder:o[2]}}),a=new De.Content({props:{content:((L=o[7])==null?void 0:L.richTextJsonRepr)??o[3],onContentChanged:o[20]}});const g=o[29].default,w=ye(g,o,o[37],null);return{c(){d&&d.c(),e=B(),C(t.$$.fragment),n=B(),C(s.$$.fragment),r=B(),C(c.$$.fragment),i=B(),C(a.$$.fragment),l=B(),w&&w.c()},m(f,I){d&&d.m(f,I),v(f,e,I),_(t,f,I),v(f,n,I),_(s,f,I),v(f,r,I),_(c,f,I),v(f,i,I),_(a,f,I),v(f,l,I),w&&w.m(f,I),u=!0},p(f,I){var J;f[9].flags.enableChatMultimodal?d?(d.p(f,I),512&I[0]&&$(d,1)):(d=dn(f),d.c(),$(d,1),d.m(e.parentNode,e)):d&&(N(),p(d,1,1,()=>{d=null}),H());const A={};8192&I[0]&&(A.shortcuts=f[13]),t.$set(A),s.$set({});const P={};4&I[0]&&(P.placeholder=f[2]),c.$set(P);const E={};136&I[0]&&(E.content=((J=f[7])==null?void 0:J.richTextJsonRepr)??f[3]),a.$set(E),w&&w.p&&(!u||64&I[1])&&Ce(w,g,f,f[37],u?ke(g,f[37],I,null):_e(f[37]),null)},i(f){u||($(d),$(t.$$.fragment,f),$(s.$$.fragment,f),$(c.$$.fragment,f),$(a.$$.fragment,f),$(w,f),u=!0)},o(f){p(d),p(t.$$.fragment,f),p(s.$$.fragment,f),p(c.$$.fragment,f),p(a.$$.fragment,f),p(w,f),u=!1},d(f){f&&(h(e),h(n),h(r),h(i),h(l)),d&&d.d(f),k(t,f),o[34](null),k(s,f),k(c,f),k(a,f),w&&w.d(f)}}}function mn(o){let e,t;return e=new Gt({props:{chatModel:o[16]}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p:U,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function $s(o){let e,t,n=o[6]&&mn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[6]?n?(n.p(s,r),64&r[0]&&$(n,1)):(n=mn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(N(),p(n,1,1,()=>{n=null}),H())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function pn(o){let e,t,n,s=o[6]&&gn(o),r=o[4]&&fn(o);return{c(){s&&s.c(),e=Q(),r&&r.c(),t=Q()},m(c,i){s&&s.m(c,i),v(c,e,i),r&&r.m(c,i),v(c,t,i),n=!0},p(c,i){c[6]?s?(s.p(c,i),64&i[0]&&$(s,1)):(s=gn(c),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(N(),p(s,1,1,()=>{s=null}),H()),c[4]?r?(r.p(c,i),16&i[0]&&$(r,1)):(r=fn(c),r.c(),$(r,1),r.m(t.parentNode,t)):r&&(N(),p(r,1,1,()=>{r=null}),H())},i(c){n||($(s),$(r),n=!0)},o(c){p(s),p(r),n=!1},d(c){c&&(h(e),h(t)),s&&s.d(c),r&&r.d(c)}}}function gn(o){let e,t,n;return e=new st.Root({props:{$$slots:{rightAlign:[gs],leftAlign:[ms]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment),t=B()},m(s,r){_(e,s,r),v(s,t,r),n=!0},p(s,r){const c={};17408&r[0]|64&r[1]&&(c.$$scope={dirty:r,ctx:s}),e.$set(c)},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){s&&h(t),k(e,s)}}}function ms(o){var n;let e,t;return e=new st.ContextMenu({props:{slot:"leftAlign",onCloseDropdown:o[22],onInsertMentionable:(n=o[10])==null?void 0:n.insertMentionNode}}),{c(){C(e.$$.fragment)},m(s,r){_(e,s,r),t=!0},p(s,r){var i;const c={};1024&r[0]&&(c.onInsertMentionable=(i=s[10])==null?void 0:i.insertMentionNode),e.$set(c)},i(s){t||($(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){k(e,s)}}}function ps(o){let e,t;return e=new Wt({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function gs(o){let e,t;return e=new Oe({props:{size:1,variant:"solid",disabled:!o[14],$$slots:{default:[ps]},$$scope:{ctx:o}}}),e.$on("click",o[18]),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};16384&s[0]&&(r.disabled=!n[14]),64&s[1]&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function fn(o){let e,t,n;return t=new Oe({props:{variant:"solid",color:"neutral",size:1,$$slots:{iconLeft:[hs],default:[fs]},$$scope:{ctx:o}}}),t.$on("click",o[31]),{c(){e=M("div"),C(t.$$.fragment),x(e,"class","c-user-msg__collapse-button svelte-q0brxu")},m(s,r){v(s,e,r),_(t,e,null),n=!0},p(s,r){const c={};64&r[1]&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),k(t)}}}function fs(o){let e;return{c(){e=M("span"),e.textContent="Expand"},m(t,n){v(t,e,n)},p:U,d(t){t&&h(e)}}}function hs(o){let e,t;return e=new Yt({props:{slot:"iconLeft"}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p:U,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function vs(o){let e,t,n=(o[6]||!o[4])&&pn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[6]||!s[4]?n?(n.p(s,r),80&r[0]&&$(n,1)):(n=pn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(N(),p(n,1,1,()=>{n=null}),H())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function ws(o){let e,t,n,s,r,c={editable:o[6],$$slots:{footer:[vs],header:[$s],default:[ds]},$$scope:{ctx:o}};return t=new De.Root({props:c}),o[35](t),t.$on("click",xs),t.$on("dblclick",o[17]),{c(){e=M("div"),C(t.$$.fragment),x(e,"class","c-chat-input svelte-q0brxu"),x(e,"role","button"),x(e,"tabindex","-1"),V(e,"is-collapsed",o[4]),V(e,"is-editing",o[6])},m(i,a){v(i,e,a),_(t,e,null),o[36](e),n=!0,s||(r=[te(window,"mousedown",o[19]),te(e,"mousedown",xt(o[30]))],s=!0)},p(i,a){const l={};64&a[0]&&(l.editable=i[6]),26623&a[0]|64&a[1]&&(l.$$scope={dirty:a,ctx:i}),t.$set(l),(!n||16&a[0])&&V(e,"is-collapsed",i[4]),(!n||64&a[0])&&V(e,"is-editing",i[6])},i(i){n||($(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&h(e),o[35](null),k(t),o[36](null),s=!1,ze(r)}}}const xs=o=>o.stopPropagation();function ys(o,e,t){let n,s,r,c,i,a,l,u=U;o.$$.on_destroy.push(()=>u());let{$$slots:d={},$$scope:m}=e;const g=ce("chatModel");le(o,g,b=>t(9,l=b));const w=ce(qe.key);let{requestId:L}=e,{placeholder:f="Edit your message..."}=e,{content:I}=e,{collapsed:A=!1}=e,{onSubmitEdit:P}=e,{onCancelEdit:E}=e,{setIsCollapsed:J}=e,{userExpanded:T}=e,S,D,q,y=!1,X=[];async function z(){r&&(t(0,T=!0),A&&J(!1),t(6,y=!0),await Zn(),$e())}function G(){return!(!c||!S)&&(P(S,X),!0)}function O(){return t(0,T=!1),t(6,y=!1),t(7,S=void 0),E(),!0}const $e=()=>q==null?void 0:q.forceFocus();let se;return o.$$set=b=>{"requestId"in b&&t(1,L=b.requestId),"placeholder"in b&&t(2,f=b.placeholder),"content"in b&&t(3,I=b.content),"collapsed"in b&&t(4,A=b.collapsed),"onSubmitEdit"in b&&t(23,P=b.onSubmitEdit),"onCancelEdit"in b&&t(24,E=b.onCancelEdit),"setIsCollapsed"in b&&t(5,J=b.setIsCollapsed),"userExpanded"in b&&t(0,T=b.userExpanded),"$$scope"in b&&t(37,m=b.$$scope)},o.$$.update=()=>{512&o.$$.dirty[0]&&(t(15,n=l.currentConversationModel),u(),u=ge(n,b=>t(8,a=b))),268435968&o.$$.dirty[0]&&t(27,r=l.flags.enableEditableHistory&&!s),134218184&o.$$.dirty[0]&&t(14,c=y&&r&&S!==void 0&&S.rawText.trim()!==""&&S.rawText!==I&&S.richTextJsonRepr!==I&&!a.awaitingReply&&!Ut.hasLoadingImages(S.richTextJsonRepr))},t(28,s=!!(w!=null&&w.isActive)),t(13,i={Enter:G,Escape:O}),[T,L,f,I,A,J,y,S,a,l,D,q,se,i,c,n,g,z,G,O,function(b){b!==S&&t(7,S=b)},function(b){X=b.current},()=>q==null?void 0:q.requestFocus(),P,E,()=>{z()},function(){return se},r,s,d,function(b){yt.call(this,o,b)},()=>{t(0,T=!0),J(!1)},b=>{L&&b&&a.updateChatItem(L,{rich_text_json_repr:b})},()=>y,function(b){re[b?"unshift":"push"](()=>{D=b,t(10,D)})},function(b){re[b?"unshift":"push"](()=>{q=b,t(11,q)})},function(b){re[b?"unshift":"push"](()=>{se=b,t(12,se)})},m]}class Cs extends j{constructor(e){super(),Z(this,e,ys,ws,W,{requestId:1,placeholder:2,content:3,collapsed:4,onSubmitEdit:23,onCancelEdit:24,setIsCollapsed:5,userExpanded:0,requestStartEdit:25,getEditorContainer:26},null,[-1,-1])}get requestStartEdit(){return this.$$.ctx[25]}get getEditorContainer(){return this.$$.ctx[26]}}const ae=class ae{constructor(e){ne(this,"_tipTapExtension");ne(this,"_resizeObserver");ne(this,"_checkHeight",e=>{var n,s;const t=e.getBoundingClientRect().height;(s=(n=this._options).onResize)==null||s.call(n,t)});ne(this,"_setResizeObserver",e=>{var n;const t=(n=e.view)==null?void 0:n.dom;t&&(this._resizeObserver=new ResizeObserver(s=>{for(const r of s)this._checkHeight(r.target)}),this._resizeObserver.observe(t),this._checkHeight(t))});ne(this,"_clearResizeObserver",()=>{var e;(e=this._resizeObserver)==null||e.disconnect(),this._resizeObserver=void 0});ne(this,"updateOptions",e=>{this._options={...this._options,...e}});this._options=e;const t=ae._getNextPluginId(),n=this._setResizeObserver,s=this._clearResizeObserver,r=this._checkHeight;this._tipTapExtension=Dt.create({name:t,onCreate:function(){var i;((i=this.editor.view)==null?void 0:i.dom)&&(n(this.editor),this.editor.on("destroy",s))},onUpdate:function(){var i;const c=(i=this.editor.view)==null?void 0:i.dom;c&&r(c)},onDestroy:()=>{var c;(c=this._resizeObserver)==null||c.disconnect(),this._resizeObserver=void 0}})}get tipTapExtension(){return this._tipTapExtension}};ne(ae,"_sequenceId",0),ne(ae,"RESIZE_OBSERVER_PLUGIN_KEY_BASE","augment-resize-observer-plugin-{}"),ne(ae,"_getSequenceId",()=>ae._sequenceId++),ne(ae,"_getNextPluginId",()=>{const e=ae._getSequenceId().toString();return ae.RESIZE_OBSERVER_PLUGIN_KEY_BASE.replace("{}",e)});let Ze=ae;function _s(o,e,t){let{height:n=0}=e;const s=a=>{t(0,n=a)},r=ce(Ot.CONTEXT_KEY),c=new Ze({onResize:s}),i=r.pluginManager.registerPlugin(c);return Wn(i),o.$$set=a=>{"height"in a&&t(0,n=a.height)},c.updateOptions({onResize:s}),[n]}let ks=class extends j{constructor(o){super(),Z(this,o,_s,null,W,{height:0})}};function bs(o){let e,t,n;function s(c){o[21](c)}let r={};return o[6]!==void 0&&(r.height=o[6]),e=new ks({props:r}),re.push(()=>Be(e,"height",s)),{c(){C(e.$$.fragment)},m(c,i){_(e,c,i),n=!0},p(c,i){const a={};!t&&64&i&&(t=!0,a.height=c[6],Pe(()=>t=!1)),e.$set(a)},i(c){n||($(e.$$.fragment,c),n=!0)},o(c){p(e.$$.fragment,c),n=!1},d(c){k(e,c)}}}function Is(o){let e,t,n;function s(c){o[23](c)}let r={collapsed:o[7],content:o[3]??o[1],requestId:o[2],onSubmitEdit:o[13],onCancelEdit:o[5],setIsCollapsed:o[11],$$slots:{default:[bs]},$$scope:{ctx:o}};return o[8]!==void 0&&(r.userExpanded=o[8]),e=new Cs({props:r}),o[22](e),re.push(()=>Be(e,"userExpanded",s)),{c(){C(e.$$.fragment)},m(c,i){_(e,c,i),n=!0},p(c,i){const a={};128&i&&(a.collapsed=c[7]),10&i&&(a.content=c[3]??c[1]),4&i&&(a.requestId=c[2]),32&i&&(a.onCancelEdit=c[5]),134217792&i&&(a.$$scope={dirty:i,ctx:c}),!t&&256&i&&(t=!0,a.userExpanded=c[8],Pe(()=>t=!1)),e.$set(a)},i(c){n||($(e.$$.fragment,c),n=!0)},o(c){p(e.$$.fragment,c),n=!1},d(c){o[22](null),k(e,c)}}}function Ss(o){let e,t,n;return t=new Et({props:{items:o[10]}}),{c(){e=M("div"),C(t.$$.fragment),x(e,"slot","edit"),x(e,"class","c-user-msg__actions svelte-1dv083n")},m(s,r){v(s,e,r),_(t,e,null),n=!0},p(s,r){const c={};1024&r&&(c.items=s[10]),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),k(t)}}}function Ts(o){let e,t,n,s;return e=new Ke({props:{timestamp:o[4],$$slots:{edit:[Ss],content:[Is]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(r,c){_(e,r,c),t=!0,n||(s=[te(window,"keydown",Xn("Escape",o[12])),te(window,"mousedown",o[12])],n=!0)},p(r,[c]){const i={};16&c&&(i.timestamp=r[4]),134219758&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){p(e.$$.fragment,r),t=!1},d(r){k(e,r),n=!1,ze(s)}}}function Rs(o,e,t){let n,s,r,c,i,a,l,u=U,d=()=>(u(),u=ge(m,y=>t(20,l=y)),m);o.$$.on_destroy.push(()=>u());let{chatModel:m}=e;d();let{msg:g}=e,{requestId:w}=e,{richTextJsonRepr:L}=e,{timestamp:f}=e,{onStartEdit:I=()=>{}}=e,{onAcceptEdit:A=()=>{}}=e,{onCancelEdit:P=()=>{}}=e;const E=ce(qe.key);let J=!1,T=!1;async function S(y){await Zn(),t(7,J=y&&r&&!T)}const D=y=>{i&&w&&(S(!1),q==null||q.requestStartEdit(),I(),y.stopPropagation())};let q;return o.$$set=y=>{"chatModel"in y&&d(t(0,m=y.chatModel)),"msg"in y&&t(1,g=y.msg),"requestId"in y&&t(2,w=y.requestId),"richTextJsonRepr"in y&&t(3,L=y.richTextJsonRepr),"timestamp"in y&&t(4,f=y.timestamp),"onStartEdit"in y&&t(14,I=y.onStartEdit),"onAcceptEdit"in y&&t(15,A=y.onAcceptEdit),"onCancelEdit"in y&&t(5,P=y.onCancelEdit)},o.$$.update=()=>{var y,X;1048580&o.$$.dirty&&t(19,s=w===void 0||w===((X=(y=l==null?void 0:l.currentConversationModel)==null?void 0:y.lastExchange)==null?void 0:X.request_id)),524288&o.$$.dirty&&t(16,r=!s&&!0),1310724&o.$$.dirty&&t(17,i=w!==void 0&&l.flags.fullFeatured&&l.flags.enableEditableHistory&&!n),131072&o.$$.dirty&&t(10,a=[...i?[{label:"Edit message",action:D,id:"edit-message",disabled:!1,icon:Xt}]:[]]),65600&o.$$.dirty&&c&&r&&(q!=null&&q.getEditorContainer())&&c&&r&&S(!(J&&c<120)&&c>120)},t(18,n=!!(E!=null&&E.isActive)),t(6,c=0),[m,g,w,L,f,P,c,J,T,q,a,S,()=>{},function(y,X){if(!w)return;m.currentConversationModel.clearHistoryFrom(w);const z=l.flags.enableChatMultimodal&&y.richTextJsonRepr?m.currentConversationModel.createStructuredRequestNodes(y.richTextJsonRepr):void 0;m.currentConversationModel.sendExchange({request_message:y.rawText,rich_text_json_repr:y.richTextJsonRepr,status:oe.draft,mentioned_items:X,structured_request_nodes:z}),A()},I,A,r,i,n,s,l,function(y){c=y,t(6,c)},function(y){re[y?"unshift":"push"](()=>{q=y,t(9,q)})},function(y){T=y,t(8,T)}]}class Es extends j{constructor(e){super(),Z(this,e,Rs,Ts,W,{chatModel:0,msg:1,requestId:2,richTextJsonRepr:3,timestamp:4,onStartEdit:14,onAcceptEdit:15,onCancelEdit:5})}}function hn(o){let e,t;return e=new Es({props:{msg:o[1].request_message??"",richTextJsonRepr:o[11],chatModel:o[0],requestId:o[9],timestamp:o[1].timestamp}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};2&s&&(r.msg=n[1].request_message??""),2048&s&&(r.richTextJsonRepr=n[11]),1&s&&(r.chatModel=n[0]),512&s&&(r.requestId=n[9]),2&s&&(r.timestamp=n[1].timestamp),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function vn(o){let e,t,n;function s(a,l){return a[1].display_error_message?As:a[1].response_text&&a[1].response_text.length>0?Ls:Ms}let r=s(o),c=r(o),i=o[9]&&wn(o);return{c(){e=M("div"),c.c(),t=B(),i&&i.c(),x(e,"class","c-msg-list__turn-response-failure svelte-1d1manc")},m(a,l){v(a,e,l),c.m(e,null),R(e,t),i&&i.m(e,null),n=!0},p(a,l){r===(r=s(a))&&c?c.p(a,l):(c.d(1),c=r(a),c&&(c.c(),c.m(e,t))),a[9]?i?(i.p(a,l),512&l&&$(i,1)):(i=wn(a),i.c(),$(i,1),i.m(e,null)):i&&(N(),p(i,1,1,()=>{i=null}),H())},i(a){n||($(i),n=!0)},o(a){p(i),n=!1},d(a){a&&h(e),c.d(),i&&i.d()}}}function Ms(o){let e,t,n,s;return{c(){e=F(`We encountered an issue sending your message. Please
        `),t=M("button"),t.textContent="try again",x(t,"class","svelte-1d1manc")},m(r,c){v(r,e,c),v(r,t,c),n||(s=te(t,"click",Ne(o[15])),n=!0)},p:U,d(r){r&&(h(e),h(t)),n=!1,s()}}}function Ls(o){let e,t,n,s,r;return{c(){e=F(`Connection lost. Please
        `),t=M("button"),t.textContent="try again",n=F(`
        to restart the conversation!`),x(t,"class","svelte-1d1manc")},m(c,i){v(c,e,i),v(c,t,i),v(c,n,i),s||(r=te(t,"click",Ne(o[15])),s=!0)},p:U,d(c){c&&(h(e),h(t),h(n)),s=!1,r()}}}function As(o){let e,t=o[1].display_error_message+"";return{c(){e=F(t)},m(n,s){v(n,e,s)},p(n,s){2&s&&t!==(t=n[1].display_error_message+"")&&K(e,t)},d(n){n&&h(e)}}}function wn(o){let e,t,n,s,r,c,i,a;function l(d){o[21](d)}let u={onOpenChange:o[16],content:o[7],triggerOn:[He.Hover],$$slots:{default:[qs]},$$scope:{ctx:o}};return o[8]!==void 0&&(u.requestClose=o[8]),c=new Ie({props:u}),re.push(()=>Be(c,"requestClose",l)),{c(){e=M("div"),t=M("span"),n=F("Request ID: "),s=F(o[9]),r=B(),C(c.$$.fragment),x(e,"class","c-msg-list__request-id svelte-1d1manc")},m(d,m){v(d,e,m),R(e,t),R(t,n),R(t,s),R(e,r),_(c,e,null),a=!0},p(d,m){(!a||512&m)&&K(s,d[9]);const g={};128&m&&(g.content=d[7]),16777216&m&&(g.$$scope={dirty:m,ctx:d}),!i&&256&m&&(i=!0,g.requestClose=d[8],Pe(()=>i=!1)),c.$set(g)},i(d){a||($(c.$$.fragment,d),a=!0)},o(d){p(c.$$.fragment,d),a=!1},d(d){d&&h(e),k(c)}}}function Fs(o){let e,t;return e=new Kt({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function qs(o){let e,t;return e=new ve({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Fs]},$$scope:{ctx:o}}}),e.$on("click",o[17]),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};16777216&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function zs(o){let e,t,n,s,r,c=!o[10]&&!Ge(o[1])&&!o[12],i=c&&hn(o);n=new Mt({props:{chatModel:o[6],turn:o[1],turnIndex:o[3],requestId:o[9],isLastTurn:o[2],showName:!o[10],group:o[5],showFooter:!Qe(o[1])||o[1].status===oe.cancelled,markdown:o[1].response_text??"",timestamp:o[1].timestamp,messageListContainer:o[4]}});let a=o[1].status===oe.failed&&vn(o);return{c(){e=M("div"),i&&i.c(),t=B(),C(n.$$.fragment),s=B(),a&&a.c(),x(e,"class","c-msg-list__turn svelte-1d1manc")},m(l,u){v(l,e,u),i&&i.m(e,null),R(e,t),_(n,e,null),R(e,s),a&&a.m(e,null),r=!0},p(l,[u]){5122&u&&(c=!l[10]&&!Ge(l[1])&&!l[12]),c?i?(i.p(l,u),5122&u&&$(i,1)):(i=hn(l),i.c(),$(i,1),i.m(e,t)):i&&(N(),p(i,1,1,()=>{i=null}),H());const d={};64&u&&(d.chatModel=l[6]),2&u&&(d.turn=l[1]),8&u&&(d.turnIndex=l[3]),512&u&&(d.requestId=l[9]),4&u&&(d.isLastTurn=l[2]),1024&u&&(d.showName=!l[10]),32&u&&(d.group=l[5]),2&u&&(d.showFooter=!Qe(l[1])||l[1].status===oe.cancelled),2&u&&(d.markdown=l[1].response_text??""),2&u&&(d.timestamp=l[1].timestamp),16&u&&(d.messageListContainer=l[4]),n.$set(d),l[1].status===oe.failed?a?(a.p(l,u),2&u&&$(a,1)):(a=vn(l),a.c(),$(a,1),a.m(e,null)):a&&(N(),p(a,1,1,()=>{a=null}),H())},i(l){r||($(i),$(n.$$.fragment,l),$(a),r=!0)},o(l){p(i),p(n.$$.fragment,l),p(a),r=!1},d(l){l&&h(e),i&&i.d(),k(n),a&&a.d()}}}function Bs(o,e,t){let n,s,r,c,i,a,l,u,d,m,g=U,w=()=>(g(),g=ge(f,y=>t(6,u=y)),f),L=U;o.$$.on_destroy.push(()=>g()),o.$$.on_destroy.push(()=>L());let{chatModel:f}=e;w();let{turn:I}=e,{isLastTurn:A=!1}=e,{turnIndex:P=0}=e,{messageListContainer:E}=e,{group:J}=e;const T=ce(qe.key);le(o,T,y=>t(20,m=y));let S,D="Copy request ID",q=()=>{};return o.$$set=y=>{"chatModel"in y&&w(t(0,f=y.chatModel)),"turn"in y&&t(1,I=y.turn),"isLastTurn"in y&&t(2,A=y.isLastTurn),"turnIndex"in y&&t(3,P=y.turnIndex),"messageListContainer"in y&&t(4,E=y.messageListContainer),"group"in y&&t(5,J=y.group)},o.$$.update=()=>{var y;2&o.$$.dirty&&t(9,n=I.request_id),64&o.$$.dirty&&(t(13,s=u==null?void 0:u.currentConversationModel),L(),L=ge(s,X=>t(23,d=X))),1048576&o.$$.dirty&&t(19,r=(m==null?void 0:m.isActive)&&((y=m==null?void 0:m.currentAgent)==null?void 0:y.is_setup_script_agent)===!0),8&o.$$.dirty&&t(18,c=P===0),786432&o.$$.dirty&&t(12,i=r&&c),66&o.$$.dirty&&t(11,a=u.flags.enableRichTextHistory?I.rich_text_json_repr:void 0),2&o.$$.dirty&&t(10,l=jn(I))},[f,I,A,P,E,J,u,D,q,n,l,a,i,s,T,()=>{d.resendTurn(I)},function(y){y||(clearTimeout(S),S=void 0,t(7,D="Copy request ID"))},async function(){n&&(await navigator.clipboard.writeText(n),t(7,D="Copied!"),clearTimeout(S),S=setTimeout(q,1500))},c,r,m,function(y){q=y,t(8,q)}]}class Sc extends j{constructor(e){super(),Z(this,e,Bs,zs,W,{chatModel:0,turn:1,isLastTurn:2,turnIndex:3,messageListContainer:4,group:5})}}function Ps(o){let e,t,n,s,r,c,i;const a=o[15].default,l=ye(a,o,o[14],null);return{c(){e=M("div"),l&&l.c(),x(e,"class",t=tn(`c-msg-list__item ${o[5]}`)+" svelte-1s0uz2w"),x(e,"style",n=`min-height: calc(${o[4]}px - (var(--msg-list-item-spacing) * 2));`),x(e,"data-request-id",o[6])},m(u,d){v(u,e,d),l&&l.m(e,null),o[16](e),r=!0,c||(i=Kn(s=Lt.call(null,e,{follow:!o[2]&&o[1],scrollContainer:o[3],disableScrollUp:!0,smooth:!0,bottom:!0})),c=!0)},p(u,[d]){l&&l.p&&(!r||16384&d)&&Ce(l,a,u,u[14],r?ke(a,u[14],d,null):_e(u[14]),null),(!r||32&d&&t!==(t=tn(`c-msg-list__item ${u[5]}`)+" svelte-1s0uz2w"))&&x(e,"class",t),(!r||16&d&&n!==(n=`min-height: calc(${u[4]}px - (var(--msg-list-item-spacing) * 2));`))&&x(e,"style",n),(!r||64&d)&&x(e,"data-request-id",u[6]),s&&be(s.update)&&14&d&&s.update.call(null,{follow:!u[2]&&u[1],scrollContainer:u[3],disableScrollUp:!0,smooth:!0,bottom:!0})},i(u){r||($(l,u),r=!0)},o(u){p(l,u),r=!1},d(u){u&&h(e),l&&l.d(u),o[16](null),c=!1,i()}}}function Ns(o,e,t){let n,s,r,c,i=U,a=U,l=()=>(a(),a=ge(g,T=>t(13,c=T)),g);o.$$.on_destroy.push(()=>i()),o.$$.on_destroy.push(()=>a());let{$$slots:u={},$$scope:d}=e,{requestId:m}=e,{chatModel:g}=e;l();let w,{isLastItem:L=!1}=e,{userControlsScroll:f=!1}=e,{releaseScroll:I=()=>{}}=e,{messageListContainer:A}=e,{minHeight:P}=e,{class:E=""}=e,{dataRequestId:J}=e;return fe(()=>{A&&L&&Qn(A,{smooth:!0,onScrollFinish:I})}),o.$$set=T=>{"requestId"in T&&t(9,m=T.requestId),"chatModel"in T&&l(t(0,g=T.chatModel)),"isLastItem"in T&&t(1,L=T.isLastItem),"userControlsScroll"in T&&t(2,f=T.userControlsScroll),"releaseScroll"in T&&t(10,I=T.releaseScroll),"messageListContainer"in T&&t(3,A=T.messageListContainer),"minHeight"in T&&t(4,P=T.minHeight),"class"in T&&t(5,E=T.class),"dataRequestId"in T&&t(6,J=T.dataRequestId),"$$scope"in T&&t(14,d=T.$$scope)},o.$$.update=()=>{var T,S;8192&o.$$.dirty&&(t(8,n=(T=c==null?void 0:c.currentConversationModel)==null?void 0:T.focusModel),i(),i=ge(n,D=>t(12,r=D))),4608&o.$$.dirty&&t(11,s=((S=r.focusedItem)==null?void 0:S.request_id)===m),2048&o.$$.dirty&&s&&A&&w&&At(A,w,{topBuffer:0,smooth:!0,scrollDuration:100,onScrollFinish:I})},[g,L,f,A,P,E,J,w,n,m,I,s,r,c,d,u,function(T){re[T?"unshift":"push"](()=>{w=T,t(7,w)})}]}class Tc extends j{constructor(e){super(),Z(this,e,Ns,Ps,W,{requestId:9,chatModel:0,isLastItem:1,userControlsScroll:2,releaseScroll:10,messageListContainer:3,minHeight:4,class:5,dataRequestId:6})}}function Hs(o){let e;return{c(){e=F("Generating response...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function xn(o){let e,t;return e=new ot.Root({props:{color:"neutral",size:1,$$slots:{default:[Os]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Ds(o){let e,t;return{c(){e=M("span"),t=F(o[2]),x(e,"class","c-gen-response__timer svelte-148snxl"),V(e,"is_minutes",o[0]>=60)},m(n,s){v(n,e,s),R(e,t)},p(n,s){4&s&&K(t,n[2]),1&s&&V(e,"is_minutes",n[0]>=60)},d(n){n&&h(e)}}}function Os(o){let e,t;return e=new ee({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[Ds]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Us(o){let e,t,n,s,r,c,i;n=new he({props:{size:1}}),r=new ee({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[Hs]},$$scope:{ctx:o}}});let a=o[1]&&xn(o);return{c(){e=M("div"),t=M("span"),C(n.$$.fragment),s=B(),C(r.$$.fragment),c=B(),a&&a.c(),x(t,"class","c-gen-response__text svelte-148snxl"),x(e,"class","c-gen-response svelte-148snxl")},m(l,u){v(l,e,u),R(e,t),_(n,t,null),R(t,s),_(r,t,null),R(e,c),a&&a.m(e,null),i=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),r.$set(d),l[1]?a?(a.p(l,u),2&u&&$(a,1)):(a=xn(l),a.c(),$(a,1),a.m(e,null)):a&&(N(),p(a,1,1,()=>{a=null}),H())},i(l){i||($(n.$$.fragment,l),$(r.$$.fragment,l),$(a),i=!0)},o(l){p(n.$$.fragment,l),p(r.$$.fragment,l),p(a),i=!1},d(l){l&&h(e),k(n),k(r),a&&a.d()}}}function Vs(o,e,t){let n,s,r,{timeToTimerMs:c=5e3}=e,i=0,a=Date.now(),l=!1;function u(){t(0,i=Math.floor((Date.now()-a)/1e3))}function d(){t(1,l=!0),u(),r=setInterval(u,1e3)}return fe(function(){return s=setTimeout(d,c),a=Date.now(),()=>{t(0,i=0),t(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&t(3,c=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&t(2,n=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(i))},[i,l,n,c]}class Js extends j{constructor(e){super(),Z(this,e,Vs,Us,W,{timeToTimerMs:3})}}class de{constructor(e){ne(this,"type","plainText");this.text=e}}class Ee{constructor(e){ne(this,"type","specialBlock");this.text=e}}function We(o){return o.map(e=>e.text).join("")}function Gs(o){let e,t,n,s,r=(!o[0].status||o[0].status===oe.success)&&o[4]===We(o[3]);e=new ns({props:{renderers:o[5],markdown:o[1]+o[4]}});let c=r&&yn(o);return{c(){C(e.$$.fragment),t=B(),c&&c.c(),n=Q()},m(i,a){_(e,i,a),v(i,t,a),c&&c.m(i,a),v(i,n,a),s=!0},p(i,a){const l={};18&a&&(l.markdown=i[1]+i[4]),e.$set(l),25&a&&(r=(!i[0].status||i[0].status===oe.success)&&i[4]===We(i[3])),r?c?(c.p(i,a),25&a&&$(c,1)):(c=yn(i),c.c(),$(c,1),c.m(n.parentNode,n)):c&&(N(),p(c,1,1,()=>{c=null}),H())},i(i){s||($(e.$$.fragment,i),$(c),s=!0)},o(i){p(e.$$.fragment,i),p(c),s=!1},d(i){i&&(h(t),h(n)),k(e,i),c&&c.d(i)}}}function js(o){let e;function t(r,c){return r[0].display_error_message?Ys:r[0].response_text&&r[0].response_text.length>0?Ks:Ws}let n=t(o),s=n(o);return{c(){e=M("div"),s.c(),x(e,"class","c-msg-failure svelte-9a9fi8")},m(r,c){v(r,e,c),s.m(e,null)},p(r,c){n===(n=t(r))&&s?s.p(r,c):(s.d(1),s=n(r),s&&(s.c(),s.m(e,null)))},i:U,o:U,d(r){r&&h(e),s.d()}}}function Zs(o){let e,t;return e=new Js({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p:U,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function yn(o){let e;const t=o[7].default,n=ye(t,o,o[8],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||256&r)&&Ce(n,t,s,s[8],e?ke(t,s[8],r,null):_e(s[8]),null)},i(s){e||($(n,s),e=!0)},o(s){p(n,s),e=!1},d(s){n&&n.d(s)}}}function Ws(o){let e,t,n=o[2]&&Cn(o);return{c(){e=F("We encountered an issue sending your message."),n&&n.c(),t=F(".")},m(s,r){v(s,e,r),n&&n.m(s,r),v(s,t,r)},p(s,r){s[2]?n?n.p(s,r):(n=Cn(s),n.c(),n.m(t.parentNode,t)):n&&(n.d(1),n=null)},d(s){s&&(h(e),h(t)),n&&n.d(s)}}}function Ks(o){let e,t,n=o[2]&&_n(o);return{c(){e=F("Connection lost."),n&&n.c(),t=Q()},m(s,r){v(s,e,r),n&&n.m(s,r),v(s,t,r)},p(s,r){s[2]?n?n.p(s,r):(n=_n(s),n.c(),n.m(t.parentNode,t)):n&&(n.d(1),n=null)},d(s){s&&(h(e),h(t)),n&&n.d(s)}}}function Ys(o){let e,t=o[0].display_error_message+"";return{c(){e=F(t)},m(n,s){v(n,e,s)},p(n,s){1&s&&t!==(t=n[0].display_error_message+"")&&K(e,t)},d(n){n&&h(e)}}}function Cn(o){let e,t,n,s;return{c(){e=F(`Please
            `),t=M("button"),t.textContent="try again",x(t,"class","svelte-9a9fi8")},m(r,c){v(r,e,c),v(r,t,c),n||(s=te(t,"click",Ne(function(){be(o[2])&&o[2].apply(this,arguments)})),n=!0)},p(r,c){o=r},d(r){r&&(h(e),h(t)),n=!1,s()}}}function _n(o){let e,t,n,s,r;return{c(){e=F(`Please
            `),t=M("button"),t.textContent="try again",n=F("."),x(t,"class","svelte-9a9fi8")},m(c,i){v(c,e,i),v(c,t,i),v(c,n,i),s||(r=te(t,"click",Ne(function(){be(o[2])&&o[2].apply(this,arguments)})),s=!0)},p(c,i){o=c},d(c){c&&(h(e),h(t),h(n)),s=!1,r()}}}function Xs(o){let e,t,n,s;const r=[Zs,js,Gs],c=[];function i(a,l){return(!a[0].status||a[0].status===oe.sent)&&a[4].length<=0?0:a[0].status===oe.failed?1:2}return e=i(o),t=c[e]=r[e](o),{c(){t.c(),n=Q()},m(a,l){c[e].m(a,l),v(a,n,l),s=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(N(),p(c[u],1,1,()=>{c[u]=null}),H(),t=c[e],t?t.p(a,l):(t=c[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n))},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),c[e].d(a)}}}function Qs(o){let e,t;return e=new Ke({props:{isAugment:!0,$$slots:{content:[Xs]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,[s]){const r={};287&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function eo(o,e,t){let n,s,{$$slots:r={},$$scope:c}=e,{turn:i}=e,{preamble:a=""}=e,{resendTurn:l}=e,{markdownBlocks:u=[]}=e,d={code:Qt,codespan:es,link:Ft};return fe(()=>{if(i.seen_state===vt.seen)return void t(0,i.response_text=We(n),i);let m=Date.now();const g=function*(f){for(const I of f)if(I.type==="specialBlock")yield I.text;else for(const A of I.text)yield A}(n);let w=g.next();const L=()=>{let f=Date.now();const I=Math.round((f-m)/8);let A="";for(let P=0;P<I&&!w.done;P++)A+=w.value,w=g.next();t(0,i.response_text+=A,i),m=f,w.done||requestAnimationFrame(L)};L()}),o.$$set=m=>{"turn"in m&&t(0,i=m.turn),"preamble"in m&&t(1,a=m.preamble),"resendTurn"in m&&t(2,l=m.resendTurn),"markdownBlocks"in m&&t(6,u=m.markdownBlocks),"$$scope"in m&&t(8,c=m.$$scope)},o.$$.update=()=>{1&o.$$.dirty&&t(0,i.response_text=i.response_text??"",i),65&o.$$.dirty&&t(3,n=i.response_text?[new de(i.response_text)]:u),1&o.$$.dirty&&t(4,s=i.response_text??"")},[i,a,l,n,s,d,u,r,c]}class no extends j{constructor(e){super(),Z(this,e,eo,Qs,W,{turn:0,preamble:1,resendTurn:2,markdownBlocks:6})}}function to(o){let e,t,n,s;return e=new De.Content({props:{content:o[2]}}),n=new tt({props:{requestEditorFocus:o[4]}}),{c(){C(e.$$.fragment),t=B(),C(n.$$.fragment)},m(r,c){_(e,r,c),v(r,t,c),_(n,r,c),s=!0},p:U,i(r){s||($(e.$$.fragment,r),$(n.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(n.$$.fragment,r),s=!1},d(r){r&&h(t),k(e,r),k(n,r)}}}function so(o){let e,t,n={slot:"content",$$slots:{default:[to]},$$scope:{ctx:o}};return e=new De.Root({props:n}),o[7](e),{c(){C(e.$$.fragment)},m(s,r){_(e,s,r),t=!0},p(s,r){const c={};512&r&&(c.$$scope={dirty:r,ctx:s}),e.$set(c)},i(s){t||($(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){o[7](null),k(e,s)}}}function oo(o){let e,t,n,s;return e=new Ke({props:{$$slots:{content:[so]},$$scope:{ctx:o}}}),n=new no({props:{turn:o[1],markdownBlocks:o[3]}}),{c(){C(e.$$.fragment),t=B(),C(n.$$.fragment)},m(r,c){_(e,r,c),v(r,t,c),_(n,r,c),s=!0},p(r,[c]){const i={};513&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i)},i(r){s||($(e.$$.fragment,r),$(n.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(n.$$.fragment,r),s=!1},d(r){r&&h(t),k(e,r),k(n,r)}}}function ro(o,e,t){let{flagsModel:n}=e,{turn:s}=e;const r={seen_state:s.seen_state,status:oe.success},c=[[new Ee("[**Chat**](https://docs.augmentcode.com/using-augment/chat)"),new de(": Explore your codebase, get up to speed on unfamiliar code, and work through technical problems using natural language.")],[new Ee("[**Code Completions**](https://docs.augmentcode.com/using-augment/completions)"),new de(": Receive intelligent code suggestions that take your entire codebase into account as you type.")],[new Ee("[**Instructions**](https://docs.augmentcode.com/using-augment/instructions)"),new de(": Use natural language prompts to write or modify code, applied as a diff for your review.")]];n.suggestedEditsAvailable&&c.push([new Ee("[**Suggested Edits**](https://docs.augmentcode.com/using-augment/suggested-edits)"),new de(": Take your completions beyond the cursor and across your workspace.")]);let i,a=[new de(`Welcome to Augment!

Augment can help you understand code, debug issues, and ship faster with its deep understanding of your codebase. Here is what Augment can do for you:

`),...c.flatMap((l,u)=>[new de(`${u+1}. `),...l,new de(`

`)]),new de("Ask questions to learn more! Just remember to tag **@Augment** when asking about Augment's capabilities.")];return o.$$set=l=>{"flagsModel"in l&&t(5,n=l.flagsModel),"turn"in l&&t(6,s=l.turn)},[i,r,{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"What can "},{type:"mention",attrs:{id:"Augment",label:"Augment",data:{id:"Augment",label:"Augment"}}},{type:"text",text:" do?"}]}]},a,function(){i==null||i.requestFocus()},n,s,function(l){re[l?"unshift":"push"](()=>{i=l,t(0,i)})}]}class Rc extends j{constructor(e){super(),Z(this,e,ro,oo,W,{flagsModel:5,turn:6})}}function co(o){let e,t;return{c(){e=me("svg"),t=me("path"),x(t,"d","M6.04995 2.74998C6.04995 2.44623 5.80371 2.19998 5.49995 2.19998C5.19619 2.19998 4.94995 2.44623 4.94995 2.74998V12.25C4.94995 12.5537 5.19619 12.8 5.49995 12.8C5.80371 12.8 6.04995 12.5537 6.04995 12.25V2.74998ZM10.05 2.74998C10.05 2.44623 9.80371 2.19998 9.49995 2.19998C9.19619 2.19998 8.94995 2.44623 8.94995 2.74998V12.25C8.94995 12.5537 9.19619 12.8 9.49995 12.8C9.80371 12.8 10.05 12.5537 10.05 12.25V2.74998Z"),x(t,"fill","currentColor"),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){v(n,e,s),R(e,t)},p:U,i:U,o:U,d(n){n&&h(e)}}}class io extends j{constructor(e){super(),Z(this,e,null,co,W,{})}}function ao(o){let e,t,n,s,r;return t=new io({}),{c(){e=M("span"),C(t.$$.fragment),n=F(`
  Waiting for user input`),s=F(o[0]),x(e,"class","c-gen-response svelte-5is5us")},m(c,i){v(c,e,i),_(t,e,null),R(e,n),R(e,s),r=!0},p(c,[i]){(!r||1&i)&&K(s,c[0])},i(c){r||($(t.$$.fragment,c),r=!0)},o(c){p(t.$$.fragment,c),r=!1},d(c){c&&h(e),k(t)}}}function lo(o,e,t){let n=".";return fe(()=>{const s=setInterval(()=>{t(0,n=n.length>=3?".":n+".")},500);return()=>clearInterval(s)}),[n]}class Ec extends j{constructor(e){super(),Z(this,e,lo,ao,W,{})}}function uo(o){let e;return{c(){e=F("Resuming remote agent...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function kn(o){let e,t;return e=new ot.Root({props:{color:"neutral",size:1,$$slots:{default:[mo]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function $o(o){let e,t;return{c(){e=M("span"),t=F(o[2]),x(e,"class","c-resuming-agent__timer svelte-16pyinb"),V(e,"is_minutes",o[0]>=60)},m(n,s){v(n,e,s),R(e,t)},p(n,s){4&s&&K(t,n[2]),1&s&&V(e,"is_minutes",n[0]>=60)},d(n){n&&h(e)}}}function mo(o){let e,t;return e=new ee({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[$o]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function po(o){let e,t,n,s,r,c,i;n=new he({props:{size:1}}),r=new ee({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[uo]},$$scope:{ctx:o}}});let a=o[1]&&kn(o);return{c(){e=M("div"),t=M("span"),C(n.$$.fragment),s=B(),C(r.$$.fragment),c=B(),a&&a.c(),x(t,"class","c-resuming-agent__text svelte-16pyinb"),x(e,"class","c-resuming-agent svelte-16pyinb")},m(l,u){v(l,e,u),R(e,t),_(n,t,null),R(t,s),_(r,t,null),R(e,c),a&&a.m(e,null),i=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),r.$set(d),l[1]?a?(a.p(l,u),2&u&&$(a,1)):(a=kn(l),a.c(),$(a,1),a.m(e,null)):a&&(N(),p(a,1,1,()=>{a=null}),H())},i(l){i||($(n.$$.fragment,l),$(r.$$.fragment,l),$(a),i=!0)},o(l){p(n.$$.fragment,l),p(r.$$.fragment,l),p(a),i=!1},d(l){l&&h(e),k(n),k(r),a&&a.d()}}}function go(o,e,t){let n,s,r,{timeToTimerMs:c=5e3}=e,i=0,a=Date.now(),l=!1;function u(){t(0,i=Math.floor((Date.now()-a)/1e3))}function d(){t(1,l=!0),u(),r=setInterval(u,1e3)}return fe(function(){return s=setTimeout(d,c),a=Date.now(),()=>{t(0,i=0),t(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&t(3,c=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&t(2,n=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(i))},[i,l,n,c]}class Mc extends j{constructor(e){super(),Z(this,e,go,po,W,{timeToTimerMs:3})}}function fo(o){let e,t,n,s,r;return t=new he({props:{size:1}}),{c(){e=M("span"),C(t.$$.fragment),n=B(),s=F(o[0]),x(e,"class","c-retry-response svelte-1lxm8qk")},m(c,i){v(c,e,i),_(t,e,null),R(e,n),R(e,s),r=!0},p(c,[i]){(!r||1&i)&&K(s,c[0])},i(c){r||($(t.$$.fragment,c),r=!0)},o(c){p(t.$$.fragment,c),r=!1},d(c){c&&h(e),k(t)}}}function ho(o,e,t){let{message:n="Retrying..."}=e;return o.$$set=s=>{"message"in s&&t(0,n=s.message)},[n]}class Lc extends j{constructor(e){super(),Z(this,e,ho,fo,W,{message:0})}}function vo(o){let e,t,n;return e=new jt({}),{c(){C(e.$$.fragment),t=F(`
    Stopped`)},m(s,r){_(e,s,r),v(s,t,r),n=!0},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){s&&h(t),k(e,s)}}}function wo(o){let e,t,n;return t=new ee({props:{size:1,$$slots:{default:[vo]},$$scope:{ctx:o}}}),{c(){e=M("span"),C(t.$$.fragment),x(e,"class","c-stopped svelte-lv19x6")},m(s,r){v(s,e,r),_(t,e,null),n=!0},p(s,[r]){const c={};1&r&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),k(t)}}}class Ac extends j{constructor(e){super(),Z(this,e,null,wo,W,{})}}function xo(o){let e,t;return{c(){e=me("svg"),t=me("path"),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd"),x(t,"d","M4.85355 2.14645C5.04882 2.34171 5.04882 2.65829 4.85355 2.85355L3.70711 4H9C11.4853 4 13.5 6.01472 13.5 8.5C13.5 10.9853 11.4853 13 9 13H5C4.72386 13 4.5 12.7761 4.5 12.5C4.5 12.2239 4.72386 12 5 12H9C10.933 12 12.5 10.433 12.5 8.5C12.5 6.567 10.933 5 9 5H3.70711L4.85355 6.14645C5.04882 6.34171 5.04882 6.65829 4.85355 6.85355C4.65829 7.04882 4.34171 7.04882 4.14645 6.85355L2.14645 4.85355C1.95118 4.65829 1.95118 4.34171 2.14645 4.14645L4.14645 2.14645C4.34171 1.95118 4.65829 1.95118 4.85355 2.14645Z"),x(t,"fill","currentColor"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){v(n,e,s),R(e,t)},p:U,i:U,o:U,d(n){n&&h(e)}}}class it extends j{constructor(e){super(),Z(this,e,null,xo,W,{})}}function yo(o){let e,t,n;return{c(){e=me("svg"),t=me("path"),n=me("path"),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd"),x(t,"d","M3.1784 5.56111C3.17842 5.85569 3.41722 6.09449 3.71173 6.09444L9.92275 6.09447C10.0585 6.09447 10.1929 6.06857 10.3189 6.01818L13.9947 4.54786C14.1973 4.46681 14.33 4.27071 14.3301 4.05261C14.33 3.83458 14.1973 3.63846 13.9948 3.55744L10.3189 2.08711C10.1929 2.0367 10.0584 2.01083 9.92278 2.01079L3.71173 2.01079C3.41722 2.01084 3.17844 2.24962 3.1784 2.54412L3.1784 5.56111ZM9.92275 5.0278L4.2451 5.02781L4.24509 3.07749L9.92278 3.07745L11.5339 3.72196L12.2527 4.05263C12.2527 4.05263 11.8167 4.25864 11.534 4.38331C10.9139 4.65675 9.92275 5.0278 9.92275 5.0278Z"),x(t,"fill","currentColor"),x(n,"fill-rule","evenodd"),x(n,"clip-rule","evenodd"),x(n,"d","M8.53346 1.59998C8.53346 1.30543 8.29468 1.06665 8.00013 1.06665C7.70558 1.06665 7.4668 1.30543 7.4668 1.59998V3.07746L8.53346 3.07745V1.59998ZM8.53346 5.0278L7.4668 5.0278V14.4C7.4668 14.6945 7.70558 14.9333 8.00013 14.9333C8.29468 14.9333 8.53346 14.6945 8.53346 14.4V5.0278Z"),x(n,"fill","currentColor"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(s,r){v(s,e,r),R(e,t),R(e,n)},p:U,i:U,o:U,d(s){s&&h(e)}}}class at extends j{constructor(e){super(),Z(this,e,null,yo,W,{})}}function bn(o){let e,t,n,s,r;return t=new ss({props:{size:1,insetContent:!0,variant:"ghost",class:"c-checkpoint-tag","data-testid":"checkpoint-version-tag",$$slots:{default:[So]},$$scope:{ctx:o}}}),t.$on("click",o[17]),s=new ve({props:{variant:o[6]?"soft":"ghost-block",color:"neutral",size:1,disabled:o[6]||o[4],class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[Ro]},$$scope:{ctx:o}}}),s.$on("click",o[12]),{c(){e=M("div"),C(t.$$.fragment),n=B(),C(s.$$.fragment),x(e,"class","c-checkpoint-container svelte-q20gs5"),x(e,"data-checkpoint-number",o[0]),V(e,"c-checkpoint-container--target-checkpoint",o[6]),V(e,"c-checkpoint-container--dimmed-marker",o[5])},m(c,i){v(c,e,i),_(t,e,null),R(e,n),_(s,e,null),r=!0},p(c,i){const a={};1048778&i&&(a.$$scope={dirty:i,ctx:c}),t.$set(a);const l={};64&i&&(l.variant=c[6]?"soft":"ghost-block"),80&i&&(l.disabled=c[6]||c[4]),1048656&i&&(l.$$scope={dirty:i,ctx:c}),s.$set(l),(!r||1&i)&&x(e,"data-checkpoint-number",c[0]),(!r||64&i)&&V(e,"c-checkpoint-container--target-checkpoint",c[6]),(!r||32&i)&&V(e,"c-checkpoint-container--dimmed-marker",c[5])},i(c){r||($(t.$$.fragment,c),$(s.$$.fragment,c),r=!0)},o(c){p(t.$$.fragment,c),p(s.$$.fragment,c),r=!1},d(c){c&&h(e),k(t),k(s)}}}function Co(o){let e,t;return e=new at({props:{slot:"leftIcon"}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p:U,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function _o(o){let e,t;return{c(){e=F("Checkpoint "),t=F(o[3])},m(n,s){v(n,e,s),v(n,t,s)},p(n,s){8&s&&K(t,n[3])},d(n){n&&(h(e),h(t))}}}function ko(o){let e,t=Ae(o[7])+"";return{c(){e=F(t)},m(n,s){v(n,e,s)},p(n,s){128&s&&t!==(t=Ae(n[7])+"")&&K(e,t)},d(n){n&&h(e)}}}function bo(o){let e;return{c(){e=F(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function Io(o){let e;function t(r,c){return r[1]?bo:r[6]?ko:void 0}let n=t(o),s=n&&n(o);return{c(){s&&s.c(),e=Q()},m(r,c){s&&s.m(r,c),v(r,e,c)},p(r,c){n===(n=t(r))&&s?s.p(r,c):(s&&s.d(1),s=n&&n(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function So(o){let e,t;return e=new nt({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[Io],text:[_o],leftIcon:[Co]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};1048778&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function To(o){let e,t;return e=new it({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Ro(o){let e,t;return e=new Ie({props:{triggerOn:[He.Hover],content:o[6]||o[4]?"Cannot revert to current version":"Revert to this version",$$slots:{default:[To]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};80&s&&(r.content=n[6]||n[4]?"Cannot revert to current version":"Revert to this version"),1048576&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Eo(o){let e,t,n=(!o[4]||o[2])&&bn(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,[r]){!s[4]||s[2]?n?(n.p(s,r),20&r&&$(n,1)):(n=bn(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(N(),p(n,1,1,()=>{n=null}),H())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Mo(o,e,t){let n,s,r,c,i,a,l,u,d,m,g,{turn:w}=e;const L=ce("checkpointStore"),{targetCheckpointIdx:f,totalCheckpointCount:I,uuidToIdx:A}=L;function P(E){Ct(f,m=E,m)}return le(o,f,E=>t(15,m=E)),le(o,I,E=>t(14,d=E)),le(o,A,E=>t(16,g=E)),o.$$set=E=>{"turn"in E&&t(13,w=E.turn)},o.$$.update=()=>{var E,J,T;73728&o.$$.dirty&&t(0,n=g.get(w.uuid)??-1),8192&o.$$.dirty&&t(7,s=w.toTimestamp),49153&o.$$.dirty&&t(6,(J=d,r=(E=n)===(T=m)||T===void 0&&E===J-1)),49153&o.$$.dirty&&t(5,c=function(S,D,q){return S===q&&q!==void 0&&q<D-1}(n,d,m)),16385&o.$$.dirty&&t(4,i=n===d-1),1&o.$$.dirty&&t(3,a=n+1),8192&o.$$.dirty&&t(2,l=Le(w)),8192&o.$$.dirty&&t(1,u=Le(w)?function(S){var D,q;if((D=S.revertTarget)!=null&&D.uuid){const y=g.get(S.revertTarget.uuid);return y===void 0?void 0:`Reverted to Checkpoint ${y+1}`}return(q=S.revertTarget)!=null&&q.filePath?`Undid changes to ${S.revertTarget.filePath.relPath}`:void 0}(w):void 0)},[n,u,l,a,i,c,r,s,f,I,A,P,async function(){await L.revertToCheckpoint(w.uuid)},w,d,m,g,()=>P(n)]}class Fc extends j{constructor(e){super(),Z(this,e,Mo,Eo,W,{turn:13})}}function Lo(o){let e,t;return e=new at({props:{slot:"leftIcon"}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p:U,i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function In(o){let e,t,n,s,r,c,i=o[1]===1?"":"s";return{c(){e=M("span"),t=F("("),n=F(o[1]),s=F(" file"),r=F(i),c=F(")"),x(e,"class","c-checkpoint-files-count")},m(a,l){v(a,e,l),R(e,t),R(e,n),R(e,s),R(e,r),R(e,c)},p(a,l){2&l&&K(n,a[1]),2&l&&i!==(i=a[1]===1?"":"s")&&K(r,i)},d(a){a&&h(e)}}}function Ao(o){let e,t,n,s,r=o[1]>0&&In(o);return{c(){e=F("Checkpoint "),t=F(o[0]),n=B(),r&&r.c(),s=Q()},m(c,i){v(c,e,i),v(c,t,i),v(c,n,i),r&&r.m(c,i),v(c,s,i)},p(c,i){1&i&&K(t,c[0]),c[1]>0?r?r.p(c,i):(r=In(c),r.c(),r.m(s.parentNode,s)):r&&(r.d(1),r=null)},d(c){c&&(h(e),h(t),h(n),h(s)),r&&r.d(c)}}}function Fo(o){let e;return{c(){e=F(o[2])},m(t,n){v(t,e,n)},p(t,n){4&n&&K(e,t[2])},d(t){t&&h(e)}}}function qo(o){let e;return{c(){e=F(o[3])},m(t,n){v(t,e,n)},p(t,n){8&n&&K(e,t[3])},d(t){t&&h(e)}}}function zo(o){let e;function t(r,c){return r[3]?qo:r[2]?Fo:void 0}let n=t(o),s=n&&n(o);return{c(){s&&s.c(),e=Q()},m(r,c){s&&s.m(r,c),v(r,e,c)},p(r,c){n===(n=t(r))&&s?s.p(r,c):(s&&s.d(1),s=n&&n(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function Sn(o){let e,t,n;return t=new Bt({props:{totalAddedLines:o[4].totalAddedLines,totalRemovedLines:o[4].totalRemovedLines}}),{c(){e=M("div"),C(t.$$.fragment),x(e,"class","c-checkpoint-summary")},m(s,r){v(s,e,r),_(t,e,null),n=!0},p(s,r){const c={};16&r&&(c.totalAddedLines=s[4].totalAddedLines),16&r&&(c.totalRemovedLines=s[4].totalRemovedLines),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),k(t)}}}function Tn(o){let e,t;return e=new ve({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[Po]},$$scope:{ctx:o}}}),e.$on("click",function(){be(o[7])&&o[7].apply(this,arguments)}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){o=n;const r={};256&s&&(r.$$scope={dirty:s,ctx:o}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Bo(o){let e,t;return e=new it({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Po(o){let e,t;return e=new Ie({props:{triggerOn:[He.Hover],content:"Revert to this Checkpoint",$$slots:{default:[Bo]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};256&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function No(o){let e,t,n,s,r,c,i,a;n=new rt({}),r=new nt({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[zo],text:[Ao],leftIcon:[Lo]},$$scope:{ctx:o}}});let l=o[5]&&Sn(o),u=!o[6]&&Tn(o);return{c(){e=M("div"),t=M("div"),C(n.$$.fragment),s=B(),C(r.$$.fragment),c=B(),l&&l.c(),i=B(),u&&u.c(),x(t,"class","c-checkpoint-tag"),x(e,"class","c-checkpoint-header svelte-htx8xt")},m(d,m){v(d,e,m),R(e,t),_(n,t,null),R(t,s),_(r,t,null),R(e,c),l&&l.m(e,null),R(e,i),u&&u.m(e,null),a=!0},p(d,[m]){const g={};271&m&&(g.$$scope={dirty:m,ctx:d}),r.$set(g),d[5]?l?(l.p(d,m),32&m&&$(l,1)):(l=Sn(d),l.c(),$(l,1),l.m(e,i)):l&&(N(),p(l,1,1,()=>{l=null}),H()),d[6]?u&&(N(),p(u,1,1,()=>{u=null}),H()):u?(u.p(d,m),64&m&&$(u,1)):(u=Tn(d),u.c(),$(u,1),u.m(e,null))},i(d){a||($(n.$$.fragment,d),$(r.$$.fragment,d),$(l),$(u),a=!0)},o(d){p(n.$$.fragment,d),p(r.$$.fragment,d),p(l),p(u),a=!1},d(d){d&&h(e),k(n),k(r),l&&l.d(),u&&u.d()}}}function Ho(o,e,t){let{displayCheckpointIdx:n}=e,{filesCount:s=0}=e,{timestamp:r=""}=e,{revertMessage:c}=e,{diffSummary:i={totalAddedLines:0,totalRemovedLines:0}}=e,{hasChanges:a=!1}=e,{isTarget:l=!1}=e,{onRevertClick:u}=e;return o.$$set=d=>{"displayCheckpointIdx"in d&&t(0,n=d.displayCheckpointIdx),"filesCount"in d&&t(1,s=d.filesCount),"timestamp"in d&&t(2,r=d.timestamp),"revertMessage"in d&&t(3,c=d.revertMessage),"diffSummary"in d&&t(4,i=d.diffSummary),"hasChanges"in d&&t(5,a=d.hasChanges),"isTarget"in d&&t(6,l=d.isTarget),"onRevertClick"in d&&t(7,u=d.onRevertClick)},[n,s,r,c,i,a,l,u]}class Do extends j{constructor(e){super(),Z(this,e,Ho,No,W,{displayCheckpointIdx:0,filesCount:1,timestamp:2,revertMessage:3,diffSummary:4,hasChanges:5,isTarget:6,onRevertClick:7})}}function Rn(o,e,t){const n=o.slice();return n[33]=e[t],n}function En(o){let e,t,n,s,r,c,i;function a(u){o[27](u)}let l={class:"c-checkpoint-collapsible",stickyHeader:!0,$$slots:{header:[Go],default:[Jo]},$$scope:{ctx:o}};return o[3]!==void 0&&(l.collapsed=o[3]),t=new ct({props:l}),re.push(()=>Be(t,"collapsed",a)),{c(){e=M("div"),C(t.$$.fragment),x(e,"class","c-checkpoint-container svelte-1e7odgw"),x(e,"data-checkpoint-number",o[2]),V(e,"c-checkpoint-container--target-checkpoint",o[11]),V(e,"c-checkpoint-container--dimmed-marker",o[10])},m(u,d){v(u,e,d),_(t,e,null),r=!0,c||(i=Kn(s=qt.call(null,e,{onVisible:o[28],scrollTarget:document.body})),c=!0)},p(u,d){const m={};6522&d[0]|32&d[1]&&(m.$$scope={dirty:d,ctx:u}),!n&&8&d[0]&&(n=!0,m.collapsed=u[3],Pe(()=>n=!1)),t.$set(m),(!r||4&d[0])&&x(e,"data-checkpoint-number",u[2]),s&&be(s.update)&&1&d[0]&&s.update.call(null,{onVisible:u[28],scrollTarget:document.body}),(!r||2048&d[0])&&V(e,"c-checkpoint-container--target-checkpoint",u[11]),(!r||1024&d[0])&&V(e,"c-checkpoint-container--dimmed-marker",u[10])},i(u){r||($(t.$$.fragment,u),r=!0)},o(u){p(t.$$.fragment,u),r=!1},d(u){u&&h(e),k(t),c=!1,i()}}}function Oo(o){let e,t,n;return t=new ee({props:{size:1,color:"neutral",$$slots:{default:[Vo]},$$scope:{ctx:o}}}),{c(){e=M("div"),C(t.$$.fragment),x(e,"class","c-edits-list c-edits-list--empty svelte-1e7odgw")},m(s,r){v(s,e,r),_(t,e,null),n=!0},p(s,r){const c={};32&r[1]&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),k(t)}}}function Uo(o){let e,t,n=Fe(o[1]),s=[];for(let c=0;c<n.length;c+=1)s[c]=Mn(Rn(o,n,c));const r=c=>p(s[c],1,1,()=>{s[c]=null});return{c(){e=M("div");for(let c=0;c<s.length;c+=1)s[c].c();x(e,"class","c-edits-list svelte-1e7odgw")},m(c,i){v(c,e,i);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);t=!0},p(c,i){if(196610&i[0]){let a;for(n=Fe(c[1]),a=0;a<n.length;a+=1){const l=Rn(c,n,a);s[a]?(s[a].p(l,i),$(s[a],1)):(s[a]=Mn(l),s[a].c(),$(s[a],1),s[a].m(e,null))}for(N(),a=n.length;a<s.length;a+=1)r(a);H()}},i(c){if(!t){for(let i=0;i<n.length;i+=1)$(s[i]);t=!0}},o(c){s=s.filter(Boolean);for(let i=0;i<s.length;i+=1)p(s[i]);t=!1},d(c){c&&h(e),Yn(s,c)}}}function Vo(o){let e;return{c(){e=F("No changes to show")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function Mn(o){let e,t;function n(){return o[25](o[33])}function s(){return o[26](o[33])}return e=new Zt({props:{qualifiedPathName:o[33].qualifiedPathName,lineChanges:o[33].changesSummary,onClickFile:n,onClickReview:s}}),{c(){C(e.$$.fragment)},m(r,c){_(e,r,c),t=!0},p(r,c){o=r;const i={};2&c[0]&&(i.qualifiedPathName=o[33].qualifiedPathName),2&c[0]&&(i.lineChanges=o[33].changesSummary),2&c[0]&&(i.onClickFile=n),2&c[0]&&(i.onClickReview=s),e.$set(i)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){p(e.$$.fragment,r),t=!1},d(r){k(e,r)}}}function Jo(o){let e,t,n,s;const r=[Uo,Oo],c=[];function i(a,l){return a[4]?0:a[3]?-1:1}return~(e=i(o))&&(t=c[e]=r[e](o)),{c(){t&&t.c(),n=Q()},m(a,l){~e&&c[e].m(a,l),v(a,n,l),s=!0},p(a,l){let u=e;e=i(a),e===u?~e&&c[e].p(a,l):(t&&(N(),p(c[u],1,1,()=>{c[u]=null}),H()),~e?(t=c[e],t?t.p(a,l):(t=c[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n)):t=null)},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),~e&&c[e].d(a)}}}function Go(o){let e,t;return e=new Do({props:{slot:"header",displayCheckpointIdx:o[8],filesCount:o[1].length,timestamp:Ae(o[12]),revertMessage:o[6],diffSummary:o[5],hasChanges:o[4],isTarget:o[11],onRevertClick:o[18]}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};256&s[0]&&(r.displayCheckpointIdx=n[8]),2&s[0]&&(r.filesCount=n[1].length),4096&s[0]&&(r.timestamp=Ae(n[12])),64&s[0]&&(r.revertMessage=n[6]),32&s[0]&&(r.diffSummary=n[5]),16&s[0]&&(r.hasChanges=n[4]),2048&s[0]&&(r.isTarget=n[11]),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function jo(o){let e,t,n=(!o[9]||o[7])&&En(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){!s[9]||s[7]?n?(n.p(s,r),640&r[0]&&$(n,1)):(n=En(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(N(),p(n,1,1,()=>{n=null}),H())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Zo(o,e,t){let n,s,r,c,i,a,l,u,d,m,g,w,L,f,I,{turn:A}=e;const P=ce("checkpointStore"),E=ce("chatModel"),{targetCheckpointIdx:J,totalCheckpointCount:T,uuidToIdx:S}=P;le(o,J,O=>t(23,f=O)),le(o,T,O=>t(22,L=O)),le(o,S,O=>t(24,I=O));let D=!0;function q(O){E==null||E.extensionClient.openFile({repoRoot:O.rootPath,pathName:O.relPath,allowOutOfWorkspace:!0})}function y(O){E==null||E.extensionClient.showAgentReview(O,r,s,!1)}let X=[],z=!1,G=!1;return o.$$set=O=>{"turn"in O&&t(19,A=O.turn)},o.$$.update=()=>{var O,$e,se;17301504&o.$$.dirty[0]&&t(2,n=I.get(A.uuid)??-1),524288&o.$$.dirty[0]&&t(12,s=A.toTimestamp),524288&o.$$.dirty[0]&&(r=A.fromTimestamp),12582916&o.$$.dirty[0]&&t(11,($e=L,c=(O=n)===(se=f)||se===void 0&&O===$e-1)),12582916&o.$$.dirty[0]&&t(10,i=function(b,ie,ue){return b===ue&&ue!==void 0&&ue<ie-1}(n,L,f)),4194308&o.$$.dirty[0]&&t(9,a=n===L-1),4&o.$$.dirty[0]&&t(8,l=n+1),524288&o.$$.dirty[0]&&t(7,u=Le(A)),524288&o.$$.dirty[0]&&t(6,d=Le(A)?function(b){var ie,ue;if((ie=b.revertTarget)!=null&&ie.uuid){const we=I.get(b.revertTarget.uuid);return we===void 0?void 0:`Reverted to Checkpoint ${we+1}`}return(ue=b.revertTarget)!=null&&ue.filePath?`Undid changes to ${b.revertTarget.filePath.relPath}`:void 0}(A):void 0),2621441&o.$$.dirty[0]&&z&&A&&!G&&P.getCheckpointSummary(A).then(b=>{t(20,X=b),t(21,G=!0)}),1048576&o.$$.dirty[0]&&t(1,m=X.filter(b=>b.changesSummary&&(b.changesSummary.totalAddedLines>0||b.changesSummary.totalRemovedLines>0))),2&o.$$.dirty[0]&&t(5,g=m.reduce((b,ie)=>{var ue,we;return b.totalAddedLines+=((ue=ie.changesSummary)==null?void 0:ue.totalAddedLines)??0,b.totalRemovedLines+=((we=ie.changesSummary)==null?void 0:we.totalRemovedLines)??0,b},{totalAddedLines:0,totalRemovedLines:0})),2&o.$$.dirty[0]&&t(4,w=m.length>0)},[z,m,n,D,w,g,d,u,l,a,i,c,s,J,T,S,q,y,function(){P.revertToCheckpoint(A.uuid)},A,X,G,L,f,I,O=>q(O.qualifiedPathName),O=>y(O.qualifiedPathName),function(O){D=O,t(3,D)},()=>t(0,z=!0)]}class qc extends j{constructor(e){super(),Z(this,e,Zo,jo,W,{turn:19},null,[-1,-1])}}const Ln={[Ve.SUCCESS]:"success",[Ve.FAILED]:"error",[Ve.SKIPPED]:"skipped"},An={[pe.success]:"success",[pe.failure]:"error",[pe.running]:null,[pe.unknown]:"unknown",[pe.skipped]:"skipped"};function Fn(o){return o in Ln?Ln[o]:o in An?An[o]:null}function qn(o){switch(o){case"success":return"Success";case"error":return"Failed";case"skipped":return"Skipped";case"unknown":return"Unknown";case null:return"Running"}}function zn(o){let e,t,n;return t=new ee({props:{size:1,type:"monospace",$$slots:{default:[Wo]},$$scope:{ctx:o}}}),{c(){e=M("div"),C(t.$$.fragment),x(e,"class","c-command-output__code-block svelte-1t0700a")},m(s,r){v(s,e,r),_(t,e,null),n=!0},p(s,r){const c={};16386&r&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),k(t)}}}function Wo(o){let e;return{c(){e=F(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function Bn(o){let e,t,n,s;const r=[Yo,Ko],c=[];function i(a,l){return a[5]?0:1}return t=i(o),n=c[t]=r[t](o),{c(){e=M("div"),n.c(),x(e,"class","c-command-output__code-block c-command-output__code-block--output svelte-1t0700a")},m(a,l){v(a,e,l),c[t].m(e,null),s=!0},p(a,l){let u=t;t=i(a),t===u?c[t].p(a,l):(N(),p(c[u],1,1,()=>{c[u]=null}),H(),n=c[t],n?n.p(a,l):(n=c[t]=r[t](a),n.c()),$(n,1),n.m(e,null))},i(a){s||($(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(e),c[t].d()}}}function Ko(o){let e,t;return e=new ee({props:{size:1,type:"monospace",$$slots:{default:[Xo]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};16388&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Yo(o){let e,t;return e=new os.Root({props:{$$slots:{default:[Qo]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};16452&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Xo(o){let e;return{c(){e=F(o[2])},m(t,n){v(t,e,n)},p(t,n){4&n&&K(e,t[2])},d(t){t&&h(e)}}}function Qo(o){let e,t;return e=new rs({props:{text:o[2],lang:o[6]}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};4&s&&(r.text=n[2]),64&s&&(r.lang=n[6]),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function er(o){let e,t,n,s,r=o[1]&&!o[0]&&zn(o),c=o[2]&&(!o[3]||o[3]!=="skipped")&&Bn(o);const i=o[12].default,a=ye(i,o,o[14],null);return{c(){e=M("div"),r&&r.c(),t=B(),c&&c.c(),n=B(),a&&a.c(),x(e,"class","c-command-output__command-details")},m(l,u){v(l,e,u),r&&r.m(e,null),R(e,t),c&&c.m(e,null),R(e,n),a&&a.m(e,null),s=!0},p(l,u){l[1]&&!l[0]?r?(r.p(l,u),3&u&&$(r,1)):(r=zn(l),r.c(),$(r,1),r.m(e,t)):r&&(N(),p(r,1,1,()=>{r=null}),H()),!l[2]||l[3]&&l[3]==="skipped"?c&&(N(),p(c,1,1,()=>{c=null}),H()):c?(c.p(l,u),12&u&&$(c,1)):(c=Bn(l),c.c(),$(c,1),c.m(e,n)),a&&a.p&&(!s||16384&u)&&Ce(a,i,l,l[14],s?ke(i,l[14],u,null):_e(l[14]),null)},i(l){s||($(r),$(c),$(a,l),s=!0)},o(l){p(r),p(c),p(a,l),s=!1},d(l){l&&h(e),r&&r.d(),c&&c.d(),a&&a.d(l)}}}function nr(o){let e;return{c(){e=M("div"),x(e,"class","c-command-output__collapsible-header__spacer svelte-1t0700a")},m(t,n){v(t,e,n)},i:U,o:U,d(t){t&&h(e)}}}function tr(o){let e,t;return e=new rt({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function sr(o){let e,t;return e=new ee({props:{size:1,type:"monospace",$$slots:{default:[rr]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};16386&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function or(o){let e,t;return e=new ee({props:{size:1,weight:"medium",$$slots:{default:[cr]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};16385&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function rr(o){let e;return{c(){e=F(o[1])},m(t,n){v(t,e,n)},p(t,n){2&n&&K(e,t[1])},d(t){t&&h(e)}}}function cr(o){let e;return{c(){e=F(o[0])},m(t,n){v(t,e,n)},p(t,n){1&n&&K(e,t[0])},d(t){t&&h(e)}}}function ir(o){let e,t,n,s=o[3]==="skipped"&&Pn(o);return t=new Ie({props:{content:qn(o[3]),triggerOn:[He.Hover],$$slots:{default:[ur]},$$scope:{ctx:o}}}),{c(){s&&s.c(),e=B(),C(t.$$.fragment)},m(r,c){s&&s.m(r,c),v(r,e,c),_(t,r,c),n=!0},p(r,c){r[3]==="skipped"?s?8&c&&$(s,1):(s=Pn(r),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(N(),p(s,1,1,()=>{s=null}),H());const i={};8&c&&(i.content=qn(r[3])),16392&c&&(i.$$scope={dirty:c,ctx:r}),t.$set(i)},i(r){n||($(s),$(t.$$.fragment,r),n=!0)},o(r){p(s),p(t.$$.fragment,r),n=!1},d(r){r&&h(e),s&&s.d(r),k(t,r)}}}function ar(o){let e,t,n;return t=new he({props:{size:1}}),{c(){e=M("div"),C(t.$$.fragment),x(e,"class","c-command-output__status-icon c-command-output__status-icon--loading svelte-1t0700a")},m(s,r){v(s,e,r),_(t,e,null),n=!0},p:U,i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),k(t)}}}function Pn(o){let e,t;return e=new ee({props:{size:1,$$slots:{default:[lr]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function lr(o){let e;return{c(){e=F("Skipped")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function ur(o){let e,t,n;var s=o[11](o[3]);return s&&(t=sn(s,{})),{c(){e=M("div"),t&&C(t.$$.fragment),x(e,"class","c-command-output__status-icon svelte-1t0700a"),V(e,"c-command-output__status-icon--success",o[3]==="success"),V(e,"c-command-output__status-icon--error",o[3]==="error"),V(e,"c-command-output__status-icon--warning",o[3]==="skipped")},m(r,c){v(r,e,c),t&&_(t,e,null),n=!0},p(r,c){if(8&c&&s!==(s=r[11](r[3]))){if(t){N();const i=t;p(i.$$.fragment,1,0,()=>{k(i,1)}),H()}s?(t=sn(s,{}),C(t.$$.fragment),$(t.$$.fragment,1),_(t,e,null)):t=null}(!n||8&c)&&V(e,"c-command-output__status-icon--success",r[3]==="success"),(!n||8&c)&&V(e,"c-command-output__status-icon--error",r[3]==="error"),(!n||8&c)&&V(e,"c-command-output__status-icon--warning",r[3]==="skipped")},i(r){n||(t&&$(t.$$.fragment,r),n=!0)},o(r){t&&p(t.$$.fragment,r),n=!1},d(r){r&&h(e),t&&k(t)}}}function Nn(o){let e,t;return e=new Ie({props:{content:o[7],align:"end",$$slots:{default:[$r]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};128&s&&(r.content=n[7]),17412&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function dr(o){let e,t;return e=new ts({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function $r(o){let e,t;return e=new ve({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[dr]},$$scope:{ctx:o}}}),e.$on("click",o[13]),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};16384&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function mr(o){let e,t,n,s,r,c,i,a,l,u,d,m;const g=[tr,nr],w=[];function L(S,D){return S[8]&&S[3]!=="skipped"?0:1}t=L(o),n=w[t]=g[t](o);const f=[or,sr],I=[];function A(S,D){return S[0]?0:1}r=A(o),c=I[r]=f[r](o);const P=[ar,ir],E=[];function J(S,D){return S[9]?0:S[3]!==null?1:-1}~(l=J(o))&&(u=E[l]=P[l](o));let T=o[2]&&(!o[3]||o[3]!=="skipped")&&!o[9]&&Nn(o);return{c(){e=M("div"),n.c(),s=B(),c.c(),i=B(),a=M("div"),u&&u.c(),d=B(),T&&T.c(),x(a,"class","c-command-output__status-indicator svelte-1t0700a"),x(e,"slot","header"),x(e,"class","c-command-output__collapsible-header svelte-1t0700a")},m(S,D){v(S,e,D),w[t].m(e,null),R(e,s),I[r].m(e,null),R(e,i),R(e,a),~l&&E[l].m(a,null),R(a,d),T&&T.m(a,null),m=!0},p(S,D){let q=t;t=L(S),t!==q&&(N(),p(w[q],1,1,()=>{w[q]=null}),H(),n=w[t],n||(n=w[t]=g[t](S),n.c()),$(n,1),n.m(e,s));let y=r;r=A(S),r===y?I[r].p(S,D):(N(),p(I[y],1,1,()=>{I[y]=null}),H(),c=I[r],c?c.p(S,D):(c=I[r]=f[r](S),c.c()),$(c,1),c.m(e,i));let X=l;l=J(S),l===X?~l&&E[l].p(S,D):(u&&(N(),p(E[X],1,1,()=>{E[X]=null}),H()),~l?(u=E[l],u?u.p(S,D):(u=E[l]=P[l](S),u.c()),$(u,1),u.m(a,d)):u=null),!S[2]||S[3]&&S[3]==="skipped"||S[9]?T&&(N(),p(T,1,1,()=>{T=null}),H()):T?(T.p(S,D),524&D&&$(T,1)):(T=Nn(S),T.c(),$(T,1),T.m(a,null))},i(S){m||($(n),$(c),$(u),$(T),m=!0)},o(S){p(n),p(c),p(u),p(T),m=!1},d(S){S&&h(e),w[t].d(),I[r].d(),~l&&E[l].d(),T&&T.d()}}}function pr(o){let e,t,n;return t=new ct({props:{collapsed:o[4],$$slots:{header:[mr],default:[er]},$$scope:{ctx:o}}}),{c(){e=M("div"),C(t.$$.fragment),x(e,"class","c-command-output__container svelte-1t0700a")},m(s,r){v(s,e,r),_(t,e,null),n=!0},p(s,[r]){const c={};16&r&&(c.collapsed=s[4]),18415&r&&(c.$$scope={dirty:r,ctx:s}),t.$set(c)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){s&&h(e),k(t)}}}function gr(o,e,t){let{$$slots:n={},$$scope:s}=e,{title:r=""}=e,{command:c=""}=e,{output:i=null}=e,{status:a=null}=e,{collapsed:l=!1}=e,{useMonaco:u=!1}=e,{monacoLang:d="bash"}=e,{viewButtonTooltip:m="View full output in editor"}=e,{showCollapseButton:g=!0}=e,{isLoading:w=!1}=e,{onViewOutput:L}=e;return o.$$set=f=>{"title"in f&&t(0,r=f.title),"command"in f&&t(1,c=f.command),"output"in f&&t(2,i=f.output),"status"in f&&t(3,a=f.status),"collapsed"in f&&t(4,l=f.collapsed),"useMonaco"in f&&t(5,u=f.useMonaco),"monacoLang"in f&&t(6,d=f.monacoLang),"viewButtonTooltip"in f&&t(7,m=f.viewButtonTooltip),"showCollapseButton"in f&&t(8,g=f.showCollapseButton),"isLoading"in f&&t(9,w=f.isLoading),"onViewOutput"in f&&t(10,L=f.onViewOutput),"$$scope"in f&&t(14,s=f.$$scope)},[r,c,i,a,l,u,d,m,g,w,L,function(f){return f==="success"||f==="skipped"?et:Pt},n,f=>L(f,i),s]}class fr extends j{constructor(e){super(),Z(this,e,gr,pr,W,{title:0,command:1,output:2,status:3,collapsed:4,useMonaco:5,monacoLang:6,viewButtonTooltip:7,showCollapseButton:8,isLoading:9,onViewOutput:10})}}function Hn(o,e,t){const n=o.slice();return n[15]=e[t],n}function hr(o){let e,t,n,s,r;return t=new ee({props:{size:1,color:"secondary",$$slots:{default:[wr]},$$scope:{ctx:o}}}),s=new he({props:{size:1}}),{c(){e=M("div"),C(t.$$.fragment),n=B(),C(s.$$.fragment),x(e,"class","c-agent-no-setup-logs svelte-12293rd")},m(c,i){v(c,e,i),_(t,e,null),R(e,n),_(s,e,null),r=!0},p(c,i){const a={};262144&i&&(a.$$scope={dirty:i,ctx:c}),t.$set(a)},i(c){r||($(t.$$.fragment,c),$(s.$$.fragment,c),r=!0)},o(c){p(t.$$.fragment,c),p(s.$$.fragment,c),r=!1},d(c){c&&h(e),k(t),k(s)}}}function vr(o){let e,t,n,s,r,c,i,a,l,u,d,m,g,w,L,f,I,A;r=new ve({props:{variant:"ghost",color:"neutral",size:1,class:"c-agent-setup-logs-toggle-button "+(o[2]?"c-agent-setup-logs-toggle-button--expanded":""),$$slots:{default:[xr]},$$scope:{ctx:o}}}),r.$on("click",o[7]);const P=[Cr,yr],E=[];function J(z,G){return z[0]?0:1}i=J(o),a=E[i]=P[i](o);const T=[Ir,br],S=[];function D(z,G){return z[0]?0:1}d=D(o),m=S[d]=T[d](o);let q=Fe(o[3].steps),y=[];for(let z=0;z<q.length;z+=1)y[z]=Dn(Hn(o,q,z));const X=z=>p(y[z],1,1,()=>{y[z]=null});return{c(){e=M("div"),t=M("div"),n=M("div"),s=M("div"),C(r.$$.fragment),c=B(),a.c(),l=B(),u=M("div"),m.c(),g=B(),w=M("div"),L=M("div");for(let z=0;z<y.length;z+=1)y[z].c();x(s,"class","c-agent-setup-logs-summary-left svelte-12293rd"),x(u,"class","c-agent-setup-logs-summary-icon svelte-12293rd"),x(n,"class","c-agent-setup-logs-summary-content svelte-12293rd"),x(t,"class","c-agent-setup-logs-summary svelte-12293rd"),x(t,"role","button"),x(t,"tabindex","0"),x(t,"aria-expanded",o[2]),x(t,"aria-controls","agent-setup-logs-details"),x(L,"class","c-agent-setup-logs svelte-12293rd"),x(w,"class","c-agent-setup-logs-wrapper svelte-12293rd"),V(w,"is-hidden",!o[2]),x(e,"class","c-agent-setup-logs-container svelte-12293rd"),V(e,"c-agent-setup-logs-container--loading",!o[0])},m(z,G){v(z,e,G),R(e,t),R(t,n),R(n,s),_(r,s,null),R(s,c),E[i].m(s,null),R(n,l),R(n,u),S[d].m(u,null),R(e,g),R(e,w),R(w,L);for(let O=0;O<y.length;O+=1)y[O]&&y[O].m(L,null);o[13](e),f=!0,I||(A=[te(t,"click",o[6]),te(t,"keydown",o[8])],I=!0)},p(z,G){const O={};4&G&&(O.class="c-agent-setup-logs-toggle-button "+(z[2]?"c-agent-setup-logs-toggle-button--expanded":"")),262144&G&&(O.$$scope={dirty:G,ctx:z}),r.$set(O);let $e=i;i=J(z),i!==$e&&(N(),p(E[$e],1,1,()=>{E[$e]=null}),H(),a=E[i],a||(a=E[i]=P[i](z),a.c()),$(a,1),a.m(s,null));let se=d;if(d=D(z),d!==se&&(N(),p(S[se],1,1,()=>{S[se]=null}),H(),m=S[d],m||(m=S[d]=T[d](z),m.c()),$(m,1),m.m(u,null)),(!f||4&G)&&x(t,"aria-expanded",z[2]),552&G){let b;for(q=Fe(z[3].steps),b=0;b<q.length;b+=1){const ie=Hn(z,q,b);y[b]?(y[b].p(ie,G),$(y[b],1)):(y[b]=Dn(ie),y[b].c(),$(y[b],1),y[b].m(L,null))}for(N(),b=q.length;b<y.length;b+=1)X(b);H()}(!f||4&G)&&V(w,"is-hidden",!z[2]),(!f||1&G)&&V(e,"c-agent-setup-logs-container--loading",!z[0])},i(z){if(!f){$(r.$$.fragment,z),$(a),$(m);for(let G=0;G<q.length;G+=1)$(y[G]);f=!0}},o(z){p(r.$$.fragment,z),p(a),p(m),y=y.filter(Boolean);for(let G=0;G<y.length;G+=1)p(y[G]);f=!1},d(z){z&&h(e),k(r),E[i].d(),S[d].d(),Yn(y,z),o[13](null),I=!1,ze(A)}}}function wr(o){let e;return{c(){e=F("Waiting to start agent environment...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function xr(o){let e,t;return e=new cs({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function yr(o){let e,t;return e=new ee({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[_r]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Cr(o){let e,t;return e=new ee({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[kr]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function _r(o){let e;return{c(){e=F("Environment is being created...")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function kr(o){let e;return{c(){e=F("Environment created")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function br(o){let e,t;return e=new he({props:{size:1}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Ir(o){let e,t;return e=new et({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Dn(o){let e,t;return e=new fr({props:{title:o[15].step_description,output:o[15].logs,status:Fn(o[15].status),isLoading:o[9](o[15].status),collapsed:!o[9](o[15].status),showCollapseButton:!!o[15].logs,viewButtonTooltip:"View full output in editor",onViewOutput:o[12]}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};8&s&&(r.title=n[15].step_description),8&s&&(r.output=n[15].logs),8&s&&(r.status=Fn(n[15].status)),8&s&&(r.isLoading=n[9](n[15].status)),8&s&&(r.collapsed=!n[9](n[15].status)),8&s&&(r.showCollapseButton=!!n[15].logs),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Sr(o){let e,t,n,s;const r=[vr,hr],c=[];function i(a,l){return a[3]&&a[3].steps&&a[3].steps.length>0?0:1}return e=i(o),t=c[e]=r[e](o),{c(){t.c(),n=Q()},m(a,l){c[e].m(a,l),v(a,n,l),s=!0},p(a,[l]){let u=e;e=i(a),e===u?c[e].p(a,l):(N(),p(c[u],1,1,()=>{c[u]=null}),H(),t=c[e],t?t.p(a,l):(t=c[e]=r[e](a),t.c()),$(t,1),t.m(n.parentNode,n))},i(a){s||($(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(n),c[e].d(a)}}}function Tr(o,e,t){let n,s,r,c,i;const a=ce(qe.key);le(o,a,g=>t(11,c=g));const l=ce("chatModel");let u=!1;function d(g){g&&l&&l.extensionClient.openScratchFile(g,"plaintext")}function m(){t(2,u=!u)}return o.$$.update=()=>{var g;2048&o.$$.dirty&&t(10,n=((g=c==null?void 0:c.currentAgent)==null?void 0:g.status)||Se.agentUnspecified),1024&o.$$.dirty&&t(0,s=[Se.agentIdle,Se.agentRunning,Se.agentFailed].includes(n)),2048&o.$$.dirty&&t(3,r=c==null?void 0:c.agentSetupLogs),1&o.$$.dirty&&t(2,u=!s)},[s,i,u,r,a,d,m,function(g){g.stopPropagation(),m()},function(g){g.key!=="Enter"&&g.key!==" "||(g.preventDefault(),m())},function(g){return g===pe.running&&!s},n,c,(g,w)=>d(w),function(g){re[g?"unshift":"push"](()=>{i=g,t(1,i)})}]}class zc extends j{constructor(e){super(),Z(this,e,Tr,Sr,W,{})}}function Rr(o){let e;const t=o[3].default,n=ye(t,o,o[4],null);return{c(){n&&n.c()},m(s,r){n&&n.m(s,r),e=!0},p(s,r){n&&n.p&&(!e||16&r)&&Ce(n,t,s,s[4],e?ke(t,s[4],r,null):_e(s[4]),null)},i(s){e||($(n,s),e=!0)},o(s){p(n,s),e=!1},d(s){n&&n.d(s)}}}function Er(o){let e,t;return e=new zt({props:{class:"c-chat-floating-container c-chat-floating-container--"+o[0],xPos:o[1],yPos:o[2],$$slots:{default:[Rr]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,[s]){const r={};1&s&&(r.class="c-chat-floating-container c-chat-floating-container--"+n[0]),2&s&&(r.xPos=n[1]),4&s&&(r.yPos=n[2]),16&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Mr(o,e,t){let{$$slots:n={},$$scope:s}=e,{position:r="bottom"}=e,{xPos:c="middle"}=e,{yPos:i=r==="top"?"top":"bottom"}=e;return o.$$set=a=>{"position"in a&&t(0,r=a.position),"xPos"in a&&t(1,c=a.xPos),"yPos"in a&&t(2,i=a.yPos),"$$scope"in a&&t(4,s=a.$$scope)},[r,c,i,n,s]}class Lr extends j{constructor(e){super(),Z(this,e,Mr,Er,W,{position:0,xPos:1,yPos:2})}}function Ar(o){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},o[0]],s={};for(let r=0;r<n.length;r+=1)s=xe(s,n[r]);return{c(){e=me("svg"),t=new _t(!0),this.h()},l(r){e=kt(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=bt(e);t=It(c,!0),c.forEach(h),this.h()},h(){t.a=null,on(e,s)},m(r,c){St(r,e,c),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M174.6 472.6c4.5 4.7 10.8 7.4 17.4 7.4s12.8-2.7 17.4-7.4l168-176c9.2-9.6 8.8-24.8-.8-33.9s-24.8-8.8-33.9.8L216 396.1V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v340.1L41.4 263.4c-9.2-9.6-24.3-9.9-33.9-.8s-9.9 24.3-.8 33.9l168 176z"/>',e)},p(r,[c]){on(e,s=je(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&c&&r[0]]))},i:U,o:U,d(r){r&&h(e)}}}function Fr(o,e,t){return o.$$set=n=>{t(0,e=xe(xe({},e),rn(n)))},[e=rn(e)]}class qr extends j{constructor(e){super(),Z(this,e,Fr,Ar,W,{})}}function On(o){let e,t,n,s,r,c;return n=new ve({props:{class:"c-chat-floating-button",variant:"outline",color:"neutral",size:1,radius:"full",$$slots:{default:[zr]},$$scope:{ctx:o}}}),n.$on("click",o[1]),{c(){e=M("div"),t=M("div"),C(n.$$.fragment),x(e,"class","c-msg-list-bottom-button svelte-1eg3it6")},m(i,a){v(i,e,a),R(e,t),_(n,t,null),c=!0},p(i,a){const l={};8&a&&(l.$$scope={dirty:a,ctx:i}),n.$set(l)},i(i){c||($(n.$$.fragment,i),i&&cn(()=>{c&&(s||(s=Te(t,Re,{duration:150},!0)),s.run(1))}),i&&cn(()=>{c&&(r||(r=Te(e,Re,{duration:150},!0)),r.run(1))}),c=!0)},o(i){p(n.$$.fragment,i),i&&(s||(s=Te(t,Re,{duration:150},!1)),s.run(0)),i&&(r||(r=Te(e,Re,{duration:150},!1)),r.run(0)),c=!1},d(i){i&&h(e),k(n),i&&s&&s.end(),i&&r&&r.end()}}}function zr(o){let e,t;return e=new qr({}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Br(o){let e,t,n=o[0]&&On(o);return{c(){n&&n.c(),e=Q()},m(s,r){n&&n.m(s,r),v(s,e,r),t=!0},p(s,r){s[0]?n?(n.p(s,r),1&r&&$(n,1)):(n=On(s),n.c(),$(n,1),n.m(e.parentNode,e)):n&&(N(),p(n,1,1,()=>{n=null}),H())},i(s){t||($(n),t=!0)},o(s){p(n),t=!1},d(s){s&&h(e),n&&n.d(s)}}}function Pr(o){let e,t;return e=new Lr({props:{position:"bottom",$$slots:{default:[Br]},$$scope:{ctx:o}}}),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,[s]){const r={};9&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Nr(o,e,t){let{showScrollDown:n=!1}=e,{messageListElement:s=null}=e;return o.$$set=r=>{"showScrollDown"in r&&t(0,n=r.showScrollDown),"messageListElement"in r&&t(2,s=r.messageListElement)},[n,()=>{s&&Qn(s,{smooth:!0})},s]}class Bc extends j{constructor(e){super(),Z(this,e,Nr,Pr,W,{showScrollDown:0,messageListElement:2})}}function Hr(o){let e;return{c(){e=M("div"),e.innerHTML='<span class="c-paused-remote-agent__text svelte-av8nea">This agent is paused and will resume when you send a new message</span>',x(e,"class","c-paused-remote-agent svelte-av8nea")},m(t,n){v(t,e,n)},p:U,i:U,o:U,d(t){t&&h(e)}}}class Pc extends j{constructor(e){super(),Z(this,e,null,Hr,W,{})}}function Un(o){let e,t;return{c(){e=F("Retrying in "),t=F(o[3])},m(n,s){v(n,e,s),v(n,t,s)},p(n,s){8&s&&K(t,n[3])},d(n){n&&(h(e),h(t))}}}function Vn(o){let e,t;return e=new Oe({props:{class:"c-remote-agent-error__button c-remote-agent-error__button--retry",variant:"soft",color:"neutral",size:1,$$slots:{default:[Dr]},$$scope:{ctx:o}}}),e.$on("click",o[11]),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};32832&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Dr(o){let e,t,n=o[6]?" now":"";return{c(){e=F("Retry"),t=F(n)},m(s,r){v(s,e,r),v(s,t,r)},p(s,r){64&r&&n!==(n=s[6]?" now":"")&&K(t,n)},d(s){s&&(h(e),h(t))}}}function Jn(o){let e,t;return e=new Oe({props:{class:"c-remote-agent-error__button c-remote-agent-error__button--delete",variant:"soft",color:"error",size:1,$$slots:{default:[Or]},$$scope:{ctx:o}}}),e.$on("click",o[12]),{c(){C(e.$$.fragment)},m(n,s){_(e,n,s),t=!0},p(n,s){const r={};32768&s&&(r.$$scope={dirty:s,ctx:n}),e.$set(r)},i(n){t||($(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Or(o){let e;return{c(){e=F("Delete Agent")},m(t,n){v(t,e,n)},d(t){t&&h(e)}}}function Ur(o){let e,t,n,s,r,c,i,a,l,u=o[6]&&o[3]&&Un(o),d=o[4]&&Vn(o),m=o[5]&&Jn(o);return{c(){e=M("div"),t=M("div"),n=M("span"),s=F(o[7]),r=B(),u&&u.c(),c=B(),i=M("div"),d&&d.c(),a=B(),m&&m.c(),x(n,"class","c-remote-agent-error__message svelte-14lhvr1"),x(i,"class","c-remote-agent-error__actions"),x(t,"class","c-remote-agent-error__content svelte-14lhvr1"),x(e,"class","c-remote-agent-error svelte-14lhvr1"),V(e,"c-remote-agent-error--unrecoverable",o[2])},m(g,w){v(g,e,w),R(e,t),R(t,n),R(n,s),R(n,r),u&&u.m(n,null),R(t,c),R(t,i),d&&d.m(i,null),R(i,a),m&&m.m(i,null),l=!0},p(g,[w]){(!l||128&w)&&K(s,g[7]),g[6]&&g[3]?u?u.p(g,w):(u=Un(g),u.c(),u.m(n,null)):u&&(u.d(1),u=null),g[4]?d?(d.p(g,w),16&w&&$(d,1)):(d=Vn(g),d.c(),$(d,1),d.m(i,a)):d&&(N(),p(d,1,1,()=>{d=null}),H()),g[5]?m?(m.p(g,w),32&w&&$(m,1)):(m=Jn(g),m.c(),$(m,1),m.m(i,null)):m&&(N(),p(m,1,1,()=>{m=null}),H()),(!l||4&w)&&V(e,"c-remote-agent-error--unrecoverable",g[2])},i(g){l||($(d),$(m),l=!0)},o(g){p(d),p(m),l=!1},d(g){g&&h(e),u&&u.d(),d&&d.d(),m&&m.d()}}}function Vr(o,e,t){let n,s,r,c,i,a,l,u,d,{error:m}=e,{onRetry:g}=e,{onDelete:w}=e;function L(){d&&(d(),d=void 0),c&&(d=is(c,f=>{t(3,u=f)}))}return fe(L),Wn(()=>{d==null||d()}),o.$$set=f=>{"error"in f&&t(8,m=f.error),"onRetry"in f&&t(0,g=f.onRetry),"onDelete"in f&&t(1,w=f.onDelete)},o.$$.update=()=>{256&o.$$.dirty&&t(10,n="type"in m),1280&o.$$.dirty&&t(2,s=n&&m.type===Gn.agentFailed),256&o.$$.dirty&&t(7,r=m.errorMessage),1280&o.$$.dirty&&t(9,c=n?void 0:m.retryAt),512&o.$$.dirty&&t(6,i=c!==void 0),6&o.$$.dirty&&t(5,a=s&&w),1&o.$$.dirty&&t(4,l=g),512&o.$$.dirty&&c&&L()},[g,w,s,u,l,a,i,r,m,c,n,f=>{f.preventDefault(),g==null||g()},f=>{f.preventDefault(),w==null||w()}]}class Nc extends j{constructor(e){super(),Z(this,e,Vr,Ur,W,{error:8,onRetry:0,onDelete:1})}}export{zc as A,Sc as C,Rc as E,Js as G,Tc as M,Pc as P,Nc as R,Ac as S,Es as U,bc as a,Bc as b,Ic as c,Ec as d,Mc as e,Lc as f,xc as g,yc as h,no as i,Fc as j,qc as k,un as l,_c as m,Cc as n,kc as t};
