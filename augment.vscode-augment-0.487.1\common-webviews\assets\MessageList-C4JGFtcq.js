import{S as Me,i as Se,s as Ce,W as P,J as N,c as H,Y as z,e as _,f as G,a9 as V,a8 as re,u as c,q,t as g,r as E,ad as de,h as I,L as Le,aa as ke,ag as oe,ah as O,a6 as xe,E as L,F as k,I as x,n as A,B as Q,C as Re,A as B}from"./SpinnerAugment-BJAAUt-n.js";import{e as U,u as _e,o as Ie}from"./BaseButton-7bccWxEO.js";import{t as ve,a as qe,g as Ee,A as De,M as Ae,R as he,b as be,c as Fe,S as Ne,d as He,G as Be,P as We,e as je,f as Te,C as ze,h as Ge,U as Pe,i as we,E as Ue,j as Je,k as Ke}from"./RemoteAgentRetry-Bi74U8IF.js";import"./Content-BldOFwN2.js";import{S as X,i as se,a as Ye,b as Oe,c as Qe,d as Ve,e as Xe,f as Ze,g as et,h as tt,j as nt,k as rt,E as ot}from"./arrow-up-right-from-square-CdEOBPRR.js";import"./folder-BB1rR2Vr.js";import{R as st}from"./check-DWGOhZOn.js";import"./isObjectLike-B3cfrJ3d.js";import{S as at}from"./main-panel-Bd7m3n0i.js";import{aq as lt,ar as it}from"./AugmentMessage-B1RETs6x.js";import"./types-DDm27S8B.js";import"./MaterialIcon-DkFwt_X2.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./index-Bb_d2FL8.js";import"./Keybindings-C-5u2652.js";import"./CalloutAugment-Bc8HnLJ3.js";import"./exclamation-triangle-BN7hPNzx.js";import"./CardAugment-qFWs8J9b.js";import"./TextTooltipAugment-DGOJQXY9.js";import"./IconButtonAugment-CqdkuyT6.js";import"./index-BAb5fkIe.js";import"./pen-to-square-SVW9AP0k.js";import"./augment-logo-f4Y8aL0S.js";import"./ButtonAugment-CLDnX_Hg.js";import"./folder-opened-BWTQdsic.js";import"./expand-CRxF6TiY.js";import"./diff-utils-RpWUB_Gw.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C9A1ZQNk.js";import"./layer-group-CeTgJtYd.js";import"./CollapseButtonAugment-BPhovcAJ.js";import"./github-DDehkTJf.js";import"./index-CGH5qOQn.js";import"./utils-BW_yYq2f.js";import"./chat-types-D7sox8tw.js";import"./globals-D0QH3NT1.js";import"./types-xGAhb6Qr.js";import"./file-paths-BcSg4gks.js";import"./types-CGlLNakm.js";import"./TextAreaAugment-CfENnz8O.js";import"./ra-diff-ops-model-CtxygvlM.js";import"./design-system-init-D2yLRPnY.js";import"./StatusIndicator-DZ8RFlU-.js";import"./await_block-CklR1HoG.js";import"./Filespan-D19TbAnP.js";import"./ellipsis-DJS5pN6w.js";import"./terminal-CUUE2e2M.js";import"./VSCodeCodicon-CVJeB9dY.js";import"./chat-flags-model-t_MCj4l9.js";import"./CopyButton-OCXKxiUh.js";import"./magnifying-glass-4Ft8m82l.js";import"./IconFilePath-BhE4l2UK.js";import"./LanguageIcon-CARIV-0P.js";import"./next-edit-types-904A5ehg.js";import"./chevron-down-CxktpQeS.js";import"./mcp-logo-CMUqUebQ.js";function ae(o,t,n){const e=o.slice();e[39]=t[n],e[42]=n;const r=e[42]+1===e[12].length;return e[40]=r,e}function le(o,t,n){const e=o.slice();e[43]=t[n].turn,e[44]=t[n].idx;const r=e[44]+1===e[13].length;return e[45]=r,e}function ie(o){let t,n;return t=new De({}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function mt(o){let t,n,e,r;const s=[wt,ht],p=[];function h(i,l){return i[17].enableRichCheckpointInfo?0:1}return t=h(o),n=p[t]=s[t](o),{c(){n.c(),e=B()},m(i,l){p[t].m(i,l),_(i,e,l),r=!0},p(i,l){let a=t;t=h(i),t===a?p[t].p(i,l):(q(),g(p[a],1,1,()=>{p[a]=null}),E(),n=p[t],n?n.p(i,l):(n=p[t]=s[t](i),n.c()),c(n,1),n.m(e.parentNode,e))},i(i){r||(c(n),r=!0)},o(i){g(n),r=!1},d(i){i&&I(e),p[t].d(i)}}}function ut(o){let t,n;return t=new ze({props:{group:o[39],chatModel:o[1],turn:o[43],turnIndex:o[44],isLastTurn:o[45],messageListContainer:o[0]}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.group=e[39]),2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.turn=e[43]),4096&r[0]&&(s.turnIndex=e[44]),12288&r[0]&&(s.isLastTurn=e[45]),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ct(o){let t,n;return t=new Ge({props:{stage:o[43].stage,iterationId:o[43].iterationId,stageCount:o[43].stageCount}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.stage=e[43].stage),4096&r[0]&&(s.iterationId=e[43].iterationId),4096&r[0]&&(s.stageCount=e[43].stageCount),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function $t(o){let t,n;return t=new Pe({props:{chatModel:o[1],msg:o[43].response_text??""}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.msg=e[43].response_text??""),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function pt(o){let t,n;return t=new lt({props:{group:o[39],markdown:o[43].response_text??"",messageListContainer:o[0]}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.group=e[39]),4096&r[0]&&(s.markdown=e[43].response_text??""),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function gt(o){let t,n;function e(){return o[30](o[43])}return t=new we({props:{turn:o[43],preamble:at,resendTurn:e,$$slots:{default:[yt]},$$scope:{ctx:o}}}),{c(){L(t.$$.fragment)},m(r,s){k(t,r,s),n=!0},p(r,s){o=r;const p={};4096&s[0]&&(p.turn=o[43]),4100&s[0]&&(p.resendTurn=e),69632&s[0]|131072&s[1]&&(p.$$scope={dirty:s,ctx:o}),t.$set(p)},i(r){n||(c(t.$$.fragment,r),n=!0)},o(r){g(t.$$.fragment,r),n=!1},d(r){x(t,r)}}}function ft(o){let t,n;return t=new Ue({props:{flagsModel:o[14],turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};16384&r[0]&&(s.flagsModel=e[14]),4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function dt(o){let t,n;return t=new we({props:{turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ht(o){let t,n;return t=new Je({props:{turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function wt(o){let t,n;return t=new Ke({props:{turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function yt(o){let t,n;return t=new it({props:{conversationModel:o[16],turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};65536&r[0]&&(s.conversationModel=e[16]),4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function me(o){let t,n,e,r;function s(){return o[31](o[43])}return{c(){t=P("div"),H(t,"class","c-msg-list__turn-seen")},m(p,h){_(p,t,h),e||(r=V(n=Fe.call(null,t,{onSeen:s,track:o[43].seen_state!==X.seen})),e=!0)},p(p,h){o=p,n&&de(n.update)&&4096&h[0]&&n.update.call(null,{onSeen:s,track:o[43].seen_state!==X.seen})},d(p){p&&I(t),e=!1,r()}}}function ue(o,t){let n,e,r,s,p,h,i,l,a,m,u,v,w,M,$=se(t[43]);const S=[dt,ft,gt,pt,$t,ct,ut,mt],f=[];function D(y,C){return 4096&C[0]&&(e=null),4096&C[0]&&(r=null),4096&C[0]&&(s=null),4096&C[0]&&(p=null),4096&C[0]&&(h=null),4096&C[0]&&(i=null),4096&C[0]&&(l=null),4096&C[0]&&(a=null),e==null&&(e=!!Ye(y[43])),e?0:(r==null&&(r=!!Oe(y[43])),r?1:(s==null&&(s=!!Qe(y[43])),s?2:(p==null&&(p=!!Ve(y[43])),p?3:(h==null&&(h=!!Xe(y[43])),h?4:(i==null&&(i=!!Ze(y[43])),i?5:(l==null&&(l=!!(et(y[43])||tt(y[43])||nt(y[43]))),l?6:(a==null&&(a=!(!rt(y[43])||y[43].status!==ot.success)),a?7:-1)))))))}~(m=D(t,[-1,-1]))&&(u=f[m]=S[m](t));let R=$&&me(t);return{key:o,first:null,c(){n=B(),u&&u.c(),v=N(),R&&R.c(),w=B(),this.first=n},m(y,C){_(y,n,C),~m&&f[m].m(y,C),_(y,v,C),R&&R.m(y,C),_(y,w,C),M=!0},p(y,C){let b=m;m=D(t=y,C),m===b?~m&&f[m].p(t,C):(u&&(q(),g(f[b],1,1,()=>{f[b]=null}),E()),~m?(u=f[m],u?u.p(t,C):(u=f[m]=S[m](t),u.c()),c(u,1),u.m(v.parentNode,v)):u=null),4096&C[0]&&($=se(t[43])),$?R?R.p(t,C):(R=me(t),R.c(),R.m(w.parentNode,w)):R&&(R.d(1),R=null)},i(y){M||(c(u),M=!0)},o(y){g(u),M=!1},d(y){y&&(I(n),I(v),I(w)),~m&&f[m].d(y),R&&R.d(y)}}}function ce(o){let t,n,e,r,s;const p=[Rt,xt,kt,Lt,Ct,St,Mt],h=[];function i(a,m){return a[9]?0:a[6].retryMessage?1:a[6].showResumingRemoteAgent?2:a[6].showPaused?3:a[6].showGeneratingResponse?4:a[6].showAwaitingUserInput?5:a[6].showStopped?6:-1}~(t=i(o))&&(n=h[t]=p[t](o));let l=o[6].showRunningSpacer&&$e();return{c(){n&&n.c(),e=N(),l&&l.c(),r=B()},m(a,m){~t&&h[t].m(a,m),_(a,e,m),l&&l.m(a,m),_(a,r,m),s=!0},p(a,m){let u=t;t=i(a),t===u?~t&&h[t].p(a,m):(n&&(q(),g(h[u],1,1,()=>{h[u]=null}),E()),~t?(n=h[t],n?n.p(a,m):(n=h[t]=p[t](a),n.c()),c(n,1),n.m(e.parentNode,e)):n=null),a[6].showRunningSpacer?l||(l=$e(),l.c(),l.m(r.parentNode,r)):l&&(l.d(1),l=null)},i(a){s||(c(n),s=!0)},o(a){g(n),s=!1},d(a){a&&(I(e),I(r)),~t&&h[t].d(a),l&&l.d(a)}}}function Mt(o){let t,n;return t=new Ne({}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p:A,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function St(o){let t,n;return t=new He({}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p:A,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Ct(o){let t,n;return t=new Be({}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p:A,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Lt(o){let t,n;return t=new We({}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p:A,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function kt(o){let t,n;return t=new je({}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p:A,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function xt(o){let t,n;return t=new Te({props:{message:o[6].retryMessage}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};64&r[0]&&(s.message=e[6].retryMessage),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function Rt(o){let t,n;return t=new he({props:{error:o[9].error,onRetry:o[9].onRetry,onDelete:o[9].onDelete}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};512&r[0]&&(s.error=e[9].error),512&r[0]&&(s.onRetry=e[9].onRetry),512&r[0]&&(s.onDelete=e[9].onDelete),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function $e(o){let t;return{c(){t=P("div"),H(t,"class","c-agent-running-spacer svelte-t9khzq")},m(n,e){_(n,t,e)},d(n){n&&I(t)}}}function _t(o){let t,n,e,r=[],s=new Map,p=U(o[39]);const h=l=>l[43].request_id??`no-request-id-${l[44]}`;for(let l=0;l<p.length;l+=1){let a=le(o,p,l),m=h(a);s.set(m,r[l]=ue(m,a))}let i=o[40]&&ce(o);return{c(){for(let l=0;l<r.length;l+=1)r[l].c();t=N(),i&&i.c(),n=B()},m(l,a){for(let m=0;m<r.length;m+=1)r[m]&&r[m].m(l,a);_(l,t,a),i&&i.m(l,a),_(l,n,a),e=!0},p(l,a){17002503&a[0]&&(p=U(l[39]),q(),r=_e(r,a,h,1,l,p,s,t.parentNode,Ie,ue,t,le),E()),l[40]?i?(i.p(l,a),4096&a[0]&&c(i,1)):(i=ce(l),i.c(),c(i,1),i.m(n.parentNode,n)):i&&(q(),g(i,1,1,()=>{i=null}),E())},i(l){if(!e){for(let a=0;a<p.length;a+=1)c(r[a]);c(i),e=!0}},o(l){for(let a=0;a<r.length;a+=1)g(r[a]);g(i),e=!1},d(l){l&&(I(t),I(n));for(let a=0;a<r.length;a+=1)r[a].d(l);i&&i.d(l)}}}function pe(o){let t,n;return t=new Ae({props:{class:"c-msg-list__item--grouped",chatModel:o[1],isLastItem:o[40],userControlsScroll:o[3],requestId:o[39][0].turn.request_id,releaseScroll:o[32],messageListContainer:o[0],minHeight:o[40]?o[8]:0,$$slots:{default:[_t]},$$scope:{ctx:o}}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.isLastItem=e[40]),8&r[0]&&(s.userControlsScroll=e[3]),4096&r[0]&&(s.requestId=e[39][0].turn.request_id),8&r[0]&&(s.releaseScroll=e[32]),1&r[0]&&(s.messageListContainer=e[0]),4352&r[0]&&(s.minHeight=e[40]?e[8]:0),225863&r[0]|131072&r[1]&&(s.$$scope={dirty:r,ctx:e}),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function ge(o){let t,n;return t=new he({props:{error:o[9].error,onRetry:o[9].onRetry,onDelete:o[9].onDelete}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};512&r[0]&&(s.error=e[9].error),512&r[0]&&(s.onRetry=e[9].onRetry),512&r[0]&&(s.onDelete=e[9].onDelete),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function fe(o){let t,n;return t=new be({props:{messageListElement:o[0],showScrollDown:o[7]}}),{c(){L(t.$$.fragment)},m(e,r){k(t,e,r),n=!0},p(e,r){const s={};1&r[0]&&(s.messageListElement=e[0]),128&r[0]&&(s.showScrollDown=e[7]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){x(t,e)}}}function It(o){let t,n,e,r,s,p,h,i,l,a=o[10]&&ie(),m=U(o[12]),u=[];for(let $=0;$<m.length;$+=1)u[$]=pe(ae(o,m,$));const v=$=>g(u[$],1,1,()=>{u[$]=null});let w=!o[13].length&&o[9]&&ge(o),M=o[11]&&fe(o);return{c(){t=P("div"),n=P("div"),a&&a.c(),e=N();for(let $=0;$<u.length;$+=1)u[$].c();r=N(),w&&w.c(),p=N(),M&&M.c(),H(n,"class","c-msg-list svelte-t9khzq"),z(n,"c-msg-list--minimal",!o[17].fullFeatured),H(t,"class","c-msg-list-container svelte-t9khzq"),H(t,"data-testid","chat-message-list"),z(t,"c-msg-list--minimal",!o[17].fullFeatured)},m($,S){_($,t,S),G(t,n),a&&a.m(n,null),G(n,e);for(let f=0;f<u.length;f+=1)u[f]&&u[f].m(n,null);G(n,r),w&&w.m(n,null),o[33](n),G(t,p),M&&M.m(t,null),h=!0,i||(l=[V(ve.call(null,n,{onScrollIntoBottom:o[21],onScrollAwayFromBottom:o[22],onScroll:o[34]})),V(s=qe.call(null,n,{onHeightChange:o[35]})),re(t,"mouseenter",o[36]),re(t,"mouseleave",o[37])],i=!0)},p($,S){if($[10]?a?1024&S[0]&&c(a,1):(a=ie(),a.c(),c(a,1),a.m(n,e)):a&&(q(),g(a,1,1,()=>{a=null}),E()),17003343&S[0]){let f;for(m=U($[12]),f=0;f<m.length;f+=1){const D=ae($,m,f);u[f]?(u[f].p(D,S),c(u[f],1)):(u[f]=pe(D),u[f].c(),c(u[f],1),u[f].m(n,r))}for(q(),f=m.length;f<u.length;f+=1)v(f);E()}!$[13].length&&$[9]?w?(w.p($,S),8704&S[0]&&c(w,1)):(w=ge($),w.c(),c(w,1),w.m(n,null)):w&&(q(),g(w,1,1,()=>{w=null}),E()),s&&de(s.update)&&32&S[0]&&s.update.call(null,{onHeightChange:$[35]}),(!h||131072&S[0])&&z(n,"c-msg-list--minimal",!$[17].fullFeatured),$[11]?M?(M.p($,S),2048&S[0]&&c(M,1)):(M=fe($),M.c(),c(M,1),M.m(t,null)):M&&(q(),g(M,1,1,()=>{M=null}),E()),(!h||131072&S[0])&&z(t,"c-msg-list--minimal",!$[17].fullFeatured)},i($){if(!h){c(a);for(let S=0;S<m.length;S+=1)c(u[S]);c(w),c(M),h=!0}},o($){g(a),u=u.filter(Boolean);for(let S=0;S<u.length;S+=1)g(u[S]);g(w),g(M),h=!1},d($){$&&I(t),a&&a.d(),Le(u,$),w&&w.d(),o[33](null),M&&M.d(),i=!1,ke(l)}}}function vt(o,t,n){let e,r,s,p,h,i,l,a,m,u,v,w,M,$,S,f,D,R=A,y=A,C=()=>(y(),y=Q(W,d=>n(29,f=d)),W),b=A;o.$$.on_destroy.push(()=>R()),o.$$.on_destroy.push(()=>y()),o.$$.on_destroy.push(()=>b());let{chatModel:W}=t;C();let{onboardingWorkspaceModel:J}=t,{msgListElement:j}=t;const ye=oe("agentConversationModel"),{agentExchangeStatus:Z,isCurrConversationAgentic:ee}=ye;O(o,Z,d=>n(28,S=d)),O(o,ee,d=>n(27,$=d));const te=oe(st.key);O(o,te,d=>n(26,M=d));let F=!1,T=!1;function K(){n(3,F=!0)}xe(()=>{var d;((d=w.lastExchange)==null?void 0:d.seen_state)===X.unseen&&K()});let Y=0;const ne=d=>w.markSeen(d);return o.$$set=d=>{"chatModel"in d&&C(n(1,W=d.chatModel)),"onboardingWorkspaceModel"in d&&n(2,J=d.onboardingWorkspaceModel),"msgListElement"in d&&n(0,j=d.msgListElement)},o.$$.update=()=>{536870912&o.$$.dirty[0]&&(n(15,e=f.currentConversationModel),R(),R=Q(e,d=>n(16,w=d))),536870912&o.$$.dirty[0]&&(n(14,r=f.flags),b(),b=Q(r,d=>n(17,D=d))),1006632960&o.$$.dirty[0]&&n(25,s=Ee(f,S,$,M)),33554432&o.$$.dirty[0]&&n(13,p=s.chatHistory),33554432&o.$$.dirty[0]&&n(12,h=s.groupedChatHistory),33554432&o.$$.dirty[0]&&n(6,i=s.lastGroupConfig),33554432&o.$$.dirty[0]&&n(11,l=s.doShowFloatingButtons),33554432&o.$$.dirty[0]&&n(10,a=s.doShowAgentSetupLogs),64&o.$$.dirty[0]&&n(9,m=i.remoteAgentErrorConfig),32&o.$$.dirty[0]&&n(8,u=Y),24&o.$$.dirty[0]&&n(7,v=F&&T)},[j,W,J,F,T,Y,i,v,u,m,a,l,h,p,r,e,w,D,Z,ee,te,function(){n(3,F=!1)},function(){n(3,F=!0)},K,ne,s,M,$,S,f,d=>J.retryProjectSummary(d),d=>ne(d),()=>n(3,F=!0),function(d){Re[d?"unshift":"push"](()=>{j=d,n(0,j)})},d=>{d<=1&&K()},d=>n(5,Y=d),()=>n(4,T=!0),()=>n(4,T=!1)]}class Fn extends Me{constructor(t){super(),Se(this,t,vt,It,Ce,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{Fn as default};
