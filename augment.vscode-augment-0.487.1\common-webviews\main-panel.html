<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment</title>
    <script nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <script type="module" crossorigin src="./assets/main-panel-Bd7m3n0i.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BJAAUt-n.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-7bccWxEO.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-CqdkuyT6.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/layer-group-CeTgJtYd.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-D2yLRPnY.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/Content-BldOFwN2.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-DGOJQXY9.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-D7sox8tw.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/types-xGAhb6Qr.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/arrow-up-right-from-square-CdEOBPRR.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/github-DDehkTJf.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-BWTQdsic.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/types-DDm27S8B.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/index-Bb_d2FL8.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/utils-BW_yYq2f.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-CtxygvlM.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/check-DWGOhZOn.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-qFWs8J9b.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/index-BAb5fkIe.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-B3cfrJ3d.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-CfENnz8O.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/index-C9A1ZQNk.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-RpWUB_Gw.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/folder-BB1rR2Vr.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/index-CGH5qOQn.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-DZ8RFlU-.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-Bc8HnLJ3.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-CLDnX_Hg.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-BN7hPNzx.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-SVW9AP0k.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-f4Y8aL0S.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-C-5u2652.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-CklR1HoG.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-D19TbAnP.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-DJS5pN6w.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-DkFwt_X2.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-CUUE2e2M.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-BPhovcAJ.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-CVJeB9dY.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/autofix-state-d-ymFdyn.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="modulepreload" crossorigin href="./assets/chat-flags-model-t_MCj4l9.js" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-CA6XnfI-.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/layer-group-Df_FYENN.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-BtHyc30H.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-B2NZuaj3.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-CXnRMJBa.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/Content-LuLOeTld.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/index-CLSyEK0S.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-DTcQ2vsq.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/folder-CQqSbZRe.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-D-yOSWp9.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-Dvw-pMXw.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings-BFFBoxX3.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-tclW2Ian.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-CLLTFP8m.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
    <link rel="stylesheet" crossorigin href="./assets/main-panel-DO-lyyxX.css" nonce="nonce-CseJsOixUxdy/WxI0Qh4pw==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
