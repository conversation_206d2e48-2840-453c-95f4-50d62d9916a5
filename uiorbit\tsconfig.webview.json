{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "node", "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": false, "noEmit": false, "declaration": false, "outDir": "./dist", "resolveJsonModule": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true}, "include": ["src/webview/react/**/*"], "exclude": ["node_modules", "dist", "**/*.test.*"]}