import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from '../utils/Logger';
import { FileOperationsService } from './FileOperationsService';
import { FrontendContextEngine } from './FrontendContextEngine';

/**
 * Real-time Preview Service
 * Provides live component rendering, hot reload, and interactive playground
 */
export class PreviewService {
  private previewPanel: vscode.WebviewPanel | null = null;
  private previewServer: PreviewServer | null = null;
  private watchers: Map<string, vscode.FileSystemWatcher> = new Map();
  private currentPreviewFile: string | null = null;

  constructor(
    private fileOpsService: FileOperationsService,
    private contextEngine: FrontendContextEngine,
    private context: vscode.ExtensionContext
  ) {}

  /**
   * Start real-time preview for a component or project
   */
  async startPreview(filePath?: string): Promise<void> {
    Logger.info('Starting real-time preview...');

    try {
      // Determine what to preview
      const targetFile = filePath || await this.getActiveFile();
      if (!targetFile) {
        vscode.window.showErrorMessage('No file selected for preview');
        return;
      }

      this.currentPreviewFile = targetFile;

      // Create or show preview panel
      await this.createPreviewPanel();

      // Start preview server
      await this.startPreviewServer(targetFile);

      // Setup file watchers for hot reload
      await this.setupFileWatchers(targetFile);

      // Initial render
      await this.renderPreview(targetFile);

      Logger.info(`Preview started for: ${targetFile}`);

    } catch (error) {
      Logger.error('Failed to start preview:', error);
      vscode.window.showErrorMessage('Failed to start preview');
    }
  }

  /**
   * Stop the preview and cleanup resources
   */
  async stopPreview(): Promise<void> {
    Logger.info('Stopping preview...');

    // Close preview panel
    if (this.previewPanel) {
      this.previewPanel.dispose();
      this.previewPanel = null;
    }

    // Stop preview server
    if (this.previewServer) {
      await this.previewServer.stop();
      this.previewServer = null;
    }

    // Cleanup file watchers
    this.watchers.forEach(watcher => watcher.dispose());
    this.watchers.clear();

    this.currentPreviewFile = null;
  }

  /**
   * Update preview with new content
   */
  async updatePreview(filePath: string): Promise<void> {
    if (!this.previewPanel || !this.previewServer) {
      return;
    }

    try {
      await this.renderPreview(filePath);
      Logger.debug(`Preview updated for: ${filePath}`);
    } catch (error) {
      Logger.error('Failed to update preview:', error);
    }
  }

  /**
   * Create the preview webview panel
   */
  private async createPreviewPanel(): Promise<void> {
    if (this.previewPanel) {
      this.previewPanel.reveal();
      return;
    }

    this.previewPanel = vscode.window.createWebviewPanel(
      'uiorbit-preview',
      'UIOrbit Preview',
      vscode.ViewColumn.Beside,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context.extensionPath, 'media')),
          vscode.Uri.file(path.join(this.context.extensionPath, 'out'))
        ]
      }
    );

    // Handle panel disposal
    this.previewPanel.onDidDispose(() => {
      this.previewPanel = null;
    });

    // Handle messages from webview
    this.previewPanel.webview.onDidReceiveMessage(async (message) => {
      await this.handlePreviewMessage(message);
    });

    // Set initial HTML
    this.previewPanel.webview.html = this.getPreviewHTML();
  }

  /**
   * Start the preview server for hot reload
   */
  private async startPreviewServer(filePath: string): Promise<void> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      throw new Error('No workspace folder found');
    }

    this.previewServer = new PreviewServer(workspaceFolder.uri.fsPath);
    await this.previewServer.start();

    Logger.info(`Preview server started on port ${this.previewServer.port}`);
  }

  /**
   * Setup file watchers for hot reload
   */
  private async setupFileWatchers(filePath: string): Promise<void> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      return;
    }

    // Watch the specific file
    const fileWatcher = vscode.workspace.createFileSystemWatcher(filePath);
    fileWatcher.onDidChange(() => this.updatePreview(filePath));
    this.watchers.set(filePath, fileWatcher);

    // Watch related files (imports, styles, etc.)
    const relatedFiles = await this.findRelatedFiles(filePath);
    for (const relatedFile of relatedFiles) {
      const watcher = vscode.workspace.createFileSystemWatcher(relatedFile);
      watcher.onDidChange(() => this.updatePreview(filePath));
      this.watchers.set(relatedFile, watcher);
    }

    Logger.debug(`Setup watchers for ${this.watchers.size} files`);
  }

  /**
   * Render the preview content
   */
  private async renderPreview(filePath: string): Promise<void> {
    if (!this.previewPanel || !this.previewServer) {
      return;
    }

    try {
      // Analyze the file to determine how to preview it
      const previewData = await this.analyzeFileForPreview(filePath);

      // Generate preview content
      const previewContent = await this.generatePreviewContent(previewData);

      // Send to webview
      this.previewPanel.webview.postMessage({
        type: 'update-preview',
        content: previewContent,
        metadata: previewData
      });

    } catch (error) {
      Logger.error('Failed to render preview:', error);
      
      // Show error in preview
      this.previewPanel.webview.postMessage({
        type: 'preview-error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Analyze file to determine preview strategy
   */
  private async analyzeFileForPreview(filePath: string): Promise<PreviewData> {
    const content = await fs.readFile(filePath, 'utf-8');
    const ext = path.extname(filePath);
    const fileName = path.basename(filePath, ext);

    // Determine file type and framework
    const fileType = this.determineFileType(ext, content);
    const framework = this.detectFramework(content);

    return {
      filePath,
      fileName,
      fileType,
      framework,
      content,
      isComponent: this.isComponentFile(content, framework),
      hasStyles: this.hasStyles(content),
      hasAnimations: this.hasAnimations(content),
      dependencies: this.extractDependencies(content)
    };
  }

  /**
   * Generate preview content based on file analysis
   */
  private async generatePreviewContent(data: PreviewData): Promise<string> {
    if (data.isComponent) {
      return await this.generateComponentPreview(data);
    } else if (data.fileType === 'style') {
      return await this.generateStylePreview(data);
    } else {
      return await this.generateGenericPreview(data);
    }
  }

  /**
   * Generate component preview with interactive playground
   */
  private async generateComponentPreview(data: PreviewData): Promise<string> {
    const template = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.fileName} Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        .preview-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .preview-controls {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .responsive-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .device-button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .device-button.active {
            background: #007acc;
            color: white;
        }
        .component-frame {
            border: 1px solid #ddd;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .desktop { width: 100%; height: 600px; }
        .tablet { width: 768px; height: 600px; margin: 0 auto; }
        .mobile { width: 375px; height: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="preview-controls">
        <h3>📱 Responsive Preview</h3>
        <div class="responsive-controls">
            <button class="device-button active" onclick="setDevice('desktop')">🖥️ Desktop</button>
            <button class="device-button" onclick="setDevice('tablet')">📱 Tablet</button>
            <button class="device-button" onclick="setDevice('mobile')">📱 Mobile</button>
        </div>
    </div>
    
    <div class="preview-container">
        <iframe 
            id="component-frame" 
            class="component-frame desktop"
            src="http://localhost:${this.previewServer?.port || 3000}/preview/${data.fileName}"
            frameborder="0">
        </iframe>
    </div>

    <script>
        function setDevice(device) {
            const frame = document.getElementById('component-frame');
            const buttons = document.querySelectorAll('.device-button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            frame.className = 'component-frame ' + device;
        }

        // Hot reload functionality
        const eventSource = new EventSource('http://localhost:${this.previewServer?.port || 3000}/events');
        eventSource.onmessage = function(event) {
            if (event.data === 'reload') {
                document.getElementById('component-frame').src += '';
            }
        };
    </script>
</body>
</html>`;

    return template;
  }

  /**
   * Generate style preview
   */
  private async generateStylePreview(data: PreviewData): Promise<string> {
    return `
<div class="style-preview">
    <h3>🎨 Style Preview: ${data.fileName}</h3>
    <div class="style-content">
        <pre><code>${data.content}</code></pre>
    </div>
</div>`;
  }

  /**
   * Generate generic file preview
   */
  private async generateGenericPreview(data: PreviewData): Promise<string> {
    return `
<div class="generic-preview">
    <h3>📄 File Preview: ${data.fileName}</h3>
    <div class="file-content">
        <pre><code>${data.content}</code></pre>
    </div>
</div>`;
  }

  // Helper methods...
  private async getActiveFile(): Promise<string | null> {
    const activeEditor = vscode.window.activeTextEditor;
    return activeEditor?.document.uri.fsPath || null;
  }

  private determineFileType(ext: string, content: string): FileType {
    if (['.tsx', '.jsx', '.vue', '.svelte'].includes(ext)) return 'component';
    if (['.css', '.scss', '.sass', '.less'].includes(ext)) return 'style';
    if (['.ts', '.js'].includes(ext)) return 'script';
    return 'other';
  }

  private detectFramework(content: string): string {
    if (content.includes('import React') || content.includes('from \'react\'')) return 'react';
    if (content.includes('<template>') || content.includes('vue')) return 'vue';
    if (content.includes('@Component') || content.includes('angular')) return 'angular';
    if (content.includes('<script>') && content.includes('svelte')) return 'svelte';
    return 'vanilla';
  }

  private isComponentFile(content: string, framework: string): boolean {
    const componentPatterns = [
      /export\s+default\s+function/,
      /export\s+const\s+\w+\s*=\s*\(/,
      /class\s+\w+\s+extends\s+Component/,
      /<template>/,
      /@Component/
    ];
    return componentPatterns.some(pattern => pattern.test(content));
  }

  private hasStyles(content: string): boolean {
    return content.includes('styled') || content.includes('className') || content.includes('style=');
  }

  private hasAnimations(content: string): boolean {
    return content.includes('gsap') || content.includes('framer-motion') || content.includes('animation');
  }

  private extractDependencies(content: string): string[] {
    const importRegex = /import.*from\s+['"]([^'"]+)['"]/g;
    const dependencies: string[] = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }
    
    return dependencies;
  }

  private async findRelatedFiles(filePath: string): Promise<string[]> {
    // TODO: Implement logic to find related files (imports, styles, etc.)
    return [];
  }

  private async handlePreviewMessage(message: any): Promise<void> {
    switch (message.type) {
      case 'refresh':
        if (this.currentPreviewFile) {
          await this.updatePreview(this.currentPreviewFile);
        }
        break;
      case 'change-device':
        // Handle device change
        break;
    }
  }

  private getPreviewHTML(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIOrbit Preview</title>
    <style>
        body { margin: 0; padding: 20px; font-family: system-ui; }
        .loading { text-align: center; padding: 50px; }
    </style>
</head>
<body>
    <div class="loading">
        <h2>🚀 UIOrbit Preview</h2>
        <p>Initializing preview...</p>
    </div>
    <script>
        window.addEventListener('message', event => {
            const message = event.data;
            if (message.type === 'update-preview') {
                document.body.innerHTML = message.content;
            }
        });
    </script>
</body>
</html>`;
  }
}

// Types and interfaces
interface PreviewData {
  filePath: string;
  fileName: string;
  fileType: FileType;
  framework: string;
  content: string;
  isComponent: boolean;
  hasStyles: boolean;
  hasAnimations: boolean;
  dependencies: string[];
}

type FileType = 'component' | 'style' | 'script' | 'other';

class PreviewServer {
  public port: number = 3000;
  
  constructor(private workspacePath: string) {}
  
  async start(): Promise<void> {
    // TODO: Implement preview server
    Logger.info('Preview server started (mock)');
  }
  
  async stop(): Promise<void> {
    // TODO: Implement server cleanup
    Logger.info('Preview server stopped (mock)');
  }
}
