import{S as V,i as W,s as X,A as Y,e as x,q as Z,t as $,r as D,u as p,h as B,Z as y,a as h,j as E,V as F,ab as f,$ as w,W as j,X as m,Y as g,a0 as A,a1 as S,a2 as q,g as N,a8 as v,aa as G}from"./SpinnerAugment-BJAAUt-n.js";import"./BaseButton-7bccWxEO.js";function H(a){let n,e;const u=a[9].default,t=w(u,a,a[8],null);let r=[a[1]],c={};for(let i=0;i<r.length;i+=1)c=h(c,r[i]);return{c(){n=j("div"),t&&t.c(),m(n,c),g(n,"svelte-149ttoo",!0)},m(i,l){x(i,n,l),t&&t.m(n,null),e=!0},p(i,l){t&&t.p&&(!e||256&l)&&A(t,u,i,i[8],e?q(u,i[8],l,null):S(i[8]),null),m(n,c=N(r,[2&l&&i[1]])),g(n,"svelte-149ttoo",!0)},i(i){e||(p(t,i),e=!0)},o(i){$(t,i),e=!1},d(i){i&&B(n),t&&t.d(i)}}}function I(a){let n,e,u,t;const r=a[9].default,c=w(r,a,a[8],null);let i=[a[1],{role:"button"},{tabindex:"0"}],l={};for(let o=0;o<i.length;o+=1)l=h(l,i[o]);return{c(){n=j("div"),c&&c.c(),m(n,l),g(n,"svelte-149ttoo",!0)},m(o,d){x(o,n,d),c&&c.m(n,null),e=!0,u||(t=[v(n,"click",a[10]),v(n,"keyup",a[11]),v(n,"keydown",a[12]),v(n,"mousedown",a[13]),v(n,"mouseover",a[14]),v(n,"focus",a[15]),v(n,"mouseleave",a[16]),v(n,"blur",a[17]),v(n,"contextmenu",a[18])],u=!0)},p(o,d){c&&c.p&&(!e||256&d)&&A(c,r,o,o[8],e?q(r,o[8],d,null):S(o[8]),null),m(n,l=N(i,[2&d&&o[1],{role:"button"},{tabindex:"0"}])),g(n,"svelte-149ttoo",!0)},i(o){e||(p(c,o),e=!0)},o(o){$(c,o),e=!1},d(o){o&&B(n),c&&c.d(o),u=!1,G(t)}}}function J(a){let n,e,u,t;const r=[I,H],c=[];function i(l,o){return l[0]?0:1}return n=i(a),e=c[n]=r[n](a),{c(){e.c(),u=Y()},m(l,o){c[n].m(l,o),x(l,u,o),t=!0},p(l,[o]){let d=n;n=i(l),n===d?c[n].p(l,o):(Z(),$(c[d],1,1,()=>{c[d]=null}),D(),e=c[n],e?e.p(l,o):(e=c[n]=r[n](l),e.c()),p(e,1),e.m(u.parentNode,u))},i(l){t||(p(e),t=!0)},o(l){$(e),t=!1},d(l){l&&B(u),c[n].d(l)}}}function K(a,n,e){let u,t,r;const c=["size","insetContent","variant","interactive","includeBackground"];let i=y(n,c),{$$slots:l={},$$scope:o}=n,{size:d=1}=n,{insetContent:k=!1}=n,{variant:b="surface"}=n,{interactive:C=!1}=n,{includeBackground:z=!0}=n;return a.$$set=s=>{n=h(h({},n),E(s)),e(19,i=y(n,c)),"size"in s&&e(2,d=s.size),"insetContent"in s&&e(3,k=s.insetContent),"variant"in s&&e(4,b=s.variant),"interactive"in s&&e(0,C=s.interactive),"includeBackground"in s&&e(5,z=s.includeBackground),"$$scope"in s&&e(8,o=s.$$scope)},a.$$.update=()=>{e(7,{class:u}=i,u),189&a.$$.dirty&&e(6,t=["c-card",`c-card--size-${d}`,`c-card--${b}`,k?"c-card--insetContent":"",C?"c-card--interactive":"",u,z?"c-card--with-background":""]),64&a.$$.dirty&&e(1,r={...F("accent"),class:t.join(" ")})},[C,r,d,k,b,z,t,u,o,l,function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)},function(s){f.call(this,a,s)}]}class O extends V{constructor(n){super(),W(this,n,K,J,X,{size:2,insetContent:3,variant:4,interactive:0,includeBackground:5})}}export{O as C};
