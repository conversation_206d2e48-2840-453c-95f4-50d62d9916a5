
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">72.67% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>258/355</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.68% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>49/88</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.25% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>61/80</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">72.57% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>254/350</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	</tr>

<tr>
	<td class="file high" data-value="src/core"><a href="src/core/index.html">src/core</a></td>
	<td data-value="94.39" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 94%"></div><div class="cover-empty" style="width: 6%"></div></div>
	</td>
	<td data-value="94.39" class="pct high">94.39%</td>
	<td data-value="107" class="abs high">101/107</td>
	<td data-value="81.81" class="pct high">81.81%</td>
	<td data-value="11" class="abs high">9/11</td>
	<td data-value="92.3" class="pct high">92.3%</td>
	<td data-value="26" class="abs high">24/26</td>
	<td data-value="94.33" class="pct high">94.33%</td>
	<td data-value="106" class="abs high">100/106</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="75.55" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.55" class="pct medium">75.55%</td>
	<td data-value="135" class="abs medium">102/135</td>
	<td data-value="62.22" class="pct medium">62.22%</td>
	<td data-value="45" class="abs medium">28/45</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="30" class="abs high">28/30</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="132" class="abs medium">99/132</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/utils"><a href="src/utils/index.html">src/utils</a></td>
	<td data-value="59.25" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 59%"></div><div class="cover-empty" style="width: 41%"></div></div>
	</td>
	<td data-value="59.25" class="pct medium">59.25%</td>
	<td data-value="54" class="abs medium">32/54</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="21" class="abs low">9/21</td>
	<td data-value="53.84" class="pct medium">53.84%</td>
	<td data-value="13" class="abs medium">7/13</td>
	<td data-value="59.25" class="pct medium">59.25%</td>
	<td data-value="54" class="abs medium">32/54</td>
	</tr>

<tr>
	<td class="file low" data-value="src/webview"><a href="src/webview/index.html">src/webview</a></td>
	<td data-value="7.69" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="39" class="abs low">3/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="7.89" class="pct low">7.89%</td>
	<td data-value="38" class="abs low">3/38</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-05T04:51:10.870Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    