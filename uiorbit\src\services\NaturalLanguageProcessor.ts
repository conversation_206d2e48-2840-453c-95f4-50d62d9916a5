import { Logger } from '../utils/Logger';

export interface UserIntent {
  action: 'generate' | 'modify' | 'explain' | 'debug' | 'optimize' | 'refactor' | 'analyze' | 'help';
  target: 'component' | 'function' | 'style' | 'test' | 'documentation' | 'project' | 'general';
  requirements: string[];
  context: string[];
  framework?: string;
  styling?: string;
  complexity?: 'simple' | 'medium' | 'complex';
  confidence: number;
}

export interface CodeContext {
  currentFile?: string;
  selectedText?: string;
  projectType?: string;
  framework?: string;
  styling?: string;
  dependencies?: string[];
  relatedFiles?: string[];
}

export interface ConversationContext {
  previousIntents: UserIntent[];
  projectContext: CodeContext;
  conversationHistory: string[];
  userPreferences: Record<string, any>;
}

/**
 * Natural Language Processor for UIOrbit
 * Handles intent recognition and context extraction from user messages
 */
export class NaturalLanguageProcessor {
  private actionKeywords = {
    generate: ['create', 'generate', 'build', 'make', 'add', 'new', 'write'],
    modify: ['update', 'change', 'edit', 'modify', 'alter', 'fix', 'improve'],
    explain: ['explain', 'describe', 'what', 'how', 'why', 'tell me about'],
    debug: ['debug', 'error', 'issue', 'problem', 'bug', 'not working', 'broken'],
    optimize: ['optimize', 'improve', 'faster', 'performance', 'speed up'],
    refactor: ['refactor', 'restructure', 'reorganize', 'clean up', 'simplify'],
    analyze: ['analyze', 'review', 'check', 'examine', 'look at'],
    help: ['help', 'assist', 'guide', 'tutorial', 'how to', 'show me']
  };

  private targetKeywords = {
    component: ['component', 'widget', 'element', 'ui', 'button', 'form', 'modal', 'card', 'navbar', 'header', 'footer'],
    function: ['function', 'method', 'logic', 'algorithm', 'utility', 'helper'],
    style: ['style', 'css', 'styling', 'design', 'theme', 'color', 'layout', 'responsive'],
    test: ['test', 'testing', 'spec', 'unit test', 'integration test', 'e2e'],
    documentation: ['docs', 'documentation', 'readme', 'comment', 'guide'],
    project: ['project', 'app', 'application', 'setup', 'structure', 'architecture'],
    general: ['general', 'overview', 'summary', 'introduction']
  };

  private frameworkKeywords = {
    react: ['react', 'jsx', 'tsx', 'hooks', 'useState', 'useEffect', 'component'],
    vue: ['vue', 'vuejs', 'composition api', 'reactive', 'ref', 'computed'],
    angular: ['angular', 'typescript', 'component', 'service', 'directive'],
    svelte: ['svelte', 'sveltekit', 'stores', 'reactive'],
    vanilla: ['vanilla', 'javascript', 'js', 'html', 'css']
  };

  private stylingKeywords = {
    tailwind: ['tailwind', 'tailwindcss', 'utility', 'classes'],
    css: ['css', 'stylesheet', 'styles'],
    scss: ['scss', 'sass', 'variables', 'mixins'],
    'styled-components': ['styled-components', 'css-in-js', 'styled'],
    emotion: ['emotion', '@emotion', 'css prop']
  };

  /**
   * Parse user intent from natural language message
   */
  async parseIntent(userMessage: string, context?: CodeContext): Promise<UserIntent> {
    try {
      Logger.debug('Parsing intent for message:', userMessage);

      const message = userMessage.toLowerCase();
      const words = message.split(/\s+/);

      // Detect action
      const action = this.detectAction(message, words);
      
      // Detect target
      const target = this.detectTarget(message, words);
      
      // Extract requirements
      const requirements = this.extractRequirements(userMessage);
      
      // Extract context
      const contextInfo = await this.extractCodeContext(message, words, context);
      
      // Detect framework and styling preferences
      const framework = this.detectFramework(message, words, context);
      const styling = this.detectStyling(message, words, context);
      
      // Determine complexity
      const complexity = this.determineComplexity(message, words);
      
      // Calculate confidence score
      const confidence = this.calculateConfidence(action, target, requirements);

      const intent: UserIntent = {
        action,
        target,
        requirements,
        context: contextInfo,
        framework,
        styling,
        complexity,
        confidence
      };

      Logger.info('Parsed intent:', intent);
      return intent;

    } catch (error) {
      Logger.error('Error parsing intent:', error);
      
      // Return default intent
      return {
        action: 'help',
        target: 'general',
        requirements: [userMessage],
        context: [],
        confidence: 0.5
      };
    }
  }

  /**
   * Extract code context from message
   */
  async extractCodeContext(message: string, words: string[], existingContext?: CodeContext): Promise<string[]> {
    const context: string[] = [];

    // Add existing context
    if (existingContext) {
      if (existingContext.currentFile) {
        context.push(`Current file: ${existingContext.currentFile}`);
      }
      if (existingContext.selectedText) {
        context.push(`Selected code: ${existingContext.selectedText.substring(0, 100)}...`);
      }
      if (existingContext.projectType) {
        context.push(`Project type: ${existingContext.projectType}`);
      }
    }

    // Extract file references
    const filePattern = /\b\w+\.(js|jsx|ts|tsx|vue|svelte|css|scss|html)\b/g;
    const fileMatches = message.match(filePattern);
    if (fileMatches) {
      context.push(`Referenced files: ${fileMatches.join(', ')}`);
    }

    // Extract component names (PascalCase words)
    const componentPattern = /\b[A-Z][a-zA-Z]*(?:Component|Button|Form|Modal|Card|Nav|Header|Footer)?\b/g;
    const componentMatches = message.match(componentPattern);
    if (componentMatches) {
      context.push(`Components mentioned: ${componentMatches.join(', ')}`);
    }

    return context;
  }

  /**
   * Generate response based on intent and context
   */
  async generateResponse(intent: UserIntent, context: CodeContext): Promise<string> {
    try {
      Logger.debug('Generating response for intent:', intent);

      // This is a placeholder - in a real implementation, this would
      // integrate with the AI service to generate contextual responses
      let response = '';

      switch (intent.action) {
        case 'generate':
          response = this.generateCreationResponse(intent, context);
          break;
        case 'modify':
          response = this.generateModificationResponse(intent, context);
          break;
        case 'explain':
          response = this.generateExplanationResponse(intent, context);
          break;
        case 'debug':
          response = this.generateDebuggingResponse(intent, context);
          break;
        case 'optimize':
          response = this.generateOptimizationResponse(intent, context);
          break;
        case 'refactor':
          response = this.generateRefactoringResponse(intent, context);
          break;
        case 'analyze':
          response = this.generateAnalysisResponse(intent, context);
          break;
        default:
          response = this.generateHelpResponse(intent, context);
      }

      return response;

    } catch (error) {
      Logger.error('Error generating response:', error);
      return "I understand you're looking for help with frontend development. Could you provide more specific details about what you'd like to accomplish?";
    }
  }

  /**
   * Handle follow-up questions and context
   */
  async handleFollowUp(previousContext: ConversationContext, newMessage: string): Promise<UserIntent> {
    // Extract intent from new message
    const newIntent = await this.parseIntent(newMessage, previousContext.projectContext);

    // Enhance intent with previous context
    if (previousContext.previousIntents.length > 0) {
      const lastIntent = previousContext.previousIntents[previousContext.previousIntents.length - 1];
      
      // If new message is vague, inherit context from previous intent
      if (newIntent.confidence < 0.7 && lastIntent.confidence > 0.7) {
        newIntent.target = newIntent.target === 'general' ? lastIntent.target : newIntent.target;
        newIntent.framework = newIntent.framework || lastIntent.framework;
        newIntent.styling = newIntent.styling || lastIntent.styling;
      }
    }

    return newIntent;
  }

  // Private helper methods
  private detectAction(message: string, words: string[]): UserIntent['action'] {
    let maxScore = 0;
    let detectedAction: UserIntent['action'] = 'help';

    for (const [action, keywords] of Object.entries(this.actionKeywords)) {
      const score = keywords.reduce((acc, keyword) => {
        return acc + (message.includes(keyword) ? 1 : 0);
      }, 0);

      if (score > maxScore) {
        maxScore = score;
        detectedAction = action as UserIntent['action'];
      }
    }

    return detectedAction;
  }

  private detectTarget(message: string, words: string[]): UserIntent['target'] {
    let maxScore = 0;
    let detectedTarget: UserIntent['target'] = 'general';

    for (const [target, keywords] of Object.entries(this.targetKeywords)) {
      const score = keywords.reduce((acc, keyword) => {
        return acc + (message.includes(keyword) ? 1 : 0);
      }, 0);

      if (score > maxScore) {
        maxScore = score;
        detectedTarget = target as UserIntent['target'];
      }
    }

    return detectedTarget;
  }

  private detectFramework(message: string, words: string[], context?: CodeContext): string | undefined {
    // First check context
    if (context?.framework) {
      return context.framework;
    }

    // Then check message content
    for (const [framework, keywords] of Object.entries(this.frameworkKeywords)) {
      if (keywords.some(keyword => message.includes(keyword))) {
        return framework;
      }
    }

    return undefined;
  }

  private detectStyling(message: string, words: string[], context?: CodeContext): string | undefined {
    // First check context
    if (context?.styling) {
      return context.styling;
    }

    // Then check message content
    for (const [styling, keywords] of Object.entries(this.stylingKeywords)) {
      if (keywords.some(keyword => message.includes(keyword))) {
        return styling;
      }
    }

    return undefined;
  }

  private extractRequirements(message: string): string[] {
    const requirements: string[] = [];

    // Extract quoted requirements
    const quotedPattern = /"([^"]+)"/g;
    let match;
    while ((match = quotedPattern.exec(message)) !== null) {
      requirements.push(match[1]);
    }

    // Extract bullet points or numbered lists
    const listPattern = /(?:^|\n)\s*(?:\d+\.|\-|\*)\s*(.+)/g;
    while ((match = listPattern.exec(message)) !== null) {
      requirements.push(match[1].trim());
    }

    // If no specific requirements found, use the whole message
    if (requirements.length === 0) {
      requirements.push(message);
    }

    return requirements;
  }

  private determineComplexity(message: string, words: string[]): UserIntent['complexity'] {
    const complexityIndicators = {
      simple: ['simple', 'basic', 'easy', 'quick', 'minimal'],
      medium: ['medium', 'standard', 'normal', 'typical'],
      complex: ['complex', 'advanced', 'sophisticated', 'enterprise', 'scalable', 'production']
    };

    for (const [complexity, indicators] of Object.entries(complexityIndicators)) {
      if (indicators.some(indicator => message.includes(indicator))) {
        return complexity as UserIntent['complexity'];
      }
    }

    // Default based on message length and technical terms
    const technicalTerms = ['api', 'database', 'authentication', 'routing', 'state management', 'testing'];
    const hasTechnicalTerms = technicalTerms.some(term => message.includes(term));
    
    if (words.length > 50 || hasTechnicalTerms) {
      return 'complex';
    } else if (words.length > 20) {
      return 'medium';
    } else {
      return 'simple';
    }
  }

  private calculateConfidence(action: string, target: string, requirements: string[]): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence based on specificity
    if (action !== 'help') confidence += 0.2;
    if (target !== 'general') confidence += 0.2;
    if (requirements.length > 0 && requirements[0].length > 10) confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  // Response generation methods (simplified for now)
  private generateCreationResponse(intent: UserIntent, context: CodeContext): string {
    return `I'll help you create a ${intent.target}. Let me generate that for you based on your requirements.`;
  }

  private generateModificationResponse(intent: UserIntent, context: CodeContext): string {
    return `I'll help you modify the ${intent.target}. Let me analyze the current code and suggest improvements.`;
  }

  private generateExplanationResponse(intent: UserIntent, context: CodeContext): string {
    return `I'll explain how the ${intent.target} works and provide detailed information.`;
  }

  private generateDebuggingResponse(intent: UserIntent, context: CodeContext): string {
    return `I'll help you debug the ${intent.target}. Let me analyze the issue and provide solutions.`;
  }

  private generateOptimizationResponse(intent: UserIntent, context: CodeContext): string {
    return `I'll help you optimize the ${intent.target} for better performance and efficiency.`;
  }

  private generateRefactoringResponse(intent: UserIntent, context: CodeContext): string {
    return `I'll help you refactor the ${intent.target} to improve code quality and maintainability.`;
  }

  private generateAnalysisResponse(intent: UserIntent, context: CodeContext): string {
    return `I'll analyze the ${intent.target} and provide insights and recommendations.`;
  }

  private generateHelpResponse(intent: UserIntent, context: CodeContext): string {
    return `I'm here to help with your frontend development needs. What would you like to work on?`;
  }
}
