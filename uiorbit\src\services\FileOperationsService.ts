import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from '../utils/Logger';

export interface FileInfo {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  extension?: string;
  lastModified?: Date;
  isReadable?: boolean;
  isWritable?: boolean;
}

export interface FileOperationResult {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

export interface FileWatcherOptions {
  patterns: string[];
  ignorePatterns?: string[];
  recursive?: boolean;
}

/**
 * File Operations Service for UIOrbit
 * Handles all file system operations with proper error handling and validation
 */
export class FileOperationsService {
  private watchers: Map<string, vscode.FileSystemWatcher> = new Map();
  private readonly maxFileSize = 10 * 1024 * 1024; // 10MB
  private readonly supportedExtensions = [
    '.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte',
    '.css', '.scss', '.sass', '.less', '.html', '.json',
    '.md', '.txt', '.yml', '.yaml'
  ];

  /**
   * Read file content
   */
  async readFile(filePath: string): Promise<FileOperationResult> {
    try {
      Logger.debug(`Reading file: ${filePath}`);

      // Validate file path
      const validation = await this.validateFilePath(filePath);
      if (!validation.success) {
        return validation;
      }

      // Check file size
      const stats = await fs.stat(filePath);
      if (stats.size > this.maxFileSize) {
        return {
          success: false,
          error: `File too large: ${stats.size} bytes (max: ${this.maxFileSize} bytes)`
        };
      }

      // Read file content
      const content = await fs.readFile(filePath, 'utf-8');
      
      Logger.info(`Successfully read file: ${filePath} (${stats.size} bytes)`);
      return {
        success: true,
        data: {
          content,
          size: stats.size,
          lastModified: stats.mtime,
          extension: path.extname(filePath)
        }
      };

    } catch (error) {
      Logger.error(`Error reading file ${filePath}:`, error);
      return {
        success: false,
        error: `Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Write content to file
   */
  async writeFile(filePath: string, content: string): Promise<FileOperationResult> {
    try {
      Logger.debug(`Writing file: ${filePath}`);

      // Ensure directory exists
      const dir = path.dirname(filePath);
      await fs.ensureDir(dir);

      // Create backup if file exists
      const backupPath = await this.createBackup(filePath);

      try {
        // Write file content
        await fs.writeFile(filePath, content, 'utf-8');
        
        Logger.info(`Successfully wrote file: ${filePath} (${content.length} characters)`);
        return {
          success: true,
          message: `File written successfully`,
          data: {
            path: filePath,
            size: content.length,
            backupPath
          }
        };

      } catch (writeError) {
        // Restore backup if write failed
        if (backupPath) {
          await this.restoreBackup(filePath, backupPath);
        }
        throw writeError;
      }

    } catch (error) {
      Logger.error(`Error writing file ${filePath}:`, error);
      return {
        success: false,
        error: `Failed to write file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Create new file
   */
  async createFile(filePath: string, content: string = ''): Promise<FileOperationResult> {
    try {
      Logger.debug(`Creating file: ${filePath}`);

      // Check if file already exists
      if (await fs.pathExists(filePath)) {
        return {
          success: false,
          error: `File already exists: ${filePath}`
        };
      }

      // Validate file extension
      const extension = path.extname(filePath);
      if (extension && !this.supportedExtensions.includes(extension)) {
        Logger.warn(`Unsupported file extension: ${extension}`);
      }

      return await this.writeFile(filePath, content);

    } catch (error) {
      Logger.error(`Error creating file ${filePath}:`, error);
      return {
        success: false,
        error: `Failed to create file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Delete file
   */
  async deleteFile(filePath: string): Promise<FileOperationResult> {
    try {
      Logger.debug(`Deleting file: ${filePath}`);

      // Validate file exists
      if (!await fs.pathExists(filePath)) {
        return {
          success: false,
          error: `File does not exist: ${filePath}`
        };
      }

      // Create backup before deletion
      const backupPath = await this.createBackup(filePath);

      // Delete file
      await fs.remove(filePath);
      
      Logger.info(`Successfully deleted file: ${filePath}`);
      return {
        success: true,
        message: `File deleted successfully`,
        data: {
          deletedPath: filePath,
          backupPath
        }
      };

    } catch (error) {
      Logger.error(`Error deleting file ${filePath}:`, error);
      return {
        success: false,
        error: `Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * List files in directory
   */
  async listFiles(directory: string, filters?: string[]): Promise<FileOperationResult> {
    try {
      Logger.debug(`Listing files in: ${directory}`);

      // Validate directory exists
      if (!await fs.pathExists(directory)) {
        return {
          success: false,
          error: `Directory does not exist: ${directory}`
        };
      }

      const stats = await fs.stat(directory);
      if (!stats.isDirectory()) {
        return {
          success: false,
          error: `Path is not a directory: ${directory}`
        };
      }

      // Read directory contents
      const entries = await fs.readdir(directory);
      const files: FileInfo[] = [];

      for (const entry of entries) {
        const entryPath = path.join(directory, entry);
        const entryStats = await fs.stat(entryPath);
        
        const fileInfo: FileInfo = {
          name: entry,
          path: entryPath,
          type: entryStats.isDirectory() ? 'directory' : 'file',
          size: entryStats.size,
          lastModified: entryStats.mtime,
          extension: entryStats.isFile() ? path.extname(entry) : undefined
        };

        // Apply filters
        if (filters && filters.length > 0) {
          const matchesFilter = filters.some(filter => {
            if (filter.startsWith('*.')) {
              return fileInfo.extension === filter.substring(1);
            }
            return fileInfo.name.includes(filter);
          });
          
          if (!matchesFilter) {
            continue;
          }
        }

        files.push(fileInfo);
      }

      Logger.info(`Listed ${files.length} files in: ${directory}`);
      return {
        success: true,
        data: {
          directory,
          files,
          totalCount: files.length
        }
      };

    } catch (error) {
      Logger.error(`Error listing files in ${directory}:`, error);
      return {
        success: false,
        error: `Failed to list files: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Watch files for changes
   */
  async watchFiles(options: FileWatcherOptions): Promise<FileOperationResult> {
    try {
      Logger.debug(`Setting up file watchers for patterns:`, options.patterns);

      const watcherId = `watcher_${Date.now()}`;
      
      // Create file system watcher
      const watcher = vscode.workspace.createFileSystemWatcher(
        new vscode.RelativePattern(
          vscode.workspace.workspaceFolders?.[0] || '',
          `{${options.patterns.join(',')}}`
        )
      );

      // Set up event handlers
      watcher.onDidCreate(uri => {
        Logger.info(`File created: ${uri.fsPath}`);
        this.notifyFileChange('created', uri.fsPath);
      });

      watcher.onDidChange(uri => {
        Logger.info(`File changed: ${uri.fsPath}`);
        this.notifyFileChange('changed', uri.fsPath);
      });

      watcher.onDidDelete(uri => {
        Logger.info(`File deleted: ${uri.fsPath}`);
        this.notifyFileChange('deleted', uri.fsPath);
      });

      // Store watcher
      this.watchers.set(watcherId, watcher);

      Logger.info(`File watcher created with ID: ${watcherId}`);
      return {
        success: true,
        message: `File watcher created successfully`,
        data: {
          watcherId,
          patterns: options.patterns
        }
      };

    } catch (error) {
      Logger.error(`Error setting up file watcher:`, error);
      return {
        success: false,
        error: `Failed to create file watcher: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Stop watching files
   */
  async stopWatching(watcherId: string): Promise<FileOperationResult> {
    try {
      const watcher = this.watchers.get(watcherId);
      if (!watcher) {
        return {
          success: false,
          error: `Watcher not found: ${watcherId}`
        };
      }

      watcher.dispose();
      this.watchers.delete(watcherId);

      Logger.info(`File watcher stopped: ${watcherId}`);
      return {
        success: true,
        message: `File watcher stopped successfully`
      };

    } catch (error) {
      Logger.error(`Error stopping file watcher ${watcherId}:`, error);
      return {
        success: false,
        error: `Failed to stop file watcher: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate file path
   */
  private async validateFilePath(filePath: string): Promise<FileOperationResult> {
    try {
      // Check if path exists
      if (!await fs.pathExists(filePath)) {
        return {
          success: false,
          error: `File does not exist: ${filePath}`
        };
      }

      // Check if it's a file (not directory)
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        return {
          success: false,
          error: `Path is not a file: ${filePath}`
        };
      }

      return { success: true };

    } catch (error) {
      return {
        success: false,
        error: `Invalid file path: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Create backup of file
   */
  private async createBackup(filePath: string): Promise<string | null> {
    try {
      if (!await fs.pathExists(filePath)) {
        return null;
      }

      const backupPath = `${filePath}.backup.${Date.now()}`;
      await fs.copy(filePath, backupPath);
      
      Logger.debug(`Created backup: ${backupPath}`);
      return backupPath;

    } catch (error) {
      Logger.warn(`Failed to create backup for ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Restore backup
   */
  private async restoreBackup(originalPath: string, backupPath: string): Promise<void> {
    try {
      if (await fs.pathExists(backupPath)) {
        await fs.copy(backupPath, originalPath);
        await fs.remove(backupPath);
        Logger.info(`Restored backup: ${originalPath}`);
      }
    } catch (error) {
      Logger.error(`Failed to restore backup ${backupPath}:`, error);
    }
  }

  /**
   * Notify file change events
   */
  private notifyFileChange(type: 'created' | 'changed' | 'deleted', filePath: string): void {
    // This can be extended to emit events or notify other services
    Logger.debug(`File ${type}: ${filePath}`);
  }

  /**
   * Dispose all watchers
   */
  dispose(): void {
    for (const [id, watcher] of this.watchers) {
      watcher.dispose();
      Logger.debug(`Disposed file watcher: ${id}`);
    }
    this.watchers.clear();
  }
}
