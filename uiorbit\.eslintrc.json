{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "rules": {"@typescript-eslint/naming-convention": ["warn", {"selector": "import", "format": ["camelCase", "PascalCase"]}], "@typescript-eslint/semi": "warn", "curly": "warn", "eqeqeq": "warn", "no-throw-literal": "warn", "semi": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn"}, "ignorePatterns": ["out", "dist", "**/*.d.ts", "node_modules"]}