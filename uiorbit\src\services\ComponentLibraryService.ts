import * as vscode from 'vscode';
import * as path from 'path';
import { Logger } from '../utils/Logger';
import { FileOperationsService } from './FileOperationsService';
import { TemplateEngineService, ComponentTemplate } from './TemplateEngineService';
import { ProjectDetectionService, ProjectContext } from './ProjectDetectionService';

export interface ComponentLibraryItem {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  preview?: string;
  code: string;
  framework: string;
  styling: string;
  dependencies: string[];
  usage: string;
  props?: ComponentProp[];
}

export interface ComponentProp {
  name: string;
  type: string;
  required: boolean;
  description: string;
  defaultValue?: string;
}

export interface LibrarySearchOptions {
  query?: string;
  category?: string;
  framework?: string;
  styling?: string;
  tags?: string[];
}

export interface ComponentInstallOptions {
  componentId: string;
  targetPath?: string;
  customName?: string;
  installDependencies?: boolean;
}

/**
 * Component Library Service for UIOrbit
 * Manages a curated library of modern UI components
 */
export class ComponentLibraryService {
  private components: Map<string, ComponentLibraryItem> = new Map();
  private fileOpsService: FileOperationsService;
  private templateEngine: TemplateEngineService;
  private projectDetectionService: ProjectDetectionService;

  constructor(
    fileOperationsService: FileOperationsService,
    templateEngineService: TemplateEngineService,
    projectDetectionService: ProjectDetectionService
  ) {
    this.fileOpsService = fileOperationsService;
    this.templateEngine = templateEngineService;
    this.projectDetectionService = projectDetectionService;
    this.initializeComponentLibrary();
  }

  /**
   * Search components in the library
   */
  searchComponents(options: LibrarySearchOptions = {}): ComponentLibraryItem[] {
    let components = Array.from(this.components.values());

    // Filter by query
    if (options.query) {
      const query = options.query.toLowerCase();
      components = components.filter(comp => 
        comp.name.toLowerCase().includes(query) ||
        comp.description.toLowerCase().includes(query) ||
        comp.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by category
    if (options.category) {
      components = components.filter(comp => comp.category === options.category);
    }

    // Filter by framework
    if (options.framework) {
      components = components.filter(comp => comp.framework === options.framework);
    }

    // Filter by styling
    if (options.styling) {
      components = components.filter(comp => comp.styling === options.styling);
    }

    // Filter by tags
    if (options.tags && options.tags.length > 0) {
      components = components.filter(comp => 
        options.tags!.some(tag => comp.tags.includes(tag))
      );
    }

    return components;
  }

  /**
   * Get component by ID
   */
  getComponent(id: string): ComponentLibraryItem | null {
    return this.components.get(id) || null;
  }

  /**
   * Get all categories
   */
  getCategories(): string[] {
    const categories = new Set<string>();
    for (const component of this.components.values()) {
      categories.add(component.category);
    }
    return Array.from(categories).sort();
  }

  /**
   * Get all tags
   */
  getTags(): string[] {
    const tags = new Set<string>();
    for (const component of this.components.values()) {
      component.tags.forEach(tag => tags.add(tag));
    }
    return Array.from(tags).sort();
  }

  /**
   * Install component to project
   */
  async installComponent(options: ComponentInstallOptions): Promise<boolean> {
    try {
      Logger.info(`Installing component: ${options.componentId}`);

      const component = this.getComponent(options.componentId);
      if (!component) {
        throw new Error(`Component not found: ${options.componentId}`);
      }

      const projectContext = await this.projectDetectionService.getProjectContext();
      
      // Check framework compatibility
      if (component.framework !== projectContext.framework && projectContext.framework !== 'none') {
        throw new Error(`Component framework (${component.framework}) doesn't match project framework (${projectContext.framework})`);
      }

      // Determine target path
      const targetPath = await this.determineTargetPath(options, projectContext);
      
      // Generate component name
      const componentName = options.customName || component.name;
      
      // Process component code
      const processedCode = this.processComponentCode(component.code, componentName);
      
      // Write component file
      const fileName = `${componentName}.${this.getFileExtension(component.framework, projectContext)}`;
      const filePath = path.join(targetPath, fileName);
      
      const writeResult = await this.fileOpsService.createFile(filePath, processedCode);
      if (!writeResult.success) {
        throw new Error(`Failed to write component file: ${writeResult.error}`);
      }

      // Install dependencies if requested
      if (options.installDependencies && component.dependencies.length > 0) {
        await this.installDependencies(component.dependencies, projectContext);
      }

      Logger.info(`Component ${componentName} installed successfully at ${filePath}`);
      return true;

    } catch (error) {
      Logger.error('Error installing component:', error);
      throw error;
    }
  }

  /**
   * Add custom component to library
   */
  addComponent(component: ComponentLibraryItem): void {
    this.components.set(component.id, component);
    Logger.info(`Added component to library: ${component.id}`);
  }

  /**
   * Get trending components
   */
  getTrendingComponents(): ComponentLibraryItem[] {
    // Return components with modern/trending tags
    return this.searchComponents({
      tags: ['modern', 'trending', 'glassmorphism', 'neumorphism', 'micro-interactions']
    });
  }

  /**
   * Get components by framework
   */
  getComponentsByFramework(framework: string): ComponentLibraryItem[] {
    return this.searchComponents({ framework });
  }

  /**
   * Initialize component library with curated components
   */
  private initializeComponentLibrary(): void {
    // Modern Glassmorphism Card
    this.addComponent({
      id: 'glassmorphism-card',
      name: 'GlassmorphismCard',
      description: 'Modern card with glassmorphism effect and smooth animations',
      category: 'Layout',
      tags: ['card', 'glassmorphism', 'modern', 'trending'],
      framework: 'react',
      styling: 'tailwind',
      dependencies: ['framer-motion'],
      usage: '<GlassmorphismCard>Content here</GlassmorphismCard>',
      code: `import React from 'react';
import { motion } from 'framer-motion';

interface GlassmorphismCardProps {
  children: React.ReactNode;
  className?: string;
}

const GlassmorphismCard: React.FC<GlassmorphismCardProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={\`
        backdrop-blur-md bg-white/10 
        border border-white/20 
        rounded-xl p-6 
        shadow-xl hover:shadow-2xl 
        transition-all duration-300
        hover:bg-white/15
        \${className}
      \`}
    >
      {children}
    </motion.div>
  );
};

export default GlassmorphismCard;`,
      props: [
        {
          name: 'children',
          type: 'React.ReactNode',
          required: true,
          description: 'Content to display inside the card'
        },
        {
          name: 'className',
          type: 'string',
          required: false,
          description: 'Additional CSS classes',
          defaultValue: ''
        }
      ]
    });

    // Neumorphism Button
    this.addComponent({
      id: 'neumorphism-button',
      name: 'NeumorphismButton',
      description: 'Button with neumorphism design and micro-interactions',
      category: 'Interactive',
      tags: ['button', 'neumorphism', 'micro-interactions', 'modern'],
      framework: 'react',
      styling: 'tailwind',
      dependencies: [],
      usage: '<NeumorphismButton onClick={handleClick}>Click me</NeumorphismButton>',
      code: `import React, { useState } from 'react';

interface NeumorphismButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}

const NeumorphismButton: React.FC<NeumorphismButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = ''
}) => {
  const [isPressed, setIsPressed] = useState(false);

  const baseClasses = \`
    relative overflow-hidden rounded-xl font-medium
    transition-all duration-200 ease-out
    focus:outline-none focus:ring-2 focus:ring-blue-500/50
    disabled:opacity-50 disabled:cursor-not-allowed
  \`;

  const variantClasses = {
    primary: \`
      bg-gradient-to-br from-blue-50 to-blue-100
      text-blue-900 border border-blue-200/50
      shadow-[8px_8px_16px_#d1d9e6,-8px_-8px_16px_#ffffff]
      hover:shadow-[4px_4px_8px_#d1d9e6,-4px_-4px_8px_#ffffff]
      active:shadow-[inset_4px_4px_8px_#d1d9e6,inset_-4px_-4px_8px_#ffffff]
    \`,
    secondary: \`
      bg-gradient-to-br from-gray-50 to-gray-100
      text-gray-900 border border-gray-200/50
      shadow-[8px_8px_16px_#d1d9e6,-8px_-8px_16px_#ffffff]
      hover:shadow-[4px_4px_8px_#d1d9e6,-4px_-4px_8px_#ffffff]
      active:shadow-[inset_4px_4px_8px_#d1d9e6,inset_-4px_-4px_8px_#ffffff]
    \`
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };

  return (
    <button
      className={\`
        \${baseClasses}
        \${variantClasses[variant]}
        \${sizeClasses[size]}
        \${isPressed ? 'transform scale-95' : ''}
        \${className}
      \`}
      onClick={onClick}
      disabled={disabled}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
    >
      {children}
    </button>
  );
};

export default NeumorphismButton;`,
      props: [
        {
          name: 'children',
          type: 'React.ReactNode',
          required: true,
          description: 'Button content'
        },
        {
          name: 'onClick',
          type: '() => void',
          required: false,
          description: 'Click handler function'
        },
        {
          name: 'variant',
          type: "'primary' | 'secondary'",
          required: false,
          description: 'Button variant',
          defaultValue: 'primary'
        }
      ]
    });

    // Animated Loading Spinner
    this.addComponent({
      id: 'animated-spinner',
      name: 'AnimatedSpinner',
      description: 'Modern loading spinner with smooth animations',
      category: 'Feedback',
      tags: ['loading', 'spinner', 'animation', 'modern'],
      framework: 'react',
      styling: 'tailwind',
      dependencies: [],
      usage: '<AnimatedSpinner size="md" />',
      code: `import React from 'react';

interface AnimatedSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
}

const AnimatedSpinner: React.FC<AnimatedSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const colorClasses = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    white: 'text-white'
  };

  return (
    <div className={\`inline-block \${className}\`}>
      <svg
        className={\`animate-spin \${sizeClasses[size]} \${colorClasses[color]}\`}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  );
};

export default AnimatedSpinner;`
    });

    Logger.info('Component library initialized with curated components');
  }

  /**
   * Process component code with custom name
   */
  private processComponentCode(code: string, componentName: string): string {
    // Replace component name in the code
    const originalName = code.match(/(?:const|function|class)\s+(\w+)/)?.[1];
    if (originalName) {
      return code.replace(new RegExp(originalName, 'g'), componentName);
    }
    return code;
  }

  /**
   * Determine target path for component installation
   */
  private async determineTargetPath(options: ComponentInstallOptions, context: ProjectContext): Promise<string> {
    if (options.targetPath) {
      return options.targetPath;
    }

    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      throw new Error('No workspace folder found');
    }

    const rootPath = workspaceFolder.uri.fsPath;
    
    // Try to find components directory
    const possiblePaths = [
      path.join(rootPath, 'src', 'components'),
      path.join(rootPath, 'components'),
      path.join(rootPath, 'src'),
      rootPath
    ];

    for (const dirPath of possiblePaths) {
      const result = await this.fileOpsService.listFiles(dirPath);
      if (result.success) {
        return dirPath;
      }
    }

    // Create components directory if it doesn't exist
    const componentsPath = path.join(rootPath, 'src', 'components');
    return componentsPath;
  }

  /**
   * Get file extension based on framework and project context
   */
  private getFileExtension(framework: string, context: ProjectContext): string {
    if (framework === 'react') {
      return context.analysis?.typescript ? 'tsx' : 'jsx';
    } else if (framework === 'vue') {
      return 'vue';
    } else if (framework === 'angular') {
      return 'ts';
    } else if (framework === 'svelte') {
      return 'svelte';
    }
    return context.analysis?.typescript ? 'ts' : 'js';
  }

  /**
   * Install component dependencies
   */
  private async installDependencies(dependencies: string[], context: ProjectContext): Promise<void> {
    if (dependencies.length === 0) return;

    const packageManager = context.packageManager || 'npm';
    const installCommand = packageManager === 'yarn' ? 'yarn add' : 
                          packageManager === 'pnpm' ? 'pnpm add' : 'npm install';

    const command = `${installCommand} ${dependencies.join(' ')}`;
    
    Logger.info(`Installing dependencies: ${command}`);
    
    // Show information to user about manual installation
    vscode.window.showInformationMessage(
      `Please run the following command to install dependencies: ${command}`,
      'Copy Command'
    ).then(selection => {
      if (selection === 'Copy Command') {
        vscode.env.clipboard.writeText(command);
      }
    });
  }
}
