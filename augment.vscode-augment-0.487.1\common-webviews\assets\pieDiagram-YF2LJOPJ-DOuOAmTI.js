import{p as L}from"./chunk-TMUBEWPD-CeNbOmcG.js";import{X as y,O as z,aH as N,D as P,q as V,r as q,s as H,g as I,c as X,b as _,_ as m,l as F,x as G,d as J,E as K,I as Q,a5 as U,k as Y}from"./AugmentMessage-B1RETs6x.js";import{p as Z}from"./gitGraph-YCYPL57B-Bqc4prk4.js";import{d as B}from"./arc-DxMNwgSo.js";import{o as tt}from"./ordinal-_rw2EY4v.js";import"./SpinnerAugment-BJAAUt-n.js";import"./CalloutAugment-Bc8HnLJ3.js";import"./TextTooltipAugment-DGOJQXY9.js";import"./BaseButton-7bccWxEO.js";import"./IconButtonAugment-CqdkuyT6.js";import"./Content-BldOFwN2.js";import"./globals-D0QH3NT1.js";import"./arrow-up-right-from-square-CdEOBPRR.js";import"./types-xGAhb6Qr.js";import"./chat-types-D7sox8tw.js";import"./file-paths-BcSg4gks.js";import"./folder-BB1rR2Vr.js";import"./github-DDehkTJf.js";import"./folder-opened-BWTQdsic.js";import"./check-DWGOhZOn.js";import"./types-DDm27S8B.js";import"./index-Bb_d2FL8.js";import"./utils-BW_yYq2f.js";import"./ra-diff-ops-model-CtxygvlM.js";import"./types-CGlLNakm.js";import"./index-BAb5fkIe.js";import"./CardAugment-qFWs8J9b.js";import"./isObjectLike-B3cfrJ3d.js";import"./TextAreaAugment-CfENnz8O.js";import"./diff-utils-RpWUB_Gw.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C9A1ZQNk.js";import"./keypress-DD1aQVr0.js";import"./await_block-CklR1HoG.js";import"./CollapseButtonAugment-BPhovcAJ.js";import"./ButtonAugment-CLDnX_Hg.js";import"./MaterialIcon-DkFwt_X2.js";import"./CopyButton-OCXKxiUh.js";import"./magnifying-glass-4Ft8m82l.js";import"./ellipsis-DJS5pN6w.js";import"./IconFilePath-BhE4l2UK.js";import"./LanguageIcon-CARIV-0P.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-D19TbAnP.js";import"./chevron-down-CxktpQeS.js";import"./mcp-logo-CMUqUebQ.js";import"./terminal-CUUE2e2M.js";import"./pen-to-square-SVW9AP0k.js";import"./augment-logo-f4Y8aL0S.js";import"./_baseUniq-D_Z4SEi2.js";import"./_basePickBy-OQ9hpBSi.js";import"./clone-B9ySJnFq.js";import"./init-g68aIKmP.js";function et(t,r){return r<t?-1:r>t?1:r>=t?0:NaN}function rt(t){return t}var W=P.pie,R={sections:new Map,showData:!1,config:W},M=R.sections,O=R.showData,it=structuredClone(W),j={getConfig:m(()=>structuredClone(it),"getConfig"),clear:m(()=>{M=new Map,O=R.showData,G()},"clear"),setDiagramTitle:V,getDiagramTitle:q,setAccTitle:H,getAccTitle:I,setAccDescription:X,getAccDescription:_,addSection:m(({label:t,value:r})=>{M.has(t)||(M.set(t,r),F.debug(`added new section: ${t}, with value: ${r}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{O=t},"setShowData"),getShowData:m(()=>O,"getShowData")},at=m((t,r)=>{L(t,r),r.setShowData(t.showData),t.sections.map(r.addSection)},"populateDb"),nt={parse:m(async t=>{const r=await Z("pie",t);F.debug(r),at(r,j)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),pt=m(t=>{const r=[...t.entries()].map(p=>({label:p[0],value:p[1]})).sort((p,u)=>u.value-p.value);return function(){var p=rt,u=et,c=null,w=y(0),S=y(z),$=y(0);function i(e){var a,l,n,A,g,s=(e=N(e)).length,v=0,D=new Array(s),d=new Array(s),f=+w.apply(this,arguments),C=Math.min(z,Math.max(-z,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/s,$.apply(this,arguments)),b=h*(C<0?-1:1);for(a=0;a<s;++a)(g=d[D[a]=a]=+p(e[a],a,e))>0&&(v+=g);for(u!=null?D.sort(function(x,T){return u(d[x],d[T])}):c!=null&&D.sort(function(x,T){return c(e[x],e[T])}),a=0,n=v?(C-s*b)/v:0;a<s;++a,f=A)l=D[a],A=f+((g=d[l])>0?g*n:0)+b,d[l]={data:e[l],index:a,value:g,startAngle:f,endAngle:A,padAngle:h};return d}return i.value=function(e){return arguments.length?(p=typeof e=="function"?e:y(+e),i):p},i.sortValues=function(e){return arguments.length?(u=e,c=null,i):u},i.sort=function(e){return arguments.length?(c=e,u=null,i):c},i.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),i):w},i.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),i):S},i.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),i):$},i}().value(p=>p.value)(r)},"createPieArcs"),le={parser:nt,db:j,renderer:{draw:m((t,r,p,u)=>{F.debug(`rendering pie chart
`+t);const c=u.db,w=J(),S=K(c.getConfig(),w.pie),$=18,i=450,e=i,a=Q(r),l=a.append("g");l.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[A]=U(n.pieOuterStrokeWidth);A??(A=2);const g=S.textPosition,s=Math.min(e,i)/2-40,v=B().innerRadius(0).outerRadius(s),D=B().innerRadius(s*g).outerRadius(s*g);l.append("circle").attr("cx",0).attr("cy",0).attr("r",s+A/2).attr("class","pieOuterCircle");const d=c.getSections(),f=pt(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=tt(C);l.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),l.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+D.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),l.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=l.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:E}=o.data;return c.getShowData()?`${k} [${E}]`:k});const T=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));a.attr("viewBox",`0 0 ${T} 450`),Y(a,i,T,S.useMaxWidth)},"draw")},styles:ot};export{le as diagram};
