import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';

/**
 * Usage tracking service for Community tier limits
 * Tracks AI requests and enforces monthly limits
 */
export class UsageTrackingService {
  private static readonly COMMUNITY_MONTHLY_LIMIT = 100;
  private static readonly STORAGE_KEY = 'uiorbit.usage';
  private static readonly RESET_KEY = 'uiorbit.usage.reset';

  constructor(private context: vscode.ExtensionContext) {}

  /**
   * Initialize the usage tracking service
   */
  async initialize(): Promise<void> {
    Logger.info('Initializing usage tracking service...');
    
    // Check if we need to reset monthly usage
    await this.checkAndResetMonthlyUsage();
    
    Logger.info('Usage tracking service initialized');
  }

  /**
   * Check if user can make a request (within limits)
   */
  async canMakeRequest(): Promise<{ allowed: boolean; remaining: number; limit: number }> {
    const currentUsage = await this.getCurrentUsage();
    const remaining = UsageTrackingService.COMMUNITY_MONTHLY_LIMIT - currentUsage;
    
    return {
      allowed: currentUsage < UsageTrackingService.COMMUNITY_MONTHLY_LIMIT,
      remaining: Math.max(0, remaining),
      limit: UsageTrackingService.COMMUNITY_MONTHLY_LIMIT
    };
  }

  /**
   * Record a new AI request
   */
  async recordRequest(): Promise<void> {
    const currentUsage = await this.getCurrentUsage();
    const newUsage = currentUsage + 1;
    
    await this.context.globalState.update(UsageTrackingService.STORAGE_KEY, newUsage);
    
    Logger.info(`AI request recorded. Usage: ${newUsage}/${UsageTrackingService.COMMUNITY_MONTHLY_LIMIT}`);
    
    // Show warning when approaching limit
    if (newUsage >= UsageTrackingService.COMMUNITY_MONTHLY_LIMIT * 0.8) {
      await this.showUsageWarning(newUsage);
    }
  }

  /**
   * Get current monthly usage
   */
  async getCurrentUsage(): Promise<number> {
    return this.context.globalState.get(UsageTrackingService.STORAGE_KEY, 0);
  }

  /**
   * Get usage statistics
   */
  async getUsageStats(): Promise<{
    current: number;
    limit: number;
    remaining: number;
    percentage: number;
    resetDate: Date;
  }> {
    const current = await this.getCurrentUsage();
    const remaining = Math.max(0, UsageTrackingService.COMMUNITY_MONTHLY_LIMIT - current);
    const percentage = (current / UsageTrackingService.COMMUNITY_MONTHLY_LIMIT) * 100;
    const resetDate = this.getNextResetDate();

    return {
      current,
      limit: UsageTrackingService.COMMUNITY_MONTHLY_LIMIT,
      remaining,
      percentage: Math.min(100, percentage),
      resetDate
    };
  }

  /**
   * Show upgrade prompt when limit is reached
   */
  async showUpgradePrompt(): Promise<void> {
    const action = await vscode.window.showWarningMessage(
      '🚀 UIOrbit Community Limit Reached',
      {
        modal: true,
        detail: `You've used all ${UsageTrackingService.COMMUNITY_MONTHLY_LIMIT} AI requests for this month.\n\nUpgrade to Professional for unlimited requests and priority support!`
      },
      'Upgrade to Professional',
      'Learn More',
      'Maybe Later'
    );

    switch (action) {
      case 'Upgrade to Professional':
        await vscode.env.openExternal(vscode.Uri.parse('https://uiorbit.com/upgrade'));
        break;
      case 'Learn More':
        await vscode.env.openExternal(vscode.Uri.parse('https://uiorbit.com/pricing'));
        break;
    }
  }

  /**
   * Show usage warning when approaching limit
   */
  private async showUsageWarning(currentUsage: number): Promise<void> {
    const remaining = UsageTrackingService.COMMUNITY_MONTHLY_LIMIT - currentUsage;
    
    if (remaining <= 0) {
      await this.showUpgradePrompt();
      return;
    }

    if (remaining <= 10) {
      const action = await vscode.window.showWarningMessage(
        `⚠️ Only ${remaining} AI requests remaining this month`,
        'Upgrade to Professional',
        'View Usage',
        'Dismiss'
      );

      switch (action) {
        case 'Upgrade to Professional':
          await vscode.env.openExternal(vscode.Uri.parse('https://uiorbit.com/upgrade'));
          break;
        case 'View Usage':
          await this.showUsageStats();
          break;
      }
    }
  }

  /**
   * Show detailed usage statistics
   */
  async showUsageStats(): Promise<void> {
    const stats = await this.getUsageStats();
    
    const message = `📊 **UIOrbit Usage Statistics**

**This Month:**
• Used: ${stats.current}/${stats.limit} requests (${stats.percentage.toFixed(1)}%)
• Remaining: ${stats.remaining} requests
• Resets: ${stats.resetDate.toLocaleDateString()}

**Professional Benefits:**
• ✅ Unlimited AI requests
• ✅ Priority support
• ✅ Early access to new features
• ✅ Advanced analytics

Upgrade for just $19/month!`;

    const action = await vscode.window.showInformationMessage(
      'UIOrbit Usage Statistics',
      {
        modal: true,
        detail: message
      },
      'Upgrade Now',
      'Close'
    );

    if (action === 'Upgrade Now') {
      await vscode.env.openExternal(vscode.Uri.parse('https://uiorbit.com/upgrade'));
    }
  }

  /**
   * Check and reset monthly usage if needed
   */
  private async checkAndResetMonthlyUsage(): Promise<void> {
    const lastReset = this.context.globalState.get(UsageTrackingService.RESET_KEY, 0);
    const now = Date.now();
    const currentMonth = new Date(now).getMonth();
    const lastResetMonth = new Date(lastReset).getMonth();

    // Reset if it's a new month
    if (currentMonth !== lastResetMonth || now - lastReset > 31 * 24 * 60 * 60 * 1000) {
      await this.context.globalState.update(UsageTrackingService.STORAGE_KEY, 0);
      await this.context.globalState.update(UsageTrackingService.RESET_KEY, now);
      
      Logger.info('Monthly usage reset');
      
      // Show reset notification
      vscode.window.showInformationMessage(
        `🔄 UIOrbit usage reset! You have ${UsageTrackingService.COMMUNITY_MONTHLY_LIMIT} AI requests for this month.`
      );
    }
  }

  /**
   * Get the next reset date
   */
  private getNextResetDate(): Date {
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    return nextMonth;
  }

  /**
   * Reset usage (for testing purposes)
   */
  async resetUsage(): Promise<void> {
    await this.context.globalState.update(UsageTrackingService.STORAGE_KEY, 0);
    await this.context.globalState.update(UsageTrackingService.RESET_KEY, Date.now());
    
    Logger.info('Usage manually reset');
    vscode.window.showInformationMessage('UIOrbit usage has been reset.');
  }

  /**
   * Check if user is Professional tier (placeholder for future implementation)
   */
  async isProfessionalUser(): Promise<boolean> {
    // TODO: Implement actual Professional tier checking
    // This could check license keys, subscription status, etc.
    return false;
  }

  /**
   * Get tier information
   */
  async getTierInfo(): Promise<{
    tier: 'Community' | 'Professional';
    hasLimits: boolean;
    features: string[];
  }> {
    const isPro = await this.isProfessionalUser();
    
    if (isPro) {
      return {
        tier: 'Professional',
        hasLimits: false,
        features: [
          'Unlimited AI requests',
          'Priority support',
          'Early access features',
          'Advanced analytics',
          'Team collaboration'
        ]
      };
    }

    return {
      tier: 'Community',
      hasLimits: true,
      features: [
        '100 AI requests/month',
        'All frameworks supported',
        'Component generation',
        'Design system integration',
        'Community support'
      ]
    };
  }
}
