import{p as Y}from"./chunk-TMUBEWPD-CeNbOmcG.js";import{I as U}from"./chunk-KFBOBJHC-D-EIdJMB.js";import{D as V,_ as h,d as J,E as Q,F as X,G as Z,l as w,s as tt,g as rt,b as et,c as ot,q as nt,r as at,e as C,x as it,j as st,v as ct,H as mt}from"./AugmentMessage-B1RETs6x.js";import{p as ht}from"./gitGraph-YCYPL57B-Bqc4prk4.js";import"./SpinnerAugment-BJAAUt-n.js";import"./CalloutAugment-Bc8HnLJ3.js";import"./TextTooltipAugment-DGOJQXY9.js";import"./BaseButton-7bccWxEO.js";import"./IconButtonAugment-CqdkuyT6.js";import"./Content-BldOFwN2.js";import"./globals-D0QH3NT1.js";import"./arrow-up-right-from-square-CdEOBPRR.js";import"./types-xGAhb6Qr.js";import"./chat-types-D7sox8tw.js";import"./file-paths-BcSg4gks.js";import"./folder-BB1rR2Vr.js";import"./github-DDehkTJf.js";import"./folder-opened-BWTQdsic.js";import"./check-DWGOhZOn.js";import"./types-DDm27S8B.js";import"./index-Bb_d2FL8.js";import"./utils-BW_yYq2f.js";import"./ra-diff-ops-model-CtxygvlM.js";import"./types-CGlLNakm.js";import"./index-BAb5fkIe.js";import"./CardAugment-qFWs8J9b.js";import"./isObjectLike-B3cfrJ3d.js";import"./TextAreaAugment-CfENnz8O.js";import"./diff-utils-RpWUB_Gw.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C9A1ZQNk.js";import"./keypress-DD1aQVr0.js";import"./await_block-CklR1HoG.js";import"./CollapseButtonAugment-BPhovcAJ.js";import"./ButtonAugment-CLDnX_Hg.js";import"./MaterialIcon-DkFwt_X2.js";import"./CopyButton-OCXKxiUh.js";import"./magnifying-glass-4Ft8m82l.js";import"./ellipsis-DJS5pN6w.js";import"./IconFilePath-BhE4l2UK.js";import"./LanguageIcon-CARIV-0P.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-D19TbAnP.js";import"./chevron-down-CxktpQeS.js";import"./mcp-logo-CMUqUebQ.js";import"./terminal-CUUE2e2M.js";import"./pen-to-square-SVW9AP0k.js";import"./augment-logo-f4Y8aL0S.js";import"./_baseUniq-D_Z4SEi2.js";import"./_basePickBy-OQ9hpBSi.js";import"./clone-B9ySJnFq.js";var g={NORMAL:0,REVERSE:1,HIGHLIGHT:2,MERGE:3,CHERRY_PICK:4},dt=V.gitGraph,R=h(()=>Q({...dt,...X().gitGraph}),"getConfig"),c=new U(()=>{const e=R(),t=e.mainBranchName,o=e.mainBranchOrder;return{mainBranchName:t,commits:new Map,head:null,branchConfig:new Map([[t,{name:t,order:o}]]),branches:new Map([[t,null]]),currBranch:t,direction:"LR",seq:0,options:{}}});function O(){return Z({length:7})}function N(e,t){const o=Object.create(null);return e.reduce((a,r)=>{const n=t(r);return o[n]||(o[n]=!0,a.push(r)),a},[])}h(O,"getID"),h(N,"uniqBy");var $t=h(function(e){c.records.direction=e},"setDirection"),pt=h(function(e){w.debug("options str",e),e=e==null?void 0:e.trim(),e=e||"{}";try{c.records.options=JSON.parse(e)}catch(t){w.error("error while parsing gitGraph options",t.message)}},"setOptions"),lt=h(function(){return c.records.options},"getOptions"),yt=h(function(e){let t=e.msg,o=e.id;const a=e.type;let r=e.tags;w.info("commit",t,o,a,r),w.debug("Entering commit:",t,o,a,r);const n=R();o=C.sanitizeText(o,n),t=C.sanitizeText(t,n),r=r==null?void 0:r.map(i=>C.sanitizeText(i,n));const s={id:o||c.records.seq+"-"+O(),message:t,seq:c.records.seq++,type:a??g.NORMAL,tags:r??[],parents:c.records.head==null?[]:[c.records.head.id],branch:c.records.currBranch};c.records.head=s,w.info("main branch",n.mainBranchName),c.records.commits.set(s.id,s),c.records.branches.set(c.records.currBranch,s.id),w.debug("in pushCommit "+s.id)},"commit"),gt=h(function(e){let t=e.name;const o=e.order;if(t=C.sanitizeText(t,R()),c.records.branches.has(t))throw new Error(`Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using "checkout ${t}")`);c.records.branches.set(t,c.records.head!=null?c.records.head.id:null),c.records.branchConfig.set(t,{name:t,order:o}),j(t),w.debug("in createBranch")},"branch"),xt=h(e=>{let t=e.branch,o=e.id;const a=e.type,r=e.tags,n=R();t=C.sanitizeText(t,n),o&&(o=C.sanitizeText(o,n));const s=c.records.branches.get(c.records.currBranch),i=c.records.branches.get(t),d=s?c.records.commits.get(s):void 0,l=i?c.records.commits.get(i):void 0;if(d&&l&&d.branch===t)throw new Error(`Cannot merge branch '${t}' into itself.`);if(c.records.currBranch===t){const p=new Error('Incorrect usage of "merge". Cannot merge a branch to itself');throw p.hash={text:`merge ${t}`,token:`merge ${t}`,expected:["branch abc"]},p}if(d===void 0||!d){const p=new Error(`Incorrect usage of "merge". Current branch (${c.records.currBranch})has no commits`);throw p.hash={text:`merge ${t}`,token:`merge ${t}`,expected:["commit"]},p}if(!c.records.branches.has(t)){const p=new Error('Incorrect usage of "merge". Branch to be merged ('+t+") does not exist");throw p.hash={text:`merge ${t}`,token:`merge ${t}`,expected:[`branch ${t}`]},p}if(l===void 0||!l){const p=new Error('Incorrect usage of "merge". Branch to be merged ('+t+") has no commits");throw p.hash={text:`merge ${t}`,token:`merge ${t}`,expected:['"commit"']},p}if(d===l){const p=new Error('Incorrect usage of "merge". Both branches have same head');throw p.hash={text:`merge ${t}`,token:`merge ${t}`,expected:["branch abc"]},p}if(o&&c.records.commits.has(o)){const p=new Error('Incorrect usage of "merge". Commit with id:'+o+" already exists, use different custom Id");throw p.hash={text:`merge ${t} ${o} ${a} ${r==null?void 0:r.join(" ")}`,token:`merge ${t} ${o} ${a} ${r==null?void 0:r.join(" ")}`,expected:[`merge ${t} ${o}_UNIQUE ${a} ${r==null?void 0:r.join(" ")}`]},p}const $=i||"",m={id:o||`${c.records.seq}-${O()}`,message:`merged branch ${t} into ${c.records.currBranch}`,seq:c.records.seq++,parents:c.records.head==null?[]:[c.records.head.id,$],branch:c.records.currBranch,type:g.MERGE,customType:a,customId:!!o,tags:r??[]};c.records.head=m,c.records.commits.set(m.id,m),c.records.branches.set(c.records.currBranch,m.id),w.debug(c.records.branches),w.debug("in mergeBranch")},"merge"),ft=h(function(e){let t=e.id,o=e.targetId,a=e.tags,r=e.parent;w.debug("Entering cherryPick:",t,o,a);const n=R();if(t=C.sanitizeText(t,n),o=C.sanitizeText(o,n),a=a==null?void 0:a.map(d=>C.sanitizeText(d,n)),r=C.sanitizeText(r,n),!t||!c.records.commits.has(t)){const d=new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');throw d.hash={text:`cherryPick ${t} ${o}`,token:`cherryPick ${t} ${o}`,expected:["cherry-pick abc"]},d}const s=c.records.commits.get(t);if(s===void 0||!s)throw new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');if(r&&(!Array.isArray(s.parents)||!s.parents.includes(r)))throw new Error("Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.");const i=s.branch;if(s.type===g.MERGE&&!r)throw new Error("Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.");if(!o||!c.records.commits.has(o)){if(i===c.records.currBranch){const m=new Error('Incorrect usage of "cherryPick". Source commit is already on current branch');throw m.hash={text:`cherryPick ${t} ${o}`,token:`cherryPick ${t} ${o}`,expected:["cherry-pick abc"]},m}const d=c.records.branches.get(c.records.currBranch);if(d===void 0||!d){const m=new Error(`Incorrect usage of "cherry-pick". Current branch (${c.records.currBranch})has no commits`);throw m.hash={text:`cherryPick ${t} ${o}`,token:`cherryPick ${t} ${o}`,expected:["cherry-pick abc"]},m}const l=c.records.commits.get(d);if(l===void 0||!l){const m=new Error(`Incorrect usage of "cherry-pick". Current branch (${c.records.currBranch})has no commits`);throw m.hash={text:`cherryPick ${t} ${o}`,token:`cherryPick ${t} ${o}`,expected:["cherry-pick abc"]},m}const $={id:c.records.seq+"-"+O(),message:`cherry-picked ${s==null?void 0:s.message} into ${c.records.currBranch}`,seq:c.records.seq++,parents:c.records.head==null?[]:[c.records.head.id,s.id],branch:c.records.currBranch,type:g.CHERRY_PICK,tags:a?a.filter(Boolean):[`cherry-pick:${s.id}${s.type===g.MERGE?`|parent:${r}`:""}`]};c.records.head=$,c.records.commits.set($.id,$),c.records.branches.set(c.records.currBranch,$.id),w.debug(c.records.branches),w.debug("in cherryPick")}},"cherryPick"),j=h(function(e){if(e=C.sanitizeText(e,R()),!c.records.branches.has(e)){const t=new Error(`Trying to checkout branch which is not yet created. (Help try using "branch ${e}")`);throw t.hash={text:`checkout ${e}`,token:`checkout ${e}`,expected:[`branch ${e}`]},t}{c.records.currBranch=e;const t=c.records.branches.get(c.records.currBranch);c.records.head=t!==void 0&&t?c.records.commits.get(t)??null:null}},"checkout");function H(e,t,o){const a=e.indexOf(t);a===-1?e.push(o):e.splice(a,1,o)}function z(e){const t=e.reduce((r,n)=>r.seq>n.seq?r:n,e[0]);let o="";e.forEach(function(r){o+=r===t?"	*":"	|"});const a=[o,t.id,t.seq];for(const r in c.records.branches)c.records.branches.get(r)===t.id&&a.push(r);if(w.debug(a.join(" ")),t.parents&&t.parents.length==2&&t.parents[0]&&t.parents[1]){const r=c.records.commits.get(t.parents[0]);H(e,t,r),t.parents[1]&&e.push(c.records.commits.get(t.parents[1]))}else{if(t.parents.length==0)return;if(t.parents[0]){const r=c.records.commits.get(t.parents[0]);H(e,t,r)}}z(e=N(e,r=>r.id))}h(H,"upsert"),h(z,"prettyPrintCommitHistory");var ut=h(function(){w.debug(c.records.commits),z([W()[0]])},"prettyPrint"),bt=h(function(){c.reset(),it()},"clear"),wt=h(function(){return[...c.records.branchConfig.values()].map((e,t)=>e.order!==null&&e.order!==void 0?e:{...e,order:parseFloat(`0.${t}`)}).sort((e,t)=>(e.order??0)-(t.order??0)).map(({name:e})=>({name:e}))},"getBranchesAsObjArray"),Bt=h(function(){return c.records.branches},"getBranches"),Et=h(function(){return c.records.commits},"getCommits"),W=h(function(){const e=[...c.records.commits.values()];return e.forEach(function(t){w.debug(t.id)}),e.sort((t,o)=>t.seq-o.seq),e},"getCommitsArray"),F={commitType:g,getConfig:R,setDirection:$t,setOptions:pt,getOptions:lt,commit:yt,branch:gt,merge:xt,cherryPick:ft,checkout:j,prettyPrint:ut,clear:bt,getBranchesAsObjArray:wt,getBranches:Bt,getCommits:Et,getCommitsArray:W,getCurrentBranch:h(function(){return c.records.currBranch},"getCurrentBranch"),getDirection:h(function(){return c.records.direction},"getDirection"),getHead:h(function(){return c.records.head},"getHead"),setAccTitle:tt,getAccTitle:rt,getAccDescription:et,setAccDescription:ot,setDiagramTitle:nt,getDiagramTitle:at},kt=h((e,t)=>{Y(e,t),e.dir&&t.setDirection(e.dir);for(const o of e.statements)Ct(o,t)},"populate"),Ct=h((e,t)=>{const o={Commit:h(a=>t.commit(Lt(a)),"Commit"),Branch:h(a=>t.branch(Tt(a)),"Branch"),Merge:h(a=>t.merge(Mt(a)),"Merge"),Checkout:h(a=>t.checkout(vt(a)),"Checkout"),CherryPicking:h(a=>t.cherryPick(Pt(a)),"CherryPicking")}[e.$type];o?o(e):w.error(`Unknown statement type: ${e.$type}`)},"parseStatement"),Lt=h(e=>({id:e.id,msg:e.message??"",type:e.type!==void 0?g[e.type]:g.NORMAL,tags:e.tags??void 0}),"parseCommit"),Tt=h(e=>({name:e.name,order:e.order??0}),"parseBranch"),Mt=h(e=>({branch:e.branch,id:e.id??"",type:e.type!==void 0?g[e.type]:void 0,tags:e.tags??void 0}),"parseMerge"),vt=h(e=>e.branch,"parseCheckout"),Pt=h(e=>{var t;return{id:e.id,targetId:"",tags:((t=e.tags)==null?void 0:t.length)===0?void 0:e.tags,parent:e.parent}},"parseCherryPicking"),Rt={parse:h(async e=>{const t=await ht("gitGraph",e);w.debug(t),kt(t,F)},"parse")},q=J(),f=q==null?void 0:q.gitGraph,M=10,v=40,E=new Map,k=new Map,I=new Map,G=[],T=0,y="LR",It=h(()=>{E.clear(),k.clear(),I.clear(),T=0,G=[],y="LR"},"clear"),_=h(e=>{const t=document.createElementNS("http://www.w3.org/2000/svg","text");return(typeof e=="string"?e.split(/\\n|\n|<br\s*\/?>/gi):e).forEach(o=>{const a=document.createElementNS("http://www.w3.org/2000/svg","tspan");a.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),a.setAttribute("dy","1em"),a.setAttribute("x","0"),a.setAttribute("class","row"),a.textContent=o.trim(),t.appendChild(a)}),t},"drawText"),K=h(e=>{let t,o,a;return y==="BT"?(o=h((r,n)=>r<=n,"comparisonFunc"),a=1/0):(o=h((r,n)=>r>=n,"comparisonFunc"),a=0),e.forEach(r=>{var s,i;const n=y==="TB"||y=="BT"?(s=k.get(r))==null?void 0:s.y:(i=k.get(r))==null?void 0:i.x;n!==void 0&&o(n,a)&&(t=r,a=n)}),t},"findClosestParent"),At=h(e=>{let t="",o=1/0;return e.forEach(a=>{const r=k.get(a).y;r<=o&&(t=a,o=r)}),t||void 0},"findClosestParentBT"),Gt=h((e,t,o)=>{let a=o,r=o;const n=[];e.forEach(s=>{const i=t.get(s);if(!i)throw new Error(`Commit not found for key ${s}`);i.parents.length?(a=qt(i),r=Math.max(a,r)):n.push(i),Ht(i,a)}),a=r,n.forEach(s=>{zt(s,a,o)}),e.forEach(s=>{const i=t.get(s);if(i!=null&&i.parents.length){const d=At(i.parents);a=k.get(d).y-v,a<=r&&(r=a);const l=E.get(i.branch).pos,$=a-M;k.set(i.id,{x:l,y:$})}})},"setParallelBTPos"),Ot=h(e=>{var a;const t=K(e.parents.filter(r=>r!==null));if(!t)throw new Error(`Closest parent not found for commit ${e.id}`);const o=(a=k.get(t))==null?void 0:a.y;if(o===void 0)throw new Error(`Closest parent position not found for commit ${e.id}`);return o},"findClosestParentPos"),qt=h(e=>Ot(e)+v,"calculateCommitPosition"),Ht=h((e,t)=>{const o=E.get(e.branch);if(!o)throw new Error(`Branch not found for commit ${e.id}`);const a=o.pos,r=t+M;return k.set(e.id,{x:a,y:r}),{x:a,y:r}},"setCommitPosition"),zt=h((e,t,o)=>{const a=E.get(e.branch);if(!a)throw new Error(`Branch not found for commit ${e.id}`);const r=t+o,n=a.pos;k.set(e.id,{x:n,y:r})},"setRootPosition"),St=h((e,t,o,a,r,n)=>{if(n===g.HIGHLIGHT)e.append("rect").attr("x",o.x-10).attr("y",o.y-10).attr("width",20).attr("height",20).attr("class",`commit ${t.id} commit-highlight${r%8} ${a}-outer`),e.append("rect").attr("x",o.x-6).attr("y",o.y-6).attr("width",12).attr("height",12).attr("class",`commit ${t.id} commit${r%8} ${a}-inner`);else if(n===g.CHERRY_PICK)e.append("circle").attr("cx",o.x).attr("cy",o.y).attr("r",10).attr("class",`commit ${t.id} ${a}`),e.append("circle").attr("cx",o.x-3).attr("cy",o.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${t.id} ${a}`),e.append("circle").attr("cx",o.x+3).attr("cy",o.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${t.id} ${a}`),e.append("line").attr("x1",o.x+3).attr("y1",o.y+1).attr("x2",o.x).attr("y2",o.y-5).attr("stroke","#fff").attr("class",`commit ${t.id} ${a}`),e.append("line").attr("x1",o.x-3).attr("y1",o.y+1).attr("x2",o.x).attr("y2",o.y-5).attr("stroke","#fff").attr("class",`commit ${t.id} ${a}`);else{const s=e.append("circle");if(s.attr("cx",o.x),s.attr("cy",o.y),s.attr("r",t.type===g.MERGE?9:10),s.attr("class",`commit ${t.id} commit${r%8}`),n===g.MERGE){const i=e.append("circle");i.attr("cx",o.x),i.attr("cy",o.y),i.attr("r",6),i.attr("class",`commit ${a} ${t.id} commit${r%8}`)}n===g.REVERSE&&e.append("path").attr("d",`M ${o.x-5},${o.y-5}L${o.x+5},${o.y+5}M${o.x-5},${o.y+5}L${o.x+5},${o.y-5}`).attr("class",`commit ${a} ${t.id} commit${r%8}`)}},"drawCommitBullet"),Dt=h((e,t,o,a)=>{var r;if(t.type!==g.CHERRY_PICK&&(t.customId&&t.type===g.MERGE||t.type!==g.MERGE)&&(f!=null&&f.showCommitLabel)){const n=e.append("g"),s=n.insert("rect").attr("class","commit-label-bkg"),i=n.append("text").attr("x",a).attr("y",o.y+25).attr("class","commit-label").text(t.id),d=(r=i.node())==null?void 0:r.getBBox();if(d&&(s.attr("x",o.posWithOffset-d.width/2-2).attr("y",o.y+13.5).attr("width",d.width+4).attr("height",d.height+4),y==="TB"||y==="BT"?(s.attr("x",o.x-(d.width+16+5)).attr("y",o.y-12),i.attr("x",o.x-(d.width+16)).attr("y",o.y+d.height-12)):i.attr("x",o.posWithOffset-d.width/2),f.rotateCommitLabel))if(y==="TB"||y==="BT")i.attr("transform","rotate(-45, "+o.x+", "+o.y+")"),s.attr("transform","rotate(-45, "+o.x+", "+o.y+")");else{const l=-7.5-(d.width+10)/25*9.5,$=10+d.width/25*8.5;n.attr("transform","translate("+l+", "+$+") rotate(-45, "+a+", "+o.y+")")}}},"drawCommitLabel"),Nt=h((e,t,o,a)=>{var r;if(t.tags.length>0){let n=0,s=0,i=0;const d=[];for(const l of t.tags.reverse()){const $=e.insert("polygon"),m=e.append("circle"),p=e.append("text").attr("y",o.y-16-n).attr("class","tag-label").text(l),x=(r=p.node())==null?void 0:r.getBBox();if(!x)throw new Error("Tag bbox not found");s=Math.max(s,x.width),i=Math.max(i,x.height),p.attr("x",o.posWithOffset-x.width/2),d.push({tag:p,hole:m,rect:$,yOffset:n}),n+=20}for(const{tag:l,hole:$,rect:m,yOffset:p}of d){const x=i/2,u=o.y-19.2-p;if(m.attr("class","tag-label-bkg").attr("points",`
      ${a-s/2-2},${u+2}  
      ${a-s/2-2},${u-2}
      ${o.posWithOffset-s/2-4},${u-x-2}
      ${o.posWithOffset+s/2+4},${u-x-2}
      ${o.posWithOffset+s/2+4},${u+x+2}
      ${o.posWithOffset-s/2-4},${u+x+2}`),$.attr("cy",u).attr("cx",a-s/2+2).attr("r",1.5).attr("class","tag-hole"),y==="TB"||y==="BT"){const b=a+p;m.attr("class","tag-label-bkg").attr("points",`
        ${o.x},${b+2}
        ${o.x},${b-2}
        ${o.x+M},${b-x-2}
        ${o.x+M+s+4},${b-x-2}
        ${o.x+M+s+4},${b+x+2}
        ${o.x+M},${b+x+2}`).attr("transform","translate(12,12) rotate(45, "+o.x+","+a+")"),$.attr("cx",o.x+2).attr("cy",b).attr("transform","translate(12,12) rotate(45, "+o.x+","+a+")"),l.attr("x",o.x+5).attr("y",b+3).attr("transform","translate(14,14) rotate(45, "+o.x+","+a+")")}}}},"drawCommitTags"),jt=h(e=>{switch(e.customType??e.type){case g.NORMAL:return"commit-normal";case g.REVERSE:return"commit-reverse";case g.HIGHLIGHT:return"commit-highlight";case g.MERGE:return"commit-merge";case g.CHERRY_PICK:return"commit-cherry-pick";default:return"commit-normal"}},"getCommitClassType"),Wt=h((e,t,o,a)=>{const r={x:0,y:0};if(!(e.parents.length>0))return t==="TB"?30:t==="BT"?(a.get(e.id)??r).y-v:0;{const n=K(e.parents);if(n){const s=a.get(n)??r;return t==="TB"?s.y+v:t==="BT"?(a.get(e.id)??r).y-v:s.x+v}}return 0},"calculatePosition"),Ft=h((e,t,o)=>{var s,i;const a=y==="BT"&&o?t:t+M,r=y==="TB"||y==="BT"?a:(s=E.get(e.branch))==null?void 0:s.pos,n=y==="TB"||y==="BT"?(i=E.get(e.branch))==null?void 0:i.pos:a;if(n===void 0||r===void 0)throw new Error(`Position were undefined for commit ${e.id}`);return{x:n,y:r,posWithOffset:a}},"getCommitPosition"),D=h((e,t,o)=>{if(!f)throw new Error("GitGraph config not found");const a=e.append("g").attr("class","commit-bullets"),r=e.append("g").attr("class","commit-labels");let n=y==="TB"||y==="BT"?30:0;const s=[...t.keys()],i=(f==null?void 0:f.parallelCommits)??!1,d=h(($,m)=>{var u,b;const p=(u=t.get($))==null?void 0:u.seq,x=(b=t.get(m))==null?void 0:b.seq;return p!==void 0&&x!==void 0?p-x:0},"sortKeys");let l=s.sort(d);y==="BT"&&(i&&Gt(l,t,n),l=l.reverse()),l.forEach($=>{var x;const m=t.get($);if(!m)throw new Error(`Commit not found for key ${$}`);i&&(n=Wt(m,y,n,k));const p=Ft(m,n,i);if(o){const u=jt(m),b=m.customType??m.type,P=((x=E.get(m.branch))==null?void 0:x.index)??0;St(a,m,p,u,P,b),Dt(r,m,p,n),Nt(r,m,p,n)}y==="TB"||y==="BT"?k.set(m.id,{x:p.x,y:p.posWithOffset}):k.set(m.id,{x:p.posWithOffset,y:p.y}),n=y==="BT"&&i?n+v:n+v+M,n>T&&(T=n)})},"drawCommits"),_t=h((e,t,o,a,r)=>{const n=(y==="TB"||y==="BT"?o.x<a.x:o.y<a.y)?t.branch:e.branch,s=h(d=>d.branch===n,"isOnBranchToGetCurve"),i=h(d=>d.seq>e.seq&&d.seq<t.seq,"isBetweenCommits");return[...r.values()].some(d=>i(d)&&s(d))},"shouldRerouteArrow"),A=h((e,t,o=0)=>{const a=e+Math.abs(e-t)/2;if(o>5)return a;if(G.every(n=>Math.abs(n-a)>=10))return G.push(a),a;const r=Math.abs(e-t);return A(e,t-r/5,o+1)},"findLane"),Kt=h((e,t,o,a)=>{var x,u,b,P,S;const r=k.get(t.id),n=k.get(o.id);if(r===void 0||n===void 0)throw new Error(`Commit positions not found for commits ${t.id} and ${o.id}`);const s=_t(t,o,r,n,a);let i,d="",l="",$=0,m=0,p=(x=E.get(o.branch))==null?void 0:x.index;if(o.type===g.MERGE&&t.id!==o.parents[0]&&(p=(u=E.get(t.branch))==null?void 0:u.index),s){d="A 10 10, 0, 0, 0,",l="A 10 10, 0, 0, 1,",$=10,m=10;const L=r.y<n.y?A(r.y,n.y):A(n.y,r.y),B=r.x<n.x?A(r.x,n.x):A(n.x,r.x);y==="TB"?r.x<n.x?i=`M ${r.x} ${r.y} L ${B-$} ${r.y} ${l} ${B} ${r.y+m} L ${B} ${n.y-$} ${d} ${B+m} ${n.y} L ${n.x} ${n.y}`:(p=(b=E.get(t.branch))==null?void 0:b.index,i=`M ${r.x} ${r.y} L ${B+$} ${r.y} ${d} ${B} ${r.y+m} L ${B} ${n.y-$} ${l} ${B-m} ${n.y} L ${n.x} ${n.y}`):y==="BT"?r.x<n.x?i=`M ${r.x} ${r.y} L ${B-$} ${r.y} ${d} ${B} ${r.y-m} L ${B} ${n.y+$} ${l} ${B+m} ${n.y} L ${n.x} ${n.y}`:(p=(P=E.get(t.branch))==null?void 0:P.index,i=`M ${r.x} ${r.y} L ${B+$} ${r.y} ${l} ${B} ${r.y-m} L ${B} ${n.y+$} ${d} ${B-m} ${n.y} L ${n.x} ${n.y}`):r.y<n.y?i=`M ${r.x} ${r.y} L ${r.x} ${L-$} ${d} ${r.x+m} ${L} L ${n.x-$} ${L} ${l} ${n.x} ${L+m} L ${n.x} ${n.y}`:(p=(S=E.get(t.branch))==null?void 0:S.index,i=`M ${r.x} ${r.y} L ${r.x} ${L+$} ${l} ${r.x+m} ${L} L ${n.x-$} ${L} ${d} ${n.x} ${L-m} L ${n.x} ${n.y}`)}else d="A 20 20, 0, 0, 0,",l="A 20 20, 0, 0, 1,",$=20,m=20,y==="TB"?(r.x<n.x&&(i=o.type===g.MERGE&&t.id!==o.parents[0]?`M ${r.x} ${r.y} L ${r.x} ${n.y-$} ${d} ${r.x+m} ${n.y} L ${n.x} ${n.y}`:`M ${r.x} ${r.y} L ${n.x-$} ${r.y} ${l} ${n.x} ${r.y+m} L ${n.x} ${n.y}`),r.x>n.x&&(d="A 20 20, 0, 0, 0,",l="A 20 20, 0, 0, 1,",$=20,m=20,i=o.type===g.MERGE&&t.id!==o.parents[0]?`M ${r.x} ${r.y} L ${r.x} ${n.y-$} ${l} ${r.x-m} ${n.y} L ${n.x} ${n.y}`:`M ${r.x} ${r.y} L ${n.x+$} ${r.y} ${d} ${n.x} ${r.y+m} L ${n.x} ${n.y}`),r.x===n.x&&(i=`M ${r.x} ${r.y} L ${n.x} ${n.y}`)):y==="BT"?(r.x<n.x&&(i=o.type===g.MERGE&&t.id!==o.parents[0]?`M ${r.x} ${r.y} L ${r.x} ${n.y+$} ${l} ${r.x+m} ${n.y} L ${n.x} ${n.y}`:`M ${r.x} ${r.y} L ${n.x-$} ${r.y} ${d} ${n.x} ${r.y-m} L ${n.x} ${n.y}`),r.x>n.x&&(d="A 20 20, 0, 0, 0,",l="A 20 20, 0, 0, 1,",$=20,m=20,i=o.type===g.MERGE&&t.id!==o.parents[0]?`M ${r.x} ${r.y} L ${r.x} ${n.y+$} ${d} ${r.x-m} ${n.y} L ${n.x} ${n.y}`:`M ${r.x} ${r.y} L ${n.x-$} ${r.y} ${d} ${n.x} ${r.y-m} L ${n.x} ${n.y}`),r.x===n.x&&(i=`M ${r.x} ${r.y} L ${n.x} ${n.y}`)):(r.y<n.y&&(i=o.type===g.MERGE&&t.id!==o.parents[0]?`M ${r.x} ${r.y} L ${n.x-$} ${r.y} ${l} ${n.x} ${r.y+m} L ${n.x} ${n.y}`:`M ${r.x} ${r.y} L ${r.x} ${n.y-$} ${d} ${r.x+m} ${n.y} L ${n.x} ${n.y}`),r.y>n.y&&(i=o.type===g.MERGE&&t.id!==o.parents[0]?`M ${r.x} ${r.y} L ${n.x-$} ${r.y} ${d} ${n.x} ${r.y-m} L ${n.x} ${n.y}`:`M ${r.x} ${r.y} L ${r.x} ${n.y+$} ${l} ${r.x+m} ${n.y} L ${n.x} ${n.y}`),r.y===n.y&&(i=`M ${r.x} ${r.y} L ${n.x} ${n.y}`));if(i===void 0)throw new Error("Line definition not found");e.append("path").attr("d",i).attr("class","arrow arrow"+p%8)},"drawArrow"),Yt=h((e,t)=>{const o=e.append("g").attr("class","commit-arrows");[...t.keys()].forEach(a=>{const r=t.get(a);r.parents&&r.parents.length>0&&r.parents.forEach(n=>{Kt(o,t.get(n),r,t)})})},"drawArrows"),Ut=h((e,t)=>{const o=e.append("g");t.forEach((a,r)=>{var x;const n=r%8,s=(x=E.get(a.name))==null?void 0:x.pos;if(s===void 0)throw new Error(`Position not found for branch ${a.name}`);const i=o.append("line");i.attr("x1",0),i.attr("y1",s),i.attr("x2",T),i.attr("y2",s),i.attr("class","branch branch"+n),y==="TB"?(i.attr("y1",30),i.attr("x1",s),i.attr("y2",T),i.attr("x2",s)):y==="BT"&&(i.attr("y1",T),i.attr("x1",s),i.attr("y2",30),i.attr("x2",s)),G.push(s);const d=a.name,l=_(d),$=o.insert("rect"),m=o.insert("g").attr("class","branchLabel").insert("g").attr("class","label branch-label"+n);m.node().appendChild(l);const p=l.getBBox();$.attr("class","branchLabelBkg label"+n).attr("rx",4).attr("ry",4).attr("x",-p.width-4-((f==null?void 0:f.rotateCommitLabel)===!0?30:0)).attr("y",-p.height/2+8).attr("width",p.width+18).attr("height",p.height+4),m.attr("transform","translate("+(-p.width-14-((f==null?void 0:f.rotateCommitLabel)===!0?30:0))+", "+(s-p.height/2-1)+")"),y==="TB"?($.attr("x",s-p.width/2-10).attr("y",0),m.attr("transform","translate("+(s-p.width/2-5)+", 0)")):y==="BT"?($.attr("x",s-p.width/2-10).attr("y",T),m.attr("transform","translate("+(s-p.width/2-5)+", "+T+")")):$.attr("transform","translate(-19, "+(s-p.height/2)+")")})},"drawBranches"),Vt=h(function(e,t,o,a,r){return E.set(e,{pos:t,index:o}),t+=50+(r?40:0)+(y==="TB"||y==="BT"?a.width/2:0)},"setBranchPosition"),Ur={parser:Rt,db:F,renderer:{draw:h(function(e,t,o,a){if(It(),w.debug("in gitgraph renderer",e+`
`,"id:",t,o),!f)throw new Error("GitGraph config not found");const r=f.rotateCommitLabel??!1,n=a.db;I=n.getCommits();const s=n.getBranchesAsObjArray();y=n.getDirection();const i=st(`[id="${t}"]`);let d=0;s.forEach((l,$)=>{var P;const m=_(l.name),p=i.append("g"),x=p.insert("g").attr("class","branchLabel"),u=x.insert("g").attr("class","label branch-label");(P=u.node())==null||P.appendChild(m);const b=m.getBBox();d=Vt(l.name,d,$,b,r),u.remove(),x.remove(),p.remove()}),D(i,I,!1),f.showBranches&&Ut(i,s),Yt(i,I),D(i,I,!0),ct.insertTitle(i,"gitTitleText",f.titleTopMargin??0,n.getDiagramTitle()),mt(void 0,i,f.diagramPadding,f.useMaxWidth)},"draw")},styles:h(e=>`
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: lightgrey;
    color: lightgrey;
    font-family: 'trebuchet ms', verdana, arial, sans-serif;
    font-family: var(--mermaid-font-family);
  }
  ${[0,1,2,3,4,5,6,7].map(t=>`
        .branch-label${t} { fill: ${e["gitBranchLabel"+t]}; }
        .commit${t} { stroke: ${e["git"+t]}; fill: ${e["git"+t]}; }
        .commit-highlight${t} { stroke: ${e["gitInv"+t]}; fill: ${e["gitInv"+t]}; }
        .label${t}  { fill: ${e["git"+t]}; }
        .arrow${t} { stroke: ${e["git"+t]}; }
        `).join(`
`)}

  .branch {
    stroke-width: 1;
    stroke: ${e.lineColor};
    stroke-dasharray: 2;
  }
  .commit-label { font-size: ${e.commitLabelFontSize}; fill: ${e.commitLabelColor};}
  .commit-label-bkg { font-size: ${e.commitLabelFontSize}; fill: ${e.commitLabelBackground}; opacity: 0.5; }
  .tag-label { font-size: ${e.tagLabelFontSize}; fill: ${e.tagLabelColor};}
  .tag-label-bkg { fill: ${e.tagLabelBackground}; stroke: ${e.tagLabelBorder}; }
  .tag-hole { fill: ${e.textColor}; }

  .commit-merge {
    stroke: ${e.primaryColor};
    fill: ${e.primaryColor};
  }
  .commit-reverse {
    stroke: ${e.primaryColor};
    fill: ${e.primaryColor};
    stroke-width: 3;
  }
  .commit-highlight-outer {
  }
  .commit-highlight-inner {
    stroke: ${e.primaryColor};
    fill: ${e.primaryColor};
  }

  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}
  .gitTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
`,"getStyles")};export{Ur as diagram};
