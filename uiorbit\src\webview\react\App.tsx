import React, { useState, useEffect, useRef } from 'react';
import { MessageBubble } from './components/MessageBubble';
import { MessageInput } from './components/MessageInput';
import { TypingIndicator } from './components/TypingIndicator';
import { WelcomeMessage } from './components/WelcomeMessage';
import './styles/App.css';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'assistant';
  timestamp: string;
  isError?: boolean;
}

declare global {
  interface Window {
    acquireVsCodeApi: () => any;
  }
}

export const App: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const vscodeRef = useRef<any>(null);

  // Initialize VS Code API
  useEffect(() => {
    try {
      vscodeRef.current = window.acquireVsCodeApi();
      console.log('VS Code API acquired successfully');

      // Send ready message
      vscodeRef.current.postMessage({ type: 'ready' });
      setIsReady(true);
    } catch (error) {
      console.error('Failed to acquire VS Code API:', error);
    }
  }, []);

  // Handle messages from extension
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      console.log('Received message from extension:', message);

      switch (message.type) {
        case 'assistant-message':
          addMessage(message.text, 'assistant', message.isError);
          setIsTyping(false);
          break;
        case 'assistant-typing':
          setIsTyping(message.isTyping);
          break;
        case 'welcome':
          addMessage(message.text, 'assistant');
          break;
        default:
          console.log('Unknown message type:', message.type);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const addMessage = (text: string, sender: 'user' | 'assistant', isError = false) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      text,
      sender,
      timestamp: new Date().toISOString(),
      isError
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleSendMessage = (text: string) => {
    if (!text.trim() || !vscodeRef.current) return;

    // Add user message to chat
    addMessage(text, 'user');

    // Send to extension
    vscodeRef.current.postMessage({
      type: 'chat-message',
      text: text
    });

    // Show typing indicator
    setIsTyping(true);
  };

  return (
    <div className="chat-app">
      <div className="chat-header">
        <h2>🚀 UIOrbit Chat</h2>
        <p>Your AI-powered frontend development assistant</p>
        <div className={`status ${isReady ? 'ready' : 'loading'}`}>
          {isReady ? '🟢 Ready to help!' : '🟡 Connecting...'}
        </div>
      </div>

      <div className="chat-messages">
        {messages.length === 0 && <WelcomeMessage />}

        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message.text}
            sender={message.sender}
            timestamp={message.timestamp}
            isError={message.isError}
          />
        ))}

        {isTyping && <TypingIndicator />}
        <div ref={messagesEndRef} />
      </div>

      <MessageInput
        onSendMessage={handleSendMessage}
        disabled={!isReady || isTyping}
        placeholder="Ask me anything about frontend development..."
      />
    </div>
  );
};
