import{F as u,R as c,a as l}from"./types-DDm27S8B.js";import{C as x,a as E}from"./chat-types-D7sox8tw.js";function $(e){return function(t){try{if(isNaN(t.getTime()))return"Unknown time";const s=new Date().getTime()-t.getTime(),o=Math.floor(s/1e3),r=Math.floor(o/60),n=Math.floor(r/60),a=Math.floor(n/24);return o<60?`${o}s ago`:r<60?`${r}m ago`:n<24?`${n}h ago`:a<30?`${a}d ago`:t.toLocaleDateString()}catch(s){return console.error("Error formatting date:",s),"Unknown time"}}(new Date(e))}function k(e,t){const s=setInterval(()=>{const o=e.getTime()-Date.now();if(o<=0)return void clearInterval(s);const r=Math.floor(o/1e3),n=Math.floor(r/60),a=Math.floor(n/60),i=Math.floor(a/24);t(r<60?`${r}s`:n<60?`${n}m ${r%60}s`:a<24?`${a}h`:i<30?`${i}d`:"1mo")},1e3);return()=>clearInterval(s)}function I(e){if(e===void 0)return"neutral";switch(e){case l.agentPending:case l.agentStarting:case l.agentRunning:return"info";case l.agentIdle:return"success";case l.agentFailed:return"error";default:return"neutral"}}function P(e){if(e===void 0)return"neutral";switch(e){case c.workspaceRunning:return"info";case c.workspacePausing:case c.workspacePaused:case c.workspaceResuming:default:return"neutral"}}function v(e,t,s){if(t===c.workspaceResuming)return"Resuming";switch(e){case l.agentStarting:return"Starting";case l.agentRunning:return"Running";case l.agentIdle:return s?"Unread":"Idle";case l.agentPending:return"Pending";case l.agentFailed:return"Failed";default:return"Unknown"}}const d=e=>C(e),C=e=>{const t={};for(const n of e){const a=n.old_path||n.new_path;if(t[a])t[a].finalExists=n.new_path!=="",t[a].finalContent=n.new_path!==""?n.new_contents:"",t[a].finalPath=n.new_path||n.old_path,t[a].latestChange=n,n.change_type===u.deleted&&n.old_contents!==""&&(t[a].originalContent=n.old_contents);else{const i=n.old_path!=="";t[a]={originalExists:i,originalContent:i?n.old_contents:"",finalExists:n.new_path!=="",finalContent:n.new_path!==""?n.new_contents:"",finalPath:n.new_path||n.old_path,latestChange:n}}}const s=[];for(const[n,a]of Object.entries(t))if(a.originalExists!==a.finalExists||a.originalExists&&a.finalExists&&a.originalContent!==a.finalContent){const i={id:a.latestChange.id,old_path:a.originalExists?n:"",new_path:a.finalExists?a.finalPath:"",old_contents:a.originalContent,new_contents:a.finalContent,change_type:(o=a.originalExists,r=a.finalExists,!o&&r?u.added:o&&!r?u.deleted:u.modified)};s.push(i)}var o,r;return s},T=e=>{const t=e.flatMap(s=>s.changed_files);return d(t)},S=(e,t)=>{var o;const s=h(e,t);return((o=e[s])==null?void 0:o.exchange.request_message)??""},h=(e,t)=>{var s;return t<0||t>=e.length?-1:(s=e[t])!=null&&s.exchange.request_message?t:e.slice(0,t).findLastIndex(o=>o.exchange.request_message)},m=(e,t)=>{const s=e.slice(t+1).findIndex(o=>o.exchange.request_message);return s===-1?e.length:t+s+1},U=(e,t)=>{if(t<0||t>=e.length)return[];if(h(e,t)===-1){const r=e.flatMap(n=>n.changed_files);return d(r)}const s=((r,n)=>{const a=h(r,n);let i=m(r,n);const g=a===-1?0:a+1;return r.slice(g,i)})(e,t),o=s.flatMap(r=>r.changed_files);return d(o)},q=(e,t)=>{var i,g;const s=m(e,t),o=e.slice(t,s),r=(i=e[t].exchange.response_nodes)==null?void 0:i.find(f=>f.type===x.TOOL_USE);if(!r)return[];const n=(g=r.tool_use)==null?void 0:g.tool_use_id;if(!n)return[];const a=o.find(f=>{var _;return(_=f.exchange.request_nodes)==null?void 0:_.some(p=>{var w;return p.type===E.TOOL_RESULT&&((w=p.tool_result_node)==null?void 0:w.tool_use_id)===n})});return a?a.changed_files:[]};export{T as a,I as b,P as c,$ as d,q as e,S as f,v as g,U as h,k as s};
