/**
 * Unit tests for ServiceRegistry
 */

import { ServiceRegistry } from '../ServiceRegistry';

describe('ServiceRegistry', () => {
  let serviceRegistry: ServiceRegistry;

  beforeEach(() => {
    serviceRegistry = new ServiceRegistry();
  });

  afterEach(async () => {
    await serviceRegistry.dispose();
  });

  describe('Service Registration', () => {
    it('should register a service successfully', () => {
      const mockService = { name: 'test-service' };
      
      serviceRegistry.register('test', mockService);
      
      expect(serviceRegistry.get('test')).toBe(mockService);
    });

    it('should warn when registering duplicate service', () => {
      const mockService1 = { name: 'service-1' };
      const mockService2 = { name: 'service-2' };

      serviceRegistry.register('test', mockService1);

      // Should not throw, but should warn and overwrite
      expect(() => {
        serviceRegistry.register('test', mockService2);
      }).not.toThrow();

      // Should have the new service
      expect(serviceRegistry.get('test')).toBe(mockService2);
    });

    it('should allow overriding service (warns but overwrites)', () => {
      const mockService1 = { name: 'service-1' };
      const mockService2 = { name: 'service-2' };

      serviceRegistry.register('test', mockService1);
      serviceRegistry.register('test', mockService2); // Should warn but overwrite

      expect(serviceRegistry.get('test')).toBe(mockService2);
    });
  });

  describe('Service Retrieval', () => {
    it('should return undefined for non-existent service', () => {
      expect(serviceRegistry.get('non-existent')).toBeUndefined();
    });

    it('should return correct service for existing key', () => {
      const mockService = { name: 'test-service' };
      serviceRegistry.register('test', mockService);
      
      expect(serviceRegistry.get('test')).toBe(mockService);
    });

    it('should support generic type casting', () => {
      interface TestService {
        name: string;
        method(): void;
      }
      
      const mockService: TestService = {
        name: 'test-service',
        method: jest.fn(),
      };
      
      serviceRegistry.register('test', mockService);
      
      const retrieved = serviceRegistry.get<TestService>('test');
      expect(retrieved?.name).toBe('test-service');
      expect(typeof retrieved?.method).toBe('function');
    });
  });

  describe('Service Unregistration', () => {
    it('should unregister service successfully', () => {
      const mockService = { name: 'test-service' };
      serviceRegistry.register('test', mockService);
      
      expect(serviceRegistry.get('test')).toBe(mockService);
      
      serviceRegistry.unregister('test');
      
      expect(serviceRegistry.get('test')).toBeUndefined();
    });

    it('should not throw when unregistering non-existent service', () => {
      expect(() => {
        serviceRegistry.unregister('non-existent');
      }).not.toThrow();
    });
  });

  describe('Service Lifecycle', () => {
    it('should check if service exists', () => {
      expect(serviceRegistry.has('test')).toBe(false);
      
      serviceRegistry.register('test', { name: 'test' });
      
      expect(serviceRegistry.has('test')).toBe(true);
    });

    it('should list all registered service names', () => {
      serviceRegistry.register('service1', { name: 'service1' });
      serviceRegistry.register('service2', { name: 'service2' });

      const services = serviceRegistry.getServiceNames();

      expect(services).toContain('service1');
      expect(services).toContain('service2');
      expect(services).toHaveLength(2);
    });

    it('should dispose all services with dispose method', async () => {
      const mockService1 = {
        name: 'service1',
        dispose: jest.fn(() => Promise.resolve()),
      };
      const mockService2 = {
        name: 'service2',
        dispose: jest.fn(() => Promise.resolve()),
      };
      
      serviceRegistry.register('service1', mockService1);
      serviceRegistry.register('service2', mockService2);
      
      await serviceRegistry.dispose();
      
      expect(mockService1.dispose).toHaveBeenCalled();
      expect(mockService2.dispose).toHaveBeenCalled();
    });

    it('should handle services without dispose method during disposal', async () => {
      const mockService = { name: 'service-without-dispose' };
      
      serviceRegistry.register('test', mockService);
      
      await expect(serviceRegistry.dispose()).resolves.not.toThrow();
    });

    it('should handle disposal errors gracefully', async () => {
      const mockService = {
        name: 'failing-service',
        dispose: jest.fn(() => Promise.reject(new Error('Disposal failed'))),
      };
      
      serviceRegistry.register('test', mockService);
      
      // Should not throw even if service disposal fails
      await expect(serviceRegistry.dispose()).resolves.not.toThrow();
      expect(mockService.dispose).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty string as service name', () => {
      const mockService = { name: 'empty-key-service' };
      
      serviceRegistry.register('', mockService);
      
      expect(serviceRegistry.get('')).toBe(mockService);
    });

    it('should handle special characters in service names', () => {
      const mockService = { name: 'special-service' };
      const specialKey = 'service.with-special_chars@123';
      
      serviceRegistry.register(specialKey, mockService);
      
      expect(serviceRegistry.get(specialKey)).toBe(mockService);
    });

    it('should maintain service references correctly', () => {
      const mockService = { name: 'reference-test', counter: 0 };
      
      serviceRegistry.register('test', mockService);
      
      const retrieved1 = serviceRegistry.get('test');
      const retrieved2 = serviceRegistry.get('test');
      
      // Should be the same reference
      expect(retrieved1).toBe(retrieved2);
      expect(retrieved1).toBe(mockService);
      
      // Modifying through one reference should affect all
      if (retrieved1) {
        (retrieved1 as any).counter = 42;
      }
      
      expect((retrieved2 as any)?.counter).toBe(42);
      expect(mockService.counter).toBe(42);
    });
  });
});
