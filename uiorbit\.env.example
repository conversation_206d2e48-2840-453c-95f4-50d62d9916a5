# UIOrbit Extension Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# AI API CONFIGURATION
# =============================================================================

# OpenAI Configuration (Primary AI Provider)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.7

# Anthropic Claude Configuration (Phase 2)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4096

# =============================================================================
# VECTOR DATABASE & EMBEDDINGS
# =============================================================================

# Vector Database Configuration
VECTOR_DB_PATH=./data/vectors.db
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_DIMENSIONS=1536
SIMILARITY_THRESHOLD=0.8

# =============================================================================
# EXTENSION CONFIGURATION
# =============================================================================

# Extension Metadata
EXTENSION_NAME=UIOrbit
EXTENSION_VERSION=1.0.0
EXTENSION_DISPLAY_NAME=UIOrbit - AI Frontend Developer
EXTENSION_DESCRIPTION=World-class AI assistant for modern UI/UX development

# Development Settings
DEBUG_MODE=true
LOG_LEVEL=debug
DEVELOPMENT_MODE=true

# =============================================================================
# RATE LIMITING & PERFORMANCE
# =============================================================================

# API Rate Limiting
API_RATE_LIMIT_PER_MINUTE=60
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT_MS=30000
RETRY_ATTEMPTS=3

# Performance Settings
INDEXING_BATCH_SIZE=100
CONTEXT_WINDOW_SIZE=8192
CACHE_TTL_MINUTES=60
MAX_MEMORY_USAGE_MB=512

# =============================================================================
# CODEBASE ANALYSIS
# =============================================================================

# File Processing
MAX_FILE_SIZE_MB=10
SUPPORTED_EXTENSIONS=.js,.jsx,.ts,.tsx,.vue,.svelte,.css,.scss,.sass,.less,.html,.json
IGNORE_PATTERNS=node_modules,dist,build,.git,.vscode,coverage,*.log

# AST Analysis
ENABLE_AST_ANALYSIS=true
ENABLE_DEPENDENCY_GRAPH=true
ENABLE_SEMANTIC_ANALYSIS=true

# =============================================================================
# UI/UX KNOWLEDGE BASE
# =============================================================================

# Trends & Patterns
TRENDS_UPDATE_INTERVAL_DAYS=30
KNOWLEDGE_BASE_VERSION=2024.1
ENABLE_TREND_UPDATES=true

# Component Generation
DEFAULT_FRAMEWORK=react
DEFAULT_STYLING=tailwind
ENABLE_ACCESSIBILITY=true
ENABLE_RESPONSIVE_DESIGN=true

# =============================================================================
# SUPABASE INTEGRATION (Phase 3-4)
# =============================================================================

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database Configuration
ENABLE_CLOUD_SYNC=false
ENABLE_TEAM_FEATURES=false
ENABLE_ANALYTICS=false

# =============================================================================
# DESIGN TOOL INTEGRATIONS (Phase 3-4)
# =============================================================================

# Figma Integration
FIGMA_ACCESS_TOKEN=your_figma_token
FIGMA_TEAM_ID=your_figma_team_id

# Sketch Integration
SKETCH_API_KEY=your_sketch_api_key

# =============================================================================
# MONITORING & ANALYTICS (Phase 4)
# =============================================================================

# Error Monitoring
SENTRY_DSN=your_sentry_dsn
ENABLE_ERROR_REPORTING=false

# Usage Analytics
ANALYTICS_API_KEY=your_analytics_key
ENABLE_USAGE_ANALYTICS=false

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=false
PERFORMANCE_SAMPLE_RATE=0.1

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# API Security
ENABLE_API_KEY_VALIDATION=true
ENABLE_REQUEST_SIGNING=false
API_KEY_ROTATION_DAYS=90

# Data Privacy
ENABLE_LOCAL_PROCESSING=true
ENABLE_DATA_ENCRYPTION=false
PRIVACY_MODE=standard

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Phase 1 Features
ENABLE_CHAT_INTERFACE=true
ENABLE_FILE_OPERATIONS=true
ENABLE_BASIC_AI=true

# Phase 2 Features (Disabled initially)
ENABLE_CODEBASE_INDEXING=false
ENABLE_VECTOR_SEARCH=false
ENABLE_AST_ANALYSIS=false

# Phase 3 Features (Disabled initially)
ENABLE_DESIGN_ANALYSIS=false
ENABLE_TREND_SUGGESTIONS=false
ENABLE_ADVANCED_GENERATION=false

# Phase 4 Features (Disabled initially)
ENABLE_AGENT_MODE=false
ENABLE_TEAM_COLLABORATION=false
ENABLE_ADVANCED_TESTING=false
