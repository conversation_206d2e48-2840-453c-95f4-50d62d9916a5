import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from '../utils/Logger';
import { FileOperationsService } from './FileOperationsService';

export interface ProjectAnalysis {
  framework: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla' | 'unknown';
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'unknown';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components' | 'emotion' | 'unknown';
  buildTool: 'vite' | 'webpack' | 'parcel' | 'rollup' | 'unknown';
  typescript: boolean;
  components: ComponentMap;
  dependencies: DependencyInfo;
  structure: ProjectStructure;
  patterns: DesignPattern[];
}

export interface ComponentMap {
  [componentName: string]: ComponentInfo;
}

export interface ComponentInfo {
  path: string;
  type: 'functional' | 'class' | 'unknown';
  framework: string;
  props?: string[];
  hooks?: string[];
  imports?: string[];
  exports?: string[];
}

export interface DependencyInfo {
  production: Record<string, string>;
  development: Record<string, string>;
  frameworks: string[];
  uiLibraries: string[];
  buildTools: string[];
  testingTools: string[];
}

export interface ProjectStructure {
  rootPath: string;
  srcPath?: string;
  publicPath?: string;
  componentsPath?: string;
  stylesPath?: string;
  testsPath?: string;
  configFiles: string[];
  entryPoints: string[];
}

export interface DesignPattern {
  name: string;
  type: 'architectural' | 'component' | 'styling' | 'state';
  description: string;
  files: string[];
  confidence: number;
}

/**
 * Workspace Analysis Service for UIOrbit
 * Analyzes project structure, dependencies, and patterns
 */
export class WorkspaceAnalysisService {
  private fileOps: FileOperationsService;
  private analysisCache: Map<string, ProjectAnalysis> = new Map();

  constructor(fileOperationsService: FileOperationsService) {
    this.fileOps = fileOperationsService;
  }

  /**
   * Analyze the current workspace
   */
  async analyzeWorkspace(): Promise<ProjectAnalysis> {
    try {
      Logger.info('Starting workspace analysis...');

      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        throw new Error('No workspace folder found');
      }

      const rootPath = workspaceFolder.uri.fsPath;
      const cacheKey = rootPath;

      // Check cache first
      if (this.analysisCache.has(cacheKey)) {
        Logger.debug('Returning cached analysis');
        return this.analysisCache.get(cacheKey)!;
      }

      // Perform analysis
      const analysis: ProjectAnalysis = {
        framework: 'unknown',
        packageManager: 'unknown',
        styling: 'unknown',
        buildTool: 'unknown',
        typescript: false,
        components: {},
        dependencies: {
          production: {},
          development: {},
          frameworks: [],
          uiLibraries: [],
          buildTools: [],
          testingTools: []
        },
        structure: {
          rootPath,
          configFiles: [],
          entryPoints: []
        },
        patterns: []
      };

      // Analyze package.json
      await this.analyzePackageJson(rootPath, analysis);

      // Analyze project structure
      await this.analyzeProjectStructure(rootPath, analysis);

      // Detect framework and build tools
      await this.detectFrameworkAndTools(analysis);

      // Analyze components
      await this.analyzeComponents(analysis);

      // Detect design patterns
      await this.detectDesignPatterns(analysis);

      // Cache the result
      this.analysisCache.set(cacheKey, analysis);

      Logger.info('Workspace analysis completed:', {
        framework: analysis.framework,
        packageManager: analysis.packageManager,
        styling: analysis.styling,
        componentCount: Object.keys(analysis.components).length
      });

      return analysis;

    } catch (error) {
      Logger.error('Error analyzing workspace:', error);
      throw error;
    }
  }

  /**
   * Analyze package.json file
   */
  private async analyzePackageJson(rootPath: string, analysis: ProjectAnalysis): Promise<void> {
    try {
      const packageJsonPath = path.join(rootPath, 'package.json');
      const result = await this.fileOps.readFile(packageJsonPath);

      if (!result.success) {
        Logger.debug('No package.json found');
        return;
      }

      const packageJson = JSON.parse(result.data.content);
      
      // Extract dependencies
      analysis.dependencies.production = packageJson.dependencies || {};
      analysis.dependencies.development = packageJson.devDependencies || {};

      // Detect package manager
      analysis.packageManager = await this.detectPackageManager(rootPath);

      // Categorize dependencies
      this.categorizeDependencies(analysis);

      Logger.debug('Package.json analyzed successfully');

    } catch (error) {
      Logger.warn('Error analyzing package.json:', error);
    }
  }

  /**
   * Analyze project structure
   */
  private async analyzeProjectStructure(rootPath: string, analysis: ProjectAnalysis): Promise<void> {
    try {
      const structure = analysis.structure;

      // Common paths to check
      const pathsToCheck = [
        'src',
        'public',
        'components',
        'styles',
        'assets',
        'tests',
        '__tests__',
        'test'
      ];

      for (const pathName of pathsToCheck) {
        const fullPath = path.join(rootPath, pathName);
        if (await fs.pathExists(fullPath)) {
          switch (pathName) {
            case 'src':
              structure.srcPath = fullPath;
              break;
            case 'public':
              structure.publicPath = fullPath;
              break;
            case 'components':
              structure.componentsPath = fullPath;
              break;
            case 'styles':
              structure.stylesPath = fullPath;
              break;
            case 'tests':
            case '__tests__':
            case 'test':
              structure.testsPath = fullPath;
              break;
          }
        }
      }

      // Find config files
      const configFiles = [
        'vite.config.js', 'vite.config.ts',
        'webpack.config.js', 'webpack.config.ts',
        'rollup.config.js', 'rollup.config.ts',
        'tsconfig.json',
        'tailwind.config.js', 'tailwind.config.ts',
        'jest.config.js', 'jest.config.ts',
        '.eslintrc.js', '.eslintrc.json',
        '.prettierrc', '.prettierrc.json'
      ];

      for (const configFile of configFiles) {
        const configPath = path.join(rootPath, configFile);
        if (await fs.pathExists(configPath)) {
          structure.configFiles.push(configFile);
        }
      }

      // Find entry points
      const entryPoints = [
        'index.html',
        'src/main.js', 'src/main.ts',
        'src/index.js', 'src/index.ts',
        'src/App.js', 'src/App.ts', 'src/App.jsx', 'src/App.tsx'
      ];

      for (const entryPoint of entryPoints) {
        const entryPath = path.join(rootPath, entryPoint);
        if (await fs.pathExists(entryPath)) {
          structure.entryPoints.push(entryPoint);
        }
      }

      Logger.debug('Project structure analyzed');

    } catch (error) {
      Logger.warn('Error analyzing project structure:', error);
    }
  }

  /**
   * Detect framework and build tools
   */
  private async detectFrameworkAndTools(analysis: ProjectAnalysis): Promise<void> {
    const deps = { ...analysis.dependencies.production, ...analysis.dependencies.development };

    // Detect framework
    if (deps.react) {
      analysis.framework = 'react';
    } else if (deps.vue) {
      analysis.framework = 'vue';
    } else if (deps['@angular/core']) {
      analysis.framework = 'angular';
    } else if (deps.svelte) {
      analysis.framework = 'svelte';
    } else {
      analysis.framework = 'vanilla';
    }

    // Detect TypeScript
    analysis.typescript = !!(deps.typescript || deps['@types/node']);

    // Detect build tool
    if (deps.vite || analysis.structure.configFiles.some(f => f.startsWith('vite.config'))) {
      analysis.buildTool = 'vite';
    } else if (deps.webpack || analysis.structure.configFiles.some(f => f.startsWith('webpack.config'))) {
      analysis.buildTool = 'webpack';
    } else if (deps.parcel) {
      analysis.buildTool = 'parcel';
    } else if (deps.rollup || analysis.structure.configFiles.some(f => f.startsWith('rollup.config'))) {
      analysis.buildTool = 'rollup';
    }

    // Detect styling approach
    if (deps.tailwindcss || analysis.structure.configFiles.some(f => f.startsWith('tailwind.config'))) {
      analysis.styling = 'tailwind';
    } else if (deps['styled-components']) {
      analysis.styling = 'styled-components';
    } else if (deps['@emotion/react'] || deps['@emotion/styled']) {
      analysis.styling = 'emotion';
    } else if (deps.sass || deps.scss) {
      analysis.styling = 'scss';
    } else {
      analysis.styling = 'css';
    }

    Logger.debug('Framework and tools detected:', {
      framework: analysis.framework,
      buildTool: analysis.buildTool,
      styling: analysis.styling,
      typescript: analysis.typescript
    });
  }

  /**
   * Analyze components in the project
   */
  private async analyzeComponents(analysis: ProjectAnalysis): Promise<void> {
    try {
      const searchPaths = [
        analysis.structure.srcPath,
        analysis.structure.componentsPath
      ].filter(Boolean);

      for (const searchPath of searchPaths) {
        await this.findComponents(searchPath!, analysis);
      }

      Logger.debug(`Found ${Object.keys(analysis.components).length} components`);

    } catch (error) {
      Logger.warn('Error analyzing components:', error);
    }
  }

  /**
   * Find components in a directory
   */
  private async findComponents(dirPath: string, analysis: ProjectAnalysis): Promise<void> {
    try {
      const result = await this.fileOps.listFiles(dirPath);
      if (!result.success) return;

      const componentExtensions = ['.jsx', '.tsx', '.vue', '.svelte'];

      for (const file of result.data.files) {
        if (file.type === 'file' && file.extension && componentExtensions.includes(file.extension)) {
          const componentName = path.basename(file.name, file.extension);
          
          // Skip if component name doesn't start with uppercase (likely not a component)
          if (!/^[A-Z]/.test(componentName)) continue;

          analysis.components[componentName] = {
            path: file.path,
            type: 'functional', // Default, could be enhanced with AST analysis
            framework: analysis.framework,
            props: [],
            hooks: [],
            imports: [],
            exports: []
          };
        } else if (file.type === 'directory') {
          // Recursively search subdirectories
          await this.findComponents(file.path, analysis);
        }
      }

    } catch (error) {
      Logger.warn(`Error finding components in ${dirPath}:`, error);
    }
  }

  /**
   * Detect design patterns
   */
  private async detectDesignPatterns(analysis: ProjectAnalysis): Promise<void> {
    const patterns: DesignPattern[] = [];

    // Detect common patterns based on project structure and dependencies
    
    // Component composition pattern
    if (Object.keys(analysis.components).length > 5) {
      patterns.push({
        name: 'Component Composition',
        type: 'component',
        description: 'Uses multiple reusable components',
        files: Object.values(analysis.components).map(c => c.path),
        confidence: 0.8
      });
    }

    // State management patterns
    const deps = { ...analysis.dependencies.production, ...analysis.dependencies.development };
    if (deps.redux || deps['@reduxjs/toolkit']) {
      patterns.push({
        name: 'Redux Pattern',
        type: 'state',
        description: 'Uses Redux for state management',
        files: [],
        confidence: 0.9
      });
    }

    if (deps.zustand) {
      patterns.push({
        name: 'Zustand Pattern',
        type: 'state',
        description: 'Uses Zustand for state management',
        files: [],
        confidence: 0.9
      });
    }

    // CSS-in-JS patterns
    if (analysis.styling === 'styled-components' || analysis.styling === 'emotion') {
      patterns.push({
        name: 'CSS-in-JS Pattern',
        type: 'styling',
        description: 'Uses CSS-in-JS for component styling',
        files: [],
        confidence: 0.8
      });
    }

    analysis.patterns = patterns;
    Logger.debug(`Detected ${patterns.length} design patterns`);
  }

  /**
   * Detect package manager
   */
  private async detectPackageManager(rootPath: string): Promise<ProjectAnalysis['packageManager']> {
    if (await fs.pathExists(path.join(rootPath, 'pnpm-lock.yaml'))) {
      return 'pnpm';
    }
    if (await fs.pathExists(path.join(rootPath, 'yarn.lock'))) {
      return 'yarn';
    }
    if (await fs.pathExists(path.join(rootPath, 'package-lock.json'))) {
      return 'npm';
    }
    return 'unknown';
  }

  /**
   * Categorize dependencies
   */
  private categorizeDependencies(analysis: ProjectAnalysis): void {
    const allDeps = { ...analysis.dependencies.production, ...analysis.dependencies.development };
    
    const frameworks = ['react', 'vue', '@angular/core', 'svelte'];
    const uiLibraries = ['@mui/material', 'antd', '@chakra-ui/react', '@headlessui/react', 'react-bootstrap'];
    const buildTools = ['vite', 'webpack', 'parcel', 'rollup', 'esbuild'];
    const testingTools = ['jest', 'vitest', '@testing-library/react', 'cypress', 'playwright'];

    analysis.dependencies.frameworks = Object.keys(allDeps).filter(dep => 
      frameworks.some(framework => dep.includes(framework))
    );

    analysis.dependencies.uiLibraries = Object.keys(allDeps).filter(dep => 
      uiLibraries.includes(dep)
    );

    analysis.dependencies.buildTools = Object.keys(allDeps).filter(dep => 
      buildTools.includes(dep)
    );

    analysis.dependencies.testingTools = Object.keys(allDeps).filter(dep => 
      testingTools.some(tool => dep.includes(tool))
    );
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.analysisCache.clear();
    Logger.debug('Analysis cache cleared');
  }

  /**
   * Get cached analysis
   */
  getCachedAnalysis(rootPath: string): ProjectAnalysis | null {
    return this.analysisCache.get(rootPath) || null;
  }
}
