
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/core</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">94.39% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>101/107</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">81.81% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>9/11</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.3% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>24/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">94.33% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>100/106</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="ServiceRegistry.ts"><a href="ServiceRegistry.ts.html">ServiceRegistry.ts</a></td>
	<td data-value="86.36" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.36" class="pct high">86.36%</td>
	<td data-value="44" class="abs high">38/44</td>
	<td data-value="77.77" class="pct medium">77.77%</td>
	<td data-value="9" class="abs medium">7/9</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="12" class="abs high">10/12</td>
	<td data-value="86.36" class="pct high">86.36%</td>
	<td data-value="44" class="abs high">38/44</td>
	</tr>

<tr>
	<td class="file high" data-value="UIOrbitExtension.ts"><a href="UIOrbitExtension.ts.html">UIOrbitExtension.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="63" class="abs high">63/63</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="62" class="abs high">62/62</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-05T04:51:10.920Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    