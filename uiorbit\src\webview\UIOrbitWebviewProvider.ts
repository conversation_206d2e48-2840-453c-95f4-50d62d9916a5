import * as vscode from 'vscode';
import { ServiceRegistry } from '../core/ServiceRegistry';
import { Logger } from '../utils/Logger';
import { ChatWebviewProvider } from './ChatWebviewProvider';

/**
 * Multi-panel webview provider for UIOrbit extension
 * Manages multiple webview panels like Augment Code
 */
export class UIOrbitWebviewProvider {
  private panels: Map<string, vscode.WebviewPanel> = new Map();
  private chatProvider: ChatWebviewProvider;

  constructor(
    private readonly extensionUri: vscode.Uri,
    private readonly serviceRegistry: ServiceRegistry
  ) {
    this.chatProvider = new ChatWebviewProvider(extensionUri, serviceRegistry);
  }

  /**
   * Create or show the main chat panel
   */
  public async createChatPanel(): Promise<vscode.WebviewPanel> {
    const existingPanel = this.panels.get('chat');
    if (existingPanel) {
      existingPanel.reveal();
      return existingPanel;
    }

    const panel = vscode.window.createWebviewPanel(
      'uiorbit.chat',
      'UIOrbit Chat',
      vscode.ViewColumn.Beside,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [this.extensionUri]
      }
    );

    // Set up the webview content using our chat provider
    await this.setupChatWebview(panel);

    // Store the panel
    this.panels.set('chat', panel);

    // Clean up when panel is disposed
    panel.onDidDispose(() => {
      this.panels.delete('chat');
    });

    return panel;
  }

  /**
   * Create or show the component preview panel
   */
  public async createPreviewPanel(): Promise<vscode.WebviewPanel> {
    const existingPanel = this.panels.get('preview');
    if (existingPanel) {
      existingPanel.reveal();
      return existingPanel;
    }

    const panel = vscode.window.createWebviewPanel(
      'uiorbit.preview',
      'Component Preview',
      vscode.ViewColumn.Two,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [this.extensionUri]
      }
    );

    // Set up preview content
    panel.webview.html = this.getPreviewHTML(panel.webview);

    // Store the panel
    this.panels.set('preview', panel);

    // Clean up when panel is disposed
    panel.onDidDispose(() => {
      this.panels.delete('preview');
    });

    return panel;
  }

  /**
   * Create or show the design system panel
   */
  public async createDesignSystemPanel(): Promise<vscode.WebviewPanel> {
    const existingPanel = this.panels.get('design-system');
    if (existingPanel) {
      existingPanel.reveal();
      return existingPanel;
    }

    const panel = vscode.window.createWebviewPanel(
      'uiorbit.designSystem',
      'Design System',
      vscode.ViewColumn.Two,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [this.extensionUri]
      }
    );

    // Set up design system content
    panel.webview.html = this.getDesignSystemHTML(panel.webview);

    // Store the panel
    this.panels.set('design-system', panel);

    // Clean up when panel is disposed
    panel.onDidDispose(() => {
      this.panels.delete('design-system');
    });

    return panel;
  }

  /**
   * Get all active panels
   */
  public getActivePanels(): Map<string, vscode.WebviewPanel> {
    return new Map(this.panels);
  }

  /**
   * Close all panels
   */
  public closeAllPanels(): void {
    for (const panel of this.panels.values()) {
      panel.dispose();
    }
    this.panels.clear();
  }

  /**
   * Set up chat webview using the chat provider
   */
  private async setupChatWebview(panel: vscode.WebviewPanel): Promise<void> {
    // Create a webview view-like object for the chat provider
    const webviewView = {
      webview: panel.webview,
      onDidDispose: panel.onDidDispose.bind(panel),
      onDidChangeVisibility: () => {}, // Not needed for panels
      visible: true,
      title: 'UIOrbit Chat',
      description: undefined,
      viewType: 'uiorbit.chat',
      show: () => panel.reveal()
    } as unknown as vscode.WebviewView;

    // Use the chat provider to resolve the webview
    this.chatProvider.resolveWebviewView(
      webviewView,
      {} as vscode.WebviewViewResolveContext,
      new vscode.CancellationTokenSource().token
    );
  }

  /**
   * Get HTML for component preview panel
   */
  private getPreviewHTML(webview: vscode.Webview): string {
    const nonce = this.getNonce();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
    <title>Component Preview</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .preview-header {
            background-color: var(--vscode-sideBar-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .preview-container {
            flex: 1;
            background-color: white;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 20px;
            overflow: auto;
        }
        .preview-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        .control-button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
        }
        .control-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
    </style>
</head>
<body>
    <div class="preview-header">
        <h2>🎨 Component Preview</h2>
        <p>Live preview of generated components</p>
        <div class="preview-controls">
            <button class="control-button" onclick="toggleResponsive()">📱 Responsive</button>
            <button class="control-button" onclick="toggleDarkMode()">🌙 Dark Mode</button>
            <button class="control-button" onclick="refreshPreview()">🔄 Refresh</button>
        </div>
    </div>
    
    <div class="preview-container" id="previewContainer">
        <div style="text-align: center; color: #666; padding: 40px;">
            <h3>No component to preview</h3>
            <p>Generate a component in the chat to see it here!</p>
        </div>
    </div>

    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();
        
        function toggleResponsive() {
            const container = document.getElementById('previewContainer');
            container.style.maxWidth = container.style.maxWidth ? '' : '375px';
            container.style.margin = container.style.maxWidth ? '0 auto' : '0';
        }
        
        function toggleDarkMode() {
            document.body.classList.toggle('dark-mode');
        }
        
        function refreshPreview() {
            vscode.postMessage({ type: 'refresh-preview' });
        }
        
        // Listen for preview updates
        window.addEventListener('message', event => {
            const message = event.data;
            if (message.type === 'update-preview') {
                document.getElementById('previewContainer').innerHTML = message.content;
            }
        });
    </script>
</body>
</html>`;
  }

  /**
   * Get HTML for design system panel
   */
  private getDesignSystemHTML(webview: vscode.Webview): string {
    const nonce = this.getNonce();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
    <title>Design System</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
        }
        .design-system-header {
            background-color: var(--vscode-sideBar-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .token-section {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .token-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }
        .token-item {
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 12px;
        }
    </style>
</head>
<body>
    <div class="design-system-header">
        <h2>🎨 Design System</h2>
        <p>Extracted design tokens and patterns from your codebase</p>
    </div>
    
    <div class="token-section">
        <h3>Colors</h3>
        <div class="token-grid" id="colorTokens">
            <div style="text-align: center; color: #666; padding: 20px; grid-column: 1 / -1;">
                Analyzing codebase for design tokens...
            </div>
        </div>
    </div>
    
    <div class="token-section">
        <h3>Typography</h3>
        <div class="token-grid" id="typographyTokens">
            <div style="text-align: center; color: #666; padding: 20px; grid-column: 1 / -1;">
                Analyzing typography patterns...
            </div>
        </div>
    </div>
    
    <div class="token-section">
        <h3>Spacing</h3>
        <div class="token-grid" id="spacingTokens">
            <div style="text-align: center; color: #666; padding: 20px; grid-column: 1 / -1;">
                Analyzing spacing patterns...
            </div>
        </div>
    </div>

    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();
        
        // Request design system analysis
        vscode.postMessage({ type: 'analyze-design-system' });
        
        // Listen for design system updates
        window.addEventListener('message', event => {
            const message = event.data;
            if (message.type === 'design-system-update') {
                updateDesignSystem(message.data);
            }
        });
        
        function updateDesignSystem(data) {
            if (data.colors) {
                updateColorTokens(data.colors);
            }
            if (data.typography) {
                updateTypographyTokens(data.typography);
            }
            if (data.spacing) {
                updateSpacingTokens(data.spacing);
            }
        }
        
        function updateColorTokens(colors) {
            const container = document.getElementById('colorTokens');
            container.innerHTML = colors.map(color => 
                '<div class="token-item">' +
                '<div style="width: 100%; height: 40px; background-color: ' + color.value + '; border-radius: 4px; margin-bottom: 8px;"></div>' +
                '<strong>' + color.name + '</strong><br>' +
                '<code>' + color.value + '</code>' +
                '</div>'
            ).join('');
        }
        
        function updateTypographyTokens(typography) {
            const container = document.getElementById('typographyTokens');
            container.innerHTML = typography.map(typo => 
                '<div class="token-item">' +
                '<div style="font-family: ' + typo.fontFamily + '; font-size: ' + typo.fontSize + '; font-weight: ' + typo.fontWeight + '; margin-bottom: 8px;">Sample Text</div>' +
                '<strong>' + typo.name + '</strong><br>' +
                '<code>' + typo.fontFamily + ' ' + typo.fontSize + ' ' + typo.fontWeight + '</code>' +
                '</div>'
            ).join('');
        }
        
        function updateSpacingTokens(spacing) {
            const container = document.getElementById('spacingTokens');
            container.innerHTML = spacing.map(space => 
                '<div class="token-item">' +
                '<div style="width: ' + space.value + '; height: 20px; background-color: var(--vscode-button-background); margin-bottom: 8px;"></div>' +
                '<strong>' + space.name + '</strong><br>' +
                '<code>' + space.value + '</code>' +
                '</div>'
            ).join('');
        }
    </script>
</body>
</html>`;
  }

  /**
   * Generate a nonce for CSP
   */
  private getNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }
}
