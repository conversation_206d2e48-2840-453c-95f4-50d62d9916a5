import * as vscode from 'vscode';

import { UIOrbitExtension } from './core/UIOrbitExtension';
import { Logger } from './utils/Logger';

let extension: UIOrbitExtension | undefined;

/**
 * This method is called when the extension is activated
 * The extension is activated the very first time the command is executed
 */
export async function activate(context: vscode.ExtensionContext): Promise<void> {
  try {
    Logger.info('UIOrbit extension is being activated...');

    // Initialize the main extension class
    extension = new UIOrbitExtension(context);

    // Activate the extension
    await extension.activate();

    Logger.info('UIOrbit extension activated successfully!');

    // Show welcome message
    vscode.window.showInformationMessage(
      'UIOrbit is ready! Your AI frontend development assistant is now active.'
    );

  } catch (error) {
    Logger.error('Failed to activate UIOrbit extension:', error);
    vscode.window.showErrorMessage(
      `Failed to activate UIOrbit: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * This method is called when your extension is deactivated
 */
export async function deactivate(): Promise<void> {
  try {
    Logger.info('UIOrbit extension is being deactivated...');

    if (extension) {
      await extension.deactivate();
      extension = undefined;
    }

    Logger.info('UIOrbit extension deactivated successfully!');

  } catch (error) {
    Logger.error('Error during extension deactivation:', error);
  }
}
