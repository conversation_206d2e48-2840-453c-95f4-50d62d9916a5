import{S as Q,i as U,s as V,$ as E,W as M,J as C,c as W,ac as T,Y as g,e as G,f as x,a8 as q,a9 as ee,a0 as F,a1 as J,a2 as R,u as y,q as ae,t as k,r as te,ad as se,h as H,aa as de,a6 as oe,E as K,F as N,ae as ne,af as S,I as O,C as X}from"./SpinnerAugment-BJAAUt-n.js";import{I as le}from"./IconButtonAugment-CqdkuyT6.js";import{f as j}from"./index-CGH5qOQn.js";import{r as re}from"./resize-observer-DdAtcrRr.js";import{E as ie}from"./ellipsis-DJS5pN6w.js";const ce=t=>({}),Y=t=>({}),ue=t=>({}),A=t=>({});function Z(t){let a,e,d,u;return e=new le({props:{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$slots:{default:[me]},$$scope:{ctx:t}}}),e.$on("click",t[12]),{c(){a=M("div"),K(e.$$.fragment),W(a,"class","c-drawer__hidden-indicator svelte-18f0m3o")},m(l,$){G(l,a,$),N(e,a,null),u=!0},p(l,$){const p={};16777216&$&&(p.$$scope={dirty:$,ctx:l}),e.$set(p)},i(l){u||(y(e.$$.fragment,l),l&&ne(()=>{u&&(d||(d=S(a,j,{y:0,x:0,duration:200},!0)),d.run(1))}),u=!0)},o(l){k(e.$$.fragment,l),l&&(d||(d=S(a,j,{y:0,x:0,duration:200},!1)),d.run(0)),u=!1},d(l){l&&H(a),O(e),l&&d&&d.end()}}}function me(t){let a,e;return a=new ie({}),{c(){K(a.$$.fragment)},m(d,u){N(a,d,u),e=!0},i(d){e||(y(a.$$.fragment,d),e=!0)},o(d){k(a.$$.fragment,d),e=!1},d(d){O(a,d)}}}function he(t){let a,e,d,u,l,$,p,B,v,f,i,m,L;const _=t[20].left,h=E(_,t,t[24],A),z=t[20].right,r=E(z,t,t[24],Y);let n=t[0]&&t[3]&&Z(t);return{c(){a=M("div"),e=M("div"),d=M("div"),h&&h.c(),u=C(),l=M("div"),$=C(),p=M("div"),r&&r.c(),B=C(),n&&n.c(),W(d,"class","c-drawer__left-content svelte-18f0m3o"),d.inert=t[7],T(d,"width","var(--augment-drawer-width)"),T(d,"min-width","var(--augment-drawer-width)"),T(d,"max-width","var(--augment-drawer-width)"),W(e,"class","c-drawer__left svelte-18f0m3o"),T(e,"--augment-drawer-width",t[8]+"px"),W(l,"aria-hidden","true"),W(l,"class","c-drawer__handle svelte-18f0m3o"),g(l,"is-locked",t[4]),W(p,"class","c-drawer__right svelte-18f0m3o"),W(a,"class",v="c-drawer "+t[2]+" svelte-18f0m3o"),g(a,"is-dragging",t[7]),g(a,"is-hidden",!t[8]),g(a,"is-column",t[4])},m(s,c){G(s,a,c),x(a,e),x(e,d),h&&h.m(d,null),t[21](e),x(a,u),x(a,l),x(a,$),x(a,p),r&&r.m(p,null),x(a,B),n&&n.m(a,null),t[22](a),i=!0,m||(L=[q(window,"mousemove",t[10]),q(window,"mouseup",t[11]),q(l,"mousedown",t[9]),q(l,"dblclick",t[12]),ee(f=re.call(null,a,{onResize:t[23]}))],m=!0)},p(s,[c]){h&&h.p&&(!i||16777216&c)&&F(h,_,s,s[24],i?R(_,s[24],c,ue):J(s[24]),A),(!i||128&c)&&(d.inert=s[7]),(!i||256&c)&&T(e,"--augment-drawer-width",s[8]+"px"),(!i||16&c)&&g(l,"is-locked",s[4]),r&&r.p&&(!i||16777216&c)&&F(r,z,s,s[24],i?R(z,s[24],c,ce):J(s[24]),Y),s[0]&&s[3]?n?(n.p(s,c),9&c&&y(n,1)):(n=Z(s),n.c(),y(n,1),n.m(a,null)):n&&(ae(),k(n,1,1,()=>{n=null}),te()),(!i||4&c&&v!==(v="c-drawer "+s[2]+" svelte-18f0m3o"))&&W(a,"class",v),f&&se(f.update)&&2&c&&f.update.call(null,{onResize:s[23]}),(!i||132&c)&&g(a,"is-dragging",s[7]),(!i||260&c)&&g(a,"is-hidden",!s[8]),(!i||20&c)&&g(a,"is-column",s[4])},i(s){i||(y(h,s),y(r,s),y(n),i=!0)},o(s){k(h,s),k(r,s),k(n),i=!1},d(s){s&&H(a),h&&h.d(s),t[21](null),r&&r.d(s),n&&n.d(),t[22](null),m=!1,de(L)}}}function fe(t,a,e){let d,u,l,$,{$$slots:p={},$$scope:B}=a,{initialWidth:v=300}=a,{expandedMinWidth:f=50}=a,{minimizedWidth:i=0}=a,{minimized:m=!1}=a,{class:L=""}=a,{showButton:_=!0}=a,{deadzone:h=0}=a,{columnLayoutThreshold:z=600}=a,{layoutMode:r}=a,n=!1,s=v,c=v,w=!1;function I(){if(u){if(r!==void 0)return e(4,w=r==="column"),void(w&&e(7,n=!1));e(4,w=u.clientWidth<z),w&&e(7,n=!1)}}return oe(I),t.$$set=o=>{"initialWidth"in o&&e(14,v=o.initialWidth),"expandedMinWidth"in o&&e(15,f=o.expandedMinWidth),"minimizedWidth"in o&&e(16,i=o.minimizedWidth),"minimized"in o&&e(0,m=o.minimized),"class"in o&&e(2,L=o.class),"showButton"in o&&e(3,_=o.showButton),"deadzone"in o&&e(17,h=o.deadzone),"columnLayoutThreshold"in o&&e(18,z=o.columnLayoutThreshold),"layoutMode"in o&&e(1,r=o.layoutMode),"$$scope"in o&&e(24,B=o.$$scope)},t.$$.update=()=>{3&t.$$.dirty&&(m?(e(1,r="row"),e(4,w=!1)):r!=="row"||m||(e(1,r=void 0),I())),18&t.$$.dirty&&r!==void 0&&(e(4,w=r==="column"),w&&e(7,n=!1)),589825&t.$$.dirty&&e(8,c=m?i:s)},[m,r,L,_,w,d,u,n,c,function(o){w||(e(7,n=!0),l=o.clientX,$=d.offsetWidth,o.preventDefault())},function(o){if(!n||!d||w)return;const P=o.clientX-l,D=u.clientWidth-200,b=$+P;b<f?b<f-h?e(0,m=!0):(e(19,s=f),e(0,m=!1)):b>D?(e(19,s=D),e(0,m=!1)):(e(19,s=b),e(0,m=!1))},function(){e(7,n=!1),e(19,s=Math.max(s,f))},function(){e(0,m=!m)},I,v,f,i,h,z,s,p,function(o){X[o?"unshift":"push"](()=>{d=o,e(5,d)})},function(o){X[o?"unshift":"push"](()=>{u=o,e(6,u)})},()=>r===void 0&&I(),B]}class We extends Q{constructor(a){super(),U(this,a,fe,he,V,{initialWidth:14,expandedMinWidth:15,minimizedWidth:16,minimized:0,class:2,showButton:3,deadzone:17,columnLayoutThreshold:18,layoutMode:1})}}export{We as D};
