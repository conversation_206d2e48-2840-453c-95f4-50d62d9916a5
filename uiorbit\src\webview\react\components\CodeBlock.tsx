import React, { useState } from 'react';

interface CodeBlockProps {
  code: string;
  language: string;
}

export const CodeBlock: React.FC<CodeBlockProps> = ({ code, language }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  // Simple syntax highlighting for common languages
  const highlightCode = (code: string, language: string) => {
    // This is a basic implementation - in a real app you'd use a library like Prism.js
    const keywords = {
      javascript: ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'class', 'import', 'export'],
      typescript: ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'class', 'import', 'export', 'interface', 'type'],
      jsx: ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'class', 'import', 'export'],
      tsx: ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'class', 'import', 'export', 'interface', 'type'],
      css: ['color', 'background', 'margin', 'padding', 'border', 'display', 'flex', 'grid'],
      html: ['div', 'span', 'p', 'h1', 'h2', 'h3', 'button', 'input', 'form']
    };

    let highlightedCode = code;
    const langKeywords = keywords[language as keyof typeof keywords] || [];

    // Simple keyword highlighting
    langKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlightedCode = highlightedCode.replace(regex, `<span class="keyword">${keyword}</span>`);
    });

    // String highlighting
    highlightedCode = highlightedCode.replace(
      /(["'`])((?:(?!\1)[^\\]|\\.)*)(\1)/g,
      '<span class="string">$1$2$3</span>'
    );

    // Comment highlighting
    highlightedCode = highlightedCode.replace(
      /(\/\/.*$|\/\*[\s\S]*?\*\/)/gm,
      '<span class="comment">$1</span>'
    );

    return highlightedCode;
  };

  return (
    <div className="code-block">
      <div className="code-header">
        <span className="language-label">{language}</span>
        <button
          className="copy-button"
          onClick={handleCopy}
          title={copied ? 'Copied!' : 'Copy code'}
        >
          {copied ? '✓' : '📋'}
        </button>
      </div>
      <pre className="code-content">
        <code
          className={`language-${language}`}
          dangerouslySetInnerHTML={{
            __html: highlightCode(code, language)
          }}
        />
      </pre>
    </div>
  );
};
