name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18'

jobs:
  # Lint and format check
  lint:
    name: Lint & Format Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: uiorbit/package-lock.json
        
    - name: Install dependencies
      run: |
        cd uiorbit
        npm ci
        
    - name: Run ESLint
      run: |
        cd uiorbit
        npm run lint
        
    - name: Check Prettier formatting
      run: |
        cd uiorbit
        npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,md}"

  # Unit tests
  test-unit:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: uiorbit/package-lock.json
        
    - name: Install dependencies
      run: |
        cd uiorbit
        npm ci
        
    - name: Run unit tests
      run: |
        cd uiorbit
        npm run test:unit:coverage
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: uiorbit/coverage/lcov.info
        directory: uiorbit/coverage
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  # Integration tests
  test-integration:
    name: Integration Tests
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: uiorbit/package-lock.json
        
    - name: Install dependencies
      run: |
        cd uiorbit
        npm ci
        
    - name: Run integration tests (Linux)
      if: runner.os == 'Linux'
      run: |
        cd uiorbit
        xvfb-run -a npm run test:integration
        
    - name: Run integration tests (Windows/macOS)
      if: runner.os != 'Linux'
      run: |
        cd uiorbit
        npm run test:integration

  # Build and package
  build:
    name: Build & Package
    runs-on: ubuntu-latest
    needs: [lint, test-unit]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: uiorbit/package-lock.json
        
    - name: Install dependencies
      run: |
        cd uiorbit
        npm ci
        
    - name: Compile TypeScript
      run: |
        cd uiorbit
        npm run compile
        
    - name: Package extension
      run: |
        cd uiorbit
        npm run package
        
    - name: Install VSCE
      run: npm install -g @vscode/vsce
      
    - name: Package VSIX
      run: |
        cd uiorbit
        vsce package --out ../uiorbit.vsix
        
    - name: Upload VSIX artifact
      uses: actions/upload-artifact@v3
      with:
        name: uiorbit-vsix
        path: uiorbit.vsix
        retention-days: 30

  # Security audit
  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: uiorbit/package-lock.json
        
    - name: Install dependencies
      run: |
        cd uiorbit
        npm ci
        
    - name: Run security audit
      run: |
        cd uiorbit
        npm audit --audit-level=moderate

  # Release (only on release events)
  release:
    name: Release Extension
    runs-on: ubuntu-latest
    needs: [lint, test-unit, test-integration, build, security]
    if: github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: uiorbit/package-lock.json
        
    - name: Install dependencies
      run: |
        cd uiorbit
        npm ci
        
    - name: Install VSCE
      run: npm install -g @vscode/vsce
      
    - name: Package and publish
      env:
        VSCE_PAT: ${{ secrets.VSCE_PAT }}
      run: |
        cd uiorbit
        vsce publish --pat $VSCE_PAT
        
    - name: Create GitHub release asset
      run: |
        cd uiorbit
        vsce package --out ../uiorbit-${{ github.event.release.tag_name }}.vsix
        
    - name: Upload release asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: uiorbit-${{ github.event.release.tag_name }}.vsix
        asset_name: uiorbit-${{ github.event.release.tag_name }}.vsix
        asset_content_type: application/zip
