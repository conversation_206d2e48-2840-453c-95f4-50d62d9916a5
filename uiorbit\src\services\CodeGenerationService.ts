import * as vscode from 'vscode';
import * as path from 'path';
import { Logger } from '../utils/Logger';
import { AIService } from './AIService';
import { ProjectDetectionService, ProjectContext } from './ProjectDetectionService';
import { FileOperationsService } from './FileOperationsService';

export interface ComponentGenerationRequest {
  name: string;
  type: 'functional' | 'class' | 'hook' | 'utility';
  framework: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components' | 'emotion';
  props?: ComponentProp[];
  features?: ComponentFeature[];
  description?: string;
  targetPath?: string;
}

export interface ComponentProp {
  name: string;
  type: string;
  required: boolean;
  description?: string;
  defaultValue?: string;
}

export interface ComponentFeature {
  name: string;
  description: string;
  enabled: boolean;
}

export interface GenerationResult {
  success: boolean;
  files: GeneratedFile[];
  message?: string;
  error?: string;
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'component' | 'style' | 'test' | 'story' | 'types';
}

export interface CodeOptimizationRequest {
  filePath: string;
  content: string;
  optimizationType: 'performance' | 'accessibility' | 'seo' | 'bundle-size' | 'readability';
  targetFramework?: string;
}

export interface RefactoringRequest {
  filePath: string;
  content: string;
  refactoringType: 'extract-component' | 'split-file' | 'modernize' | 'typescript-conversion';
  options?: Record<string, any>;
}

/**
 * Code Generation Service for UIOrbit
 * Handles AI-powered code generation, optimization, and refactoring
 */
export class CodeGenerationService {
  private aiService: AIService;
  private projectDetectionService: ProjectDetectionService;
  private fileOpsService: FileOperationsService;

  constructor(
    aiService: AIService,
    projectDetectionService: ProjectDetectionService,
    fileOperationsService: FileOperationsService
  ) {
    this.aiService = aiService;
    this.projectDetectionService = projectDetectionService;
    this.fileOpsService = fileOperationsService;
  }

  /**
   * Generate a React component with modern best practices
   */
  async generateReactComponent(request: ComponentGenerationRequest): Promise<GenerationResult> {
    try {
      Logger.info(`Generating React component: ${request.name}`);

      // Get project context
      const projectContext = await this.projectDetectionService.getProjectContext();
      
      // Build generation prompt
      const prompt = this.buildComponentPrompt(request, projectContext);
      
      // Generate code using AI
      const aiResponse = await this.aiService.processMessage(prompt);
      
      if (aiResponse.isError) {
        return {
          success: false,
          files: [],
          error: aiResponse.content
        };
      }

      // Parse AI response and extract files
      const generatedFiles = this.parseGeneratedCode(aiResponse.content, request);
      
      // Determine target directory
      const targetDir = await this.determineTargetDirectory(request, projectContext);
      
      // Write files to disk
      const writtenFiles: GeneratedFile[] = [];
      for (const file of generatedFiles) {
        const fullPath = path.join(targetDir, file.path);
        const writeResult = await this.fileOpsService.writeFile(fullPath, file.content);
        
        if (writeResult.success) {
          writtenFiles.push({
            ...file,
            path: fullPath
          });
        } else {
          Logger.warn(`Failed to write file: ${fullPath}`);
        }
      }

      Logger.info(`Successfully generated ${writtenFiles.length} files for component: ${request.name}`);
      
      return {
        success: true,
        files: writtenFiles,
        message: `Generated ${request.name} component with ${writtenFiles.length} files`
      };

    } catch (error) {
      Logger.error('Error generating React component:', error);
      return {
        success: false,
        files: [],
        error: `Failed to generate component: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate modern UI components with trending patterns
   */
  async generateModernUIComponent(description: string): Promise<GenerationResult> {
    try {
      Logger.info('Generating modern UI component from description');

      const projectContext = await this.projectDetectionService.getProjectContext();
      
      const prompt = `Create a modern, trending UI component based on this description: "${description}"

Project Context:
- Framework: ${projectContext.framework}
- Styling: ${projectContext.styling}
- TypeScript: ${projectContext.analysis?.typescript ? 'Yes' : 'No'}

Requirements:
- Use latest UI/UX trends (glassmorphism, neumorphism, micro-interactions)
- Implement accessibility best practices
- Include responsive design
- Add smooth animations and transitions
- Use modern CSS features (CSS Grid, Flexbox, Custom Properties)
- Include hover and focus states
- Add proper TypeScript types if applicable

Generate:
1. Main component file
2. Styles file (CSS/SCSS/Tailwind classes)
3. TypeScript types if applicable
4. Usage example

Make it production-ready with clean, maintainable code.`;

      const aiResponse = await this.aiService.processMessage(prompt);
      
      if (aiResponse.isError) {
        return {
          success: false,
          files: [],
          error: aiResponse.content
        };
      }

      const generatedFiles = this.parseGeneratedCode(aiResponse.content, {
        name: 'ModernUIComponent',
        type: 'functional',
        framework: projectContext.framework as any,
        styling: projectContext.styling as any
      });

      return {
        success: true,
        files: generatedFiles,
        message: 'Generated modern UI component with trending patterns'
      };

    } catch (error) {
      Logger.error('Error generating modern UI component:', error);
      return {
        success: false,
        files: [],
        error: `Failed to generate component: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Optimize existing code for performance and best practices
   */
  async optimizeCode(request: CodeOptimizationRequest): Promise<GenerationResult> {
    try {
      Logger.info(`Optimizing code: ${request.filePath}`);

      const prompt = `Optimize this ${request.targetFramework || 'JavaScript'} code for ${request.optimizationType}:

\`\`\`
${request.content}
\`\`\`

Optimization Focus: ${request.optimizationType}

Please provide:
1. Optimized code with improvements
2. Explanation of changes made
3. Performance impact analysis
4. Best practices applied

Focus on:
${this.getOptimizationFocus(request.optimizationType)}`;

      const aiResponse = await this.aiService.processMessage(prompt);
      
      if (aiResponse.isError) {
        return {
          success: false,
          files: [],
          error: aiResponse.content
        };
      }

      const optimizedFiles = this.parseGeneratedCode(aiResponse.content, {
        name: path.basename(request.filePath, path.extname(request.filePath)),
        type: 'functional',
        framework: 'react',
        styling: 'css'
      });

      return {
        success: true,
        files: optimizedFiles,
        message: `Code optimized for ${request.optimizationType}`
      };

    } catch (error) {
      Logger.error('Error optimizing code:', error);
      return {
        success: false,
        files: [],
        error: `Failed to optimize code: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Refactor code with modern patterns
   */
  async refactorCode(request: RefactoringRequest): Promise<GenerationResult> {
    try {
      Logger.info(`Refactoring code: ${request.filePath}`);

      const prompt = `Refactor this code using ${request.refactoringType}:

\`\`\`
${request.content}
\`\`\`

Refactoring Type: ${request.refactoringType}

Please provide:
1. Refactored code with modern patterns
2. Explanation of changes
3. Benefits of the refactoring
4. Migration guide if needed

${this.getRefactoringInstructions(request.refactoringType)}`;

      const aiResponse = await this.aiService.processMessage(prompt);
      
      if (aiResponse.isError) {
        return {
          success: false,
          files: [],
          error: aiResponse.content
        };
      }

      const refactoredFiles = this.parseGeneratedCode(aiResponse.content, {
        name: path.basename(request.filePath, path.extname(request.filePath)),
        type: 'functional',
        framework: 'react',
        styling: 'css'
      });

      return {
        success: true,
        files: refactoredFiles,
        message: `Code refactored using ${request.refactoringType}`
      };

    } catch (error) {
      Logger.error('Error refactoring code:', error);
      return {
        success: false,
        files: [],
        error: `Failed to refactor code: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Build component generation prompt
   */
  private buildComponentPrompt(request: ComponentGenerationRequest, context: ProjectContext): string {
    let prompt = `Generate a ${request.framework} ${request.type} component named "${request.name}"`;
    
    if (request.description) {
      prompt += ` that ${request.description}`;
    }

    prompt += `\n\nProject Context:
- Framework: ${request.framework}
- Styling: ${request.styling}
- TypeScript: ${context.analysis?.typescript ? 'Yes' : 'No'}`;

    if (request.props && request.props.length > 0) {
      prompt += `\n\nProps:`;
      request.props.forEach(prop => {
        prompt += `\n- ${prop.name}: ${prop.type}${prop.required ? ' (required)' : ' (optional)'}`;
        if (prop.description) {
          prompt += ` - ${prop.description}`;
        }
      });
    }

    if (request.features && request.features.length > 0) {
      prompt += `\n\nFeatures to include:`;
      request.features.filter(f => f.enabled).forEach(feature => {
        prompt += `\n- ${feature.name}: ${feature.description}`;
      });
    }

    prompt += `\n\nRequirements:
- Use modern ${request.framework} best practices
- Include proper TypeScript types if applicable
- Add accessibility attributes
- Include responsive design
- Use ${request.styling} for styling
- Add proper error handling
- Include JSDoc comments
- Follow clean code principles

Generate:
1. Main component file
2. Styles file (if separate)
3. TypeScript types file (if applicable)
4. Test file (basic structure)
5. Usage example`;

    return prompt;
  }

  /**
   * Parse generated code from AI response
   */
  private parseGeneratedCode(aiResponse: string, request: ComponentGenerationRequest): GeneratedFile[] {
    const files: GeneratedFile[] = [];
    
    // Extract code blocks from AI response
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    let match;
    let fileIndex = 0;

    while ((match = codeBlockRegex.exec(aiResponse)) !== null) {
      const language = match[1] || 'javascript';
      const content = match[2].trim();
      
      if (content.length === 0) continue;

      // Determine file type and extension
      const fileInfo = this.determineFileInfo(content, language, request, fileIndex);
      
      files.push({
        path: fileInfo.path,
        content: content,
        type: fileInfo.type
      });
      
      fileIndex++;
    }

    // If no code blocks found, treat entire response as a single file
    if (files.length === 0) {
      const extension = request.framework === 'react' ? 'tsx' : 'js';
      files.push({
        path: `${request.name}.${extension}`,
        content: aiResponse,
        type: 'component'
      });
    }

    return files;
  }

  /**
   * Determine file information from content
   */
  private determineFileInfo(content: string, language: string, request: ComponentGenerationRequest, index: number): { path: string; type: GeneratedFile['type'] } {
    const name = request.name;
    
    // Determine file type based on content and language
    if (content.includes('test') || content.includes('describe') || content.includes('it(')) {
      return { path: `${name}.test.${language === 'typescript' ? 'ts' : 'js'}`, type: 'test' };
    }
    
    if (content.includes('@types') || content.includes('interface') || content.includes('type ')) {
      return { path: `${name}.types.ts`, type: 'types' };
    }
    
    if (language === 'css' || language === 'scss' || content.includes('styles') || content.includes('.css')) {
      const ext = request.styling === 'scss' ? 'scss' : 'css';
      return { path: `${name}.${ext}`, type: 'style' };
    }
    
    if (content.includes('storiesOf') || content.includes('export default')) {
      return { path: `${name}.stories.${request.framework === 'react' ? 'tsx' : 'js'}`, type: 'story' };
    }
    
    // Default to component file
    const ext = request.framework === 'react' && content.includes('tsx') ? 'tsx' : 
                request.framework === 'vue' ? 'vue' :
                language === 'typescript' ? 'ts' : 'js';
    
    return { path: `${name}.${ext}`, type: 'component' };
  }

  /**
   * Determine target directory for generated files
   */
  private async determineTargetDirectory(request: ComponentGenerationRequest, context: ProjectContext): Promise<string> {
    if (request.targetPath) {
      return request.targetPath;
    }

    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      throw new Error('No workspace folder found');
    }

    const rootPath = workspaceFolder.uri.fsPath;
    
    // Try to find components directory
    const possiblePaths = [
      path.join(rootPath, 'src', 'components'),
      path.join(rootPath, 'components'),
      path.join(rootPath, 'src'),
      rootPath
    ];

    for (const dirPath of possiblePaths) {
      const result = await this.fileOpsService.listFiles(dirPath);
      if (result.success) {
        return path.join(dirPath, request.name);
      }
    }

    return path.join(rootPath, 'src', 'components', request.name);
  }

  /**
   * Get optimization focus instructions
   */
  private getOptimizationFocus(type: string): string {
    switch (type) {
      case 'performance':
        return '- Reduce re-renders\n- Optimize bundle size\n- Lazy loading\n- Memoization\n- Virtual scrolling';
      case 'accessibility':
        return '- ARIA attributes\n- Keyboard navigation\n- Screen reader support\n- Color contrast\n- Focus management';
      case 'seo':
        return '- Meta tags\n- Semantic HTML\n- Structured data\n- Performance metrics\n- Core Web Vitals';
      case 'bundle-size':
        return '- Tree shaking\n- Code splitting\n- Dynamic imports\n- Remove unused code\n- Optimize dependencies';
      case 'readability':
        return '- Clean code principles\n- Better naming\n- Extract functions\n- Add comments\n- Reduce complexity';
      default:
        return '- General best practices\n- Modern patterns\n- Clean code\n- Performance\n- Maintainability';
    }
  }

  /**
   * Get refactoring instructions
   */
  private getRefactoringInstructions(type: string): string {
    switch (type) {
      case 'extract-component':
        return 'Extract reusable components from large components. Focus on single responsibility principle.';
      case 'split-file':
        return 'Split large files into smaller, focused modules. Maintain clear interfaces between modules.';
      case 'modernize':
        return 'Update to modern JavaScript/TypeScript patterns. Use hooks, async/await, optional chaining, etc.';
      case 'typescript-conversion':
        return 'Convert JavaScript to TypeScript with proper types, interfaces, and type safety.';
      default:
        return 'Apply modern best practices and clean code principles.';
    }
  }
}
