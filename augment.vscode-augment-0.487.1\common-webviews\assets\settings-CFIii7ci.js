var Qi=Object.defineProperty;var eo=(s,e,t)=>e in s?Qi(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var ye=(s,e,t)=>eo(s,typeof e!="symbol"?e+"":e,t);import{N as De,al as to,am as gn,S as ge,i as $e,s as fe,W as C,J as Z,E as w,K as L,c as _,e as y,f as S,F as x,a8 as Le,ad as zt,q as B,r as J,u as p,t as m,h as v,I as b,aa as ks,Y as be,M as he,T as oe,aC as pr,A as ke,n as W,B as Ss,b as Be,$ as Ie,a as Te,X as rs,a0 as Re,a1 as Oe,a2 as Pe,g as wt,Z as as,_ as er,j as Qe,ab as is,aA as Tt,ah as ot,L as Gt,a9 as no,a4 as mr,H as Bn,w as Jn,x as Gn,y as Wn,d as dt,z as Hn,aj as ii,C as Me,D as Ae,G as Ne,aE as so,ao as Cs,a6 as oi,ap as fr,a5 as ci,ac as hr,ax as gr}from"./SpinnerAugment-BJAAUt-n.js";import{P as Ts,B as li,T as ro,F as ao,L as io}from"./layer-group-CeTgJtYd.js";import"./design-system-init-D2yLRPnY.js";import{W as ve,a as tt,e as _e,u as Rt,o as Ot,h as je,g as oo,H as $r}from"./BaseButton-7bccWxEO.js";import{T as hn,M as di}from"./TextTooltipAugment-DGOJQXY9.js";import{G as co,S as lo,a as uo,C as po,N as mo,J as fo,L as ho,F as bt,b as Fe,D as go,c as $o,M as vo}from"./mcp-logo-CMUqUebQ.js";import{a as Ps,b as ft,G as yo,L as Ee,F as _o,c as wo,R as xo,d as bo,M as ko,C as So,e as ui,f as Co,T as To,U as Mo,g as Ao}from"./github-DDehkTJf.js";import{M as Yt,C as No}from"./magnifying-glass-4Ft8m82l.js";import{V as pi}from"./VSCodeCodicon-CVJeB9dY.js";import{m as Ds,R as Zo,a as js,o as es,A as vr,d as mi,e as fi}from"./types-xGAhb6Qr.js";import{I as Ms,A as Eo}from"./IconButtonAugment-CqdkuyT6.js";import{o as Io}from"./keypress-DD1aQVr0.js";import{D as Ro}from"./Drawer-4fqDeA7C.js";import{B as Ze}from"./ButtonAugment-CLDnX_Hg.js";import{T as Oo}from"./Content-BldOFwN2.js";import{T as Dt,D as Ce}from"./index-BAb5fkIe.js";import{C as As}from"./CalloutAugment-Bc8HnLJ3.js";import{E as Po}from"./ellipsis-DJS5pN6w.js";import{P as jo}from"./pen-to-square-SVW9AP0k.js";import{T as hi}from"./TextAreaAugment-CfENnz8O.js";import{a as Lo,C as Fo}from"./CollapseButtonAugment-BPhovcAJ.js";import{M as zo}from"./index-C9A1ZQNk.js";import{M as Do}from"./MarkdownEditor-B9_YR3nk.js";import{A as Uo,n as Vo}from"./arrow-up-right-from-square-CdEOBPRR.js";import{C as qo,E as Bo}from"./chat-flags-model-t_MCj4l9.js";import{R as yr}from"./chat-types-D7sox8tw.js";import{R as Jo}from"./RulesDropdown-ZQ49o-mi.js";import{C as gi}from"./chevron-down-CxktpQeS.js";import{C as Go}from"./CardAugment-qFWs8J9b.js";import"./index-CGH5qOQn.js";import"./resize-observer-DdAtcrRr.js";import"./globals-D0QH3NT1.js";import"./index-Bb_d2FL8.js";import"./file-paths-BcSg4gks.js";const ln={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class Wo{constructor(e,t=ln){ye(this,"timerId",null);ye(this,"currentMS");ye(this,"step",0);ye(this,"params");this.callback=e;const n={...t};n.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),n.maxMS=ln.maxMS),n.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),n.initialMS=ln.initialMS),n.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),n.mult=ln.mult),n.maxSteps!==void 0&&n.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),n.maxSteps=ln.maxSteps),this.params=n,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}class Ho{constructor(e){ye(this,"configs",De([]));ye(this,"pollingManager");ye(this,"_enableDebugFeatures",De(!1));ye(this,"_settingsComponentSupported",De({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));ye(this,"_enableAgentMode",De(!1));ye(this,"_enableInitialOrientation",De(!1));ye(this,"_userTier",De("unknown"));ye(this,"_guidelines",De({}));this._host=e,this.pollingManager=new Wo(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,n=e.oauthUrl;if(e.identifier.hostName===Ps.remoteToolHost){let r=e.identifier.toolId;switch(typeof r=="string"&&/^\d+$/.test(r)&&(r=Number(r)),r){case ft.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:yo,requiresAuthentication:t,authUrl:n};case ft.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:ho,requiresAuthentication:t,authUrl:n};case ft.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:fo,requiresAuthentication:t,authUrl:n};case ft.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:mo,requiresAuthentication:t,authUrl:n};case ft.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:po,requiresAuthentication:t,authUrl:n};case ft.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:uo,requiresAuthentication:t,authUrl:n};case ft.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:lo,requiresAuthentication:t,authUrl:n};case ft.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:co,requiresAuthentication:t,authUrl:n};case ft.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled RemoteToolId: ${r}`)}}else if(e.identifier.hostName===Ps.localToolHost){const r=e.identifier.toolId;switch(r){case Ee.readFile:case Ee.editFile:case Ee.saveFile:case Ee.launchProcess:case Ee.killProcess:case Ee.readProcess:case Ee.writeProcess:case Ee.listProcesses:case Ee.waitProcess:case Ee.openBrowser:case Ee.clarify:case Ee.onboardingSubAgent:case Ee.strReplaceEditor:case Ee.remember:case Ee.diagnostics:case Ee.setupScript:case Ee.readTerminal:case Ee.gitCommitRetrieval:case Ee.spawnSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:bt,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled LocalToolType: ${r}`)}}else if(e.identifier.hostName===Ps.sidecarToolHost){const r=e.identifier.toolId;switch(r){case Fe.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:Yt,requiresAuthentication:t,authUrl:n};case Fe.shell:return{displayName:"Shell",description:"Shell",icon:Yt,requiresAuthentication:t,authUrl:n};case Fe.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:Yt,requiresAuthentication:t,authUrl:n};case Fe.view:return{displayName:"File View",description:"File Viewer",icon:Yt,requiresAuthentication:t,authUrl:n};case Fe.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:Yt,requiresAuthentication:t,authUrl:n};case Fe.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:$o,requiresAuthentication:t,authUrl:n};case Fe.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:bt,requiresAuthentication:t,authUrl:n};case Fe.saveFile:return{displayName:"Save File",description:"Save a new file",icon:_o,requiresAuthentication:t,authUrl:n};case Fe.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:bt,requiresAuthentication:t,authUrl:n};case Fe.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:bt,requiresAuthentication:t,authUrl:n};case Fe.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:bt,requiresAuthentication:t,authUrl:n};case Fe.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:bt,requiresAuthentication:t,authUrl:n};case Fe.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:bt,requiresAuthentication:t,authUrl:n};case Fe.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:bt,requiresAuthentication:t,authUrl:n};case Fe.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:go,requiresAuthentication:t,authUrl:n};case Fe.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:Yt,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled SidecarToolType: ${r}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:n}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case ve.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),!0;case ve.toolConfigDefinitionsResponse:return this.configs.update(n=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(r=>{const a=n.find(i=>i.name===r.name);return a?{...a,displayName:r.displayName,description:r.description,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,isConfigured:r.isConfigured}:r})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(n=>{const r=this.transformToolDisplay(n),a=t.find(o=>o.name===n.definition.name),i=(a==null?void 0:a.isConfigured)??!r.requiresAuthentication;return{config:(a==null?void 0:a.config)??{},configString:JSON.stringify((a==null?void 0:a.config)??{},null,2),isConfigured:i,name:n.definition.name.toString(),displayName:r.displayName,description:r.description,identifier:n.identifier,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,showStatus:!1,statusMessage:"",statusType:"info"}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return to(this.configs,e=>{const t=e.filter(r=>this.isDisplayableTool(r)),n=new Map;for(const r of t)n.set(r.displayName,r);return Array.from(n.values()).sort((r,a)=>{const i={GitHub:1,Linear:2,Notion:3},o=Number.MAX_SAFE_INTEGER,c=i[r.displayName]||o,l=i[a.displayName]||o;return c<o&&l<o||c===o&&l===o?c!==l?c-l:r.displayName.localeCompare(a.displayName):c-l})})}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:ve.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:ve.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}getSettingsComponentSupported(){return this._settingsComponentSupported}}var pe,Us;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(pe||(pe={})),(Us||(Us={})).mergeShapes=(s,e)=>({...s,...e});const V=pe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),gt=s=>{switch(typeof s){case"undefined":return V.undefined;case"string":return V.string;case"number":return isNaN(s)?V.nan:V.number;case"boolean":return V.boolean;case"function":return V.function;case"bigint":return V.bigint;case"symbol":return V.symbol;case"object":return Array.isArray(s)?V.array:s===null?V.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?V.promise:typeof Map<"u"&&s instanceof Map?V.map:typeof Set<"u"&&s instanceof Set?V.set:typeof Date<"u"&&s instanceof Date?V.date:V.object;default:return V.unknown}},R=pe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);let at=class $i extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof $i))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,pe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}};at.create=s=>new at(s);const tn=(s,e)=>{let t;switch(s.code){case R.invalid_type:t=s.received===V.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case R.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,pe.jsonStringifyReplacer)}`;break;case R.unrecognized_keys:t=`Unrecognized key(s) in object: ${pe.joinValues(s.keys,", ")}`;break;case R.invalid_union:t="Invalid input";break;case R.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${pe.joinValues(s.options)}`;break;case R.invalid_enum_value:t=`Invalid enum value. Expected ${pe.joinValues(s.options)}, received '${s.received}'`;break;case R.invalid_arguments:t="Invalid function arguments";break;case R.invalid_return_type:t="Invalid function return type";break;case R.invalid_date:t="Invalid date";break;case R.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:pe.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case R.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case R.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case R.custom:t="Invalid input";break;case R.invalid_intersection_types:t="Intersection results could not be merged";break;case R.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case R.not_finite:t="Number must be finite";break;default:t=e.defaultError,pe.assertNever(s)}return{message:t}};let vi=tn;function os(){return vi}const cs=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function F(s,e){const t=os(),n=cs({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===tn?void 0:tn].filter(r=>!!r)});s.common.issues.push(n)}let Je=class yi{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return ne;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return yi.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return ne;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}};const ne=Object.freeze({status:"aborted"}),ls=s=>({status:"dirty",value:s}),Ve=s=>({status:"valid",value:s}),Vs=s=>s.status==="aborted",qs=s=>s.status==="dirty",Ut=s=>s.status==="valid",$n=s=>typeof Promise<"u"&&s instanceof Promise;function ds(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function _i(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var H,dn,un;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(H||(H={}));let ut=class{constructor(s,e,t,n){this._cachedPath=[],this.parent=s,this.data=e,this._path=t,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}};const _r=(s,e)=>{if(Ut(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new at(s.common.issues);return this._error=t,this._error}}};function ae(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}let ce=class{get description(){return this._def.description}_getType(s){return gt(s.data)}_getOrReturnCtx(s,e){return e||{common:s.parent.common,data:s.data,parsedType:gt(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}_processInputParams(s){return{status:new Je,ctx:{common:s.parent.common,data:s.data,parsedType:gt(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}}_parseSync(s){const e=this._parse(s);if($n(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(s){const e=this._parse(s);return Promise.resolve(e)}parse(s,e){const t=this.safeParse(s,e);if(t.success)return t.data;throw t.error}safeParse(s,e){var t;const n={common:{issues:[],async:(t=e==null?void 0:e.async)!==null&&t!==void 0&&t,contextualErrorMap:e==null?void 0:e.errorMap},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:gt(s)},r=this._parseSync({data:s,path:n.path,parent:n});return _r(n,r)}"~validate"(s){var e,t;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:gt(s)};if(!this["~standard"].async)try{const r=this._parseSync({data:s,path:[],parent:n});return Ut(r)?{value:r.value}:{issues:n.common.issues}}catch(r){!((t=(e=r==null?void 0:r.message)===null||e===void 0?void 0:e.toLowerCase())===null||t===void 0)&&t.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:s,path:[],parent:n}).then(r=>Ut(r)?{value:r.value}:{issues:n.common.issues})}async parseAsync(s,e){const t=await this.safeParseAsync(s,e);if(t.success)return t.data;throw t.error}async safeParseAsync(s,e){const t={common:{issues:[],contextualErrorMap:e==null?void 0:e.errorMap,async:!0},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:gt(s)},n=this._parse({data:s,path:t.path,parent:t}),r=await($n(n)?n:Promise.resolve(n));return _r(t,r)}refine(s,e){const t=n=>typeof e=="string"||e===void 0?{message:e}:typeof e=="function"?e(n):e;return this._refinement((n,r)=>{const a=s(n),i=()=>r.addIssue({code:R.custom,...t(n)});return typeof Promise<"u"&&a instanceof Promise?a.then(o=>!!o||(i(),!1)):!!a||(i(),!1)})}refinement(s,e){return this._refinement((t,n)=>!!s(t)||(n.addIssue(typeof e=="function"?e(t,n):e),!1))}_refinement(s){return new nt({schema:this,typeName:ee.ZodEffects,effect:{type:"refinement",refinement:s}})}superRefine(s){return this._refinement(s)}constructor(s){this.spa=this.safeParseAsync,this._def=s,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ct.create(this,this._def)}nullable(){return At.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Vt.create(this)}promise(){return rn.create(this,this._def)}or(s){return kn.create([this,s],this._def)}and(s){return Sn.create(this,s,this._def)}transform(s){return new nt({...ae(this._def),schema:this,typeName:ee.ZodEffects,effect:{type:"transform",transform:s}})}default(s){const e=typeof s=="function"?s:()=>s;return new An({...ae(this._def),innerType:this,defaultValue:e,typeName:ee.ZodDefault})}brand(){return new tr({typeName:ee.ZodBranded,type:this,...ae(this._def)})}catch(s){const e=typeof s=="function"?s:()=>s;return new Nn({...ae(this._def),innerType:this,catchValue:e,typeName:ee.ZodCatch})}describe(s){return new this.constructor({...this._def,description:s})}pipe(s){return nr.create(this,s)}readonly(){return Zn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}};const Ko=/^c[^\s-]{8,}$/i,Yo=/^[0-9a-z]+$/,Xo=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Qo=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,ec=/^[a-z0-9_-]{21}$/i,tc=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,nc=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,sc=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Ls;const rc=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ac=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ic=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,oc=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,cc=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,lc=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,wi="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",dc=new RegExp(`^${wi}$`);function xi(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function bi(s){let e=`${wi}T${xi(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function uc(s,e){if(!tc.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function pc(s,e){return!(e!=="v4"&&e||!ac.test(s))||!(e!=="v6"&&e||!oc.test(s))}let nn=class pn extends ce{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==V.string){const i=this._getOrReturnCtx(e);return F(i,{code:R.invalid_type,expected:V.string,received:i.parsedType}),ne}const t=new Je;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),F(n,{code:R.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),F(n,{code:R.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?F(n,{code:R.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&F(n,{code:R.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")sc.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"email",code:R.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")Ls||(Ls=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Ls.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"emoji",code:R.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Qo.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"uuid",code:R.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")ec.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"nanoid",code:R.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")Ko.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"cuid",code:R.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")Yo.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"cuid2",code:R.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Xo.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"ulid",code:R.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),F(n,{validation:"url",code:R.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"regex",code:R.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),F(n,{code:R.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),F(n,{code:R.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),F(n,{code:R.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?bi(i).test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{code:R.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?dc.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{code:R.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${xi(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{code:R.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?nc.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"duration",code:R.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!rc.test(r))&&(a!=="v6"&&a||!ic.test(r))&&(n=this._getOrReturnCtx(e,n),F(n,{validation:"ip",code:R.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?uc(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"jwt",code:R.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?pc(e.data,i.version)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"cidr",code:R.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?cc.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"base64",code:R.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?lc.test(e.data)||(n=this._getOrReturnCtx(e,n),F(n,{validation:"base64url",code:R.invalid_string,message:i.message}),t.dirty()):pe.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:R.invalid_string,...H.errToObj(n)})}_addCheck(e){return new pn({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...H.errToObj(e)})}url(e){return this._addCheck({kind:"url",...H.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...H.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...H.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...H.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...H.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...H.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...H.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...H.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...H.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...H.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...H.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...H.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...H.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...H.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...H.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...H.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...H.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...H.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...H.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...H.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...H.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...H.errToObj(t)})}nonempty(e){return this.min(1,H.errToObj(e))}trim(){return new pn({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new pn({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new pn({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};function mc(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}nn.create=s=>{var e;return new nn({checks:[],typeName:ee.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ae(s)})};let vn=class Bs extends ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==V.number){const r=this._getOrReturnCtx(e);return F(r,{code:R.invalid_type,expected:V.number,received:r.parsedType}),ne}let t;const n=new Je;for(const r of this._def.checks)r.kind==="int"?pe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),F(t,{code:R.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),F(t,{code:R.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),F(t,{code:R.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?mc(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),F(t,{code:R.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),F(t,{code:R.not_finite,message:r.message}),n.dirty()):pe.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,H.toString(t))}gt(e,t){return this.setLimit("min",e,!1,H.toString(t))}lte(e,t){return this.setLimit("max",e,!0,H.toString(t))}lt(e,t){return this.setLimit("max",e,!1,H.toString(t))}setLimit(e,t,n,r){return new Bs({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:H.toString(r)}]})}_addCheck(e){return new Bs({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:H.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:H.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:H.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:H.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:H.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:H.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:H.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:H.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:H.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&pe.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}};vn.create=s=>new vn({checks:[],typeName:ee.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...ae(s)});let yn=class Js extends ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==V.bigint)return this._getInvalidInput(e);let t;const n=new Je;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),F(t,{code:R.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),F(t,{code:R.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),F(t,{code:R.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):pe.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return F(t,{code:R.invalid_type,expected:V.bigint,received:t.parsedType}),ne}gte(e,t){return this.setLimit("min",e,!0,H.toString(t))}gt(e,t){return this.setLimit("min",e,!1,H.toString(t))}lte(e,t){return this.setLimit("max",e,!0,H.toString(t))}lt(e,t){return this.setLimit("max",e,!1,H.toString(t))}setLimit(e,t,n,r){return new Js({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:H.toString(r)}]})}_addCheck(e){return new Js({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:H.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:H.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:H.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:H.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:H.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};yn.create=s=>{var e;return new yn({checks:[],typeName:ee.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ae(s)})};let _n=class extends ce{_parse(s){if(this._def.coerce&&(s.data=!!s.data),this._getType(s)!==V.boolean){const e=this._getOrReturnCtx(s);return F(e,{code:R.invalid_type,expected:V.boolean,received:e.parsedType}),ne}return Ve(s.data)}};_n.create=s=>new _n({typeName:ee.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...ae(s)});let wn=class ki extends ce{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==V.date){const r=this._getOrReturnCtx(e);return F(r,{code:R.invalid_type,expected:V.date,received:r.parsedType}),ne}if(isNaN(e.data.getTime()))return F(this._getOrReturnCtx(e),{code:R.invalid_date}),ne;const t=new Je;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),F(n,{code:R.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),F(n,{code:R.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):pe.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ki({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:H.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:H.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}};wn.create=s=>new wn({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:ee.ZodDate,...ae(s)});let us=class extends ce{_parse(s){if(this._getType(s)!==V.symbol){const e=this._getOrReturnCtx(s);return F(e,{code:R.invalid_type,expected:V.symbol,received:e.parsedType}),ne}return Ve(s.data)}};us.create=s=>new us({typeName:ee.ZodSymbol,...ae(s)});let xn=class extends ce{_parse(s){if(this._getType(s)!==V.undefined){const e=this._getOrReturnCtx(s);return F(e,{code:R.invalid_type,expected:V.undefined,received:e.parsedType}),ne}return Ve(s.data)}};xn.create=s=>new xn({typeName:ee.ZodUndefined,...ae(s)});let bn=class extends ce{_parse(s){if(this._getType(s)!==V.null){const e=this._getOrReturnCtx(s);return F(e,{code:R.invalid_type,expected:V.null,received:e.parsedType}),ne}return Ve(s.data)}};bn.create=s=>new bn({typeName:ee.ZodNull,...ae(s)});let sn=class extends ce{constructor(){super(...arguments),this._any=!0}_parse(s){return Ve(s.data)}};sn.create=s=>new sn({typeName:ee.ZodAny,...ae(s)});let Lt=class extends ce{constructor(){super(...arguments),this._unknown=!0}_parse(s){return Ve(s.data)}};Lt.create=s=>new Lt({typeName:ee.ZodUnknown,...ae(s)});let yt=class extends ce{_parse(s){const e=this._getOrReturnCtx(s);return F(e,{code:R.invalid_type,expected:V.never,received:e.parsedType}),ne}};yt.create=s=>new yt({typeName:ee.ZodNever,...ae(s)});let ps=class extends ce{_parse(s){if(this._getType(s)!==V.undefined){const e=this._getOrReturnCtx(s);return F(e,{code:R.invalid_type,expected:V.void,received:e.parsedType}),ne}return Ve(s.data)}};ps.create=s=>new ps({typeName:ee.ZodVoid,...ae(s)});let Vt=class ns extends ce{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==V.array)return F(t,{code:R.invalid_type,expected:V.array,received:t.parsedType}),ne;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(F(t,{code:i?R.too_big:R.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(F(t,{code:R.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(F(t,{code:R.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new ut(t,i,t.path,o)))).then(i=>Je.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new ut(t,i,t.path,o)));return Je.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new ns({...this._def,minLength:{value:e,message:H.toString(t)}})}max(e,t){return new ns({...this._def,maxLength:{value:e,message:H.toString(t)}})}length(e,t){return new ns({...this._def,exactLength:{value:e,message:H.toString(t)}})}nonempty(e){return this.min(1,e)}};function Xt(s){if(s instanceof Ke){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=ct.create(Xt(n))}return new Ke({...s._def,shape:()=>e})}return s instanceof Vt?new Vt({...s._def,type:Xt(s.element)}):s instanceof ct?ct.create(Xt(s.unwrap())):s instanceof At?At.create(Xt(s.unwrap())):s instanceof Mt?Mt.create(s.items.map(e=>Xt(e))):s}Vt.create=(s,e)=>new Vt({type:s,minLength:null,maxLength:null,exactLength:null,typeName:ee.ZodArray,...ae(e)});let Ke=class et extends ce{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=pe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==V.object){const c=this._getOrReturnCtx(e);return F(c,{code:R.invalid_type,expected:V.object,received:c.parsedType}),ne}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof yt&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new ut(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof yt){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(F(n,{code:R.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new ut(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>Je.mergeObjectSync(t,c)):Je.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return H.errToObj,new et({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=H.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new et({...this._def,unknownKeys:"strip"})}passthrough(){return new et({...this._def,unknownKeys:"passthrough"})}extend(e){return new et({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new et({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:ee.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new et({...this._def,catchall:e})}pick(e){const t={};return pe.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new et({...this._def,shape:()=>t})}omit(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new et({...this._def,shape:()=>t})}deepPartial(){return Xt(this)}partial(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new et({...this._def,shape:()=>t})}required(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof ct;)r=r._def.innerType;t[n]=r}}),new et({...this._def,shape:()=>t})}keyof(){return Zi(pe.objectKeys(this.shape))}};Ke.create=(s,e)=>new Ke({shape:()=>s,unknownKeys:"strip",catchall:yt.create(),typeName:ee.ZodObject,...ae(e)}),Ke.strictCreate=(s,e)=>new Ke({shape:()=>s,unknownKeys:"strict",catchall:yt.create(),typeName:ee.ZodObject,...ae(e)}),Ke.lazycreate=(s,e)=>new Ke({shape:s,unknownKeys:"strip",catchall:yt.create(),typeName:ee.ZodObject,...ae(e)});let kn=class extends ce{_parse(s){const{ctx:e}=this._processInputParams(s),t=this._def.options;if(e.common.async)return Promise.all(t.map(async n=>{const r={...e,common:{...e.common,issues:[]},parent:null};return{result:await n._parseAsync({data:e.data,path:e.path,parent:r}),ctx:r}})).then(function(n){for(const a of n)if(a.result.status==="valid")return a.result;for(const a of n)if(a.result.status==="dirty")return e.common.issues.push(...a.ctx.common.issues),a.result;const r=n.map(a=>new at(a.ctx.common.issues));return F(e,{code:R.invalid_union,unionErrors:r}),ne});{let n;const r=[];for(const i of t){const o={...e,common:{...e.common,issues:[]},parent:null},c=i._parseSync({data:e.data,path:e.path,parent:o});if(c.status==="valid")return c;c.status!=="dirty"||n||(n={result:c,ctx:o}),o.common.issues.length&&r.push(o.common.issues)}if(n)return e.common.issues.push(...n.ctx.common.issues),n.result;const a=r.map(i=>new at(i));return F(e,{code:R.invalid_union,unionErrors:a}),ne}}get options(){return this._def.options}};kn.create=(s,e)=>new kn({options:s,typeName:ee.ZodUnion,...ae(e)});const kt=s=>s instanceof Cn?kt(s.schema):s instanceof nt?kt(s.innerType()):s instanceof Tn?[s.value]:s instanceof Kn?s.options:s instanceof Mn?pe.objectValues(s.enum):s instanceof An?kt(s._def.innerType):s instanceof xn?[void 0]:s instanceof bn?[null]:s instanceof ct?[void 0,...kt(s.unwrap())]:s instanceof At?[null,...kt(s.unwrap())]:s instanceof tr||s instanceof Zn?kt(s.unwrap()):s instanceof Nn?kt(s._def.innerType):[];let Si=class Ci extends ce{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==V.object)return F(t,{code:R.invalid_type,expected:V.object,received:t.parsedType}),ne;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(F(t,{code:R.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),ne)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=kt(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new Ci({typeName:ee.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...ae(n)})}};function Gs(s,e){const t=gt(s),n=gt(e);if(s===e)return{valid:!0,data:s};if(t===V.object&&n===V.object){const r=pe.objectKeys(e),a=pe.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Gs(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===V.array&&n===V.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Gs(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===V.date&&n===V.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}let Sn=class extends ce{_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=(r,a)=>{if(Vs(r)||Vs(a))return ne;const i=Gs(r.value,a.value);return i.valid?((qs(r)||qs(a))&&e.dirty(),{status:e.value,value:i.data}):(F(t,{code:R.invalid_intersection_types}),ne)};return t.common.async?Promise.all([this._def.left._parseAsync({data:t.data,path:t.path,parent:t}),this._def.right._parseAsync({data:t.data,path:t.path,parent:t})]).then(([r,a])=>n(r,a)):n(this._def.left._parseSync({data:t.data,path:t.path,parent:t}),this._def.right._parseSync({data:t.data,path:t.path,parent:t}))}};Sn.create=(s,e,t)=>new Sn({left:s,right:e,typeName:ee.ZodIntersection,...ae(t)});let Mt=class Ti extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==V.array)return F(n,{code:R.invalid_type,expected:V.array,received:n.parsedType}),ne;if(n.data.length<this._def.items.length)return F(n,{code:R.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),ne;!this._def.rest&&n.data.length>this._def.items.length&&(F(n,{code:R.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new ut(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>Je.mergeArray(t,a)):Je.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new Ti({...this._def,rest:e})}};Mt.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Mt({items:s,typeName:ee.ZodTuple,rest:null,...ae(e)})};let Mi=class Ai extends ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==V.object)return F(n,{code:R.invalid_type,expected:V.object,received:n.parsedType}),ne;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new ut(n,o,n.path,o)),value:i._parse(new ut(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?Je.mergeObjectAsync(t,r):Je.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new Ai(t instanceof ce?{keyType:e,valueType:t,typeName:ee.ZodRecord,...ae(n)}:{keyType:nn.create(),valueType:e,typeName:ee.ZodRecord,...ae(t)})}},ms=class extends ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(s){const{status:e,ctx:t}=this._processInputParams(s);if(t.parsedType!==V.map)return F(t,{code:R.invalid_type,expected:V.map,received:t.parsedType}),ne;const n=this._def.keyType,r=this._def.valueType,a=[...t.data.entries()].map(([i,o],c)=>({key:n._parse(new ut(t,i,t.path,[c,"key"])),value:r._parse(new ut(t,o,t.path,[c,"value"]))}));if(t.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const o of a){const c=await o.key,l=await o.value;if(c.status==="aborted"||l.status==="aborted")return ne;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}})}{const i=new Map;for(const o of a){const c=o.key,l=o.value;if(c.status==="aborted"||l.status==="aborted")return ne;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}}}};ms.create=(s,e,t)=>new ms({valueType:e,keyType:s,typeName:ee.ZodMap,...ae(t)});let fs=class Ws extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==V.set)return F(n,{code:R.invalid_type,expected:V.set,received:n.parsedType}),ne;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(F(n,{code:R.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(F(n,{code:R.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return ne;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new ut(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Ws({...this._def,minSize:{value:e,message:H.toString(t)}})}max(e,t){return new Ws({...this._def,maxSize:{value:e,message:H.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};fs.create=(s,e)=>new fs({valueType:s,minSize:null,maxSize:null,typeName:ee.ZodSet,...ae(e)});let Ni=class ss extends ce{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==V.function)return F(t,{code:R.invalid_type,expected:V.function,received:t.parsedType}),ne;function n(o,c){return cs({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,os(),tn].filter(l=>!!l),issueData:{code:R.invalid_arguments,argumentsError:c}})}function r(o,c){return cs({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,os(),tn].filter(l=>!!l),issueData:{code:R.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof rn){const o=this;return Ve(async function(...c){const l=new at([]),d=await o._def.args.parseAsync(c,a).catch(g=>{throw l.addIssue(n(c,g)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(g=>{throw l.addIssue(r(u,g)),l})})}{const o=this;return Ve(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new at([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new at([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ss({...this._def,args:Mt.create(e).rest(Lt.create())})}returns(e){return new ss({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new ss({args:e||Mt.create([]).rest(Lt.create()),returns:t||Lt.create(),typeName:ee.ZodFunction,...ae(n)})}},Cn=class extends ce{get schema(){return this._def.getter()}_parse(s){const{ctx:e}=this._processInputParams(s);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}};Cn.create=(s,e)=>new Cn({getter:s,typeName:ee.ZodLazy,...ae(e)});let Tn=class extends ce{_parse(s){if(s.data!==this._def.value){const e=this._getOrReturnCtx(s);return F(e,{received:e.data,code:R.invalid_literal,expected:this._def.value}),ne}return{status:"valid",value:s.data}}get value(){return this._def.value}};function Zi(s,e){return new Kn({values:s,typeName:ee.ZodEnum,...ae(e)})}Tn.create=(s,e)=>new Tn({value:s,typeName:ee.ZodLiteral,...ae(e)});let Kn=class Hs extends ce{constructor(){super(...arguments),dn.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return F(t,{expected:pe.joinValues(n),received:t.parsedType,code:R.invalid_type}),ne}if(ds(this,dn)||_i(this,dn,new Set(this._def.values)),!ds(this,dn).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return F(t,{received:t.data,code:R.invalid_enum_value,options:n}),ne}return Ve(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Hs.create(e,{...this._def,...t})}exclude(e,t=this._def){return Hs.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}};dn=new WeakMap,Kn.create=Zi;let Mn=class extends ce{constructor(){super(...arguments),un.set(this,void 0)}_parse(s){const e=pe.getValidEnumValues(this._def.values),t=this._getOrReturnCtx(s);if(t.parsedType!==V.string&&t.parsedType!==V.number){const n=pe.objectValues(e);return F(t,{expected:pe.joinValues(n),received:t.parsedType,code:R.invalid_type}),ne}if(ds(this,un)||_i(this,un,new Set(pe.getValidEnumValues(this._def.values))),!ds(this,un).has(s.data)){const n=pe.objectValues(e);return F(t,{received:t.data,code:R.invalid_enum_value,options:n}),ne}return Ve(s.data)}get enum(){return this._def.values}};un=new WeakMap,Mn.create=(s,e)=>new Mn({values:s,typeName:ee.ZodNativeEnum,...ae(e)});let rn=class extends ce{unwrap(){return this._def.type}_parse(s){const{ctx:e}=this._processInputParams(s);if(e.parsedType!==V.promise&&e.common.async===!1)return F(e,{code:R.invalid_type,expected:V.promise,received:e.parsedType}),ne;const t=e.parsedType===V.promise?e.data:Promise.resolve(e.data);return Ve(t.then(n=>this._def.type.parseAsync(n,{path:e.path,errorMap:e.common.contextualErrorMap})))}};rn.create=(s,e)=>new rn({type:s,typeName:ee.ZodPromise,...ae(e)});let nt=class extends ce{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ee.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=this._def.effect||null,r={addIssue:a=>{F(t,a),a.fatal?e.abort():e.dirty()},get path(){return t.path}};if(r.addIssue=r.addIssue.bind(r),n.type==="preprocess"){const a=n.transform(t.data,r);if(t.common.async)return Promise.resolve(a).then(async i=>{if(e.value==="aborted")return ne;const o=await this._def.schema._parseAsync({data:i,path:t.path,parent:t});return o.status==="aborted"?ne:o.status==="dirty"||e.value==="dirty"?ls(o.value):o});{if(e.value==="aborted")return ne;const i=this._def.schema._parseSync({data:a,path:t.path,parent:t});return i.status==="aborted"?ne:i.status==="dirty"||e.value==="dirty"?ls(i.value):i}}if(n.type==="refinement"){const a=i=>{const o=n.refinement(i,r);if(t.common.async)return Promise.resolve(o);if(o instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(t.common.async===!1){const i=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});return i.status==="aborted"?ne:(i.status==="dirty"&&e.dirty(),a(i.value),{status:e.value,value:i.value})}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(i=>i.status==="aborted"?ne:(i.status==="dirty"&&e.dirty(),a(i.value).then(()=>({status:e.value,value:i.value}))))}if(n.type==="transform"){if(t.common.async===!1){const a=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});if(!Ut(a))return a;const i=n.transform(a.value,r);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:i}}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(a=>Ut(a)?Promise.resolve(n.transform(a.value,r)).then(i=>({status:e.value,value:i})):a)}pe.assertNever(n)}};nt.create=(s,e,t)=>new nt({schema:s,typeName:ee.ZodEffects,effect:e,...ae(t)}),nt.createWithPreprocess=(s,e,t)=>new nt({schema:e,effect:{type:"preprocess",transform:s},typeName:ee.ZodEffects,...ae(t)});let ct=class extends ce{_parse(s){return this._getType(s)===V.undefined?Ve(void 0):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};ct.create=(s,e)=>new ct({innerType:s,typeName:ee.ZodOptional,...ae(e)});let At=class extends ce{_parse(s){return this._getType(s)===V.null?Ve(null):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};At.create=(s,e)=>new At({innerType:s,typeName:ee.ZodNullable,...ae(e)});let An=class extends ce{_parse(s){const{ctx:e}=this._processInputParams(s);let t=e.data;return e.parsedType===V.undefined&&(t=this._def.defaultValue()),this._def.innerType._parse({data:t,path:e.path,parent:e})}removeDefault(){return this._def.innerType}};An.create=(s,e)=>new An({innerType:s,typeName:ee.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ae(e)});let Nn=class extends ce{_parse(s){const{ctx:e}=this._processInputParams(s),t={...e,common:{...e.common,issues:[]}},n=this._def.innerType._parse({data:t.data,path:t.path,parent:{...t}});return $n(n)?n.then(r=>({status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new at(t.common.issues)},input:t.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new at(t.common.issues)},input:t.data})}}removeCatch(){return this._def.innerType}};Nn.create=(s,e)=>new Nn({innerType:s,typeName:ee.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ae(e)});let hs=class extends ce{_parse(s){if(this._getType(s)!==V.nan){const e=this._getOrReturnCtx(s);return F(e,{code:R.invalid_type,expected:V.nan,received:e.parsedType}),ne}return{status:"valid",value:s.data}}};hs.create=s=>new hs({typeName:ee.ZodNaN,...ae(s)});const fc=Symbol("zod_brand");let tr=class extends ce{_parse(s){const{ctx:e}=this._processInputParams(s),t=e.data;return this._def.type._parse({data:t,path:e.path,parent:e})}unwrap(){return this._def.type}},nr=class Ei extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?ne:r.status==="dirty"?(t.dirty(),ls(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?ne:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Ei({in:e,out:t,typeName:ee.ZodPipeline})}},Zn=class extends ce{_parse(s){const e=this._def.innerType._parse(s),t=n=>(Ut(n)&&(n.value=Object.freeze(n.value)),n);return $n(e)?e.then(n=>t(n)):t(e)}unwrap(){return this._def.innerType}};function wr(s,e={},t){return s?sn.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):sn.create()}Zn.create=(s,e)=>new Zn({innerType:s,typeName:ee.ZodReadonly,...ae(e)});const hc={object:Ke.lazycreate};var ee;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(ee||(ee={}));const xr=nn.create,br=vn.create,gc=hs.create,$c=yn.create,kr=_n.create,vc=wn.create,yc=us.create,_c=xn.create,wc=bn.create,xc=sn.create,bc=Lt.create,kc=yt.create,Sc=ps.create,Cc=Vt.create,Tc=Ke.create,Mc=Ke.strictCreate,Ac=kn.create,Nc=Si.create,Zc=Sn.create,Ec=Mt.create,Ic=Mi.create,Rc=ms.create,Oc=fs.create,Pc=Ni.create,jc=Cn.create,Lc=Tn.create,Fc=Kn.create,zc=Mn.create,Dc=rn.create,Sr=nt.create,Uc=ct.create,Vc=At.create,qc=nt.createWithPreprocess,Bc=nr.create,Jc={string:s=>nn.create({...s,coerce:!0}),number:s=>vn.create({...s,coerce:!0}),boolean:s=>_n.create({...s,coerce:!0}),bigint:s=>yn.create({...s,coerce:!0}),date:s=>wn.create({...s,coerce:!0})},Gc=ne;var we=Object.freeze({__proto__:null,defaultErrorMap:tn,setErrorMap:function(s){vi=s},getErrorMap:os,makeIssue:cs,EMPTY_PATH:[],addIssueToContext:F,ParseStatus:Je,INVALID:ne,DIRTY:ls,OK:Ve,isAborted:Vs,isDirty:qs,isValid:Ut,isAsync:$n,get util(){return pe},get objectUtil(){return Us},ZodParsedType:V,getParsedType:gt,ZodType:ce,datetimeRegex:bi,ZodString:nn,ZodNumber:vn,ZodBigInt:yn,ZodBoolean:_n,ZodDate:wn,ZodSymbol:us,ZodUndefined:xn,ZodNull:bn,ZodAny:sn,ZodUnknown:Lt,ZodNever:yt,ZodVoid:ps,ZodArray:Vt,ZodObject:Ke,ZodUnion:kn,ZodDiscriminatedUnion:Si,ZodIntersection:Sn,ZodTuple:Mt,ZodRecord:Mi,ZodMap:ms,ZodSet:fs,ZodFunction:Ni,ZodLazy:Cn,ZodLiteral:Tn,ZodEnum:Kn,ZodNativeEnum:Mn,ZodPromise:rn,ZodEffects:nt,ZodTransformer:nt,ZodOptional:ct,ZodNullable:At,ZodDefault:An,ZodCatch:Nn,ZodNaN:hs,BRAND:fc,ZodBranded:tr,ZodPipeline:nr,ZodReadonly:Zn,custom:wr,Schema:ce,ZodSchema:ce,late:hc,get ZodFirstPartyTypeKind(){return ee},coerce:Jc,any:xc,array:Cc,bigint:$c,boolean:kr,date:vc,discriminatedUnion:Nc,effect:Sr,enum:Fc,function:Pc,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>wr(t=>t instanceof s,e),intersection:Zc,lazy:jc,literal:Lc,map:Rc,nan:gc,nativeEnum:zc,never:kc,null:wc,nullable:Vc,number:br,object:Tc,oboolean:()=>kr().optional(),onumber:()=>br().optional(),optional:Uc,ostring:()=>xr().optional(),pipeline:Bc,preprocess:qc,promise:Dc,record:Ic,set:Oc,strictObject:Mc,string:xr,symbol:yc,transformer:Sr,tuple:Ec,undefined:_c,union:Ac,unknown:bc,void:Sc,NEVER:Gc,ZodIssueCode:R,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:at});const Ye=we.object({name:we.string().optional(),title:we.string().optional(),command:we.string().optional(),args:we.array(we.union([we.string(),we.number(),we.boolean()])).optional(),env:we.record(we.union([we.string(),we.number(),we.boolean(),we.null(),we.undefined()])).optional()}).passthrough(),Wc=we.array(Ye),Hc=we.object({servers:we.array(Ye)}).passthrough(),Kc=we.object({mcpServers:we.array(Ye)}).passthrough(),Yc=we.object({servers:we.record(Ye)}).passthrough(),Xc=we.object({mcpServers:we.record(Ye)}).passthrough(),Qc=we.record(Ye),el=Ye.refine(s=>s.command!==void 0,{message:"Server must have a 'command' property"}),tl=Symbol("MCPServerError");let He=class Ii extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,Ii.prototype)}};var ri,zs;let nl=(ri=tl,zs=class{constructor(s){ye(this,"servers",De([]));this.host=s,this.loadServersFromStorage()}handleMessageFromExtension(s){const e=s.data;if(e.type===ve.getStoredMCPServersResponse){const t=e.data;return Array.isArray(t)&&this.servers.set(t),!0}return!1}async importServersFromJSON(s){return this.importFromJSON(s)}loadServersFromStorage(){try{this.host.postMessage({type:ve.getStoredMCPServers})}catch(s){console.error("Failed to load MCP servers:",s),this.servers.set([])}}saveServers(s){try{this.host.postMessage({type:ve.setStoredMCPServers,data:s})}catch(e){throw console.error("Failed to save MCP servers:",e),new He("Failed to save MCP servers")}}getServers(){return this.servers}addServer(s){this.checkExistingServerName(s.name),this.servers.update(e=>{const t=[...e,{...s,id:crypto.randomUUID()}];return this.saveServers(t),t})}addServers(s){for(const e of s)this.checkExistingServerName(e.name);this.servers.update(e=>{const t=[...e,...s.map(n=>({...n,id:crypto.randomUUID()}))];return this.saveServers(t),t})}checkExistingServerName(s,e){const t=gn(this.servers).find(n=>n.name===s);if(t&&(t==null?void 0:t.id)!==e)throw new He(`Server name '${s}' already exists`)}updateServer(s){this.checkExistingServerName(s.name,s.id),this.servers.update(e=>{const t=e.map(n=>n.id===s.id?s:n);return this.saveServers(t),t})}deleteServer(s){this.servers.update(e=>{const t=e.filter(n=>n.id!==s);return this.saveServers(t),t})}toggleDisabledServer(s){this.servers.update(e=>{const t=e.map(n=>n.id===s?{...n,disabled:!n.disabled}:n);return this.saveServers(t),t})}static convertServerToJSON(s){return JSON.stringify({mcpServers:{[s.name]:{command:s.command.split(" ")[0],args:s.command.split(" ").slice(1),env:s.env}}},null,2)}static parseServerValidationMessages(s){const e=new Map,t=new Map;s.forEach(r=>{var a;r.disabled?e.set(r.id,"MCP server has been manually disabled"):r.tools&&r.tools.length===0?e.set(r.id,"No tools are available for this MCP server"):r.disabledTools&&r.disabledTools.length===((a=r.tools)==null?void 0:a.length)?e.set(r.id,"All tools for this MCP server have validation errors: "+r.disabledTools.join(", ")):r.disabledTools&&r.disabledTools.length>0&&t.set(r.id,"MCP server has validation errors in the following tools which have been disabled: "+r.disabledTools.join(", "))});const n=this.parseDuplicateServerIds(s);return{errors:new Map([...e,...n]),warnings:t}}static parseDuplicateServerIds(s){const e=new Map;for(const n of s)e.has(n.name)||e.set(n.name,[]),e.get(n.name).push(n.id);const t=new Map;for(const[,n]of e)if(n.length>1)for(let r=1;r<n.length;r++)t.set(n[r],"MCP server is disabled due to duplicate server names");return t}parseServerConfigFromJSON(s){try{const e=JSON.parse(s),t=we.union([Wc.transform(n=>n.map(r=>this.normalizeServerConfig(r))),Hc.transform(n=>n.servers.map(r=>this.normalizeServerConfig(r))),Kc.transform(n=>n.mcpServers.map(r=>this.normalizeServerConfig(r))),Yc.transform(n=>Object.entries(n.servers).map(([r,a])=>{const i=Ye.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),Xc.transform(n=>Object.entries(n.mcpServers).map(([r,a])=>{const i=Ye.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),Qc.transform(n=>{if(!Object.values(n).some(r=>{const a=Ye.safeParse(r);return a.success&&a.data.command!==void 0}))throw new Error("No command property found in any server config");return Object.entries(n).map(([r,a])=>{const i=Ye.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})}),el.transform(n=>[this.normalizeServerConfig(n)])]).safeParse(e);if(t.success)return t.data;throw new He("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(e){throw e instanceof He?e:new He("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(s){try{const e=this.parseServerConfigFromJSON(s),t=gn(this.servers),n=new Set(t.map(r=>r.name));for(const r of e){if(!r.name)throw new He("All servers must have a name.");if(n.has(r.name))throw new He(`A server with the name '${r.name}' already exists.`);n.add(r.name)}return this.servers.update(r=>{const a=[...r,...e.map(i=>({...i,id:crypto.randomUUID()}))];return this.saveServers(a),a}),e.length}catch(e){throw e instanceof He?e:new He("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(s){try{const e=Ye.transform(t=>{const n=t.command||"",r=t.args?t.args.map(c=>String(c)):[];if(!n)throw new Error("Server must have a 'command' property");const a=r.length>0?`${n} ${r.join(" ")}`:n,i=t.name||t.title||(n?n.split(" ")[0]:""),o=t.env?Object.fromEntries(Object.entries(t.env).filter(([c,l])=>l!=null).map(([c,l])=>[c,String(l)])):void 0;return{name:i,command:a,arguments:"",useShellInterpolation:!0,env:Object.keys(o||{}).length>0?o:void 0}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>!!t.command,{message:"Server must have a command",path:["command"]}).safeParse(s);if(!e.success)throw new He(e.error.message);return e.data}catch(e){throw e instanceof Error?new He(`Invalid server configuration: ${e.message}`):new He("Invalid server configuration")}}},ye(zs,ri,"MCPServerError"),zs);class sl{constructor(e){ye(this,"_terminalSettings",De({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===ve.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:ve.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:ve.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:ve.updateTerminalSettings,data:{startupScript:e}})}}function En(s,e){return t=>!t.shiftKey&&t.key===s&&(e(t),!0)}var vt=(s=>(s.file="file",s.folder="folder",s))(vt||{});class Ct{constructor(e,t){ye(this,"subscribe");ye(this,"set");ye(this,"update");ye(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case ve.wsContextSourceFoldersChanged:case ve.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case ve.sourceFoldersSyncStatus:this.update(n=>({...n,syncStatus:t.data.status}))}});ye(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:ve.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);ye(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:ve.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,n)=>t.type===n.type?t.name.localeCompare(n.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:n,set:r,update:a}=De({sourceFolders:[],sourceTree:[],syncStatus:Ds.done});this.subscribe=n,this.set=r,this.update=a,this.getSourceFolders().then(i=>{this.update(o=>({...o,sourceFolders:i,sourceTree:Ct.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==tt.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:ve.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:ve.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:ve.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=gn(this);const n=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(r=>({...r,sourceFolders:e,sourceTree:n}))}async getRefreshedSourceTree(e,t){const n=Ct.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,n)}async getRefreshedSourceTreeRecurse(e,t){const n=new Map(e.map(r=>[JSON.stringify([r.fileId.folderRoot,r.fileId.relPath]),r]));for(let r of t){const a=Ct.fileIdToString(r.fileId);if(r.type==="folder"){const i=n.get(a);i&&(r.expanded=i.type==="folder"&&i.expanded,r.expanded&&(r.children=await this.getChildren(r.fileId),r.children=await this.getRefreshedSourceTreeRecurse(i.children,r.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,n)=>t.name.localeCompare(n.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}function Cr(s,e,t){const n=s.slice();return n[6]=e[t],n}function Tr(s){let e,t;function n(){return s[5](s[6])}return e=new Ms({props:{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$slots:{default:[rl]},$$scope:{ctx:s}}}),e.$on("click",function(){return s[4](s[6])}),e.$on("keyup",function(){zt(En("Enter",n))&&En("Enter",n).apply(this,arguments)}),{c(){w(e.$$.fragment)},m(r,a){x(e,r,a),t=!0},p(r,a){s=r;const i={};512&a&&(i.$$scope={dirty:a,ctx:s}),e.$set(i)},i(r){t||(p(e.$$.fragment,r),t=!0)},o(r){m(e.$$.fragment,r),t=!1},d(r){b(e,r)}}}function rl(s){let e,t;return e=new wo({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Mr(s){let e,t;return e=new oe({props:{size:1,class:"file-count",$$slots:{default:[al]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};513&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function al(s){let e,t=s[6].trackedFileCount.toLocaleString()+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){1&r&&t!==(t=n[6].trackedFileCount.toLocaleString()+"")&&he(e,t)},d(n){n&&v(e)}}}function Ar(s,e){let t,n,r,a,i,o,c,l,d,u,g,f=e[6].name+"",$=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"",h=!e[6].isWorkspaceFolder&&Tr(e);r=new pi({props:{class:"source-folder-v-adjust",icon:e[3](e[6])}});let k=e[6].trackedFileCount&&Mr(e);return{key:s,first:null,c(){t=C("div"),h&&h.c(),n=Z(),w(r.$$.fragment),a=Z(),i=C("span"),o=L(f),c=Z(),l=C("span"),d=L($),u=Z(),k&&k.c(),_(l,"class","folderRoot svelte-1skknri"),_(i,"class","name svelte-1skknri"),_(t,"class","item svelte-1skknri"),be(t,"workspace-folder",e[6].isWorkspaceFolder),this.first=t},m(A,E){y(A,t,E),h&&h.m(t,null),S(t,n),x(r,t,null),S(t,a),S(t,i),S(i,o),S(i,c),S(i,l),S(l,d),S(t,u),k&&k.m(t,null),g=!0},p(A,E){(e=A)[6].isWorkspaceFolder?h&&(B(),m(h,1,1,()=>{h=null}),J()):h?(h.p(e,E),1&E&&p(h,1)):(h=Tr(e),h.c(),p(h,1),h.m(t,n));const N={};1&E&&(N.icon=e[3](e[6])),r.$set(N),(!g||1&E)&&f!==(f=e[6].name+"")&&he(o,f),(!g||1&E)&&$!==($=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"")&&he(d,$),e[6].trackedFileCount?k?(k.p(e,E),1&E&&p(k,1)):(k=Mr(e),k.c(),p(k,1),k.m(t,null)):k&&(B(),m(k,1,1,()=>{k=null}),J()),(!g||1&E)&&be(t,"workspace-folder",e[6].isWorkspaceFolder)},i(A){g||(p(h),p(r.$$.fragment,A),p(k),g=!0)},o(A){m(h),m(r.$$.fragment,A),m(k),g=!1},d(A){A&&v(t),h&&h.d(),b(r),k&&k.d()}}}function il(s){let e,t,n,r,a,i,o,c,l=[],d=new Map,u=_e(s[0]);const g=f=>Ct.fileIdToString(f[6].fileId);for(let f=0;f<u.length;f+=1){let $=Cr(s,u,f),h=g($);d.set(h,l[f]=Ar(h,$))}return r=new Ts({}),{c(){e=C("div");for(let f=0;f<l.length;f+=1)l[f].c();t=Z(),n=C("div"),w(r.$$.fragment),a=L(" Add more..."),_(n,"role","button"),_(n,"tabindex","0"),_(n,"class","add-more svelte-1skknri"),_(e,"class","source-folder svelte-1skknri")},m(f,$){y(f,e,$);for(let h=0;h<l.length;h+=1)l[h]&&l[h].m(e,null);S(e,t),S(e,n),x(r,n,null),S(n,a),i=!0,o||(c=[Le(n,"keyup",function(){zt(En("Enter",s[1]))&&En("Enter",s[1]).apply(this,arguments)}),Le(n,"click",function(){zt(s[1])&&s[1].apply(this,arguments)})],o=!0)},p(f,[$]){s=f,13&$&&(u=_e(s[0]),B(),l=Rt(l,$,g,1,s,u,d,e,Ot,Ar,t,Cr),J())},i(f){if(!i){for(let $=0;$<u.length;$+=1)p(l[$]);p(r.$$.fragment,f),i=!0}},o(f){for(let $=0;$<l.length;$+=1)m(l[$]);m(r.$$.fragment,f),i=!1},d(f){f&&v(e);for(let $=0;$<l.length;$+=1)l[$].d();b(r),o=!1,ks(c)}}}function ol(s,e,t){let{folders:n=[]}=e,{onAddMore:r}=e,{onRemove:a}=e;return s.$$set=i=>{"folders"in i&&t(0,n=i.folders),"onAddMore"in i&&t(1,r=i.onAddMore),"onRemove"in i&&t(2,a=i.onRemove)},[n,r,a,i=>i.isWorkspaceFolder?"root-folder":"folder",i=>a(i.fileId.folderRoot),i=>a(i.fileId.folderRoot)]}class cl extends ge{constructor(e){super(),$e(this,e,ol,il,fe,{folders:0,onAddMore:1,onRemove:2})}}function Nr(s,e,t){const n=s.slice();return n[10]=e[t],n}function Zr(s){let e,t;return e=new oe({props:{size:1,class:"file-count",$$slots:{default:[ll]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8193&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ll(s){let e,t=s[0].trackedFileCount.toLocaleString()+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){1&r&&t!==(t=n[0].trackedFileCount.toLocaleString()+"")&&he(e,t)},d(n){n&&v(e)}}}function Er(s){let e,t,n=[],r=new Map,a=_e(s[5].children);const i=o=>Ct.fileIdToString(o[10].fileId);for(let o=0;o<a.length;o+=1){let c=Nr(s,a,o),l=i(c);r.set(l,n[o]=Ir(l,c))}return{c(){e=C("div");for(let o=0;o<n.length;o+=1)n[o].c();_(e,"class","children-container")},m(o,c){y(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,c){38&c&&(a=_e(o[5].children),B(),n=Rt(n,c,i,1,o,a,r,e,Ot,Ir,null,Nr),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function Ir(s,e){let t,n,r;return n=new Ri({props:{data:e[10],wsContextModel:e[1],indentLevel:e[2]+1}}),{key:s,first:null,c(){t=ke(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};32&i&&(o.data=e[10]),2&i&&(o.wsContextModel=e[1]),4&i&&(o.indentLevel=e[2]+1),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function dl(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h,k,A,E,N=s[0].name+"";n=new pi({props:{icon:s[4]}});let T=s[0].type===vt.folder&&s[0].inclusionState!==tt.excluded&&typeof s[0].trackedFileCount=="number"&&Zr(s),M=s[5]&&Er(s);return{c(){e=C("div"),t=C("div"),w(n.$$.fragment),r=Z(),a=C("span"),i=L(N),o=Z(),T&&T.c(),c=Z(),l=C("img"),h=Z(),M&&M.c(),_(a,"class","name svelte-sympus"),pr(l.src,d=s[7][s[0].inclusionState])||_(l,"src",d),_(l,"alt",u=s[8][s[0].inclusionState]),_(t,"class","tree-item svelte-sympus"),_(t,"role","treeitem"),_(t,"aria-selected","false"),_(t,"tabindex","0"),_(t,"title",g=s[0].reason),_(t,"aria-expanded",f=s[0].type===vt.folder&&s[0].expanded),_(t,"aria-level",s[2]),_(t,"style",$=`padding-left: ${10*s[2]+20}px;`),be(t,"included-folder",s[3])},m(I,U){y(I,e,U),S(e,t),x(n,t,null),S(t,r),S(t,a),S(a,i),S(t,o),T&&T.m(t,null),S(t,c),S(t,l),S(e,h),M&&M.m(e,null),k=!0,A||(E=[Le(t,"click",s[6]),Le(t,"keyup",En("Enter",s[6]))],A=!0)},p(I,[U]){const Q={};16&U&&(Q.icon=I[4]),n.$set(Q),(!k||1&U)&&N!==(N=I[0].name+"")&&he(i,N),I[0].type===vt.folder&&I[0].inclusionState!==tt.excluded&&typeof I[0].trackedFileCount=="number"?T?(T.p(I,U),1&U&&p(T,1)):(T=Zr(I),T.c(),p(T,1),T.m(t,c)):T&&(B(),m(T,1,1,()=>{T=null}),J()),(!k||1&U&&!pr(l.src,d=I[7][I[0].inclusionState]))&&_(l,"src",d),(!k||1&U&&u!==(u=I[8][I[0].inclusionState]))&&_(l,"alt",u),(!k||1&U&&g!==(g=I[0].reason))&&_(t,"title",g),(!k||1&U&&f!==(f=I[0].type===vt.folder&&I[0].expanded))&&_(t,"aria-expanded",f),(!k||4&U)&&_(t,"aria-level",I[2]),(!k||4&U&&$!==($=`padding-left: ${10*I[2]+20}px;`))&&_(t,"style",$),(!k||8&U)&&be(t,"included-folder",I[3]),I[5]?M?(M.p(I,U),32&U&&p(M,1)):(M=Er(I),M.c(),p(M,1),M.m(e,null)):M&&(B(),m(M,1,1,()=>{M=null}),J())},i(I){k||(p(n.$$.fragment,I),p(T),p(M),k=!0)},o(I){m(n.$$.fragment,I),m(T),m(M),k=!1},d(I){I&&v(e),b(n),T&&T.d(),M&&M.d(),A=!1,ks(E)}}}function ul(s,e,t){let{data:n}=e,{wsContextModel:r}=e,{indentLevel:a}=e;const i={[tt.included]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",[tt.excluded]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",[tt.partial]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e"},o={[tt.included]:"included",[tt.excluded]:"excluded",[tt.partial]:"partially included"};let c,l,d;return s.$$set=u=>{"data"in u&&t(0,n=u.data),"wsContextModel"in u&&t(1,r=u.wsContextModel),"indentLevel"in u&&t(2,a=u.indentLevel)},s.$$.update=()=>{var u;1&s.$$.dirty&&t(4,l=(u=n).type===vt.folder&&u.inclusionState!==tt.excluded?u.expanded?"chevron-down":"chevron-right":u.type===vt.folder?"folder":"file"),1&s.$$.dirty&&t(3,c=n.type===vt.folder&&n.inclusionState!==tt.excluded),1&s.$$.dirty&&t(5,d=n.type===vt.folder&&n.expanded&&n.children&&n.children.length>0?n:null)},[n,r,a,c,l,d,()=>{r.toggleNode(n)},i,o]}class Ri extends ge{constructor(e){super(),$e(this,e,ul,dl,fe,{data:0,wsContextModel:1,indentLevel:2})}}function Rr(s,e,t){const n=s.slice();return n[3]=e[t],n}function Or(s,e){let t,n,r;return n=new Ri({props:{wsContextModel:e[0],data:e[3],indentLevel:0}}),{key:s,first:null,c(){t=ke(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};1&i&&(o.wsContextModel=e[0]),2&i&&(o.data=e[3]),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function pl(s){let e,t,n=[],r=new Map,a=_e(s[1]);const i=o=>Ct.fileIdToString(o[3].fileId);for(let o=0;o<a.length;o+=1){let c=Rr(s,a,o),l=i(c);r.set(l,n[o]=Or(l,c))}return{c(){e=C("div");for(let o=0;o<n.length;o+=1)n[o].c();_(e,"class","files-container svelte-8hfqhl")},m(o,c){y(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,[c]){3&c&&(a=_e(o[1]),B(),n=Rt(n,c,i,1,o,a,r,e,Ot,Or,null,Rr),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function ml(s,e,t){let n,r=W,a=()=>(r(),r=Ss(o,c=>t(2,n=c)),o);s.$$.on_destroy.push(()=>r());let i,{wsContextModel:o}=e;return a(),s.$$set=c=>{"wsContextModel"in c&&a(t(0,o=c.wsContextModel))},s.$$.update=()=>{4&s.$$.dirty&&t(1,i=n.sourceTree)},[o,i,n]}class fl extends ge{constructor(e){super(),$e(this,e,ml,pl,fe,{wsContextModel:0})}}function hl(s){let e,t,n;return{c(){e=Be("svg"),t=Be("rect"),n=Be("path"),_(t,"width","16"),_(t,"height","16"),_(t,"transform","matrix(-1 0 0 -1 16 16)"),_(t,"fill","currentColor"),_(t,"fill-opacity","0.01"),_(n,"fill-rule","evenodd"),_(n,"clip-rule","evenodd"),_(n,"d","M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z"),_(n,"fill","currentColor"),_(e,"width","15"),_(e,"height","15"),_(e,"viewBox","0 0 16 16"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(r,a){y(r,e,a),S(e,t),S(e,n)},p:W,i:W,o:W,d(r){r&&v(e)}}}class gl extends ge{constructor(e){super(),$e(this,e,null,hl,fe,{})}}const $l=s=>({}),Pr=s=>({}),vl=s=>({}),jr=s=>({});function yl(s){let e;const t=s[8]["header-left"],n=Ie(t,s,s[10],jr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||1024&a)&&Re(n,t,r,r[10],e?Pe(t,r[10],a,vl):Oe(r[10]),jr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function _l(s){let e,t,n,r=s[0]&&Lr(s),a=s[1]&&Fr(s);return{c(){r&&r.c(),e=Z(),a&&a.c(),t=ke()},m(i,o){r&&r.m(i,o),y(i,e,o),a&&a.m(i,o),y(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=Lr(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(B(),m(r,1,1,()=>{r=null}),J()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=Fr(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(B(),m(a,1,1,()=>{a=null}),J())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&(v(e),v(t)),r&&r.d(i),a&&a.d(i)}}}function Lr(s){let e,t,n;var r=s[0];return r&&(t=Tt(r,{})),{c(){e=C("div"),t&&w(t.$$.fragment),_(e,"class","icon-wrapper svelte-13uht7n")},m(a,i){y(a,e,i),t&&x(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){B();const o=t;m(o.$$.fragment,1,0,()=>{b(o,1)}),J()}r?(t=Tt(r,{}),w(t.$$.fragment),p(t.$$.fragment,1),x(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&v(e),t&&b(t)}}}function Fr(s){let e,t;return e=new oe({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[wl]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1026&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function wl(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&he(e,t[1])},d(t){t&&v(e)}}}function zr(s){let e,t;const n=s[8].default,r=Ie(n,s,s[10],null);return{c(){e=C("div"),r&&r.c(),_(e,"class","settings-card-body")},m(a,i){y(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||1024&i)&&Re(r,n,a,a[10],t?Pe(n,a[10],i,null):Oe(a[10]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&v(e),r&&r.d(a)}}}function xl(s){let e,t,n,r,a,i,o,c,l,d,u;const g=[_l,yl],f=[];function $(T,M){return T[0]||T[1]?0:1}r=$(s),a=f[r]=g[r](s);const h=s[8]["header-right"],k=Ie(h,s,s[10],Pr);let A=s[5].default&&zr(s),E=[{role:"button"},{class:s[3]},s[4]],N={};for(let T=0;T<E.length;T+=1)N=Te(N,E[T]);return{c(){e=C("div"),t=C("div"),n=C("div"),a.c(),i=Z(),o=C("div"),k&&k.c(),c=Z(),A&&A.c(),_(n,"class","settings-card-left svelte-13uht7n"),_(o,"class","settings-card-right svelte-13uht7n"),_(t,"class","settings-card-content svelte-13uht7n"),rs(e,N),be(e,"clickable",s[2]),be(e,"svelte-13uht7n",!0)},m(T,M){y(T,e,M),S(e,t),S(t,n),f[r].m(n,null),S(t,i),S(t,o),k&&k.m(o,null),S(e,c),A&&A.m(e,null),l=!0,d||(u=Le(e,"click",s[9]),d=!0)},p(T,[M]){let I=r;r=$(T),r===I?f[r].p(T,M):(B(),m(f[I],1,1,()=>{f[I]=null}),J(),a=f[r],a?a.p(T,M):(a=f[r]=g[r](T),a.c()),p(a,1),a.m(n,null)),k&&k.p&&(!l||1024&M)&&Re(k,h,T,T[10],l?Pe(h,T[10],M,$l):Oe(T[10]),Pr),T[5].default?A?(A.p(T,M),32&M&&p(A,1)):(A=zr(T),A.c(),p(A,1),A.m(e,null)):A&&(B(),m(A,1,1,()=>{A=null}),J()),rs(e,N=wt(E,[{role:"button"},(!l||8&M)&&{class:T[3]},16&M&&T[4]])),be(e,"clickable",T[2]),be(e,"svelte-13uht7n",!0)},i(T){l||(p(a),p(k,T),p(A),l=!0)},o(T){m(a),m(k,T),m(A),l=!1},d(T){T&&v(e),f[r].d(),k&&k.d(T),A&&A.d(),d=!1,u()}}}function bl(s,e,t){let n,r,a;const i=["class","icon","title","isClickable"];let o=as(e,i),{$$slots:c={},$$scope:l}=e;const d=er(c);let{class:u=""}=e,{icon:g}=e,{title:f}=e,{isClickable:$=!1}=e;return s.$$set=h=>{e=Te(Te({},e),Qe(h)),t(11,o=as(e,i)),"class"in h&&t(6,u=h.class),"icon"in h&&t(0,g=h.icon),"title"in h&&t(1,f=h.title),"isClickable"in h&&t(2,$=h.isClickable),"$$scope"in h&&t(10,l=h.$$scope)},s.$$.update=()=>{t(7,{class:n,...r}=o,n,(t(4,r),t(11,o))),192&s.$$.dirty&&t(3,a=`settings-card ${u} ${n||""}`)},[g,f,$,a,r,d,u,n,c,function(h){is.call(this,s,h)},l]}let Oi=class extends ge{constructor(s){super(),$e(this,s,bl,xl,fe,{class:6,icon:0,title:1,isClickable:2})}};function kl(s){let e;return{c(){e=L("SOURCE FOLDERS")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Sl(s){let e;return{c(){e=L("FILES")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Cl(s){let e,t=s[2].toLocaleString()+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){4&r&&t!==(t=n[2].toLocaleString()+"")&&he(e,t)},d(n){n&&v(e)}}}function Tl(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$;return n=new oe({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[kl]},$$scope:{ctx:s}}}),a=new cl({props:{folders:s[0],onRemove:s[7],onAddMore:s[8]}}),l=new oe({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Sl]},$$scope:{ctx:s}}}),u=new oe({props:{size:1,class:"file-count",$$slots:{default:[Cl]},$$scope:{ctx:s}}}),f=new fl({props:{wsContextModel:s[3]}}),{c(){e=C("div"),t=C("div"),w(n.$$.fragment),r=Z(),w(a.$$.fragment),i=Z(),o=C("div"),c=C("div"),w(l.$$.fragment),d=Z(),w(u.$$.fragment),g=Z(),w(f.$$.fragment),_(c,"class","files-header svelte-qsnirf"),_(e,"class","context-list svelte-qsnirf")},m(h,k){y(h,e,k),S(e,t),x(n,t,null),S(t,r),x(a,t,null),S(e,i),S(e,o),S(o,c),x(l,c,null),S(c,d),x(u,c,null),S(o,g),x(f,o,null),$=!0},p(h,k){const A={};512&k&&(A.$$scope={dirty:k,ctx:h}),n.$set(A);const E={};1&k&&(E.folders=h[0]),a.$set(E);const N={};512&k&&(N.$$scope={dirty:k,ctx:h}),l.$set(N);const T={};516&k&&(T.$$scope={dirty:k,ctx:h}),u.$set(T)},i(h){$||(p(n.$$.fragment,h),p(a.$$.fragment,h),p(l.$$.fragment,h),p(u.$$.fragment,h),p(f.$$.fragment,h),$=!0)},o(h){m(n.$$.fragment,h),m(a.$$.fragment,h),m(l.$$.fragment,h),m(u.$$.fragment,h),m(f.$$.fragment,h),$=!1},d(h){h&&v(e),b(n),b(a),b(l),b(u),b(f)}}}function Dr(s){let e,t;return e=new Ms({props:{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[Ml]},$$scope:{ctx:s}}}),e.$on("click",s[5]),e.$on("keyup",Io("Enter",s[6])),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};512&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ml(s){let e,t;return e=new xo({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Al(s){let e,t,n=s[1]===Ds.done&&Dr(s);return{c(){e=C("div"),n&&n.c(),_(e,"slot","header-right")},m(r,a){y(r,e,a),n&&n.m(e,null),t=!0},p(r,a){r[1]===Ds.done?n?(n.p(r,a),2&a&&p(n,1)):(n=Dr(r),n.c(),p(n,1),n.m(e,null)):n&&(B(),m(n,1,1,()=>{n=null}),J())},i(r){t||(p(n),t=!0)},o(r){m(n),t=!1},d(r){r&&v(e),n&&n.d()}}}function Nl(s){let e,t,n,r;return e=new Oi({props:{icon:gl,title:"Context",$$slots:{"header-right":[Al],default:[Tl]},$$scope:{ctx:s}}}),e.$on("contextmenu",Zl),{c(){w(e.$$.fragment)},m(a,i){x(e,a,i),t=!0,n||(r=Le(window,"message",s[3].handleMessageFromExtension),n=!0)},p(a,[i]){const o={};519&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){b(e,a),n=!1,r()}}}const Zl=s=>s.preventDefault();function El(s,e,t){let n,r,a,i,o=new Ct(je,new Eo(je.postMessage));return ot(s,o,c=>t(4,r=c)),s.$$.update=()=>{16&s.$$.dirty&&t(0,a=r.sourceFolders.sort((c,l)=>c.isWorkspaceFolder!==l.isWorkspaceFolder?c.isWorkspaceFolder?-1:1:c.fileId.folderRoot.localeCompare(l.fileId.folderRoot))),16&s.$$.dirty&&t(1,i=r.syncStatus),1&s.$$.dirty&&t(2,n=a.reduce((c,l)=>c+(l.trackedFileCount??0),0))},[a,i,n,o,r,()=>o.requestRefresh(),()=>o.requestRefresh(),c=>o.removeSourceFolder(c),()=>o.addMoreSourceFolders()]}class Il extends ge{constructor(e){super(),$e(this,e,El,Nl,fe,{})}}function Pi(s){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(s)&&"name"in s}function Ur(s){return Pi(s)&&"component"in s}function Rl(s){let e,t;return{c(){e=Be("svg"),t=Be("path"),_(t,"d","M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","15"),_(e,"viewBox","0 0 16 15"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){y(n,e,r),S(e,t)},p:W,i:W,o:W,d(n){n&&v(e)}}}class ji extends ge{constructor(e){super(),$e(this,e,null,Rl,fe,{})}}const Ol=s=>({item:1&s}),Vr=s=>({item:s[0]}),Pl=s=>({}),qr=s=>({});function Br(s){var l;let e,t,n,r,a;e=new oe({props:{size:4,weight:"medium",color:"neutral",$$slots:{default:[jl]},$$scope:{ctx:s}}});let i=((l=s[0])==null?void 0:l.description)&&Jr(s);const o=s[1].content,c=Ie(o,s,s[2],Vr);return{c(){w(e.$$.fragment),t=Z(),i&&i.c(),n=Z(),r=C("div"),c&&c.c(),_(r,"class","c-navigation__content-container svelte-z0ijuz")},m(d,u){x(e,d,u),y(d,t,u),i&&i.m(d,u),y(d,n,u),y(d,r,u),c&&c.m(r,null),a=!0},p(d,u){var f;const g={};5&u&&(g.$$scope={dirty:u,ctx:d}),e.$set(g),(f=d[0])!=null&&f.description?i?(i.p(d,u),1&u&&p(i,1)):(i=Jr(d),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(B(),m(i,1,1,()=>{i=null}),J()),c&&c.p&&(!a||5&u)&&Re(c,o,d,d[2],a?Pe(o,d[2],u,Ol):Oe(d[2]),Vr)},i(d){a||(p(e.$$.fragment,d),p(i),p(c,d),a=!0)},o(d){m(e.$$.fragment,d),m(i),m(c,d),a=!1},d(d){d&&(v(t),v(n),v(r)),b(e,d),i&&i.d(d),c&&c.d(d)}}}function jl(s){var r;let e,t,n=((r=s[0])==null?void 0:r.name)+"";return{c(){e=C("div"),t=L(n),_(e,"class","c-navigation__content-header svelte-z0ijuz")},m(a,i){y(a,e,i),S(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.name)+"")&&he(t,n)},d(a){a&&v(e)}}}function Jr(s){let e,t;return e=new oe({props:{color:"secondary",size:1,weight:"light",$$slots:{default:[Ll]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};5&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ll(s){var r;let e,t,n=((r=s[0])==null?void 0:r.description)+"";return{c(){e=C("div"),t=L(n),_(e,"class","c-navigation__content-description svelte-z0ijuz")},m(a,i){y(a,e,i),S(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.description)+"")&&he(t,n)},d(a){a&&v(e)}}}function Fl(s){let e,t,n,r,a;const i=s[1].header,o=Ie(i,s,s[2],qr);let c=s[0]!=null&&Br(s);return{c(){var l;e=C("div"),o&&o.c(),t=Z(),n=C("div"),c&&c.c(),_(e,"class","c-navigation__content svelte-z0ijuz"),_(e,"id",r=(l=s[0])==null?void 0:l.id)},m(l,d){y(l,e,d),o&&o.m(e,null),S(e,t),S(e,n),c&&c.m(n,null),a=!0},p(l,[d]){var u;o&&o.p&&(!a||4&d)&&Re(o,i,l,l[2],a?Pe(i,l[2],d,Pl):Oe(l[2]),qr),l[0]!=null?c?(c.p(l,d),1&d&&p(c,1)):(c=Br(l),c.c(),p(c,1),c.m(n,null)):c&&(B(),m(c,1,1,()=>{c=null}),J()),(!a||1&d&&r!==(r=(u=l[0])==null?void 0:u.id))&&_(e,"id",r)},i(l){a||(p(o,l),p(c),a=!0)},o(l){m(o,l),m(c),a=!1},d(l){l&&v(e),o&&o.d(l),c&&c.d()}}}function zl(s,e,t){let{$$slots:n={},$$scope:r}=e,{item:a}=e;return s.$$set=i=>{"item"in i&&t(0,a=i.item),"$$scope"in i&&t(2,r=i.$$scope)},[a,n,r]}class Li extends ge{constructor(e){super(),$e(this,e,zl,Fl,fe,{item:0})}}function Dl(s,e){let t;function n({scrollTo:r,delay:a,options:i}){clearTimeout(t),r&&(t=setTimeout(()=>{s.scrollIntoView(i)},a))}return n(e),{update:n,destroy(){clearTimeout(t)}}}function Gr(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function Wr(s,e,t){const n=s.slice();return n[22]=e[t],n}const Ul=s=>({item:32&s}),Hr=s=>({slot:"content",item:s[22]}),Vl=s=>({label:32&s,mode:4&s}),Kr=s=>({label:s[13],mode:s[2]}),ql=s=>({item:1&s}),Yr=s=>({item:s[0]});function Xr(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function Qr(s,e,t){const n=s.slice();return n[17]=e[t],n}const Bl=s=>({label:32&s,mode:4&s}),ea=s=>({label:s[13],mode:s[2]}),Jl=s=>({item:1&s,selectedId:2&s}),ta=s=>({slot:"header",item:s[0],selectedId:s[1]}),Gl=s=>({item:1&s,isSelected:3&s}),na=s=>{var e;return{slot:"content",item:s[0],isSelected:((e=s[0])==null?void 0:e.id)===s[1]}};function Wl(s){let e,t,n;const r=s[10].header,a=Ie(r,s,s[12],Yr);let i=_e(s[5]),o=[];for(let l=0;l<i.length;l+=1)o[l]=ra(Gr(s,i,l));const c=l=>m(o[l],1,1,()=>{o[l]=null});return{c(){e=C("div"),a&&a.c(),t=Z();for(let l=0;l<o.length;l+=1)o[l].c();_(e,"class","c-navigation__flat svelte-n5ccbo")},m(l,d){y(l,e,d),a&&a.m(e,null),S(e,t);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(e,null);n=!0},p(l,d){if(a&&a.p&&(!n||4097&d)&&Re(a,r,l,l[12],n?Pe(r,l[12],d,ql):Oe(l[12]),Yr),4134&d){let u;for(i=_e(l[5]),u=0;u<i.length;u+=1){const g=Gr(l,i,u);o[u]?(o[u].p(g,d),p(o[u],1)):(o[u]=ra(g),o[u].c(),p(o[u],1),o[u].m(e,null))}for(B(),u=i.length;u<o.length;u+=1)c(u);J()}},i(l){if(!n){p(a,l);for(let d=0;d<i.length;d+=1)p(o[d]);n=!0}},o(l){m(a,l),o=o.filter(Boolean);for(let d=0;d<o.length;d+=1)m(o[d]);n=!1},d(l){l&&v(e),a&&a.d(l),Gt(o,l)}}}function Hl(s){let e,t;return e=new Ro({props:{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,showButton:s[3],minimized:!1,$$slots:{right:[sd],left:[ed]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.showButton=n[3]),4135&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Kl(s){let e,t,n,r,a,i,o=s[13]+"";return t=new ji({}),{c(){e=C("span"),w(t.$$.fragment),n=Z(),r=C("span"),a=L(o),_(e,"class","c-navigation__head-icon")},m(c,l){y(c,e,l),x(t,e,null),y(c,n,l),y(c,r,l),S(r,a),i=!0},p(c,l){(!i||32&l)&&o!==(o=c[13]+"")&&he(a,o)},i(c){i||(p(t.$$.fragment,c),i=!0)},o(c){m(t.$$.fragment,c),i=!1},d(c){c&&(v(e),v(n),v(r)),b(t)}}}function Yl(s){let e;const t=s[10].content,n=Ie(t,s,s[12],Hr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4128&a)&&Re(n,t,r,r[12],e?Pe(t,r[12],a,Ul):Oe(r[12]),Hr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function sa(s){let e,t,n,r,a,i,o;return t=new Li({props:{item:s[22],$$slots:{content:[Yl]},$$scope:{ctx:s}}}),{c(){e=C("span"),w(t.$$.fragment),n=Z()},m(c,l){y(c,e,l),x(t,e,null),S(e,n),a=!0,i||(o=no(r=Dl.call(null,e,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})),i=!0)},p(c,l){s=c;const d={};32&l&&(d.item=s[22]),4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),r&&zt(r.update)&&38&l&&r.update.call(null,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})},i(c){a||(p(t.$$.fragment,c),a=!0)},o(c){m(t.$$.fragment,c),a=!1},d(c){c&&v(e),b(t),i=!1,o()}}}function ra(s){let e,t,n,r;const a=s[10].group,i=Ie(a,s,s[12],Kr),o=i||function(u){let g,f;return g=new oe({props:{color:"secondary",size:2,weight:"medium",$$slots:{default:[Kl]},$$scope:{ctx:u}}}),{c(){w(g.$$.fragment)},m($,h){x(g,$,h),f=!0},p($,h){const k={};4128&h&&(k.$$scope={dirty:h,ctx:$}),g.$set(k)},i($){f||(p(g.$$.fragment,$),f=!0)},o($){m(g.$$.fragment,$),f=!1},d($){b(g,$)}}}(s);let c=_e(s[14]),l=[];for(let u=0;u<c.length;u+=1)l[u]=sa(Wr(s,c,u));const d=u=>m(l[u],1,1,()=>{l[u]=null});return{c(){e=C("div"),o&&o.c(),t=Z();for(let u=0;u<l.length;u+=1)l[u].c();n=ke(),_(e,"class","c-navigation__head svelte-n5ccbo")},m(u,g){y(u,e,g),o&&o.m(e,null),y(u,t,g);for(let f=0;f<l.length;f+=1)l[f]&&l[f].m(u,g);y(u,n,g),r=!0},p(u,g){if(i?i.p&&(!r||4132&g)&&Re(i,a,u,u[12],r?Pe(a,u[12],g,Vl):Oe(u[12]),Kr):o&&o.p&&(!r||32&g)&&o.p(u,r?g:-1),4134&g){let f;for(c=_e(u[14]),f=0;f<c.length;f+=1){const $=Wr(u,c,f);l[f]?(l[f].p($,g),p(l[f],1)):(l[f]=sa($),l[f].c(),p(l[f],1),l[f].m(n.parentNode,n))}for(B(),f=c.length;f<l.length;f+=1)d(f);J()}},i(u){if(!r){p(o,u);for(let g=0;g<c.length;g+=1)p(l[g]);r=!0}},o(u){m(o,u),l=l.filter(Boolean);for(let g=0;g<l.length;g+=1)m(l[g]);r=!1},d(u){u&&(v(e),v(t),v(n)),o&&o.d(u),Gt(l,u)}}}function Xl(s){let e,t=s[13]+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){32&r&&t!==(t=n[13]+"")&&he(e,t)},d(n){n&&v(e)}}}function Ql(s){let e,t,n,r,a,i=s[17].name+"";var o=s[17].icon;return o&&(t=Tt(o,{})),{c(){e=C("span"),t&&w(t.$$.fragment),n=Z(),r=L(i),_(e,"class","c-navigation__head-icon")},m(c,l){y(c,e,l),t&&x(t,e,null),y(c,n,l),y(c,r,l),a=!0},p(c,l){if(32&l&&o!==(o=c[17].icon)){if(t){B();const d=t;m(d.$$.fragment,1,0,()=>{b(d,1)}),J()}o?(t=Tt(o,{}),w(t.$$.fragment),p(t.$$.fragment,1),x(t,e,null)):t=null}(!a||32&l)&&i!==(i=c[17].name+"")&&he(r,i)},i(c){a||(t&&p(t.$$.fragment,c),a=!0)},o(c){t&&m(t.$$.fragment,c),a=!1},d(c){c&&(v(e),v(n),v(r)),t&&b(t)}}}function aa(s){let e,t,n,r,a,i;function o(){return s[11](s[17])}return t=new oe({props:{size:2,weight:"regular",color:"primary",$$slots:{default:[Ql]},$$scope:{ctx:s}}}),{c(){e=C("button"),w(t.$$.fragment),n=Z(),_(e,"class","c-navigation__item svelte-n5ccbo"),be(e,"is-active",s[17].id===s[1])},m(c,l){y(c,e,l),x(t,e,null),S(e,n),r=!0,a||(i=Le(e,"click",o),a=!0)},p(c,l){s=c;const d={};4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),(!r||34&l)&&be(e,"is-active",s[17].id===s[1])},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&v(e),b(t),a=!1,i()}}}function ia(s){let e,t,n,r,a;const i=s[10].group,o=Ie(i,s,s[12],ea),c=o||function(g){let f,$,h,k,A;return $=new ji({}),k=new oe({props:{size:2,color:"primary",$$slots:{default:[Xl]},$$scope:{ctx:g}}}),{c(){f=C("div"),w($.$$.fragment),h=Z(),w(k.$$.fragment),_(f,"class","c-navigation__head svelte-n5ccbo")},m(E,N){y(E,f,N),x($,f,null),S(f,h),x(k,f,null),A=!0},p(E,N){const T={};4128&N&&(T.$$scope={dirty:N,ctx:E}),k.$set(T)},i(E){A||(p($.$$.fragment,E),p(k.$$.fragment,E),A=!0)},o(E){m($.$$.fragment,E),m(k.$$.fragment,E),A=!1},d(E){E&&v(f),b($),b(k)}}}(s);let l=_e(s[14]),d=[];for(let g=0;g<l.length;g+=1)d[g]=aa(Qr(s,l,g));const u=g=>m(d[g],1,1,()=>{d[g]=null});return{c(){e=C("div"),c&&c.c(),t=Z(),n=C("div");for(let g=0;g<d.length;g+=1)d[g].c();r=Z(),_(n,"class","c-navigation__items svelte-n5ccbo"),_(e,"class","c-navigation__group")},m(g,f){y(g,e,f),c&&c.m(e,null),S(e,t),S(e,n);for(let $=0;$<d.length;$+=1)d[$]&&d[$].m(n,null);S(e,r),a=!0},p(g,f){if(o?o.p&&(!a||4132&f)&&Re(o,i,g,g[12],a?Pe(i,g[12],f,Bl):Oe(g[12]),ea):c&&c.p&&(!a||32&f)&&c.p(g,a?f:-1),98&f){let $;for(l=_e(g[14]),$=0;$<l.length;$+=1){const h=Qr(g,l,$);d[$]?(d[$].p(h,f),p(d[$],1)):(d[$]=aa(h),d[$].c(),p(d[$],1),d[$].m(n,null))}for(B(),$=l.length;$<d.length;$+=1)u($);J()}},i(g){if(!a){p(c,g);for(let f=0;f<l.length;f+=1)p(d[f]);a=!0}},o(g){m(c,g),d=d.filter(Boolean);for(let f=0;f<d.length;f+=1)m(d[f]);a=!1},d(g){g&&v(e),c&&c.d(g),Gt(d,g)}}}function oa(s){let e,t,n=_e(s[5]),r=[];for(let i=0;i<n.length;i+=1)r[i]=ia(Xr(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){for(let i=0;i<r.length;i+=1)r[i].c();e=ke()},m(i,o){for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(i,o);y(i,e,o),t=!0},p(i,o){if(4198&o){let c;for(n=_e(i[5]),c=0;c<n.length;c+=1){const l=Xr(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=ia(l),r[c].c(),p(r[c],1),r[c].m(e.parentNode,e))}for(B(),c=n.length;c<r.length;c+=1)a(c);J()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&v(e),Gt(r,i)}}}function ed(s){let e,t,n=s[1],r=oa(s);return{c(){e=C("nav"),r.c(),_(e,"class","c-navigation__nav svelte-n5ccbo"),_(e,"slot","left")},m(a,i){y(a,e,i),r.m(e,null),t=!0},p(a,i){2&i&&fe(n,n=a[1])?(B(),m(r,1,1,W),J(),r=oa(a),r.c(),p(r,1),r.m(e,null)):r.p(a,i)},i(a){t||(p(r),t=!0)},o(a){m(r),t=!1},d(a){a&&v(e),r.d(a)}}}function td(s){let e;const t=s[10].header,n=Ie(t,s,s[12],ta);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4099&a)&&Re(n,t,r,r[12],e?Pe(t,r[12],a,Jl):Oe(r[12]),ta)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function ca(s){let e,t,n;const r=[s[0].props];var a=s[0].component;function i(o,c){let l={};for(let d=0;d<r.length;d+=1)l=Te(l,r[d]);return c!==void 0&&1&c&&(l=Te(l,wt(r,[mr(o[0].props)]))),{props:l}}return a&&(e=Tt(a,i(s))),{c(){e&&w(e.$$.fragment),t=ke()},m(o,c){e&&x(e,o,c),y(o,t,c),n=!0},p(o,c){if(1&c&&a!==(a=o[0].component)){if(e){B();const l=e;m(l.$$.fragment,1,0,()=>{b(l,1)}),J()}a?(e=Tt(a,i(o,c)),w(e.$$.fragment),p(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(a){const l=1&c?wt(r,[mr(o[0].props)]):{};e.$set(l)}},i(o){n||(e&&p(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&v(t),e&&b(e,o)}}}function nd(s){let e;const t=s[10].content,n=Ie(t,s,s[12],na),r=n||function(a){let i,o,c=Ur(a[0])&&la(a[0],a[2],a[1]),l=c&&ca(a);return{c(){l&&l.c(),i=ke()},m(d,u){l&&l.m(d,u),y(d,i,u),o=!0},p(d,u){7&u&&(c=Ur(d[0])&&la(d[0],d[2],d[1])),c?l?(l.p(d,u),7&u&&p(l,1)):(l=ca(d),l.c(),p(l,1),l.m(i.parentNode,i)):l&&(B(),m(l,1,1,()=>{l=null}),J())},i(d){o||(p(l),o=!0)},o(d){m(l),o=!1},d(d){d&&v(i),l&&l.d(d)}}}(s);return{c(){r&&r.c()},m(a,i){r&&r.m(a,i),e=!0},p(a,i){n?n.p&&(!e||4099&i)&&Re(n,t,a,a[12],e?Pe(t,a[12],i,Gl):Oe(a[12]),na):r&&r.p&&(!e||7&i)&&r.p(a,e?i:-1)},i(a){e||(p(r,a),e=!0)},o(a){m(r,a),e=!1},d(a){r&&r.d(a)}}}function sd(s){let e,t;return e=new Li({props:{item:s[0],slot:"right",$$slots:{content:[nd],header:[td]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1&r&&(a.item=n[0]),4103&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function rd(s){let e,t,n,r,a;const i=[Hl,Wl],o=[];function c(l,d){return l[2]==="tree"?0:1}return t=c(s),n=o[t]=i[t](s),{c(){e=C("div"),n.c(),_(e,"class",r="c-navigation c-navigation--mode__"+s[2]+" "+s[4]+" svelte-n5ccbo")},m(l,d){y(l,e,d),o[t].m(e,null),a=!0},p(l,[d]){let u=t;t=c(l),t===u?o[t].p(l,d):(B(),m(o[u],1,1,()=>{o[u]=null}),J(),n=o[t],n?n.p(l,d):(n=o[t]=i[t](l),n.c()),p(n,1),n.m(e,null)),(!a||20&d&&r!==(r="c-navigation c-navigation--mode__"+l[2]+" "+l[4]+" svelte-n5ccbo"))&&_(e,"class",r)},i(l){a||(p(n),a=!0)},o(l){m(n),a=!1},d(l){l&&v(e),o[t].d()}}}function ts(s,e,t,n,r,a){return{name:s,description:e,icon:t,id:n}}function la(s,e,t){return e!=="tree"||(s==null?void 0:s.id)===t}function ad(s,e,t){let{$$slots:n={},$$scope:r}=e,{group:a="Workspace Settings"}=e,{items:i=[]}=e,{item:o}=e,{mode:c="tree"}=e,{selectedId:l}=e,{onNavigationChangeItem:d=h=>{}}=e,{showButton:u=!0}=e,{class:g=""}=e,f=new Map;function $(h){t(0,o=h),t(1,l=h==null?void 0:h.id)}return s.$$set=h=>{"group"in h&&t(7,a=h.group),"items"in h&&t(8,i=h.items),"item"in h&&t(0,o=h.item),"mode"in h&&t(2,c=h.mode),"selectedId"in h&&t(1,l=h.selectedId),"onNavigationChangeItem"in h&&t(9,d=h.onNavigationChangeItem),"showButton"in h&&t(3,u=h.showButton),"class"in h&&t(4,g=h.class),"$$scope"in h&&t(12,r=h.$$scope)},s.$$.update=()=>{259&s.$$.dirty&&(l?t(0,o=i.find(h=>(h==null?void 0:h.id)===l)):t(1,l=o==null?void 0:o.id)),384&s.$$.dirty&&t(5,f=i.reduce((h,k)=>{if(!k)return h;const A=k.group??a,E=h.get(A)??[];return E.push(k),h.set(A,E),h},new Map)),257&s.$$.dirty&&(o||t(0,o=i[0])),514&s.$$.dirty&&d(l)},[o,l,c,u,g,f,$,a,i,d,n,h=>$(h),r]}class id extends ge{constructor(e){super(),$e(this,e,ad,rd,fe,{group:7,items:8,item:0,mode:2,selectedId:1,onNavigationChangeItem:9,showButton:3,class:4})}}function od(s){let e,t;return{c(){e=Be("svg"),t=Be("path"),_(t,"d","M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","16"),_(e,"viewBox","0 0 16 16"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){y(n,e,r),S(e,t)},p:W,i:W,o:W,d(n){n&&v(e)}}}class cd extends ge{constructor(e){super(),$e(this,e,null,od,fe,{})}}function ld(s){let e,t;return{c(){e=Be("svg"),t=Be("path"),_(t,"d","M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","16"),_(e,"viewBox","0 0 16 16"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){y(n,e,r),S(e,t)},p:W,i:W,o:W,d(n){n&&v(e)}}}class dd extends ge{constructor(e){super(),$e(this,e,null,ld,fe,{})}}const ud=s=>({}),da=s=>({}),pd=s=>({}),ua=s=>({});function md(s){let e;const t=s[8]["header-left"],n=Ie(t,s,s[10],ua);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||1024&a)&&Re(n,t,r,r[10],e?Pe(t,r[10],a,pd):Oe(r[10]),ua)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function fd(s){let e,t,n,r=s[0]&&pa(s),a=s[1]&&ma(s);return{c(){r&&r.c(),e=Z(),a&&a.c(),t=ke()},m(i,o){r&&r.m(i,o),y(i,e,o),a&&a.m(i,o),y(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=pa(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(B(),m(r,1,1,()=>{r=null}),J()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=ma(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(B(),m(a,1,1,()=>{a=null}),J())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&(v(e),v(t)),r&&r.d(i),a&&a.d(i)}}}function pa(s){let e,t,n;var r=s[0];return r&&(t=Tt(r,{})),{c(){e=C("div"),t&&w(t.$$.fragment),_(e,"class","icon-wrapper svelte-13uht7n")},m(a,i){y(a,e,i),t&&x(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){B();const o=t;m(o.$$.fragment,1,0,()=>{b(o,1)}),J()}r?(t=Tt(r,{}),w(t.$$.fragment),p(t.$$.fragment,1),x(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&v(e),t&&b(t)}}}function ma(s){let e,t;return e=new oe({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[hd]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1026&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function hd(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&he(e,t[1])},d(t){t&&v(e)}}}function fa(s){let e,t;const n=s[8].default,r=Ie(n,s,s[10],null);return{c(){e=C("div"),r&&r.c(),_(e,"class","settings-card-body")},m(a,i){y(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||1024&i)&&Re(r,n,a,a[10],t?Pe(n,a[10],i,null):Oe(a[10]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&v(e),r&&r.d(a)}}}function gd(s){let e,t,n,r,a,i,o,c,l,d,u;const g=[fd,md],f=[];function $(T,M){return T[0]||T[1]?0:1}r=$(s),a=f[r]=g[r](s);const h=s[8]["header-right"],k=Ie(h,s,s[10],da);let A=s[5].default&&fa(s),E=[{role:"button"},{class:s[3]},s[4]],N={};for(let T=0;T<E.length;T+=1)N=Te(N,E[T]);return{c(){e=C("div"),t=C("div"),n=C("div"),a.c(),i=Z(),o=C("div"),k&&k.c(),c=Z(),A&&A.c(),_(n,"class","settings-card-left svelte-13uht7n"),_(o,"class","settings-card-right svelte-13uht7n"),_(t,"class","settings-card-content svelte-13uht7n"),rs(e,N),be(e,"clickable",s[2]),be(e,"svelte-13uht7n",!0)},m(T,M){y(T,e,M),S(e,t),S(t,n),f[r].m(n,null),S(t,i),S(t,o),k&&k.m(o,null),S(e,c),A&&A.m(e,null),l=!0,d||(u=Le(e,"click",s[9]),d=!0)},p(T,[M]){let I=r;r=$(T),r===I?f[r].p(T,M):(B(),m(f[I],1,1,()=>{f[I]=null}),J(),a=f[r],a?a.p(T,M):(a=f[r]=g[r](T),a.c()),p(a,1),a.m(n,null)),k&&k.p&&(!l||1024&M)&&Re(k,h,T,T[10],l?Pe(h,T[10],M,ud):Oe(T[10]),da),T[5].default?A?(A.p(T,M),32&M&&p(A,1)):(A=fa(T),A.c(),p(A,1),A.m(e,null)):A&&(B(),m(A,1,1,()=>{A=null}),J()),rs(e,N=wt(E,[{role:"button"},(!l||8&M)&&{class:T[3]},16&M&&T[4]])),be(e,"clickable",T[2]),be(e,"svelte-13uht7n",!0)},i(T){l||(p(a),p(k,T),p(A),l=!0)},o(T){m(a),m(k,T),m(A),l=!1},d(T){T&&v(e),f[r].d(),k&&k.d(T),A&&A.d(),d=!1,u()}}}function $d(s,e,t){let n,r,a;const i=["class","icon","title","isClickable"];let o=as(e,i),{$$slots:c={},$$scope:l}=e;const d=er(c);let{class:u=""}=e,{icon:g}=e,{title:f}=e,{isClickable:$=!1}=e;return s.$$set=h=>{e=Te(Te({},e),Qe(h)),t(11,o=as(e,i)),"class"in h&&t(6,u=h.class),"icon"in h&&t(0,g=h.icon),"title"in h&&t(1,f=h.title),"isClickable"in h&&t(2,$=h.isClickable),"$$scope"in h&&t(10,l=h.$$scope)},s.$$.update=()=>{t(7,{class:n,...r}=o,n,(t(4,r),t(11,o))),192&s.$$.dirty&&t(3,a=`settings-card ${u} ${n||""}`)},[g,f,$,a,r,d,u,n,c,function(h){is.call(this,s,h)},l]}class Ns extends ge{constructor(e){super(),$e(this,e,$d,gd,fe,{class:6,icon:0,title:1,isClickable:2})}}function vd(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Te(r,n[a]);return{c(){e=Be("svg"),t=new Bn(!0),this.h()},l(a){e=Jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Gn(e);t=Wn(i,!0),i.forEach(v),this.h()},h(){t.a=null,dt(e,r)},m(a,i){Hn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.568.568 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.568.568 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.568.568 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.568.568 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.567.567 0 0 1-.06-.734zm3.759-3.759a.568.568 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.568.568 0 0 1-.804 0L7.31 4.204a.568.568 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',e)},p(a,[i]){dt(e,r=wt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},1&i&&a[0]]))},i:W,o:W,d(a){a&&v(e)}}}function yd(s,e,t){return s.$$set=n=>{t(0,e=Te(Te({},e),Qe(n)))},[e=Qe(e)]}class _d extends ge{constructor(e){super(),$e(this,e,yd,vd,fe,{})}}function wd(s){let e,t,n,r,a,i,o,c;return a=new hn({props:{triggerOn:[Oo.Hover],content:"Revoke Access",$$slots:{default:[kd]},$$scope:{ctx:s}}}),o=new li.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Sd]},$$scope:{ctx:s}}}),{c(){e=C("div"),t=C("div"),n=C("div"),r=C("div"),w(a.$$.fragment),i=Z(),w(o.$$.fragment),_(r,"class","icon-button-wrapper svelte-js5lik"),be(r,"active",s[3]),_(n,"class","connection-status svelte-js5lik"),_(t,"class","icon-container svelte-js5lik"),_(e,"class","status-controls svelte-js5lik")},m(l,d){y(l,e,d),S(e,t),S(t,n),S(n,r),x(a,r,null),S(t,i),x(o,t,null),c=!0},p(l,d){const u={};1027&d&&(u.$$scope={dirty:d,ctx:l}),a.$set(u),(!c||8&d)&&be(r,"active",l[3]);const g={};1024&d&&(g.$$scope={dirty:d,ctx:l}),o.$set(g)},i(l){c||(p(a.$$.fragment,l),p(o.$$.fragment,l),c=!0)},o(l){m(a.$$.fragment,l),m(o.$$.fragment,l),c=!1},d(l){l&&v(e),b(a),b(o)}}}function xd(s){let e,t;return e=new Ze({props:{variant:"ghost-block",color:s[2]?"neutral":"accent",size:1,$$slots:{default:[Md]},$$scope:{ctx:s}}}),e.$on("click",s[4]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};4&r&&(a.color=n[2]?"neutral":"accent"),1028&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function bd(s){let e,t;return e=new _d({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function kd(s){let e,t;return e=new Ms({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[bd]},$$scope:{ctx:s}}}),e.$on("click",s[7]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1024&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Sd(s){let e;return{c(){e=L("Connected")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Cd(s){let e;return{c(){e=C("span"),e.textContent="Connect"},m(t,n){y(t,e,n)},i:W,o:W,d(t){t&&v(e)}}}function Td(s){let e,t,n,r,a;return t=new ii({props:{size:1,useCurrentColor:!0}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),r=C("span"),r.textContent="Cancel",_(e,"class","connect-button-spinner svelte-js5lik")},m(i,o){y(i,e,o),x(t,e,null),y(i,n,o),y(i,r,o),a=!0},i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&(v(e),v(n),v(r)),b(t)}}}function Md(s){let e,t,n,r;const a=[Td,Cd],i=[];function o(c,l){return c[2]?0:1}return t=o(s),n=i[t]=a[t](s),{c(){e=C("div"),n.c(),_(e,"class","connect-button-content svelte-js5lik")},m(c,l){y(c,e,l),i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t!==d&&(B(),m(i[d],1,1,()=>{i[d]=null}),J(),n=i[t],n||(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null))},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&v(e),i[t].d()}}}function Ad(s){let e,t,n,r;const a=[xd,wd],i=[];function o(c,l){return!c[0].isConfigured&&c[0].authUrl?0:c[0].isConfigured?1:-1}return~(t=o(s))&&(n=i[t]=a[t](s)),{c(){e=C("div"),n&&n.c(),_(e,"slot","header-right")},m(c,l){y(c,e,l),~t&&i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?~t&&i[t].p(c,l):(n&&(B(),m(i[d],1,1,()=>{i[d]=null}),J()),~t?(n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null)):n=null)},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&v(e),~t&&i[t].d()}}}function ha(s){let e,t,n,r=s[0].statusMessage+"";return{c(){e=C("div"),t=L(r),_(e,"class",n="status-message "+s[0].statusType+" svelte-js5lik")},m(a,i){y(a,e,i),S(e,t)},p(a,i){1&i&&r!==(r=a[0].statusMessage+"")&&he(t,r),1&i&&n!==(n="status-message "+a[0].statusType+" svelte-js5lik")&&_(e,"class",n)},d(a){a&&v(e)}}}function Nd(s){let e,t,n,r,a,i;t=new Ns({props:{icon:s[0].icon,title:s[0].displayName,$$slots:{"header-right":[Ad]},$$scope:{ctx:s}}});let o=s[0].showStatus&&ha(s);return{c(){e=C("div"),w(t.$$.fragment),n=Z(),o&&o.c(),_(e,"class","config-wrapper"),_(e,"role","group"),_(e,"aria-label","Connection status controls")},m(c,l){y(c,e,l),x(t,e,null),S(e,n),o&&o.m(e,null),r=!0,a||(i=[Le(e,"mouseenter",s[8]),Le(e,"mouseleave",s[9])],a=!0)},p(c,[l]){const d={};1&l&&(d.icon=c[0].icon),1&l&&(d.title=c[0].displayName),1039&l&&(d.$$scope={dirty:l,ctx:c}),t.$set(d),c[0].showStatus?o?o.p(c,l):(o=ha(c),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&v(e),b(t),o&&o.d(),a=!1,ks(i)}}}function Zd(s,e,t){let{config:n}=e,{onAuthenticate:r}=e,{onRevokeAccess:a}=e,i=!1,o=null,c=!1;return s.$$set=l=>{"config"in l&&t(0,n=l.config),"onAuthenticate"in l&&t(5,r=l.onAuthenticate),"onRevokeAccess"in l&&t(1,a=l.onRevokeAccess)},s.$$.update=()=>{69&s.$$.dirty&&n.isConfigured&&i&&(t(2,i=!1),o&&(clearTimeout(o),t(6,o=null)))},[n,a,i,c,function(){if(i)t(2,i=!1),o&&(clearTimeout(o),t(6,o=null));else{t(2,i=!0);const l=n.authUrl||"";r(l),t(6,o=setTimeout(()=>{t(2,i=!1),t(6,o=null)},6e4))}},r,o,()=>a(n),()=>t(3,c=!0),()=>t(3,c=!1)]}class Ed extends ge{constructor(e){super(),$e(this,e,Zd,Nd,fe,{config:0,onAuthenticate:5,onRevokeAccess:1})}}function Id(s){let e;return{c(){e=L(s[0])},m(t,n){y(t,e,n)},p(t,n){1&n&&he(e,t[0])},d(t){t&&v(e)}}}function Rd(s){let e,t;const n=s[2].default,r=Ie(n,s,s[3],null);return{c(){e=C("div"),r&&r.c(),_(e,"class","category-content")},m(a,i){y(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||8&i)&&Re(r,n,a,a[3],t?Pe(n,a[3],i,null):Oe(a[3]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&v(e),r&&r.d(a)}}}function Od(s){let e,t,n,r,a;return t=new ii({props:{size:1}}),r=new oe({props:{size:1,color:"secondary",$$slots:{default:[Pd]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"class","loading-container svelte-2bsejd")},m(i,o){y(i,e,o),x(t,e,null),S(e,n),x(r,e,null),a=!0},p(i,o){const c={};8&o&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function Pd(s){let e;return{c(){e=L("Loading...")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function jd(s){let e,t,n,r,a,i,o;n=new oe({props:{size:1,color:"secondary",weight:"regular",$$slots:{default:[Id]},$$scope:{ctx:s}}});const c=[Od,Rd],l=[];function d(u,g){return u[1]?0:1}return a=d(s),i=l[a]=c[a](s),{c(){e=C("div"),t=C("div"),w(n.$$.fragment),r=Z(),i.c(),_(t,"class","category-heading"),_(e,"class","category")},m(u,g){y(u,e,g),S(e,t),x(n,t,null),S(e,r),l[a].m(e,null),o=!0},p(u,[g]){const f={};9&g&&(f.$$scope={dirty:g,ctx:u}),n.$set(f);let $=a;a=d(u),a===$?l[a].p(u,g):(B(),m(l[$],1,1,()=>{l[$]=null}),J(),i=l[a],i?i.p(u,g):(i=l[a]=c[a](u),i.c()),p(i,1),i.m(e,null))},i(u){o||(p(n.$$.fragment,u),p(i),o=!0)},o(u){m(n.$$.fragment,u),m(i),o=!1},d(u){u&&v(e),b(n),l[a].d()}}}function Ld(s,e,t){let{$$slots:n={},$$scope:r}=e,{title:a}=e,{loading:i=!1}=e;return s.$$set=o=>{"title"in o&&t(0,a=o.title),"loading"in o&&t(1,i=o.loading),"$$scope"in o&&t(3,r=o.$$scope)},[a,i,n,r]}class Fd extends ge{constructor(e){super(),$e(this,e,Ld,jd,fe,{title:0,loading:1})}}function ga(s,e,t){const n=s.slice();return n[4]=e[t],n}function $a(s){let e,t;return e=new Ed({props:{config:s[4],onAuthenticate:s[2],onRevokeAccess:s[3]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.config=n[4]),4&r&&(a.onAuthenticate=n[2]),8&r&&(a.onRevokeAccess=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function zd(s){let e,t,n=_e(s[1]),r=[];for(let i=0;i<n.length;i+=1)r[i]=$a(ga(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){e=C("div");for(let i=0;i<r.length;i+=1)r[i].c();_(e,"class","tool-category-list svelte-on3wl5")},m(i,o){y(i,e,o);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null);t=!0},p(i,o){if(14&o){let c;for(n=_e(i[1]),c=0;c<n.length;c+=1){const l=ga(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=$a(l),r[c].c(),p(r[c],1),r[c].m(e,null))}for(B(),c=n.length;c<r.length;c+=1)a(c);J()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&v(e),Gt(r,i)}}}function Dd(s){let e,t;return e=new Fd({props:{title:s[0],loading:s[1].length===0,$$slots:{default:[zd]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.title=n[0]),2&r&&(a.loading=n[1].length===0),142&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ud(s,e,t){let{title:n}=e,{tools:r=[]}=e,{onAuthenticate:a}=e,{onRevokeAccess:i}=e;return s.$$set=o=>{"title"in o&&t(0,n=o.title),"tools"in o&&t(1,r=o.tools),"onAuthenticate"in o&&t(2,a=o.onAuthenticate),"onRevokeAccess"in o&&t(3,i=o.onRevokeAccess)},[n,r,a,i]}class Vd extends ge{constructor(e){super(),$e(this,e,Ud,Dd,fe,{title:0,tools:1,onAuthenticate:2,onRevokeAccess:3})}}var me,Ks;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(me||(me={})),function(s){s.mergeShapes=(e,t)=>({...e,...t})}(Ks||(Ks={}));const q=me.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),$t=s=>{switch(typeof s){case"undefined":return q.undefined;case"string":return q.string;case"number":return isNaN(s)?q.nan:q.number;case"boolean":return q.boolean;case"function":return q.function;case"bigint":return q.bigint;case"symbol":return q.symbol;case"object":return Array.isArray(s)?q.array:s===null?q.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?q.promise:typeof Map<"u"&&s instanceof Map?q.map:typeof Set<"u"&&s instanceof Set?q.set:typeof Date<"u"&&s instanceof Date?q.date:q.object;default:return q.unknown}},O=me.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class We extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof We))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,me.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}We.create=s=>new We(s);const an=(s,e)=>{let t;switch(s.code){case O.invalid_type:t=s.received===q.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case O.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,me.jsonStringifyReplacer)}`;break;case O.unrecognized_keys:t=`Unrecognized key(s) in object: ${me.joinValues(s.keys,", ")}`;break;case O.invalid_union:t="Invalid input";break;case O.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${me.joinValues(s.options)}`;break;case O.invalid_enum_value:t=`Invalid enum value. Expected ${me.joinValues(s.options)}, received '${s.received}'`;break;case O.invalid_arguments:t="Invalid function arguments";break;case O.invalid_return_type:t="Invalid function return type";break;case O.invalid_date:t="Invalid date";break;case O.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:me.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case O.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case O.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case O.custom:t="Invalid input";break;case O.invalid_intersection_types:t="Intersection results could not be merged";break;case O.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case O.not_finite:t="Number must be finite";break;default:t=e.defaultError,me.assertNever(s)}return{message:t}};let Fi=an;function gs(){return Fi}const $s=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function z(s,e){const t=gs(),n=$s({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===an?void 0:an].filter(r=>!!r)});s.common.issues.push(n)}class Ue{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return se;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return Ue.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return se;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}}const se=Object.freeze({status:"aborted"}),vs=s=>({status:"dirty",value:s}),qe=s=>({status:"valid",value:s}),Ys=s=>s.status==="aborted",Xs=s=>s.status==="dirty",qt=s=>s.status==="valid",In=s=>typeof Promise<"u"&&s instanceof Promise;function ys(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function zi(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var K,mn,fn;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(K||(K={}));class pt{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const va=(s,e)=>{if(qt(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new We(s.common.issues);return this._error=t,this._error}}};function ie(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}class le{get description(){return this._def.description}_getType(e){return $t(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:$t(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new Ue,ctx:{common:e.parent.common,data:e.data,parsedType:$t(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(In(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;const r={common:{issues:[],async:(n=t==null?void 0:t.async)!==null&&n!==void 0&&n,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:$t(e)},a=this._parseSync({data:e,path:r.path,parent:r});return va(r,a)}"~validate"(e){var t,n;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:$t(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:r});return qt(a)?{value:a.value}:{issues:r.common.issues}}catch(a){!((n=(t=a==null?void 0:a.message)===null||t===void 0?void 0:t.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(a=>qt(a)?{value:a.value}:{issues:r.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:$t(e)},r=this._parse({data:e,path:n.path,parent:n}),a=await(In(r)?r:Promise.resolve(r));return va(n,a)}refine(e,t){const n=r=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(r):t;return this._refinement((r,a)=>{const i=e(r),o=()=>a.addIssue({code:O.custom,...n(r)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(o(),!1)):!!i||(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue(typeof t=="function"?t(n,r):t),!1))}_refinement(e){return new rt({schema:this,typeName:te.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return lt.create(this,this._def)}nullable(){return It.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return it.create(this)}promise(){return cn.create(this,this._def)}or(e){return jn.create([this,e],this._def)}and(e){return Ln.create(this,e,this._def)}transform(e){return new rt({...ie(this._def),schema:this,typeName:te.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new Un({...ie(this._def),innerType:this,defaultValue:t,typeName:te.ZodDefault})}brand(){return new sr({typeName:te.ZodBranded,type:this,...ie(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new Vn({...ie(this._def),innerType:this,catchValue:t,typeName:te.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Yn.create(this,e)}readonly(){return qn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const qd=/^c[^\s-]{8,}$/i,Bd=/^[0-9a-z]+$/,Jd=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Gd=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Wd=/^[a-z0-9_-]{21}$/i,Hd=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Kd=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Yd=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Fs;const Xd=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Qd=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,eu=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,tu=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,nu=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,su=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Di="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ru=new RegExp(`^${Di}$`);function Ui(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Vi(s){let e=`${Di}T${Ui(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function au(s,e){if(!Hd.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function iu(s,e){return!(e!=="v4"&&e||!Qd.test(s))||!(e!=="v6"&&e||!tu.test(s))}class st extends le{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==q.string){const i=this._getOrReturnCtx(e);return z(i,{code:O.invalid_type,expected:q.string,received:i.parsedType}),se}const t=new Ue;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),z(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),z(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?z(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&z(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")Yd.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"email",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")Fs||(Fs=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Fs.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"emoji",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Gd.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"uuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")Wd.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"nanoid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")qd.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"cuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")Bd.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"cuid2",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Jd.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"ulid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),z(n,{validation:"url",code:O.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"regex",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?Vi(i).test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?ru.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${Ui(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{code:O.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?Kd.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"duration",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!Xd.test(r))&&(a!=="v6"&&a||!eu.test(r))&&(n=this._getOrReturnCtx(e,n),z(n,{validation:"ip",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?au(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"jwt",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?iu(e.data,i.version)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"cidr",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?nu.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"base64",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?su.test(e.data)||(n=this._getOrReturnCtx(e,n),z(n,{validation:"base64url",code:O.invalid_string,message:i.message}),t.dirty()):me.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:O.invalid_string,...K.errToObj(n)})}_addCheck(e){return new st({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...K.errToObj(e)})}url(e){return this._addCheck({kind:"url",...K.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...K.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...K.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...K.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...K.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...K.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...K.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...K.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...K.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...K.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...K.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...K.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...K.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...K.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...K.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...K.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...K.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...K.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...K.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...K.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...K.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...K.errToObj(t)})}nonempty(e){return this.min(1,K.errToObj(e))}trim(){return new st({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new st({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new st({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function ou(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}st.create=s=>{var e;return new st({checks:[],typeName:te.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ie(s)})};class Nt extends le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==q.number){const r=this._getOrReturnCtx(e);return z(r,{code:O.invalid_type,expected:q.number,received:r.parsedType}),se}let t;const n=new Ue;for(const r of this._def.checks)r.kind==="int"?me.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),z(t,{code:O.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?ou(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),z(t,{code:O.not_finite,message:r.message}),n.dirty()):me.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,K.toString(t))}gt(e,t){return this.setLimit("min",e,!1,K.toString(t))}lte(e,t){return this.setLimit("max",e,!0,K.toString(t))}lt(e,t){return this.setLimit("max",e,!1,K.toString(t))}setLimit(e,t,n,r){return new Nt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:K.toString(r)}]})}_addCheck(e){return new Nt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:K.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:K.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:K.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:K.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:K.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:K.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:K.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:K.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:K.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&me.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Nt.create=s=>new Nt({checks:[],typeName:te.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...ie(s)});class Zt extends le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==q.bigint)return this._getInvalidInput(e);let t;const n=new Ue;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),z(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):me.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:q.bigint,received:t.parsedType}),se}gte(e,t){return this.setLimit("min",e,!0,K.toString(t))}gt(e,t){return this.setLimit("min",e,!1,K.toString(t))}lte(e,t){return this.setLimit("max",e,!0,K.toString(t))}lt(e,t){return this.setLimit("max",e,!1,K.toString(t))}setLimit(e,t,n,r){return new Zt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:K.toString(r)}]})}_addCheck(e){return new Zt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:K.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:K.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:K.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:K.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:K.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Zt.create=s=>{var e;return new Zt({checks:[],typeName:te.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ie(s)})};class Rn extends le{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==q.boolean){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:q.boolean,received:t.parsedType}),se}return qe(e.data)}}Rn.create=s=>new Rn({typeName:te.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...ie(s)});class Bt extends le{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==q.date){const r=this._getOrReturnCtx(e);return z(r,{code:O.invalid_type,expected:q.date,received:r.parsedType}),se}if(isNaN(e.data.getTime()))return z(this._getOrReturnCtx(e),{code:O.invalid_date}),se;const t=new Ue;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),z(n,{code:O.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),z(n,{code:O.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):me.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Bt({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:K.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:K.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}Bt.create=s=>new Bt({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:te.ZodDate,...ie(s)});class _s extends le{_parse(e){if(this._getType(e)!==q.symbol){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:q.symbol,received:t.parsedType}),se}return qe(e.data)}}_s.create=s=>new _s({typeName:te.ZodSymbol,...ie(s)});class On extends le{_parse(e){if(this._getType(e)!==q.undefined){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:q.undefined,received:t.parsedType}),se}return qe(e.data)}}On.create=s=>new On({typeName:te.ZodUndefined,...ie(s)});class Pn extends le{_parse(e){if(this._getType(e)!==q.null){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:q.null,received:t.parsedType}),se}return qe(e.data)}}Pn.create=s=>new Pn({typeName:te.ZodNull,...ie(s)});class on extends le{constructor(){super(...arguments),this._any=!0}_parse(e){return qe(e.data)}}on.create=s=>new on({typeName:te.ZodAny,...ie(s)});class Ft extends le{constructor(){super(...arguments),this._unknown=!0}_parse(e){return qe(e.data)}}Ft.create=s=>new Ft({typeName:te.ZodUnknown,...ie(s)});class _t extends le{_parse(e){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:q.never,received:t.parsedType}),se}}_t.create=s=>new _t({typeName:te.ZodNever,...ie(s)});class ws extends le{_parse(e){if(this._getType(e)!==q.undefined){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:q.void,received:t.parsedType}),se}return qe(e.data)}}ws.create=s=>new ws({typeName:te.ZodVoid,...ie(s)});class it extends le{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==q.array)return z(t,{code:O.invalid_type,expected:q.array,received:t.parsedType}),se;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(z(t,{code:i?O.too_big:O.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(z(t,{code:O.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(z(t,{code:O.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new pt(t,i,t.path,o)))).then(i=>Ue.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new pt(t,i,t.path,o)));return Ue.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new it({...this._def,minLength:{value:e,message:K.toString(t)}})}max(e,t){return new it({...this._def,maxLength:{value:e,message:K.toString(t)}})}length(e,t){return new it({...this._def,exactLength:{value:e,message:K.toString(t)}})}nonempty(e){return this.min(1,e)}}function Qt(s){if(s instanceof Se){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=lt.create(Qt(n))}return new Se({...s._def,shape:()=>e})}return s instanceof it?new it({...s._def,type:Qt(s.element)}):s instanceof lt?lt.create(Qt(s.unwrap())):s instanceof It?It.create(Qt(s.unwrap())):s instanceof mt?mt.create(s.items.map(e=>Qt(e))):s}it.create=(s,e)=>new it({type:s,minLength:null,maxLength:null,exactLength:null,typeName:te.ZodArray,...ie(e)});class Se extends le{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=me.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==q.object){const c=this._getOrReturnCtx(e);return z(c,{code:O.invalid_type,expected:q.object,received:c.parsedType}),se}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof _t&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new pt(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof _t){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(z(n,{code:O.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new pt(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>Ue.mergeObjectSync(t,c)):Ue.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return K.errToObj,new Se({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=K.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new Se({...this._def,unknownKeys:"strip"})}passthrough(){return new Se({...this._def,unknownKeys:"passthrough"})}extend(e){return new Se({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Se({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:te.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new Se({...this._def,catchall:e})}pick(e){const t={};return me.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new Se({...this._def,shape:()=>t})}omit(e){const t={};return me.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new Se({...this._def,shape:()=>t})}deepPartial(){return Qt(this)}partial(e){const t={};return me.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new Se({...this._def,shape:()=>t})}required(e){const t={};return me.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof lt;)r=r._def.innerType;t[n]=r}}),new Se({...this._def,shape:()=>t})}keyof(){return qi(me.objectKeys(this.shape))}}Se.create=(s,e)=>new Se({shape:()=>s,unknownKeys:"strip",catchall:_t.create(),typeName:te.ZodObject,...ie(e)}),Se.strictCreate=(s,e)=>new Se({shape:()=>s,unknownKeys:"strict",catchall:_t.create(),typeName:te.ZodObject,...ie(e)}),Se.lazycreate=(s,e)=>new Se({shape:s,unknownKeys:"strip",catchall:_t.create(),typeName:te.ZodObject,...ie(e)});class jn extends le{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async r=>{const a={...t,common:{...t.common,issues:[]},parent:null};return{result:await r._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(r){for(const i of r)if(i.result.status==="valid")return i.result;for(const i of r)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const a=r.map(i=>new We(i.ctx.common.issues));return z(t,{code:O.invalid_union,unionErrors:a}),se});{let r;const a=[];for(const o of n){const c={...t,common:{...t.common,issues:[]},parent:null},l=o._parseSync({data:t.data,path:t.path,parent:c});if(l.status==="valid")return l;l.status!=="dirty"||r||(r={result:l,ctx:c}),c.common.issues.length&&a.push(c.common.issues)}if(r)return t.common.issues.push(...r.ctx.common.issues),r.result;const i=a.map(o=>new We(o));return z(t,{code:O.invalid_union,unionErrors:i}),se}}get options(){return this._def.options}}jn.create=(s,e)=>new jn({options:s,typeName:te.ZodUnion,...ie(e)});const St=s=>s instanceof Fn?St(s.schema):s instanceof rt?St(s.innerType()):s instanceof zn?[s.value]:s instanceof Et?s.options:s instanceof Dn?me.objectValues(s.enum):s instanceof Un?St(s._def.innerType):s instanceof On?[void 0]:s instanceof Pn?[null]:s instanceof lt?[void 0,...St(s.unwrap())]:s instanceof It?[null,...St(s.unwrap())]:s instanceof sr||s instanceof qn?St(s.unwrap()):s instanceof Vn?St(s._def.innerType):[];class Zs extends le{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==q.object)return z(t,{code:O.invalid_type,expected:q.object,received:t.parsedType}),se;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(z(t,{code:O.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),se)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=St(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new Zs({typeName:te.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...ie(n)})}}function Qs(s,e){const t=$t(s),n=$t(e);if(s===e)return{valid:!0,data:s};if(t===q.object&&n===q.object){const r=me.objectKeys(e),a=me.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Qs(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===q.array&&n===q.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Qs(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===q.date&&n===q.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}class Ln extends le{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(a,i)=>{if(Ys(a)||Ys(i))return se;const o=Qs(a.value,i.value);return o.valid?((Xs(a)||Xs(i))&&t.dirty(),{status:t.value,value:o.data}):(z(n,{code:O.invalid_intersection_types}),se)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([a,i])=>r(a,i)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Ln.create=(s,e,t)=>new Ln({left:s,right:e,typeName:te.ZodIntersection,...ie(t)});class mt extends le{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==q.array)return z(n,{code:O.invalid_type,expected:q.array,received:n.parsedType}),se;if(n.data.length<this._def.items.length)return z(n,{code:O.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),se;!this._def.rest&&n.data.length>this._def.items.length&&(z(n,{code:O.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new pt(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>Ue.mergeArray(t,a)):Ue.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new mt({...this._def,rest:e})}}mt.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new mt({items:s,typeName:te.ZodTuple,rest:null,...ie(e)})};class Es extends le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==q.object)return z(n,{code:O.invalid_type,expected:q.object,received:n.parsedType}),se;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new pt(n,o,n.path,o)),value:i._parse(new pt(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?Ue.mergeObjectAsync(t,r):Ue.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new Es(t instanceof le?{keyType:e,valueType:t,typeName:te.ZodRecord,...ie(n)}:{keyType:st.create(),valueType:e,typeName:te.ZodRecord,...ie(t)})}}class xs extends le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==q.map)return z(n,{code:O.invalid_type,expected:q.map,received:n.parsedType}),se;const r=this._def.keyType,a=this._def.valueType,i=[...n.data.entries()].map(([o,c],l)=>({key:r._parse(new pt(n,o,n.path,[l,"key"])),value:a._parse(new pt(n,c,n.path,[l,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of i){const l=await c.key,d=await c.value;if(l.status==="aborted"||d.status==="aborted")return se;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}})}{const o=new Map;for(const c of i){const l=c.key,d=c.value;if(l.status==="aborted"||d.status==="aborted")return se;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}}}}xs.create=(s,e,t)=>new xs({valueType:e,keyType:s,typeName:te.ZodMap,...ie(t)});class Jt extends le{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==q.set)return z(n,{code:O.invalid_type,expected:q.set,received:n.parsedType}),se;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(z(n,{code:O.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(z(n,{code:O.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return se;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new pt(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Jt({...this._def,minSize:{value:e,message:K.toString(t)}})}max(e,t){return new Jt({...this._def,maxSize:{value:e,message:K.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Jt.create=(s,e)=>new Jt({valueType:s,minSize:null,maxSize:null,typeName:te.ZodSet,...ie(e)});class en extends le{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==q.function)return z(t,{code:O.invalid_type,expected:q.function,received:t.parsedType}),se;function n(o,c){return $s({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,gs(),an].filter(l=>!!l),issueData:{code:O.invalid_arguments,argumentsError:c}})}function r(o,c){return $s({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,gs(),an].filter(l=>!!l),issueData:{code:O.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof cn){const o=this;return qe(async function(...c){const l=new We([]),d=await o._def.args.parseAsync(c,a).catch(g=>{throw l.addIssue(n(c,g)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(g=>{throw l.addIssue(r(u,g)),l})})}{const o=this;return qe(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new We([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new We([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new en({...this._def,args:mt.create(e).rest(Ft.create())})}returns(e){return new en({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new en({args:e||mt.create([]).rest(Ft.create()),returns:t||Ft.create(),typeName:te.ZodFunction,...ie(n)})}}class Fn extends le{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Fn.create=(s,e)=>new Fn({getter:s,typeName:te.ZodLazy,...ie(e)});class zn extends le{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return z(t,{received:t.data,code:O.invalid_literal,expected:this._def.value}),se}return{status:"valid",value:e.data}}get value(){return this._def.value}}function qi(s,e){return new Et({values:s,typeName:te.ZodEnum,...ie(e)})}zn.create=(s,e)=>new zn({value:s,typeName:te.ZodLiteral,...ie(e)});class Et extends le{constructor(){super(...arguments),mn.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return z(t,{expected:me.joinValues(n),received:t.parsedType,code:O.invalid_type}),se}if(ys(this,mn)||zi(this,mn,new Set(this._def.values)),!ys(this,mn).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return z(t,{received:t.data,code:O.invalid_enum_value,options:n}),se}return qe(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Et.create(e,{...this._def,...t})}exclude(e,t=this._def){return Et.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}}mn=new WeakMap,Et.create=qi;class Dn extends le{constructor(){super(...arguments),fn.set(this,void 0)}_parse(e){const t=me.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==q.string&&n.parsedType!==q.number){const r=me.objectValues(t);return z(n,{expected:me.joinValues(r),received:n.parsedType,code:O.invalid_type}),se}if(ys(this,fn)||zi(this,fn,new Set(me.getValidEnumValues(this._def.values))),!ys(this,fn).has(e.data)){const r=me.objectValues(t);return z(n,{received:n.data,code:O.invalid_enum_value,options:r}),se}return qe(e.data)}get enum(){return this._def.values}}fn=new WeakMap,Dn.create=(s,e)=>new Dn({values:s,typeName:te.ZodNativeEnum,...ie(e)});class cn extends le{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==q.promise&&t.common.async===!1)return z(t,{code:O.invalid_type,expected:q.promise,received:t.parsedType}),se;const n=t.parsedType===q.promise?t.data:Promise.resolve(t.data);return qe(n.then(r=>this._def.type.parseAsync(r,{path:t.path,errorMap:t.common.contextualErrorMap})))}}cn.create=(s,e)=>new cn({type:s,typeName:te.ZodPromise,...ie(e)});class rt extends le{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===te.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:i=>{z(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),r.type==="preprocess"){const i=r.transform(n.data,a);if(n.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return se;const c=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return c.status==="aborted"?se:c.status==="dirty"||t.value==="dirty"?vs(c.value):c});{if(t.value==="aborted")return se;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?se:o.status==="dirty"||t.value==="dirty"?vs(o.value):o}}if(r.type==="refinement"){const i=o=>{const c=r.refinement(o,a);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?se:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?se:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(r.type==="transform"){if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!qt(i))return i;const o=r.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>qt(i)?Promise.resolve(r.transform(i.value,a)).then(o=>({status:t.value,value:o})):i)}me.assertNever(r)}}rt.create=(s,e,t)=>new rt({schema:s,typeName:te.ZodEffects,effect:e,...ie(t)}),rt.createWithPreprocess=(s,e,t)=>new rt({schema:e,effect:{type:"preprocess",transform:s},typeName:te.ZodEffects,...ie(t)});class lt extends le{_parse(e){return this._getType(e)===q.undefined?qe(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}lt.create=(s,e)=>new lt({innerType:s,typeName:te.ZodOptional,...ie(e)});class It extends le{_parse(e){return this._getType(e)===q.null?qe(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}It.create=(s,e)=>new It({innerType:s,typeName:te.ZodNullable,...ie(e)});class Un extends le{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===q.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Un.create=(s,e)=>new Un({innerType:s,typeName:te.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ie(e)});class Vn extends le{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return In(r)?r.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new We(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new We(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Vn.create=(s,e)=>new Vn({innerType:s,typeName:te.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ie(e)});class bs extends le{_parse(e){if(this._getType(e)!==q.nan){const t=this._getOrReturnCtx(e);return z(t,{code:O.invalid_type,expected:q.nan,received:t.parsedType}),se}return{status:"valid",value:e.data}}}bs.create=s=>new bs({typeName:te.ZodNaN,...ie(s)});const cu=Symbol("zod_brand");class sr extends le{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class Yn extends le{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?se:r.status==="dirty"?(t.dirty(),vs(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?se:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Yn({in:e,out:t,typeName:te.ZodPipeline})}}class qn extends le{_parse(e){const t=this._def.innerType._parse(e),n=r=>(qt(r)&&(r.value=Object.freeze(r.value)),r);return In(t)?t.then(r=>n(r)):n(t)}unwrap(){return this._def.innerType}}function ya(s,e={},t){return s?on.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):on.create()}qn.create=(s,e)=>new qn({innerType:s,typeName:te.ZodReadonly,...ie(e)});const lu={object:Se.lazycreate};var te;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(te||(te={}));const _a=st.create,wa=Nt.create,du=bs.create,uu=Zt.create,xa=Rn.create,pu=Bt.create,mu=_s.create,fu=On.create,hu=Pn.create,gu=on.create,$u=Ft.create,vu=_t.create,yu=ws.create,_u=it.create,wu=Se.create,xu=Se.strictCreate,bu=jn.create,ku=Zs.create,Su=Ln.create,Cu=mt.create,Tu=Es.create,Mu=xs.create,Au=Jt.create,Nu=en.create,Zu=Fn.create,Eu=zn.create,Iu=Et.create,Ru=Dn.create,Ou=cn.create,ba=rt.create,Pu=lt.create,ju=It.create,Lu=rt.createWithPreprocess,Fu=Yn.create,zu={string:s=>st.create({...s,coerce:!0}),number:s=>Nt.create({...s,coerce:!0}),boolean:s=>Rn.create({...s,coerce:!0}),bigint:s=>Zt.create({...s,coerce:!0}),date:s=>Bt.create({...s,coerce:!0})},Du=se;var xe=Object.freeze({__proto__:null,defaultErrorMap:an,setErrorMap:function(s){Fi=s},getErrorMap:gs,makeIssue:$s,EMPTY_PATH:[],addIssueToContext:z,ParseStatus:Ue,INVALID:se,DIRTY:vs,OK:qe,isAborted:Ys,isDirty:Xs,isValid:qt,isAsync:In,get util(){return me},get objectUtil(){return Ks},ZodParsedType:q,getParsedType:$t,ZodType:le,datetimeRegex:Vi,ZodString:st,ZodNumber:Nt,ZodBigInt:Zt,ZodBoolean:Rn,ZodDate:Bt,ZodSymbol:_s,ZodUndefined:On,ZodNull:Pn,ZodAny:on,ZodUnknown:Ft,ZodNever:_t,ZodVoid:ws,ZodArray:it,ZodObject:Se,ZodUnion:jn,ZodDiscriminatedUnion:Zs,ZodIntersection:Ln,ZodTuple:mt,ZodRecord:Es,ZodMap:xs,ZodSet:Jt,ZodFunction:en,ZodLazy:Fn,ZodLiteral:zn,ZodEnum:Et,ZodNativeEnum:Dn,ZodPromise:cn,ZodEffects:rt,ZodTransformer:rt,ZodOptional:lt,ZodNullable:It,ZodDefault:Un,ZodCatch:Vn,ZodNaN:bs,BRAND:cu,ZodBranded:sr,ZodPipeline:Yn,ZodReadonly:qn,custom:ya,Schema:le,ZodSchema:le,late:lu,get ZodFirstPartyTypeKind(){return te},coerce:zu,any:gu,array:_u,bigint:uu,boolean:xa,date:pu,discriminatedUnion:ku,effect:ba,enum:Iu,function:Nu,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>ya(t=>t instanceof s,e),intersection:Su,lazy:Zu,literal:Eu,map:Mu,nan:du,nativeEnum:Ru,never:vu,null:hu,nullable:ju,number:wa,object:wu,oboolean:()=>xa().optional(),onumber:()=>wa().optional(),optional:Pu,ostring:()=>_a().optional(),pipeline:Fu,preprocess:Lu,promise:Ou,record:Tu,set:Au,strictObject:xu,string:_a,symbol:mu,transformer:ba,tuple:Cu,undefined:fu,union:bu,unknown:$u,void:yu,NEVER:Du,ZodIssueCode:O,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:We});const Xe=xe.object({name:xe.string().optional(),title:xe.string().optional(),command:xe.string().optional(),args:xe.array(xe.union([xe.string(),xe.number(),xe.boolean()])).optional(),env:xe.record(xe.union([xe.string(),xe.number(),xe.boolean(),xe.null(),xe.undefined()])).optional()}).passthrough(),Uu=xe.array(Xe),Vu=xe.object({servers:xe.array(Xe)}).passthrough(),qu=xe.object({mcpServers:xe.array(Xe)}).passthrough(),Bu=xe.object({servers:xe.record(Xe)}).passthrough(),Ju=xe.object({mcpServers:xe.record(Xe)}).passthrough(),Gu=xe.record(Xe),Wu=Xe.refine(s=>s.command!==void 0,{message:"Server must have a 'command' property"}),Bi=Symbol("MCPServerError");class ze extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,ze.prototype)}}var ai;ai=Bi;class rr{constructor(e){ye(this,"servers",De([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===ve.getStoredMCPServersResponse){const n=t.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:ve.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:ve.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new ze("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const n=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(n),n})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const n=[...t,...e.map(r=>({...r,id:crypto.randomUUID()}))];return this.saveServers(n),n})}checkExistingServerName(e,t){const n=gn(this.servers).find(r=>r.name===e);if(n&&(n==null?void 0:n.id)!==t)throw new ze(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const n=t.map(r=>r.id===e.id?e:r);return this.saveServers(n),n})}deleteServer(e){this.servers.update(t=>{const n=t.filter(r=>r.id!==e);return this.saveServers(n),n})}toggleDisabledServer(e){this.servers.update(t=>{const n=t.map(r=>r.id===e?{...r,disabled:!r.disabled}:r);return this.saveServers(n),n})}static convertServerToJSON(e){return JSON.stringify({mcpServers:{[e.name]:{command:e.command.split(" ")[0],args:e.command.split(" ").slice(1),env:e.env}}},null,2)}static parseServerValidationMessages(e){const t=new Map,n=new Map;e.forEach(a=>{var i;a.disabled?t.set(a.id,"MCP server has been manually disabled"):a.tools&&a.tools.length===0?t.set(a.id,"No tools are available for this MCP server"):a.disabledTools&&a.disabledTools.length===((i=a.tools)==null?void 0:i.length)?t.set(a.id,"All tools for this MCP server have validation errors: "+a.disabledTools.join(", ")):a.disabledTools&&a.disabledTools.length>0&&n.set(a.id,"MCP server has validation errors in the following tools which have been disabled: "+a.disabledTools.join(", "))});const r=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...r]),warnings:n}}static parseDuplicateServerIds(e){const t=new Map;for(const r of e)t.has(r.name)||t.set(r.name,[]),t.get(r.name).push(r.id);const n=new Map;for(const[,r]of t)if(r.length>1)for(let a=1;a<r.length;a++)n.set(r[a],"MCP server is disabled due to duplicate server names");return n}parseServerConfigFromJSON(e){try{const t=JSON.parse(e),n=xe.union([Uu.transform(r=>r.map(a=>this.normalizeServerConfig(a))),Vu.transform(r=>r.servers.map(a=>this.normalizeServerConfig(a))),qu.transform(r=>r.mcpServers.map(a=>this.normalizeServerConfig(a))),Bu.transform(r=>Object.entries(r.servers).map(([a,i])=>{const o=Xe.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),Ju.transform(r=>Object.entries(r.mcpServers).map(([a,i])=>{const o=Xe.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),Gu.transform(r=>{if(!Object.values(r).some(a=>{const i=Xe.safeParse(a);return i.success&&i.data.command!==void 0}))throw new Error("No command property found in any server config");return Object.entries(r).map(([a,i])=>{const o=Xe.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})}),Wu.transform(r=>[this.normalizeServerConfig(r)])]).safeParse(t);if(n.success)return n.data;throw new ze("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(t){throw t instanceof ze?t:new ze("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(e){try{const t=this.parseServerConfigFromJSON(e),n=gn(this.servers),r=new Set(n.map(a=>a.name));for(const a of t){if(!a.name)throw new ze("All servers must have a name.");if(r.has(a.name))throw new ze(`A server with the name '${a.name}' already exists.`);r.add(a.name)}return this.servers.update(a=>{const i=[...a,...t.map(o=>({...o,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof ze?t:new ze("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(e){try{const t=Xe.transform(n=>{const r=n.command||"",a=n.args?n.args.map(l=>String(l)):[];if(!r)throw new Error("Server must have a 'command' property");const i=a.length>0?`${r} ${a.join(" ")}`:r,o=n.name||n.title||(r?r.split(" ")[0]:""),c=n.env?Object.fromEntries(Object.entries(n.env).filter(([l,d])=>d!=null).map(([l,d])=>[l,String(d)])):void 0;return{name:o,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}).refine(n=>!!n.name,{message:"Server must have a name",path:["name"]}).refine(n=>!!n.command,{message:"Server must have a command",path:["command"]}).safeParse(e);if(!t.success)throw new ze(t.error.message);return t.data}catch(t){throw t instanceof Error?new ze(`Invalid server configuration: ${t.message}`):new ze("Invalid server configuration")}}}ye(rr,ai,"MCPServerError");function Hu(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Te(r,n[a]);return{c(){e=Be("svg"),t=new Bn(!0),this.h()},l(a){e=Jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Gn(e);t=Wn(i,!0),i.forEach(v),this.h()},h(){t.a=null,dt(e,r)},m(a,i){Hn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',e)},p(a,[i]){dt(e,r=wt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&a[0]]))},i:W,o:W,d(a){a&&v(e)}}}function Ku(s,e,t){return s.$$set=n=>{t(0,e=Te(Te({},e),Qe(n)))},[e=Qe(e)]}class Ji extends ge{constructor(e){super(),$e(this,e,Ku,Hu,fe,{})}}function ka(s,e,t){const n=s.slice();return n[11]=e[t],n[12]=e,n[13]=t,n}function Yu(s){let e;return{c(){e=L("Environment Variables")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Sa(s){let e,t,n=[],r=new Map,a=_e(s[0]);const i=o=>o[11].id;for(let o=0;o<a.length;o+=1){let c=ka(s,a,o),l=i(c);r.set(l,n[o]=Ca(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=ke()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);y(o,e,c),t=!0},p(o,c){59&c&&(a=_e(o[0]),B(),n=Rt(n,c,i,1,o,a,r,e.parentNode,Ot,Ca,e,ka),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Xu(s){let e,t;return e=new Ji({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Qu(s){let e,t;return e=new Ze({props:{variant:"ghost",color:"neutral",type:"button",size:1,$$slots:{iconLeft:[Xu]},$$scope:{ctx:s}}}),e.$on("focus",function(){zt(s[1])&&s[1].apply(this,arguments)}),e.$on("click",function(){return s[10](s[11])}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){s=n;const a={};16384&r&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ca(s,e){let t,n,r,a,i,o,c,l,d,u,g,f,$;function h(N){e[6](N,e[11])}let k={size:1,placeholder:"Name",class:"full-width"};function A(N){e[8](N,e[11])}e[11].key!==void 0&&(k.value=e[11].key),r=new Dt({props:k}),Me.push(()=>Ae(r,"value",h)),r.$on("focus",function(){zt(e[1])&&e[1].apply(this,arguments)}),r.$on("change",function(){return e[7](e[11])});let E={size:1,placeholder:"Value",class:"full-width"};return e[11].value!==void 0&&(E.value=e[11].value),c=new Dt({props:E}),Me.push(()=>Ae(c,"value",A)),c.$on("focus",function(){zt(e[1])&&e[1].apply(this,arguments)}),c.$on("change",function(){return e[9](e[11])}),g=new hn({props:{content:"Remove",$$slots:{default:[Qu]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=C("tr"),n=C("td"),w(r.$$.fragment),i=Z(),o=C("td"),w(c.$$.fragment),d=Z(),u=C("td"),w(g.$$.fragment),f=Z(),_(n,"class","name-cell svelte-1mazg1z"),_(o,"class","value-cell svelte-1mazg1z"),_(u,"class","action-cell svelte-1mazg1z"),_(t,"class","env-var-row svelte-1mazg1z"),this.first=t},m(N,T){y(N,t,T),S(t,n),x(r,n,null),S(t,i),S(t,o),x(c,o,null),S(t,d),S(t,u),x(g,u,null),S(t,f),$=!0},p(N,T){e=N;const M={};!a&&1&T&&(a=!0,M.value=e[11].key,Ne(()=>a=!1)),r.$set(M);const I={};!l&&1&T&&(l=!0,I.value=e[11].value,Ne(()=>l=!1)),c.$set(I);const U={};16387&T&&(U.$$scope={dirty:T,ctx:e}),g.$set(U)},i(N){$||(p(r.$$.fragment,N),p(c.$$.fragment,N),p(g.$$.fragment,N),$=!0)},o(N){m(r.$$.fragment,N),m(c.$$.fragment,N),m(g.$$.fragment,N),$=!1},d(N){N&&v(t),b(r),b(c),b(g)}}}function ep(s){let e;return{c(){e=L("Variable")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function tp(s){let e,t;return e=new Ts({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function np(s){let e,t,n,r,a,i,o,c;e=new oe({props:{size:1,weight:"medium",$$slots:{default:[Yu]},$$scope:{ctx:s}}});let l=s[0].length>0&&Sa(s);return o=new Ze({props:{size:1,variant:"soft",color:"neutral",type:"button",$$slots:{iconLeft:[tp],default:[ep]},$$scope:{ctx:s}}}),o.$on("click",s[2]),{c(){w(e.$$.fragment),t=Z(),n=C("table"),r=C("tbody"),l&&l.c(),a=Z(),i=C("div"),w(o.$$.fragment),_(n,"class","env-vars-table svelte-1mazg1z"),_(i,"class","new-var-button-container svelte-1mazg1z")},m(d,u){x(e,d,u),y(d,t,u),y(d,n,u),S(n,r),l&&l.m(r,null),y(d,a,u),y(d,i,u),x(o,i,null),c=!0},p(d,[u]){const g={};16384&u&&(g.$$scope={dirty:u,ctx:d}),e.$set(g),d[0].length>0?l?(l.p(d,u),1&u&&p(l,1)):(l=Sa(d),l.c(),p(l,1),l.m(r,null)):l&&(B(),m(l,1,1,()=>{l=null}),J());const f={};16384&u&&(f.$$scope={dirty:u,ctx:d}),o.$set(f)},i(d){c||(p(e.$$.fragment,d),p(l),p(o.$$.fragment,d),c=!0)},o(d){m(e.$$.fragment,d),m(l),m(o.$$.fragment,d),c=!1},d(d){d&&(v(t),v(n),v(a),v(i)),b(e,d),l&&l.d(),b(o)}}}function sp(s,e,t){let{handleEnterEditMode:n}=e,{envVarEntries:r=[]}=e;function a(c){n(),t(0,r=r.filter(l=>l.id!==c))}function i(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].key=l,r),t(0,r))}function o(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].value=l,r),t(0,r))}return s.$$set=c=>{"handleEnterEditMode"in c&&t(1,n=c.handleEnterEditMode),"envVarEntries"in c&&t(0,r=c.envVarEntries)},[r,n,function(){n(),t(0,r=[...r,{id:crypto.randomUUID(),key:"",value:""}])},a,i,o,function(c,l){s.$$.not_equal(l.key,c)&&(l.key=c,t(0,r))},c=>i(c.id,c.key),function(c,l){s.$$.not_equal(l.value,c)&&(l.value=c,t(0,r))},c=>o(c.id,c.value),c=>a(c.id)]}class rp extends ge{constructor(e){super(),$e(this,e,sp,np,fe,{handleEnterEditMode:1,envVarEntries:0})}}function ap(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h,k,A,E,N,T,M,I,U,Q,G;i=new vo({}),c=new oe({props:{color:"secondary",size:1,weight:"medium",$$slots:{default:[op]},$$scope:{ctx:s}}});const P=[lp,cp],j=[];function D(X,de){return X[0]==="addJson"?0:X[0]==="add"||X[0]==="edit"?1:-1}~(d=D(s))&&(u=j[d]=P[d](s));let re=(s[0]==="add"||s[0]==="edit")&&Ta(s);return k=new As({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[gp],default:[hp]},$$scope:{ctx:s}}}),N=new Ze({props:{size:1,variant:"ghost",color:"neutral",type:"button",$$slots:{default:[$p]},$$scope:{ctx:s}}}),N.$on("click",s[18]),M=new Ze({props:{size:1,variant:"solid",color:"accent",loading:s[2],type:"submit",disabled:s[14],$$slots:{default:[wp]},$$scope:{ctx:s}}}),{c(){e=C("form"),t=C("div"),n=C("div"),r=C("div"),a=C("div"),w(i.$$.fragment),o=Z(),w(c.$$.fragment),l=Z(),u&&u.c(),g=Z(),re&&re.c(),f=Z(),$=C("div"),h=C("div"),w(k.$$.fragment),A=Z(),E=C("div"),w(N.$$.fragment),T=Z(),w(M.$$.fragment),_(a,"class","server-icon svelte-nlsbjs"),_(r,"class","server-title svelte-nlsbjs"),_(n,"class","server-header svelte-nlsbjs"),_(h,"class","error-container svelte-nlsbjs"),be(h,"is-error",!!s[1]),_(E,"class","form-actions svelte-nlsbjs"),_($,"class","form-actions-row svelte-nlsbjs"),_(t,"class","server-edit-form svelte-nlsbjs"),_(e,"class",I="c-mcp-server-card "+(s[0]==="add"||s[0]==="addJson"?"add-server-section":"server-item")+" svelte-nlsbjs")},m(X,de){y(X,e,de),S(e,t),S(t,n),S(n,r),S(r,a),x(i,a,null),S(r,o),x(c,r,null),S(t,l),~d&&j[d].m(t,null),S(t,g),re&&re.m(t,null),S(t,f),S(t,$),S($,h),x(k,h,null),S($,A),S($,E),x(N,E,null),S(E,T),x(M,E,null),U=!0,Q||(G=Le(e,"submit",so(s[17])),Q=!0)},p(X,de){const Wt={};8192&de[0]|32&de[1]&&(Wt.$$scope={dirty:de,ctx:X}),c.$set(Wt);let Ge=d;d=D(X),d===Ge?~d&&j[d].p(X,de):(u&&(B(),m(j[Ge],1,1,()=>{j[Ge]=null}),J()),~d?(u=j[d],u?u.p(X,de):(u=j[d]=P[d](X),u.c()),p(u,1),u.m(t,g)):u=null),X[0]==="add"||X[0]==="edit"?re?(re.p(X,de),1&de[0]&&p(re,1)):(re=Ta(X),re.c(),p(re,1),re.m(t,f)):re&&(B(),m(re,1,1,()=>{re=null}),J());const Pt={};2&de[0]|32&de[1]&&(Pt.$$scope={dirty:de,ctx:X}),k.$set(Pt),(!U||2&de[0])&&be(h,"is-error",!!X[1]);const Ht={};32&de[1]&&(Ht.$$scope={dirty:de,ctx:X}),N.$set(Ht);const jt={};4&de[0]&&(jt.loading=X[2]),16384&de[0]&&(jt.disabled=X[14]),1&de[0]|32&de[1]&&(jt.$$scope={dirty:de,ctx:X}),M.$set(jt),(!U||1&de[0]&&I!==(I="c-mcp-server-card "+(X[0]==="add"||X[0]==="addJson"?"add-server-section":"server-item")+" svelte-nlsbjs"))&&_(e,"class",I)},i(X){U||(p(i.$$.fragment,X),p(c.$$.fragment,X),p(u),p(re),p(k.$$.fragment,X),p(N.$$.fragment,X),p(M.$$.fragment,X),U=!0)},o(X){m(i.$$.fragment,X),m(c.$$.fragment,X),m(u),m(re),m(k.$$.fragment,X),m(N.$$.fragment,X),m(M.$$.fragment,X),U=!1},d(X){X&&v(e),b(i),b(c),~d&&j[d].d(),re&&re.d(),b(k),b(N),b(M),Q=!1,G()}}}function ip(s){let e,t;return e=new Ns({props:{$$slots:{"header-right":[Lp],"header-left":[Tp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};4344&r[0]|32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function op(s){let e;return{c(){e=L(s[13])},m(t,n){y(t,e,n)},p(t,n){8192&n[0]&&he(e,t[13])},d(t){t&&v(e)}}}function cp(s){let e,t,n,r,a,i,o,c,l,d;function u(h){s[32](h)}let g={size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",$$slots:{label:[up]},$$scope:{ctx:s}};function f(h){s[33](h)}s[8]!==void 0&&(g.value=s[8]),n=new Dt({props:g}),Me.push(()=>Ae(n,"value",u)),n.$on("focus",s[15]);let $={size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",$$slots:{label:[mp]},$$scope:{ctx:s}};return s[9]!==void 0&&($.value=s[9]),c=new Dt({props:$}),Me.push(()=>Ae(c,"value",f)),c.$on("focus",s[15]),{c(){e=C("div"),t=C("div"),w(n.$$.fragment),a=Z(),i=C("div"),o=C("div"),w(c.$$.fragment),_(t,"class","input-field svelte-nlsbjs"),_(e,"class","form-row svelte-nlsbjs"),_(o,"class","input-field svelte-nlsbjs"),_(i,"class","form-row svelte-nlsbjs")},m(h,k){y(h,e,k),S(e,t),x(n,t,null),y(h,a,k),y(h,i,k),S(i,o),x(c,o,null),d=!0},p(h,k){const A={};32&k[1]&&(A.$$scope={dirty:k,ctx:h}),!r&&256&k[0]&&(r=!0,A.value=h[8],Ne(()=>r=!1)),n.$set(A);const E={};32&k[1]&&(E.$$scope={dirty:k,ctx:h}),!l&&512&k[0]&&(l=!0,E.value=h[9],Ne(()=>l=!1)),c.$set(E)},i(h){d||(p(n.$$.fragment,h),p(c.$$.fragment,h),d=!0)},o(h){m(n.$$.fragment,h),m(c.$$.fragment,h),d=!1},d(h){h&&(v(e),v(a),v(i)),b(n),b(c)}}}function lp(s){let e,t,n,r,a,i,o,c,l;function d(g){s[31](g)}n=new oe({props:{size:1,weight:"medium",$$slots:{default:[fp]},$$scope:{ctx:s}}});let u={size:1,placeholder:"Paste JSON here..."};return s[10]!==void 0&&(u.value=s[10]),o=new hi({props:u}),Me.push(()=>Ae(o,"value",d)),{c(){e=C("div"),t=C("div"),w(n.$$.fragment),r=Z(),a=C("div"),i=C("div"),w(o.$$.fragment),_(t,"class","input-field svelte-nlsbjs"),_(e,"class","form-row svelte-nlsbjs"),_(i,"class","input-field svelte-nlsbjs"),_(a,"class","form-row svelte-nlsbjs")},m(g,f){y(g,e,f),S(e,t),x(n,t,null),y(g,r,f),y(g,a,f),S(a,i),x(o,i,null),l=!0},p(g,f){const $={};32&f[1]&&($.$$scope={dirty:f,ctx:g}),n.$set($);const h={};!c&&1024&f[0]&&(c=!0,h.value=g[10],Ne(()=>c=!1)),o.$set(h)},i(g){l||(p(n.$$.fragment,g),p(o.$$.fragment,g),l=!0)},o(g){m(n.$$.fragment,g),m(o.$$.fragment,g),l=!1},d(g){g&&(v(e),v(r),v(a)),b(n),b(o)}}}function dp(s){let e;return{c(){e=L("Name")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function up(s){let e,t;return e=new oe({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[dp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function pp(s){let e;return{c(){e=L("Command")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function mp(s){let e,t;return e=new oe({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[pp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function fp(s){let e;return{c(){e=L("Code Snippet")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ta(s){let e,t,n;function r(i){s[34](i)}let a={handleEnterEditMode:s[15]};return s[11]!==void 0&&(a.envVarEntries=s[11]),e=new rp({props:a}),Me.push(()=>Ae(e,"envVarEntries",r)),{c(){w(e.$$.fragment)},m(i,o){x(e,i,o),n=!0},p(i,o){const c={};!t&&2048&o[0]&&(t=!0,c.envVarEntries=i[11],Ne(()=>t=!1)),e.$set(c)},i(i){n||(p(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){b(e,i)}}}function hp(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n[0]&&he(e,t[1])},d(t){t&&v(e)}}}function gp(s){let e,t;return e=new bo({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function $p(s){let e;return{c(){e=L("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function vp(s){let e;return{c(){e=L("Save")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function yp(s){let e;return{c(){e=L("Add")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function _p(s){let e;return{c(){e=L("Import")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function wp(s){let e;function t(a,i){return a[0]==="addJson"?_p:a[0]==="add"?yp:a[0]==="edit"?vp:void 0}let n=t(s),r=n&&n(s);return{c(){r&&r.c(),e=ke()},m(a,i){r&&r.m(a,i),y(a,e,i)},p(a,i){n!==(n=t(a))&&(r&&r.d(1),r=n&&n(a),r&&(r.c(),r.m(e.parentNode,e)))},d(a){a&&v(e),r&&r.d(a)}}}function xp(s){let e;return{c(){e=C("div"),_(e,"class","c-dot svelte-nlsbjs"),be(e,"c-green",!s[6]),be(e,"c-warning",!s[6]&&!!s[7]),be(e,"c-red",!!s[6]),be(e,"c-disabled",s[3].disabled)},m(t,n){y(t,e,n)},p(t,n){64&n[0]&&be(e,"c-green",!t[6]),192&n[0]&&be(e,"c-warning",!t[6]&&!!t[7]),64&n[0]&&be(e,"c-red",!!t[6]),8&n[0]&&be(e,"c-disabled",t[3].disabled)},d(t){t&&v(e)}}}function bp(s){let e,t=s[3].name+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].name+"")&&he(e,t)},d(n){n&&v(e)}}}function kp(s){let e,t,n;return t=new oe({props:{size:1,weight:"medium",$$slots:{default:[bp]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),_(e,"class","server-name svelte-nlsbjs")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};8&a[0]|32&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Sp(s){let e,t=s[3].command+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].command+"")&&he(e,t)},d(n){n&&v(e)}}}function Cp(s){let e,t,n;return t=new oe({props:{color:"secondary",size:1,weight:"regular",$$slots:{default:[Sp]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),_(e,"class","command-text svelte-nlsbjs")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};8&a[0]|32&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Tp(s){let e,t,n,r,a,i,o;return t=new hn({props:{content:s[6]||s[7],$$slots:{default:[xp]},$$scope:{ctx:s}}}),r=new hn({props:{content:s[3].name,side:"top",align:"start",$$slots:{default:[kp]},$$scope:{ctx:s}}}),i=new hn({props:{content:s[3].command,side:"top",align:"start",$$slots:{default:[Cp]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),a=Z(),w(i.$$.fragment),_(e,"slot","header-left"),_(e,"class","l-header svelte-nlsbjs")},m(c,l){y(c,e,l),x(t,e,null),S(e,n),x(r,e,null),S(e,a),x(i,e,null),o=!0},p(c,l){const d={};192&l[0]&&(d.content=c[6]||c[7]),200&l[0]|32&l[1]&&(d.$$scope={dirty:l,ctx:c}),t.$set(d);const u={};8&l[0]&&(u.content=c[3].name),8&l[0]|32&l[1]&&(u.$$scope={dirty:l,ctx:c}),r.$set(u);const g={};8&l[0]&&(g.content=c[3].command),8&l[0]|32&l[1]&&(g.$$scope={dirty:l,ctx:c}),i.$set(g)},i(c){o||(p(t.$$.fragment,c),p(r.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(r.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&v(e),b(t),b(r),b(i)}}}function Mp(s){let e,t;return e=new Po({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ap(s){let e,t;return e=new Ms({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[Mp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Np(s){let e;return{c(){e=L("Edit")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Zp(s){let e,t,n,r,a;return t=new jo({}),r=new oe({props:{size:1,weight:"medium",$$slots:{default:[Np]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){y(i,e,o),x(t,e,null),S(e,n),x(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function Ep(s){let e;return{c(){e=L("Copy JSON")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ip(s){let e,t,n,r,a;return t=new No({}),r=new oe({props:{size:1,weight:"medium",$$slots:{default:[Ep]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){y(i,e,o),x(t,e,null),S(e,n),x(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function Rp(s){let e;return{c(){e=L("Delete")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Op(s){let e,t,n,r,a;return t=new Ji({}),r=new oe({props:{size:1,weight:"medium",$$slots:{default:[Rp]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){y(i,e,o),x(t,e,null),S(e,n),x(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function Pp(s){let e,t,n,r,a,i;return e=new Ce.Item({props:{onSelect:s[15],$$slots:{default:[Zp]},$$scope:{ctx:s}}}),n=new Ce.Item({props:{onSelect:s[28],$$slots:{default:[Ip]},$$scope:{ctx:s}}}),a=new Ce.Item({props:{color:"error",onSelect:s[29],$$slots:{default:[Op]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment),r=Z(),w(a.$$.fragment)},m(o,c){x(e,o,c),y(o,t,c),x(n,o,c),y(o,r,c),x(a,o,c),i=!0},p(o,c){const l={};32&c[1]&&(l.$$scope={dirty:c,ctx:o}),e.$set(l);const d={};4096&c[0]&&(d.onSelect=o[28]),32&c[1]&&(d.$$scope={dirty:c,ctx:o}),n.$set(d);const u={};4120&c[0]&&(u.onSelect=o[29]),32&c[1]&&(u.$$scope={dirty:c,ctx:o}),a.$set(u)},i(o){i||(p(e.$$.fragment,o),p(n.$$.fragment,o),p(a.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),m(n.$$.fragment,o),m(a.$$.fragment,o),i=!1},d(o){o&&(v(t),v(r)),b(e,o),b(n,o),b(a,o)}}}function jp(s){let e,t,n,r;return e=new Ce.Trigger({props:{$$slots:{default:[Ap]},$$scope:{ctx:s}}}),n=new Ce.Content({props:{side:"bottom",align:"end",$$slots:{default:[Pp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};32&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};4120&i[0]|32&i[1]&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Lp(s){let e,t,n,r,a,i,o;function c(d){s[30](d)}n=new ro({props:{size:1,checked:!s[3].disabled}}),n.$on("change",s[27]);let l={$$slots:{default:[jp]},$$scope:{ctx:s}};return s[12]!==void 0&&(l.requestClose=s[12]),a=new Ce.Root({props:l}),Me.push(()=>Ae(a,"requestClose",c)),{c(){e=C("div"),t=C("div"),w(n.$$.fragment),r=Z(),w(a.$$.fragment),_(t,"class","status-controls svelte-nlsbjs"),_(e,"class","server-actions svelte-nlsbjs"),_(e,"slot","header-right")},m(d,u){y(d,e,u),S(e,t),x(n,t,null),S(t,r),x(a,t,null),o=!0},p(d,u){const g={};8&u[0]&&(g.checked=!d[3].disabled),n.$set(g);const f={};4120&u[0]|32&u[1]&&(f.$$scope={dirty:u,ctx:d}),!i&&4096&u[0]&&(i=!0,f.requestClose=d[12],Ne(()=>i=!1)),a.$set(f)},i(d){o||(p(n.$$.fragment,d),p(a.$$.fragment,d),o=!0)},o(d){m(n.$$.fragment,d),m(a.$$.fragment,d),o=!1},d(d){d&&v(e),b(n),b(a)}}}function Fp(s){let e,t,n,r;const a=[ip,ap],i=[];function o(c,l){return c[0]==="view"&&c[3]?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=ke()},m(c,l){i[e].m(c,l),y(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(B(),m(i[d],1,1,()=>{i[d]=null}),J(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&v(n),i[e].d(c)}}}function zp({key:s,value:e}){return s.trim()&&e.trim()}function Dp(s,e,t){let n,r,a,i,{server:o=null}=e,{onDelete:c}=e,{onAdd:l}=e,{onSave:d}=e,{onEdit:u}=e,{onToggleDisableServer:g}=e,{onJSONImport:f}=e,{onCancel:$}=e,{disabledText:h}=e,{warningText:k}=e,{mode:A="view"}=e,{mcpServerError:E=""}=e,N=(o==null?void 0:o.name)??"",T=(o==null?void 0:o.command)??"",M=(o==null?void 0:o.env)??{},I="",U=[];function Q(){t(11,U=Object.entries(M).map(([D,re])=>({id:crypto.randomUUID(),key:D,value:re})))}Q();let G=()=>{},{busy:P=!1}=e;function j(){if(o){const D=rr.convertServerToJSON(o);navigator.clipboard.writeText(D)}}return s.$$set=D=>{"server"in D&&t(3,o=D.server),"onDelete"in D&&t(4,c=D.onDelete),"onAdd"in D&&t(19,l=D.onAdd),"onSave"in D&&t(20,d=D.onSave),"onEdit"in D&&t(21,u=D.onEdit),"onToggleDisableServer"in D&&t(5,g=D.onToggleDisableServer),"onJSONImport"in D&&t(22,f=D.onJSONImport),"onCancel"in D&&t(23,$=D.onCancel),"disabledText"in D&&t(6,h=D.disabledText),"warningText"in D&&t(7,k=D.warningText),"mode"in D&&t(0,A=D.mode),"mcpServerError"in D&&t(1,E=D.mcpServerError),"busy"in D&&t(2,P=D.busy)},s.$$.update=()=>{768&s.$$.dirty[0]&&N&&T&&t(1,E=""),769&s.$$.dirty[0]&&t(26,n=!(A!=="add"||N.trim()&&T.trim())),1025&s.$$.dirty[0]&&t(25,r=A==="addJson"&&!I.trim()),100663297&s.$$.dirty[0]&&t(14,a=n||A==="view"||r),1&s.$$.dirty[0]&&t(13,i=A==="add"||A==="addJson"?"New MCP Server":"Edit MCP Server")},[A,E,P,o,c,g,h,k,N,T,I,U,G,i,a,function(){o&&A==="view"&&(t(0,A="edit"),u(o),G())},j,async function(){t(1,E=""),t(2,P=!0);const D=U.filter(zp);M=Object.fromEntries(D.map(({key:X,value:de})=>[X.trim(),de.trim()])),Q();try{if(A==="add")await l({name:N.trim(),command:T.trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(M).length>0?M:void 0});else if(A==="addJson"){try{JSON.parse(I)}catch(X){const de=X instanceof Error?X.message:String(X);throw new ze(`Invalid JSON format: ${de}`)}await f(I)}else A==="edit"&&o&&await d({...o,name:N.trim(),command:T.trim(),arguments:"",env:Object.keys(M).length>0?M:void 0})}catch(X){t(1,E=(re=X)!=null&&typeof re=="object"&&(re instanceof ze||Bi in(re.constructor||re))?X.message:"Failed to save server"),console.warn(X)}finally{t(2,P=!1)}var re},function(){t(2,P=!1),t(1,E=""),$==null||$(),t(10,I=""),t(8,N=(o==null?void 0:o.name)??""),t(9,T=(o==null?void 0:o.command)??""),M=o!=null&&o.env?{...o.env}:{},Q()},l,d,u,f,$,Q,r,n,()=>{o&&g(o.id),G()},()=>{j(),G()},()=>{c(o.id),G()},function(D){G=D,t(12,G)},function(D){I=D,t(10,I)},function(D){N=D,t(8,N)},function(D){T=D,t(9,T)},function(D){U=D,t(11,U)}]}class Gi extends ge{constructor(e){super(),$e(this,e,Dp,Fp,fe,{server:3,onDelete:4,onAdd:19,onSave:20,onEdit:21,onToggleDisableServer:5,onJSONImport:22,onCancel:23,disabledText:6,warningText:7,mode:0,mcpServerError:1,setLocalEnvVarFormState:24,busy:2},null,[-1,-1])}get setLocalEnvVarFormState(){return this.$$.ctx[24]}}function Up(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Te(r,n[a]);return{c(){e=Be("svg"),t=new Bn(!0),this.h()},l(a){e=Jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Gn(e);t=Wn(i,!0),i.forEach(v),this.h()},h(){t.a=null,dt(e,r)},m(a,i){Hn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',e)},p(a,[i]){dt(e,r=wt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:W,o:W,d(a){a&&v(e)}}}function Vp(s,e,t){return s.$$set=n=>{t(0,e=Te(Te({},e),Qe(n)))},[e=Qe(e)]}class Wi extends ge{constructor(e){super(),$e(this,e,Vp,Up,fe,{})}}function Ma(s,e,t){const n=s.slice();return n[20]=e[t],n}function qp(s){let e,t,n;return t=new ko({}),{c(){e=C("div"),w(t.$$.fragment),_(e,"slot","iconLeft"),_(e,"class","search-icon")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p:W,i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Bp(s){let e,t=s[20].label+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p:W,d(n){n&&v(e)}}}function Jp(s){let e,t=s[20].description+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p:W,d(n){n&&v(e)}}}function Aa(s){let e,t,n,r,a,i,o,c,l,d;function u($){s[15]($)}function g($){s[16]($)}let f={placeholder:"Enter your API key...",size:1,variant:"surface",type:"password"};return s[4]!==void 0&&(f.value=s[4]),s[5]!==void 0&&(f.textInput=s[5]),t=new Dt({props:f}),Me.push(()=>Ae(t,"value",u)),Me.push(()=>Ae(t,"textInput",g)),t.$on("keydown",function(...$){return s[17](s[20],...$)}),o=new Ze({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[Gp]},$$scope:{ctx:s}}}),o.$on("click",function(){return s[18](s[20])}),l=new Ze({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[Wp]},$$scope:{ctx:s}}}),l.$on("click",s[10]),{c(){e=C("div"),w(t.$$.fragment),a=Z(),i=C("div"),w(o.$$.fragment),c=Z(),w(l.$$.fragment),_(i,"class","api-key-actions svelte-1y2bury"),_(e,"class","api-key-input-container svelte-1y2bury")},m($,h){y($,e,h),x(t,e,null),S(e,a),S(e,i),x(o,i,null),S(i,c),x(l,i,null),d=!0},p($,h){s=$;const k={};!n&&16&h&&(n=!0,k.value=s[4],Ne(()=>n=!1)),!r&&32&h&&(r=!0,k.textInput=s[5],Ne(()=>r=!1)),t.$set(k);const A={};8388608&h&&(A.$$scope={dirty:h,ctx:s}),o.$set(A);const E={};8388608&h&&(E.$$scope={dirty:h,ctx:s}),l.$set(E)},i($){d||(p(t.$$.fragment,$),p(o.$$.fragment,$),p(l.$$.fragment,$),d=!0)},o($){m(t.$$.fragment,$),m(o.$$.fragment,$),m(l.$$.fragment,$),d=!1},d($){$&&v(e),b(t),b(o),b(l)}}}function Gp(s){let e;return{c(){e=L("Install")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Wp(s){let e;return{c(){e=L("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Hp(s){let e,t,n,r,a,i;n=new oe({props:{size:1,weight:"medium",$$slots:{default:[Bp]},$$scope:{ctx:s}}});let o=s[20].description&&function(l){let d,u;return d=new oe({props:{size:1,color:"secondary",$$slots:{default:[Jp]},$$scope:{ctx:l}}}),{c(){w(d.$$.fragment)},m(g,f){x(d,g,f),u=!0},p(g,f){const $={};8388608&f&&($.$$scope={dirty:f,ctx:g}),d.$set($)},i(g){u||(p(d.$$.fragment,g),u=!0)},o(g){m(d.$$.fragment,g),u=!1},d(g){b(d,g)}}}(s),c=s[3]===s[20].value&&Aa(s);return{c(){e=C("div"),t=C("div"),w(n.$$.fragment),r=Z(),o&&o.c(),a=Z(),c&&c.c(),_(t,"class","mcp-service-title svelte-1y2bury"),_(e,"slot","header-left"),_(e,"class","mcp-service-info svelte-1y2bury")},m(l,d){y(l,e,d),S(e,t),x(n,t,null),S(e,r),o&&o.m(e,null),S(e,a),c&&c.m(e,null),i=!0},p(l,d){const u={};8388608&d&&(u.$$scope={dirty:d,ctx:l}),n.$set(u),l[20].description&&o.p(l,d),l[3]===l[20].value?c?(c.p(l,d),8&d&&p(c,1)):(c=Aa(l),c.c(),p(c,1),c.m(e,null)):c&&(B(),m(c,1,1,()=>{c=null}),J())},i(l){i||(p(n.$$.fragment,l),p(o),p(c),i=!0)},o(l){m(n.$$.fragment,l),m(o),m(c),i=!1},d(l){l&&v(e),b(n),o&&o.d(),c&&c.d()}}}function Kp(s){let e,t;return e=new Ze({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[Xp]},$$scope:{ctx:s}}}),e.$on("click",function(){return s[14](s[20])}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){s=n;const a={};8388608&r&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Yp(s){let e,t,n;return t=new li.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Qp]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),_(e,"class","installed-indicator svelte-1y2bury")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};8388608&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Xp(s){let e;return{c(){e=C("span"),e.textContent="+"},m(t,n){y(t,e,n)},p:W,d(t){t&&v(e)}}}function Qp(s){let e;return{c(){e=L("Installed")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function em(s){let e,t,n,r,a;function i(...d){return s[12](s[20],...d)}const o=[Yp,Kp],c=[];function l(d,u){return 1&u&&(t=null),t==null&&(t=!!d[0].some(i)),t?0:1}return n=l(s,-1),r=c[n]=o[n](s),{c(){e=C("div"),r.c(),_(e,"slot","header-right"),_(e,"class","mcp-service-actions svelte-1y2bury")},m(d,u){y(d,e,u),c[n].m(e,null),a=!0},p(d,u){let g=n;n=l(s=d,u),n===g?c[n].p(s,u):(B(),m(c[g],1,1,()=>{c[g]=null}),J(),r=c[n],r?r.p(s,u):(r=c[n]=o[n](s),r.c()),p(r,1),r.m(e,null))},i(d){a||(p(r),a=!0)},o(d){m(r),a=!1},d(d){d&&v(e),c[n].d()}}}function Na(s){let e,t,n,r;return t=new Ns({props:{$$slots:{"header-right":[em],"header-left":[Hp]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),_(e,"class","mcp-service-item")},m(a,i){y(a,e,i),x(t,e,null),S(e,n),r=!0},p(a,i){const o={};8388665&i&&(o.$$scope={dirty:i,ctx:a}),t.$set(o)},i(a){r||(p(t.$$.fragment,a),r=!0)},o(a){m(t.$$.fragment,a),r=!1},d(a){a&&v(e),b(t)}}}function tm(s){let e,t,n,r,a=s[6].length>9&&function(l){let d,u,g,f;function $(k){l[13](k)}let h={placeholder:"Search MCPs...",size:1,variant:"surface",$$slots:{iconLeft:[qp]},$$scope:{ctx:l}};return l[2]!==void 0&&(h.value=l[2]),u=new Dt({props:h}),Me.push(()=>Ae(u,"value",$)),u.$on("input",l[7]),{c(){d=C("div"),w(u.$$.fragment),_(d,"class","mcp-search-container")},m(k,A){y(k,d,A),x(u,d,null),f=!0},p(k,A){const E={};8388608&A&&(E.$$scope={dirty:A,ctx:k}),!g&&4&A&&(g=!0,E.value=k[2],Ne(()=>g=!1)),u.$set(E)},i(k){f||(p(u.$$.fragment,k),f=!0)},o(k){m(u.$$.fragment,k),f=!1},d(k){k&&v(d),b(u)}}}(s),i=_e(s[6]),o=[];for(let l=0;l<i.length;l+=1)o[l]=Na(Ma(s,i,l));const c=l=>m(o[l],1,1,()=>{o[l]=null});return{c(){e=C("div"),a&&a.c(),t=Z(),n=C("div");for(let l=0;l<o.length;l+=1)o[l].c();_(n,"class","mcp-list-container svelte-1y2bury"),_(e,"class","mcp-install-content svelte-1y2bury")},m(l,d){y(l,e,d),a&&a.m(e,null),S(e,t),S(e,n);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(n,null);r=!0},p(l,d){if(l[6].length>9&&a.p(l,d),1913&d){let u;for(i=_e(l[6]),u=0;u<i.length;u+=1){const g=Ma(l,i,u);o[u]?(o[u].p(g,d),p(o[u],1)):(o[u]=Na(g),o[u].c(),p(o[u],1),o[u].m(n,null))}for(B(),u=i.length;u<o.length;u+=1)c(u);J()}},i(l){if(!r){p(a);for(let d=0;d<i.length;d+=1)p(o[d]);r=!0}},o(l){m(a),o=o.filter(Boolean);for(let d=0;d<o.length;d+=1)m(o[d]);r=!1},d(l){l&&v(e),a&&a.d(),Gt(o,l)}}}function nm(s){let e;return{c(){e=L("Easy MCP Installation")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function sm(s){let e,t,n,r,a;return t=new Fo({}),r=new oe({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[nm]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"slot","header-left"),_(e,"class","mcp-install-left svelte-1y2bury")},m(i,o){y(i,e,o),x(t,e,null),S(e,n),x(r,e,null),a=!0},p(i,o){const c={};8388608&o&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function rm(s){let e,t,n;return t=new Ns({props:{$$slots:{"header-left":[sm]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),_(e,"slot","header"),_(e,"class","mcp-install-header svelte-1y2bury")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};8388608&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function am(s){let e,t,n,r;function a(o){s[19](o)}let i={$$slots:{header:[rm],default:[tm]},$$scope:{ctx:s}};return s[1]!==void 0&&(i.collapsed=s[1]),t=new Lo({props:i}),Me.push(()=>Ae(t,"collapsed",a)),{c(){e=C("div"),w(t.$$.fragment),_(e,"class","mcp-install-wrapper svelte-1y2bury")},m(o,c){y(o,e,c),x(t,e,null),r=!0},p(o,[c]){const l={};8388669&c&&(l.$$scope={dirty:c,ctx:o}),!n&&2&c&&(n=!0,l.collapsed=o[1],Ne(()=>n=!1)),t.$set(l)},i(o){r||(p(t.$$.fragment,o),r=!0)},o(o){m(t.$$.fragment,o),r=!1},d(o){o&&v(e),b(t)}}}function im(s,e){return s.value==="tavily"?`npx -y tavily-mcp@latest --TAVILY_API_KEY=${e}`:s.command}function om(s,e){switch(s.value){case"tavily":return{};case"exa-search":return{EXA_API_KEY:e};default:return{API_KEY:e}}}function cm(s,e,t){let{onMCPServerAdd:n}=e,{servers:r=[]}=e,a,i=!0,o="",c=null,l="";async function d(f){try{if(r.some(h=>h.name===f.label))return;const $={name:f.label,command:f.command,arguments:"",useShellInterpolation:!0,env:void 0};n&&n($)}catch($){console.error(`Failed to install ${f.label}:`,$)}}async function u(f){try{if(!l.trim())return void(a==null?void 0:a.focus());const $={name:f.label,command:im(f,l.trim()),arguments:"",useShellInterpolation:!0,env:om(f,l.trim())};n&&n($),t(3,c=null),t(4,l="")}catch($){console.error(`Failed to install ${f.label}:`,$)}}function g(){t(3,c=null),t(4,l="")}return s.$$set=f=>{"onMCPServerAdd"in f&&t(11,n=f.onMCPServerAdd),"servers"in f&&t(0,r=f.servers)},[r,i,o,c,l,a,[{value:"context7",label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{value:"playwright",label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{value:"sequential-thinking",label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],function(f){const $=f.target;t(2,o=$.value)},d,u,g,n,(f,$)=>$.name===f.label,function(f){o=f,t(2,o)},f=>d(f),function(f){l=f,t(4,l)},function(f){a=f,t(5,a)},(f,$)=>{$.key==="Enter"?u(f):$.key==="Escape"&&g()},f=>u(f),function(f){i=f,t(1,i)}]}class lm extends ge{constructor(e){super(),$e(this,e,cm,am,fe,{onMCPServerAdd:11,servers:0})}}const dm={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},um={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},pm=oo(),mm=new class{constructor(s){ye(this,"strings");let e={[$r.vscode]:{},[$r.jetbrains]:um};this.strings={...dm,...e[s]}}get(s){return this.strings[s]}}(pm.clientType);function Za(s,e,t){const n=s.slice();return n[23]=e[t],n}function fm(s){let e;return{c(){e=C("div"),e.textContent="MCP",_(e,"class","section-heading-text")},m(t,n){y(t,e,n)},p:W,d(t){t&&v(e)}}}function Ea(s,e){let t,n,r;return n=new Gi({props:{mode:e[2]===e[23].id?"edit":"view",server:e[23],onAdd:e[8],onSave:e[9],onDelete:e[11],onToggleDisableServer:e[12],onEdit:e[7],onCancel:e[6],onJSONImport:e[10],disabledText:e[4].errors.get(e[23].id),warningText:e[4].warnings.get(e[23].id)}}),{key:s,first:null,c(){t=ke(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};5&i&&(o.mode=e[2]===e[23].id?"edit":"view"),1&i&&(o.server=e[23]),17&i&&(o.disabledText=e[4].errors.get(e[23].id)),17&i&&(o.warningText=e[4].warnings.get(e[23].id)),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function Ia(s){let e,t;return e=new Gi({props:{mode:s[3],onAdd:s[8],onSave:s[9],onDelete:s[11],onToggleDisableServer:s[12],onEdit:s[7],onCancel:s[6],onJSONImport:s[10]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.mode=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function hm(s){let e;return{c(){e=L("Add MCP")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function gm(s){let e,t;return e=new Ts({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ra(s){let e,t;return e=new Ze({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$slots:{iconLeft:[vm],default:[$m]},$$scope:{ctx:s}}}),e.$on("click",s[21]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r&&(a.disabled=n[5]),67108864&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function $m(s){let e;return{c(){e=L("Import from JSON")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function vm(s){let e,t;return e=new Wi({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ym(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h,k,A,E,N=[],T=new Map;n=new oe({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[fm]},$$scope:{ctx:s}}}),u=new lm({props:{onMCPServerAdd:s[8],servers:s[0]}});let M=_e(s[0]);const I=G=>G[23].id;for(let G=0;G<M.length;G+=1){let P=Za(s,M,G),j=I(P);T.set(j,N[G]=Ea(j,P))}let U=(s[3]==="add"||s[3]==="addJson")&&Ia(s);k=new Ze({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[gm],default:[hm]},$$scope:{ctx:s}}}),k.$on("click",s[20]);let Q=s[1]&&Ra(s);return{c(){e=C("div"),t=C("div"),w(n.$$.fragment),r=Z(),a=C("div"),i=L(`Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP `),o=C("a"),c=L("in the docs"),l=L("."),d=Z(),w(u.$$.fragment),g=Z();for(let G=0;G<N.length;G+=1)N[G].c();f=Z(),U&&U.c(),$=Z(),h=C("div"),w(k.$$.fragment),A=Z(),Q&&Q.c(),_(t,"class","section-heading svelte-1vnq4q3"),_(o,"href",s[13]),_(a,"class","description-text svelte-1vnq4q3"),_(e,"class","mcp-servers svelte-1vnq4q3"),_(h,"class","add-mcp-button-container svelte-1vnq4q3")},m(G,P){y(G,e,P),S(e,t),x(n,t,null),S(e,r),S(e,a),S(a,i),S(a,o),S(o,c),S(a,l),S(e,d),x(u,e,null),S(e,g);for(let j=0;j<N.length;j+=1)N[j]&&N[j].m(e,null);y(G,f,P),U&&U.m(G,P),y(G,$,P),y(G,h,P),x(k,h,null),S(h,A),Q&&Q.m(h,null),E=!0},p(G,[P]){const j={};67108864&P&&(j.$$scope={dirty:P,ctx:G}),n.$set(j);const D={};1&P&&(D.servers=G[0]),u.$set(D),8149&P&&(M=_e(G[0]),B(),N=Rt(N,P,I,1,G,M,T,e,Ot,Ea,null,Za),J()),G[3]==="add"||G[3]==="addJson"?U?(U.p(G,P),8&P&&p(U,1)):(U=Ia(G),U.c(),p(U,1),U.m($.parentNode,$)):U&&(B(),m(U,1,1,()=>{U=null}),J());const re={};32&P&&(re.disabled=G[5]),67108864&P&&(re.$$scope={dirty:P,ctx:G}),k.$set(re),G[1]?Q?(Q.p(G,P),2&P&&p(Q,1)):(Q=Ra(G),Q.c(),p(Q,1),Q.m(h,null)):Q&&(B(),m(Q,1,1,()=>{Q=null}),J())},i(G){if(!E){p(n.$$.fragment,G),p(u.$$.fragment,G);for(let P=0;P<M.length;P+=1)p(N[P]);p(U),p(k.$$.fragment,G),p(Q),E=!0}},o(G){m(n.$$.fragment,G),m(u.$$.fragment,G);for(let P=0;P<N.length;P+=1)m(N[P]);m(U),m(k.$$.fragment,G),m(Q),E=!1},d(G){G&&(v(e),v(f),v($),v(h)),b(n),b(u);for(let P=0;P<N.length;P+=1)N[P].d();U&&U.d(G),b(k),Q&&Q.d()}}}function _m(s,e,t){let n,r,{servers:a}=e,{onMCPServerAdd:i}=e,{onMCPServerSave:o}=e,{onMCPServerDelete:c}=e,{onMCPServerToggleDisable:l}=e,{onCancel:d}=e,{onMCPServerJSONImport:u}=e,{isMCPImportEnabled:g=!0}=e,f=null,$=null;function h(I){return async function(...U){const Q=await I(...U);return t(3,$=null),t(2,f=null),Q}}const k=h(i),A=h(o),E=h(u),N=h(c),T=h(l),M=mm.get("mcpDocsURL");return s.$$set=I=>{"servers"in I&&t(0,a=I.servers),"onMCPServerAdd"in I&&t(14,i=I.onMCPServerAdd),"onMCPServerSave"in I&&t(15,o=I.onMCPServerSave),"onMCPServerDelete"in I&&t(16,c=I.onMCPServerDelete),"onMCPServerToggleDisable"in I&&t(17,l=I.onMCPServerToggleDisable),"onCancel"in I&&t(18,d=I.onCancel),"onMCPServerJSONImport"in I&&t(19,u=I.onMCPServerJSONImport),"isMCPImportEnabled"in I&&t(1,g=I.isMCPImportEnabled)},s.$$.update=()=>{12&s.$$.dirty&&t(5,n=$==="add"||$==="addJson"||f!==null),1&s.$$.dirty&&t(4,r=rr.parseServerValidationMessages(a))},[a,g,f,$,r,n,function(){t(2,f=null),t(3,$=null),d==null||d()},function(I){t(2,f=I.id)},k,A,E,N,T,M,i,o,c,l,d,u,()=>{t(3,$="add")},()=>{t(3,$="addJson")}]}class wm extends ge{constructor(e){super(),$e(this,e,_m,ym,fe,{servers:0,onMCPServerAdd:14,onMCPServerSave:15,onMCPServerDelete:16,onMCPServerToggleDisable:17,onCancel:18,onMCPServerJSONImport:19,isMCPImportEnabled:1})}}function Oa(s,e,t){const n=s.slice();return n[12]=e[t],n}function xm(s){let e;return{c(){e=C("div"),e.textContent="Terminal",_(e,"class","section-heading-text")},m(t,n){y(t,e,n)},p:W,d(t){t&&v(e)}}}function bm(s){let e;return{c(){e=L("Shell:")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function km(s){let e;return{c(){e=L("Select a shell")},m(t,n){y(t,e,n)},p:W,d(t){t&&v(e)}}}function Sm(s){let e;return{c(){e=L("No shells available")},m(t,n){y(t,e,n)},p:W,d(t){t&&v(e)}}}function Cm(s){let e,t,n,r,a=s[5].friendlyName+"",i=s[5].supportString+"";return{c(){e=L(a),t=L(`
            (`),n=L(i),r=L(")")},m(o,c){y(o,e,c),y(o,t,c),y(o,n,c),y(o,r,c)},p(o,c){32&c&&a!==(a=o[5].friendlyName+"")&&he(e,a),32&c&&i!==(i=o[5].supportString+"")&&he(n,i)},d(o){o&&(v(e),v(t),v(n),v(r))}}}function Tm(s){let e;function t(a,i){return a[5]&&a[1].length>0?Cm:a[1].length===0?Sm:km}let n=t(s),r=n(s);return{c(){r.c(),e=ke()},m(a,i){r.m(a,i),y(a,e,i)},p(a,i){n===(n=t(a))&&r?r.p(a,i):(r.d(1),r=n(a),r&&(r.c(),r.m(e.parentNode,e)))},d(a){a&&v(e),r.d(a)}}}function Mm(s){let e,t;return e=new So({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Am(s){let e,t;return e=new Ze({props:{size:1,variant:"outline",color:"neutral",disabled:s[1].length===0,$$slots:{iconRight:[Mm],default:[Tm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.disabled=n[1].length===0),32802&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Nm(s){let e,t;return e=new Ce.Label({props:{$$slots:{default:[Em]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32768&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Zm(s){let e,t,n=[],r=new Map,a=_e(s[1]);const i=o=>o[12].friendlyName;for(let o=0;o<a.length;o+=1){let c=Oa(s,a,o),l=i(c);r.set(l,n[o]=Pa(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=ke()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);y(o,e,c),t=!0},p(o,c){30&c&&(a=_e(o[1]),B(),n=Rt(n,c,i,1,o,a,r,e.parentNode,Ot,Pa,e,Oa),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Em(s){let e;return{c(){e=L("No shells available")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Im(s){let e,t,n,r,a=s[12].friendlyName+"",i=s[12].supportString+"";return{c(){e=L(a),t=L(`
              (`),n=L(i),r=L(`)
            `)},m(o,c){y(o,e,c),y(o,t,c),y(o,n,c),y(o,r,c)},p(o,c){2&c&&a!==(a=o[12].friendlyName+"")&&he(e,a),2&c&&i!==(i=o[12].supportString+"")&&he(n,i)},d(o){o&&(v(e),v(t),v(n),v(r))}}}function Pa(s,e){let t,n,r;function a(){return e[8](e[12])}return n=new Ce.Item({props:{onSelect:a,highlight:e[2]===e[12].friendlyName,$$slots:{default:[Im]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=ke(),w(n.$$.fragment),this.first=t},m(i,o){y(i,t,o),x(n,i,o),r=!0},p(i,o){e=i;const c={};26&o&&(c.onSelect=a),6&o&&(c.highlight=e[2]===e[12].friendlyName),32770&o&&(c.$$scope={dirty:o,ctx:e}),n.$set(c)},i(i){r||(p(n.$$.fragment,i),r=!0)},o(i){m(n.$$.fragment,i),r=!1},d(i){i&&v(t),b(n,i)}}}function Rm(s){let e,t,n,r;const a=[Zm,Nm],i=[];function o(c,l){return c[1].length>0?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=ke()},m(c,l){i[e].m(c,l),y(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(B(),m(i[d],1,1,()=>{i[d]=null}),J(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&v(n),i[e].d(c)}}}function Om(s){let e,t,n,r;return e=new Ce.Trigger({props:{$$slots:{default:[Am]},$$scope:{ctx:s}}}),n=new Ce.Content({props:{side:"bottom",align:"start",$$slots:{default:[Rm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};32802&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};32798&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Pm(s){let e;return{c(){e=L("Start-up script: Code to run wherever a new terminal is opened")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function jm(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h;function k(T){s[9](T)}t=new oe({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[xm]},$$scope:{ctx:s}}}),a=new oe({props:{size:1,$$slots:{default:[bm]},$$scope:{ctx:s}}});let A={$$slots:{default:[Om]},$$scope:{ctx:s}};function E(T){s[10](T)}s[4]!==void 0&&(A.requestClose=s[4]),o=new Ce.Root({props:A}),Me.push(()=>Ae(o,"requestClose",k)),u=new oe({props:{size:1,$$slots:{default:[Pm]},$$scope:{ctx:s}}});let N={placeholder:"Enter shell commands to run on terminal startup",resize:"vertical"};return s[0]!==void 0&&(N.value=s[0]),f=new hi({props:N}),Me.push(()=>Ae(f,"value",E)),f.$on("change",s[6]),{c(){e=C("div"),w(t.$$.fragment),n=Z(),r=C("div"),w(a.$$.fragment),i=Z(),w(o.$$.fragment),l=Z(),d=C("div"),w(u.$$.fragment),g=Z(),w(f.$$.fragment),_(r,"class","shell-selector svelte-dndd5n"),_(d,"class","startup-script-container svelte-dndd5n"),_(e,"class","terminal-settings svelte-dndd5n")},m(T,M){y(T,e,M),x(t,e,null),S(e,n),S(e,r),x(a,r,null),S(r,i),x(o,r,null),S(e,l),S(e,d),x(u,d,null),S(d,g),x(f,d,null),h=!0},p(T,[M]){const I={};32768&M&&(I.$$scope={dirty:M,ctx:T}),t.$set(I);const U={};32768&M&&(U.$$scope={dirty:M,ctx:T}),a.$set(U);const Q={};32830&M&&(Q.$$scope={dirty:M,ctx:T}),!c&&16&M&&(c=!0,Q.requestClose=T[4],Ne(()=>c=!1)),o.$set(Q);const G={};32768&M&&(G.$$scope={dirty:M,ctx:T}),u.$set(G);const P={};!$&&1&M&&($=!0,P.value=T[0],Ne(()=>$=!1)),f.$set(P)},i(T){h||(p(t.$$.fragment,T),p(a.$$.fragment,T),p(o.$$.fragment,T),p(u.$$.fragment,T),p(f.$$.fragment,T),h=!0)},o(T){m(t.$$.fragment,T),m(a.$$.fragment,T),m(o.$$.fragment,T),m(u.$$.fragment,T),m(f.$$.fragment,T),h=!1},d(T){T&&v(e),b(t),b(a),b(o),b(u),b(f)}}}function Lm(s,e,t){let n,r,{supportedShells:a=[]}=e,{selectedShell:i}=e,{startupScript:o}=e,{onShellSelect:c}=e,{onStartupScriptChange:l}=e;return s.$$set=d=>{"supportedShells"in d&&t(1,a=d.supportedShells),"selectedShell"in d&&t(2,i=d.selectedShell),"startupScript"in d&&t(0,o=d.startupScript),"onShellSelect"in d&&t(3,c=d.onShellSelect),"onStartupScriptChange"in d&&t(7,l=d.onStartupScriptChange)},s.$$.update=()=>{var d;4&s.$$.dirty&&t(5,n=i?(d=i,a.find(u=>u.friendlyName===d)):void 0)},[o,a,i,c,r,n,function(d){const u=d.target;l(u.value)},l,d=>{c(d.friendlyName),r()},function(d){r=d,t(4,r)},function(d){o=d,t(0,o)}]}class Fm extends ge{constructor(e){super(),$e(this,e,Lm,jm,fe,{supportedShells:1,selectedShell:2,startupScript:0,onShellSelect:3,onStartupScriptChange:7})}}function ja(s){let e,t;return e=new wm({props:{servers:s[1],onMCPServerAdd:s[7],onMCPServerSave:s[8],onMCPServerDelete:s[9],onMCPServerToggleDisable:s[10],onMCPServerJSONImport:s[11],onCancel:s[12],isMCPImportEnabled:s[3]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.servers=n[1]),128&r&&(a.onMCPServerAdd=n[7]),256&r&&(a.onMCPServerSave=n[8]),512&r&&(a.onMCPServerDelete=n[9]),1024&r&&(a.onMCPServerToggleDisable=n[10]),2048&r&&(a.onMCPServerJSONImport=n[11]),4096&r&&(a.onCancel=n[12]),8&r&&(a.isMCPImportEnabled=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function La(s){let e,t;return e=new Fm({props:{supportedShells:s[13],selectedShell:s[14],startupScript:s[15],onShellSelect:s[16],onStartupScriptChange:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8192&r&&(a.supportedShells=n[13]),16384&r&&(a.selectedShell=n[14]),32768&r&&(a.startupScript=n[15]),65536&r&&(a.onShellSelect=n[16]),131072&r&&(a.onStartupScriptChange=n[17]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function zm(s){let e,t,n,r,a;t=new Vd({props:{title:"Services",tools:s[0],onAuthenticate:s[5],onRevokeAccess:s[6]}});let i=s[2]&&ja(s),o=s[4]&&La(s);return{c(){e=C("div"),w(t.$$.fragment),n=Z(),i&&i.c(),r=Z(),o&&o.c(),_(e,"class","c-settings-tools svelte-181yusq")},m(c,l){y(c,e,l),x(t,e,null),S(e,n),i&&i.m(e,null),S(e,r),o&&o.m(e,null),a=!0},p(c,[l]){const d={};1&l&&(d.tools=c[0]),32&l&&(d.onAuthenticate=c[5]),64&l&&(d.onRevokeAccess=c[6]),t.$set(d),c[2]?i?(i.p(c,l),4&l&&p(i,1)):(i=ja(c),i.c(),p(i,1),i.m(e,r)):i&&(B(),m(i,1,1,()=>{i=null}),J()),c[4]?o?(o.p(c,l),16&l&&p(o,1)):(o=La(c),o.c(),p(o,1),o.m(e,null)):o&&(B(),m(o,1,1,()=>{o=null}),J())},i(c){a||(p(t.$$.fragment,c),p(i),p(o),a=!0)},o(c){m(t.$$.fragment,c),m(i),m(o),a=!1},d(c){c&&v(e),b(t),i&&i.d(),o&&o.d()}}}function Dm(s,e,t){let{tools:n=[]}=e,{servers:r=[]}=e,{isMCPEnabled:a=!0}=e,{isMCPImportEnabled:i=!0}=e,{isTerminalEnabled:o=!0}=e,{onAuthenticate:c}=e,{onRevokeAccess:l}=e,{onMCPServerAdd:d}=e,{onMCPServerSave:u}=e,{onMCPServerDelete:g}=e,{onMCPServerToggleDisable:f}=e,{onMCPServerJSONImport:$}=e,{onCancel:h}=e,{supportedShells:k=[]}=e,{selectedShell:A}=e,{startupScript:E}=e,{onShellSelect:N=()=>{}}=e,{onStartupScriptChange:T=()=>{}}=e;return s.$$set=M=>{"tools"in M&&t(0,n=M.tools),"servers"in M&&t(1,r=M.servers),"isMCPEnabled"in M&&t(2,a=M.isMCPEnabled),"isMCPImportEnabled"in M&&t(3,i=M.isMCPImportEnabled),"isTerminalEnabled"in M&&t(4,o=M.isTerminalEnabled),"onAuthenticate"in M&&t(5,c=M.onAuthenticate),"onRevokeAccess"in M&&t(6,l=M.onRevokeAccess),"onMCPServerAdd"in M&&t(7,d=M.onMCPServerAdd),"onMCPServerSave"in M&&t(8,u=M.onMCPServerSave),"onMCPServerDelete"in M&&t(9,g=M.onMCPServerDelete),"onMCPServerToggleDisable"in M&&t(10,f=M.onMCPServerToggleDisable),"onMCPServerJSONImport"in M&&t(11,$=M.onMCPServerJSONImport),"onCancel"in M&&t(12,h=M.onCancel),"supportedShells"in M&&t(13,k=M.supportedShells),"selectedShell"in M&&t(14,A=M.selectedShell),"startupScript"in M&&t(15,E=M.startupScript),"onShellSelect"in M&&t(16,N=M.onShellSelect),"onStartupScriptChange"in M&&t(17,T=M.onStartupScriptChange)},[n,r,a,i,o,c,l,d,u,g,f,$,h,k,A,E,N,T]}class Um extends ge{constructor(e){super(),$e(this,e,Dm,zm,fe,{tools:0,servers:1,isMCPEnabled:2,isMCPImportEnabled:3,isTerminalEnabled:4,onAuthenticate:5,onRevokeAccess:6,onMCPServerAdd:7,onMCPServerSave:8,onMCPServerDelete:9,onMCPServerToggleDisable:10,onMCPServerJSONImport:11,onCancel:12,supportedShells:13,selectedShell:14,startupScript:15,onShellSelect:16,onStartupScriptChange:17})}}function Vm(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Te(r,n[a]);return{c(){e=Be("svg"),t=new Bn(!0),this.h()},l(a){e=Jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Gn(e);t=Wn(i,!0),i.forEach(v),this.h()},h(){t.a=null,dt(e,r)},m(a,i){Hn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',e)},p(a,[i]){dt(e,r=wt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:W,o:W,d(a){a&&v(e)}}}function qm(s,e,t){return s.$$set=n=>{t(0,e=Te(Te({},e),Qe(n)))},[e=Qe(e)]}class Bm extends ge{constructor(e){super(),$e(this,e,qm,Vm,fe,{})}}function Jm(s){let e,t,n,r;function a(o){s[6](o)}let i={placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:s[3]};return s[0]!==void 0&&(i.value=s[0]),t=new Do({props:i}),Me.push(()=>Ae(t,"value",a)),t.$on("focus",s[7]),{c(){e=C("div"),w(t.$$.fragment),_(e,"class","c-user-guidelines-category__input svelte-10borzo")},m(o,c){y(o,e,c),x(t,e,null),r=!0},p(o,[c]){const l={};!n&&1&c&&(n=!0,l.value=o[0],Ne(()=>n=!1)),t.$set(l)},i(o){r||(p(t.$$.fragment,o),r=!0)},o(o){m(t.$$.fragment,o),r=!1},d(o){o&&v(e),b(t)}}}function Gm(s,e,t){let n;const r=Cs();let{userGuidelines:a=""}=e,{userGuidelinesLengthLimit:i}=e,{updateUserGuideline:o=()=>!1}=e;const c=De(void 0);function l(){const d=a.trim();if(n!==d){if(!o(d))throw i&&d.length>i?`The user guideline must be less than ${i} character long`:"An error occurred updating the user";fr(c,n=d,n)}}return ot(s,c,d=>t(8,n=d)),oi(()=>{fr(c,n=a.trim(),n)}),ci(()=>{l()}),s.$$set=d=>{"userGuidelines"in d&&t(0,a=d.userGuidelines),"userGuidelinesLengthLimit"in d&&t(4,i=d.userGuidelinesLengthLimit),"updateUserGuideline"in d&&t(5,o=d.updateUserGuideline)},[a,r,c,l,i,o,function(d){a=d,t(0,a)},d=>{r("focus",d)}]}class Hi extends ge{constructor(e){super(),$e(this,e,Gm,Jm,fe,{userGuidelines:0,userGuidelinesLengthLimit:4,updateUserGuideline:5})}}var ht=(s=>(s.getRulesListRequest="get-rules-list-request",s.getRulesListResponse="get-rules-list-response",s.createRule="create-rule",s.createRuleResponse="create-rule-response",s.openRule="open-rule",s.openGuidelines="open-guidelines",s.deleteRule="delete-rule",s.updateRuleFile="update-rule-file",s.updateRuleFileResponse="update-rule-file-response",s.getWorkspaceRoot="get-workspace-root",s.getWorkspaceRootResponse="get-workspace-root-response",s.autoImportRules="auto-import-rules",s.autoImportRulesOptionsResponse="auto-import-rules-options-response",s.autoImportRulesSelectionRequest="auto-import-rules-selection-request",s.autoImportRulesResponse="auto-import-rules-response",s.processSelectedPathsRequest="process-selected-paths-request",s.processSelectedPathsResponse="process-selected-paths-response",s))(ht||{});class Wm{constructor(e){ye(this,"_rulesFiles",De([]));ye(this,"_loading",De(!0));this._msgBroker=e,this.requestRules()}handleMessageFromExtension(e){return!(!e.data||e.data.type!==ve.getRulesListResponse)&&(this._rulesFiles.set(e.data.data),this._loading.set(!1),!0)}async requestRules(){this._loading.set(!0);try{const e=await this._msgBroker.sendToSidecar({type:ht.getRulesListRequest});this._rulesFiles.set(e.data)}catch(e){console.error("Failed to get rules list:",e)}finally{this._loading.set(!1)}}async createRule(e){try{const t=await this._msgBroker.sendToSidecar({type:ht.createRule,data:{ruleName:e.trim()}});return await this.requestRules(),t.data.createdRule||null}catch(t){throw console.error("Failed to create rule:",t),t}}async getWorkspaceRoot(){try{return(await this._msgBroker.sendToSidecar({type:ht.getWorkspaceRoot})).data.workspaceRoot||""}catch(e){return console.error("Failed to get workspace root:",e),""}}async updateRuleType(e,t,n){const r=Zo.updateAlwaysApplyFrontmatterKey(t,n);try{await this._msgBroker.sendToSidecar({type:ht.updateRuleFile,data:{path:e,content:r}}),await this.requestRules()}catch(a){console.error("Failed to update rule file:",a)}}async deleteRule(e){try{await this._msgBroker.sendToSidecar({type:ht.deleteRule,data:{path:e,confirmed:!0}}),await this.requestRules()}catch(t){throw console.error("Failed to delete rule:",t),t}}async processSelectedPaths(e){try{const t=await this._msgBroker.sendToSidecar({type:ht.processSelectedPathsRequest,data:{selectedPaths:e,autoImport:!0}});return await this.requestRules(),{importedRulesCount:t.data.importedRulesCount,directoryOrFile:t.data.directoryOrFile,errors:t.data.errors}}catch(t){throw console.error("Failed to process selected paths:",t),t}}async getAutoImportOptions(){return await this._msgBroker.sendToSidecar({type:ht.autoImportRules})}async processAutoImportSelection(e){try{const t=await this._msgBroker.sendToSidecar({type:ht.autoImportRulesSelectionRequest,data:{selectedLabel:e.label}});return await this.requestRules(),{importedRulesCount:t.data.importedRulesCount,duplicatesCount:t.data.duplicatesCount,totalAttempted:t.data.totalAttempted,source:t.data.source}}catch(t){throw console.error("Failed to process auto-import selection:",t),t}}getRulesFiles(){return this._rulesFiles}getLoading(){return this._loading}}class Hm{constructor(e,t,n){ye(this,"_showCreateRuleDialog",De(!1));ye(this,"_createRuleError",De(""));ye(this,"_extensionClient");this._host=e,this._msgBroker=t,this._rulesModel=n;const r=new qo;this._extensionClient=new Bo(e,t,r)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const t=await this._rulesModel.createRule(e.trim());t&&t.path&&await this.openRule(t.path),this._extensionClient.reportAgentSessionEvent({eventName:js.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:es.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const n=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(n)}}else this.hideCreateRuleDialog()}async openRule(e){try{const t=await this._rulesModel.getWorkspaceRoot();e===vr?this._extensionClient.openFile({repoRoot:t,pathName:vr}):this._extensionClient.openFile({repoRoot:t,pathName:`${mi}/${fi}/${e}`})}catch(t){console.error("Failed to open rule:",t)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(t){console.error("Failed to delete rule:",t)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:ve.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const t=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(t),this._reportSelectedImportMetrics(t)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const t=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(t),this._reportAutoImportMetrics(t),t}async updateRuleType(e,t,n){await this._rulesModel.updateRuleType(e,t,n)}_showImportNotification(e){let t;e.importedRulesCount===0?t=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(t=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(t+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:t,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const t=e.directoryOrFile==="directory"?es.selectedDirectory:(e.directoryOrFile,es.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:js.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:js.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:es.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}const Km=s=>({}),Fa=s=>({}),Ym=s=>({}),za=s=>({}),Xm=s=>({}),Da=s=>({});function Ua(s){let e,t,n,r,a,i;return n=new Go({props:{variant:"classic",size:3,$$slots:{default:[nf]},$$scope:{ctx:s}}}),{c(){e=C("div"),t=C("div"),w(n.$$.fragment),_(t,"class","c-modal svelte-1hwqfwo"),_(t,"role","dialog"),_(t,"aria-modal","true"),_(t,"aria-labelledby",s[3]),hr(t,"max-width",s[2]),_(e,"class","c-modal-backdrop svelte-1hwqfwo"),_(e,"role","presentation")},m(o,c){y(o,e,c),S(e,t),x(n,t,null),r=!0,a||(i=[Le(t,"click",gr(s[10])),Le(t,"keydown",gr(s[11])),Le(e,"click",s[4]),Le(e,"keydown",s[5])],a=!0)},p(o,c){const l={};4170&c&&(l.$$scope={dirty:c,ctx:o}),n.$set(l),(!r||8&c)&&_(t,"aria-labelledby",o[3]),(!r||4&c)&&hr(t,"max-width",o[2])},i(o){r||(p(n.$$.fragment,o),r=!0)},o(o){m(n.$$.fragment,o),r=!1},d(o){o&&v(e),b(n),a=!1,ks(i)}}}function Va(s){let e,t,n,r;const a=[ef,Qm],i=[];function o(c,l){return c[6].header?0:c[1]?1:-1}return~(t=o(s))&&(n=i[t]=a[t](s)),{c(){e=C("div"),n&&n.c(),_(e,"class","c-modal-header svelte-1hwqfwo")},m(c,l){y(c,e,l),~t&&i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?~t&&i[t].p(c,l):(n&&(B(),m(i[d],1,1,()=>{i[d]=null}),J()),~t?(n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null)):n=null)},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&v(e),~t&&i[t].d()}}}function Qm(s){let e,t;return e=new oe({props:{id:s[3],size:3,weight:"bold",color:"primary",$$slots:{default:[tf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.id=n[3]),4098&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ef(s){let e;const t=s[9].header,n=Ie(t,s,s[12],Da);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4096&a)&&Re(n,t,r,r[12],e?Pe(t,r[12],a,Xm):Oe(r[12]),Da)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function tf(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&he(e,t[1])},d(t){t&&v(e)}}}function qa(s){let e,t;const n=s[9].body,r=Ie(n,s,s[12],za),a=r||function(i){let o;const c=i[9].default,l=Ie(c,i,i[12],null);return{c(){l&&l.c()},m(d,u){l&&l.m(d,u),o=!0},p(d,u){l&&l.p&&(!o||4096&u)&&Re(l,c,d,d[12],o?Pe(c,d[12],u,null):Oe(d[12]),null)},i(d){o||(p(l,d),o=!0)},o(d){m(l,d),o=!1},d(d){l&&l.d(d)}}}(s);return{c(){e=C("div"),a&&a.c(),_(e,"class","c-modal-body svelte-1hwqfwo")},m(i,o){y(i,e,o),a&&a.m(e,null),t=!0},p(i,o){r?r.p&&(!t||4096&o)&&Re(r,n,i,i[12],t?Pe(n,i[12],o,Ym):Oe(i[12]),za):a&&a.p&&(!t||4096&o)&&a.p(i,t?o:-1)},i(i){t||(p(a,i),t=!0)},o(i){m(a,i),t=!1},d(i){i&&v(e),a&&a.d(i)}}}function Ba(s){let e,t;const n=s[9].footer,r=Ie(n,s,s[12],Fa);return{c(){e=C("div"),r&&r.c(),_(e,"class","c-modal-footer svelte-1hwqfwo")},m(a,i){y(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||4096&i)&&Re(r,n,a,a[12],t?Pe(n,a[12],i,Km):Oe(a[12]),Fa)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&v(e),r&&r.d(a)}}}function nf(s){let e,t,n,r,a=(s[1]||s[6].header)&&Va(s),i=(s[6].body||s[6].default)&&qa(s),o=s[6].footer&&Ba(s);return{c(){e=C("div"),a&&a.c(),t=Z(),i&&i.c(),n=Z(),o&&o.c(),_(e,"class","c-modal-content svelte-1hwqfwo")},m(c,l){y(c,e,l),a&&a.m(e,null),S(e,t),i&&i.m(e,null),S(e,n),o&&o.m(e,null),r=!0},p(c,l){c[1]||c[6].header?a?(a.p(c,l),66&l&&p(a,1)):(a=Va(c),a.c(),p(a,1),a.m(e,t)):a&&(B(),m(a,1,1,()=>{a=null}),J()),c[6].body||c[6].default?i?(i.p(c,l),64&l&&p(i,1)):(i=qa(c),i.c(),p(i,1),i.m(e,n)):i&&(B(),m(i,1,1,()=>{i=null}),J()),c[6].footer?o?(o.p(c,l),64&l&&p(o,1)):(o=Ba(c),o.c(),p(o,1),o.m(e,null)):o&&(B(),m(o,1,1,()=>{o=null}),J())},i(c){r||(p(a),p(i),p(o),r=!0)},o(c){m(a),m(i),m(o),r=!1},d(c){c&&v(e),a&&a.d(),i&&i.d(),o&&o.d()}}}function sf(s){let e,t,n=s[0]&&Ua(s);return{c(){n&&n.c(),e=ke()},m(r,a){n&&n.m(r,a),y(r,e,a),t=!0},p(r,[a]){r[0]?n?(n.p(r,a),1&a&&p(n,1)):(n=Ua(r),n.c(),p(n,1),n.m(e.parentNode,e)):n&&(B(),m(n,1,1,()=>{n=null}),J())},i(r){t||(p(n),t=!0)},o(r){m(n),t=!1},d(r){r&&v(e),n&&n.d(r)}}}function rf(s,e,t){let{$$slots:n={},$$scope:r}=e;const a=er(n),i=Cs();let{show:o=!1}=e,{title:c=""}=e,{maxWidth:l="400px"}=e,{preventBackdropClose:d=!1}=e,{preventEscapeClose:u=!1}=e,{ariaLabelledBy:g="modal-title"}=e;return s.$$set=f=>{"show"in f&&t(0,o=f.show),"title"in f&&t(1,c=f.title),"maxWidth"in f&&t(2,l=f.maxWidth),"preventBackdropClose"in f&&t(7,d=f.preventBackdropClose),"preventEscapeClose"in f&&t(8,u=f.preventEscapeClose),"ariaLabelledBy"in f&&t(3,g=f.ariaLabelledBy),"$$scope"in f&&t(12,r=f.$$scope)},[o,c,l,g,function(){d||i("cancel"),i("backdropClick")},function(f){f.key!=="Escape"||u||(f.preventDefault(),i("cancel")),i("keydown",f)},a,d,u,n,function(f){is.call(this,s,f)},function(f){is.call(this,s,f)},r]}class Ki extends ge{constructor(e){super(),$e(this,e,rf,sf,fe,{show:0,title:1,maxWidth:2,preventBackdropClose:7,preventEscapeClose:8,ariaLabelledBy:3})}}function af(s){let e;return{c(){e=L("Enter a name for the new rule file (e.g., architecture.md):")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ja(s){let e,t;return e=new As({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[cf],default:[of]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};4098&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function of(s){let e;return{c(){e=L(s[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&he(e,t[1])},d(t){t&&v(e)}}}function cf(s){let e,t;return e=new ui({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function lf(s){let e,t,n,r,a,i,o,c;function l(f){s[9](f)}function d(f){s[10](f)}e=new oe({props:{size:2,color:"secondary",$$slots:{default:[af]},$$scope:{ctx:s}}});let u={placeholder:"rule-name.md",disabled:s[4]};s[3]!==void 0&&(u.value=s[3]),s[2]!==void 0&&(u.textInput=s[2]),n=new Dt({props:u}),Me.push(()=>Ae(n,"value",l)),Me.push(()=>Ae(n,"textInput",d)),n.$on("keydown",s[8]);let g=s[1]&&Ja(s);return{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment),i=Z(),g&&g.c(),o=ke()},m(f,$){x(e,f,$),y(f,t,$),x(n,f,$),y(f,i,$),g&&g.m(f,$),y(f,o,$),c=!0},p(f,$){const h={};4096&$&&(h.$$scope={dirty:$,ctx:f}),e.$set(h);const k={};16&$&&(k.disabled=f[4]),!r&&8&$&&(r=!0,k.value=f[3],Ne(()=>r=!1)),!a&&4&$&&(a=!0,k.textInput=f[2],Ne(()=>a=!1)),n.$set(k),f[1]?g?(g.p(f,$),2&$&&p(g,1)):(g=Ja(f),g.c(),p(g,1),g.m(o.parentNode,o)):g&&(B(),m(g,1,1,()=>{g=null}),J())},i(f){c||(p(e.$$.fragment,f),p(n.$$.fragment,f),p(g),c=!0)},o(f){m(e.$$.fragment,f),m(n.$$.fragment,f),m(g),c=!1},d(f){f&&(v(t),v(i),v(o)),b(e,f),b(n,f),g&&g.d(f)}}}function df(s){let e;return{c(){e=L("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function uf(s){let e,t=s[4]?"Creating...":"Create";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){16&r&&t!==(t=n[4]?"Creating...":"Create")&&he(e,t)},d(n){n&&v(e)}}}function pf(s){let e,t,n,r,a;return t=new Ze({props:{variant:"solid",color:"neutral",disabled:s[4],$$slots:{default:[df]},$$scope:{ctx:s}}}),t.$on("click",s[6]),r=new Ze({props:{variant:"solid",color:"accent",disabled:!s[3].trim()||s[4],loading:s[4],$$slots:{default:[uf]},$$scope:{ctx:s}}}),r.$on("click",s[5]),{c(){e=C("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"slot","footer")},m(i,o){y(i,e,o),x(t,e,null),S(e,n),x(r,e,null),a=!0},p(i,o){const c={};16&o&&(c.disabled=i[4]),4096&o&&(c.$$scope={dirty:o,ctx:i}),t.$set(c);const l={};24&o&&(l.disabled=!i[3].trim()||i[4]),16&o&&(l.loading=i[4]),4112&o&&(l.$$scope={dirty:o,ctx:i}),r.$set(l)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&v(e),b(t),b(r)}}}function mf(s){let e,t;return e=new Ki({props:{show:s[0],title:"Create New Rule",ariaLabelledBy:"dialog-title",preventBackdropClose:s[4],preventEscapeClose:s[4],$$slots:{footer:[pf],body:[lf]},$$scope:{ctx:s}}}),e.$on("cancel",s[6]),e.$on("keydown",s[7]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.show=n[0]),16&r&&(a.preventBackdropClose=n[4]),16&r&&(a.preventEscapeClose=n[4]),4126&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ff(s,e,t){const n=Cs();let r,{show:a=!1}=e,{errorMessage:i=""}=e,o="",c=!1;function l(){o.trim()&&!c&&(t(4,c=!0),n("create",o.trim()))}function d(){c||(n("cancel"),t(3,o=""))}return s.$$set=u=>{"show"in u&&t(0,a=u.show),"errorMessage"in u&&t(1,i=u.errorMessage)},s.$$.update=()=>{5&s.$$.dirty&&a&&r&&setTimeout(()=>r==null?void 0:r.focus(),100),3&s.$$.dirty&&(a&&!i||t(4,c=!1)),3&s.$$.dirty&&(a||i||t(3,o=""))},[a,i,r,o,c,l,d,function(u){c||u.detail.key==="Enter"&&(u.detail.preventDefault(),l())},function(u){c||(u.key==="Enter"?(u.preventDefault(),l()):u.key==="Escape"&&(u.preventDefault(),d()))},function(u){o=u,t(3,o),t(0,a),t(1,i)},function(u){r=u,t(2,r)}]}class hf extends ge{constructor(e){super(),$e(this,e,ff,mf,fe,{show:0,errorMessage:1})}}function Ga(s,e,t){const n=s.slice();return n[18]=e[t],n}function gf(s){let e,t,n,r,a,i,o,c,l;function d(h){s[15](h)}function u(h){s[16](h)}e=new oe({props:{size:2,color:"secondary",$$slots:{default:[vf]},$$scope:{ctx:s}}});let g={triggerOn:s[1].length===0?[]:void 0,$$slots:{default:[bf]},$$scope:{ctx:s}};s[7]!==void 0&&(g.requestClose=s[7]),s[6]!==void 0&&(g.focusedIndex=s[6]),n=new Ce.Root({props:g}),Me.push(()=>Ae(n,"requestClose",d)),Me.push(()=>Ae(n,"focusedIndex",u));let f=s[3]&&Ka(s),$=s[4]&&Ya(s);return{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment),i=Z(),f&&f.c(),o=Z(),$&&$.c(),c=ke()},m(h,k){x(e,h,k),y(h,t,k),x(n,h,k),y(h,i,k),f&&f.m(h,k),y(h,o,k),$&&$.m(h,k),y(h,c,k),l=!0},p(h,k){const A={};2097152&k&&(A.$$scope={dirty:k,ctx:h}),e.$set(A);const E={};2&k&&(E.triggerOn=h[1].length===0?[]:void 0),2097442&k&&(E.$$scope={dirty:k,ctx:h}),!r&&128&k&&(r=!0,E.requestClose=h[7],Ne(()=>r=!1)),!a&&64&k&&(a=!0,E.focusedIndex=h[6],Ne(()=>a=!1)),n.$set(E),h[3]?f?(f.p(h,k),8&k&&p(f,1)):(f=Ka(h),f.c(),p(f,1),f.m(o.parentNode,o)):f&&(B(),m(f,1,1,()=>{f=null}),J()),h[4]?$?($.p(h,k),16&k&&p($,1)):($=Ya(h),$.c(),p($,1),$.m(c.parentNode,c)):$&&(B(),m($,1,1,()=>{$=null}),J())},i(h){l||(p(e.$$.fragment,h),p(n.$$.fragment,h),p(f),p($),l=!0)},o(h){m(e.$$.fragment,h),m(n.$$.fragment,h),m(f),m($),l=!1},d(h){h&&(v(t),v(i),v(o),v(c)),b(e,h),b(n,h),f&&f.d(h),$&&$.d(h)}}}function $f(s){let e;return{c(){e=C("input"),_(e,"type","text"),e.value="No existing rules found",e.readOnly=!0,_(e,"class","c-dropdown-input svelte-z1s6x7")},m(t,n){y(t,e,n)},p:W,i:W,o:W,d(t){t&&v(e)}}}function vf(s){let e;return{c(){e=L("Select existing rules to auto import to .augment/rules")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function yf(s){let e,t,n,r,a,i;return a=new gi({props:{class:"c-dropdown-chevron"}}),{c(){e=C("div"),t=C("input"),r=Z(),w(a.$$.fragment),_(t,"type","text"),t.value=n=s[5]?s[5].label:"Existing rules",t.readOnly=!0,_(t,"class","c-dropdown-input svelte-z1s6x7"),_(e,"class","c-dropdown-trigger svelte-z1s6x7")},m(o,c){y(o,e,c),S(e,t),S(e,r),x(a,e,null),i=!0},p(o,c){(!i||32&c&&n!==(n=o[5]?o[5].label:"Existing rules")&&t.value!==n)&&(t.value=n)},i(o){i||(p(a.$$.fragment,o),i=!0)},o(o){m(a.$$.fragment,o),i=!1},d(o){o&&v(e),b(a)}}}function _f(s){let e,t=s[18].label+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){2&r&&t!==(t=n[18].label+"")&&he(e,t)},d(n){n&&v(e)}}}function Wa(s){var r;let e,t;function n(){return s[14](s[18])}return e=new Ce.Item({props:{onSelect:n,highlight:((r=s[5])==null?void 0:r.label)===s[18].label,$$slots:{default:[_f]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(a,i){x(e,a,i),t=!0},p(a,i){var c;s=a;const o={};2&i&&(o.onSelect=n),34&i&&(o.highlight=((c=s[5])==null?void 0:c.label)===s[18].label),2097154&i&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){b(e,a)}}}function Ha(s){let e,t,n,r;return e=new Ce.Separator({}),n=new Ce.Label({props:{$$slots:{default:[wf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};2097442&i&&(o.$$scope={dirty:i,ctx:a}),n.$set(o)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function wf(s){var n;let e,t=(s[8]!==void 0?s[1][s[8]].description:(n=s[5])==null?void 0:n.description)+"";return{c(){e=L(t)},m(r,a){y(r,e,a)},p(r,a){var i;290&a&&t!==(t=(r[8]!==void 0?r[1][r[8]].description:(i=r[5])==null?void 0:i.description)+"")&&he(e,t)},d(r){r&&v(e)}}}function xf(s){let e,t,n,r=_e(s[1]),a=[];for(let c=0;c<r.length;c+=1)a[c]=Wa(Ga(s,r,c));const i=c=>m(a[c],1,1,()=>{a[c]=null});let o=(s[8]!==void 0||s[5])&&Ha(s);return{c(){for(let c=0;c<a.length;c+=1)a[c].c();e=Z(),o&&o.c(),t=ke()},m(c,l){for(let d=0;d<a.length;d+=1)a[d]&&a[d].m(c,l);y(c,e,l),o&&o.m(c,l),y(c,t,l),n=!0},p(c,l){if(546&l){let d;for(r=_e(c[1]),d=0;d<r.length;d+=1){const u=Ga(c,r,d);a[d]?(a[d].p(u,l),p(a[d],1)):(a[d]=Wa(u),a[d].c(),p(a[d],1),a[d].m(e.parentNode,e))}for(B(),d=r.length;d<a.length;d+=1)i(d);J()}c[8]!==void 0||c[5]?o?(o.p(c,l),288&l&&p(o,1)):(o=Ha(c),o.c(),p(o,1),o.m(t.parentNode,t)):o&&(B(),m(o,1,1,()=>{o=null}),J())},i(c){if(!n){for(let l=0;l<r.length;l+=1)p(a[l]);p(o),n=!0}},o(c){a=a.filter(Boolean);for(let l=0;l<a.length;l+=1)m(a[l]);m(o),n=!1},d(c){c&&(v(e),v(t)),Gt(a,c),o&&o.d(c)}}}function bf(s){let e,t,n,r;return e=new Ce.Trigger({props:{$$slots:{default:[yf]},$$scope:{ctx:s}}}),n=new Ce.Content({props:{align:"start",side:"bottom",$$slots:{default:[xf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};2097184&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};2097442&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Ka(s){let e,t;return e=new As({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Sf],default:[kf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2097160&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function kf(s){let e;return{c(){e=L(s[3])},m(t,n){y(t,e,n)},p(t,n){8&n&&he(e,t[3])},d(t){t&&v(e)}}}function Sf(s){let e,t;return e=new ui({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Ya(s){let e,t;return e=new As({props:{variant:"soft",color:"success",size:1,$$slots:{icon:[Tf],default:[Cf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2097168&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Cf(s){let e;return{c(){e=L(s[4])},m(t,n){y(t,e,n)},p(t,n){16&n&&he(e,t[4])},d(t){t&&v(e)}}}function Tf(s){let e,t;return e=new Co({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Mf(s){let e,t,n,r;const a=[$f,gf],i=[];function o(c,l){return c[1].length===0?0:1}return t=o(s),n=i[t]=a[t](s),{c(){e=C("div"),n.c(),_(e,"slot","body"),_(e,"class","c-auto-import-rules-dialog svelte-z1s6x7")},m(c,l){y(c,e,l),i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?i[t].p(c,l):(B(),m(i[d],1,1,()=>{i[d]=null}),J(),n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null))},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&v(e),i[t].d()}}}function Af(s){let e;return{c(){e=L("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Xa(s){let e,t;return e=new Ze({props:{color:"accent",variant:"solid",disabled:!s[5]||s[2],loading:s[2],$$slots:{default:[Nf]},$$scope:{ctx:s}}}),e.$on("click",s[10]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};36&r&&(a.disabled=!n[5]||n[2]),4&r&&(a.loading=n[2]),2097156&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Nf(s){let e,t=s[2]?"Importing...":"Import ";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){4&r&&t!==(t=n[2]?"Importing...":"Import ")&&he(e,t)},d(n){n&&v(e)}}}function Zf(s){let e,t,n,r;t=new Ze({props:{variant:"solid",color:"neutral",disabled:s[2],$$slots:{default:[Af]},$$scope:{ctx:s}}}),t.$on("click",s[11]);let a=s[1].length>0&&Xa(s);return{c(){e=C("div"),w(t.$$.fragment),n=Z(),a&&a.c(),_(e,"slot","footer")},m(i,o){y(i,e,o),x(t,e,null),S(e,n),a&&a.m(e,null),r=!0},p(i,o){const c={};4&o&&(c.disabled=i[2]),2097152&o&&(c.$$scope={dirty:o,ctx:i}),t.$set(c),i[1].length>0?a?(a.p(i,o),2&o&&p(a,1)):(a=Xa(i),a.c(),p(a,1),a.m(e,null)):a&&(B(),m(a,1,1,()=>{a=null}),J())},i(i){r||(p(t.$$.fragment,i),p(a),r=!0)},o(i){m(t.$$.fragment,i),m(a),r=!1},d(i){i&&v(e),b(t),a&&a.d()}}}function Ef(s){let e,t,n,r;return e=new Ki({props:{show:s[0],title:"Auto Import Rules",ariaLabelledBy:"dialog-title",preventBackdropClose:s[2],preventEscapeClose:s[2],$$slots:{footer:[Zf],body:[Mf]},$$scope:{ctx:s}}}),e.$on("cancel",s[11]),{c(){w(e.$$.fragment)},m(a,i){x(e,a,i),t=!0,n||(r=Le(window,"keydown",s[12]),n=!0)},p(a,[i]){const o={};1&i&&(o.show=a[0]),4&i&&(o.preventBackdropClose=a[2]),4&i&&(o.preventEscapeClose=a[2]),2097662&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){b(e,a),n=!1,r()}}}function If(s,e,t){let n,r,a=W,i=()=>(a(),a=Ss(c,N=>t(8,r=N)),c);s.$$.on_destroy.push(()=>a());const o=Cs();let c,{show:l=!1}=e,{options:d=[]}=e,{isLoading:u=!1}=e,{errorMessage:g=""}=e,{successMessage:f=""}=e,$=n;i();let h=()=>{};function k(N){t(5,$=N),h()}function A(){$&&!u&&o("select",$)}function E(){u||(o("cancel"),t(5,$=n))}return s.$$set=N=>{"show"in N&&t(0,l=N.show),"options"in N&&t(1,d=N.options),"isLoading"in N&&t(2,u=N.isLoading),"errorMessage"in N&&t(3,g=N.errorMessage),"successMessage"in N&&t(4,f=N.successMessage)},s.$$.update=()=>{2&s.$$.dirty&&t(13,n=d.length>0?d[0]:null),8193&s.$$.dirty&&l&&t(5,$=n)},[l,d,u,g,f,$,c,h,r,k,A,E,function(N){l&&!u&&(N.key==="Escape"?(N.preventDefault(),E()):N.key==="Enter"&&$&&(N.preventDefault(),A()))},n,N=>k(N),function(N){h=N,t(7,h)},function(N){c=N,i(t(6,c))}]}class Rf extends ge{constructor(e){super(),$e(this,e,If,Ef,fe,{show:0,options:1,isLoading:2,errorMessage:3,successMessage:4})}}function Qa(s,e,t){const n=s.slice();return n[35]=e[t],n}function ei(s,e,t){const n=s.slice();return n[38]=e[t],n}function Of(s){let e;return{c(){e=L("Rules")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Pf(s){let e;return{c(){e=L("Learn more")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function jf(s){let e,t,n=[],r=new Map,a=_e(s[10]);const i=o=>o[38].path;for(let o=0;o<a.length;o+=1){let c=ei(s,a,o),l=i(c);r.set(l,n[o]=ti(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=ke()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);y(o,e,c),t=!0},p(o,c){33792&c[0]&&(a=_e(o[10]),B(),n=Rt(n,c,i,1,o,a,r,e.parentNode,Ot,ti,e,ei),J())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&v(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Lf(s){let e,t,n;return t=new oe({props:{size:1,color:"neutral",$$slots:{default:[qf]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),_(e,"class","c-rules-list-empty svelte-16sxavx")},m(r,a){y(r,e,a),x(t,e,null),n=!0},p(r,a){const i={};1024&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&v(e),b(t)}}}function Ff(s){let e,t=s[38].path+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){1024&r[0]&&t!==(t=n[38].path+"")&&he(e,t)},d(n){n&&v(e)}}}function zf(s){let e,t,n,r,a,i,o;return t=new ao({}),a=new oe({props:{size:1,color:"neutral",$$slots:{default:[Ff]},$$scope:{ctx:s}}}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),r=C("div"),w(a.$$.fragment),i=Z(),_(r,"class","c-rule-item-path svelte-16sxavx"),_(e,"class","c-rule-item-info svelte-16sxavx"),_(e,"slot","header-left")},m(c,l){y(c,e,l),x(t,e,null),S(e,n),S(e,r),x(a,r,null),S(e,i),o=!0},p(c,l){const d={};1024&l[0]|1024&l[1]&&(d.$$scope={dirty:l,ctx:c}),a.$set(d)},i(c){o||(p(t.$$.fragment,c),p(a.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(a.$$.fragment,c),o=!1},d(c){c&&v(e),b(t),b(a)}}}function Df(s){let e,t;return e=new Uo({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Uf(s){let e,t;return e=new To({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Vf(s){let e,t,n,r,a,i,o,c,l,d;function u(...g){return s[25](s[38],...g)}return r=new Jo({props:{path:s[38].path,onSave:u,alwaysApply:s[38].type===yr.ALWAYS_ATTACHED}}),i=new Ze({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Df]},$$scope:{ctx:s}}}),i.$on("click",function(...g){return s[26](s[38],...g)}),c=new Ze({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Uf]},$$scope:{ctx:s}}}),c.$on("click",function(...g){return s[27](s[38],...g)}),{c(){e=C("div"),t=C("div"),n=C("div"),w(r.$$.fragment),a=Z(),w(i.$$.fragment),o=Z(),w(c.$$.fragment),l=Z(),_(n,"class","c-rules-dropdown svelte-16sxavx"),_(t,"class","status-controls svelte-16sxavx"),_(e,"class","server-actions"),_(e,"slot","header-right")},m(g,f){y(g,e,f),S(e,t),S(t,n),x(r,n,null),S(t,a),x(i,t,null),S(t,o),x(c,t,null),S(e,l),d=!0},p(g,f){s=g;const $={};1024&f[0]&&($.path=s[38].path),1024&f[0]&&($.onSave=u),1024&f[0]&&($.alwaysApply=s[38].type===yr.ALWAYS_ATTACHED),r.$set($);const h={};1024&f[1]&&(h.$$scope={dirty:f,ctx:s}),i.$set(h);const k={};1024&f[1]&&(k.$$scope={dirty:f,ctx:s}),c.$set(k)},i(g){d||(p(r.$$.fragment,g),p(i.$$.fragment,g),p(c.$$.fragment,g),d=!0)},o(g){m(r.$$.fragment,g),m(i.$$.fragment,g),m(c.$$.fragment,g),d=!1},d(g){g&&v(e),b(r),b(i),b(c)}}}function ti(s,e){let t,n,r;return n=new Oi({props:{isClickable:!0,$$slots:{"header-right":[Vf],"header-left":[zf]},$$scope:{ctx:e}}}),n.$on("click",function(){return e[28](e[38])}),{key:s,first:null,c(){t=ke(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};1024&i[0]|1024&i[1]&&(o.$$scope={dirty:i,ctx:e}),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function qf(s){let e;return{c(){e=L("No rules files found")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Bf(s){let e,t,n,r,a;return t=new Ts({}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),r=C("span"),r.textContent="Create new rule file",_(e,"class","c-rules-actions-button-content svelte-16sxavx")},m(i,o){y(i,e,o),x(t,e,null),S(e,n),S(e,r),a=!0},p:W,i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&v(e),b(t)}}}function Jf(s){let e,t,n,r,a,i,o;return t=new Wi({}),i=new gi({}),{c(){e=C("div"),w(t.$$.fragment),n=Z(),r=C("span"),r.textContent="Import rules",a=Z(),w(i.$$.fragment),_(e,"class","c-rules-actions-button-content svelte-16sxavx")},m(c,l){y(c,e,l),x(t,e,null),S(e,n),S(e,r),S(e,a),x(i,e,null),o=!0},p:W,i(c){o||(p(t.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&v(e),b(t),b(i)}}}function Gf(s){let e,t;return e=new Ze({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[Jf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};1024&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function Wf(s){let e,t=s[35].label+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p:W,d(n){n&&v(e)}}}function ni(s,e){let t,n,r;return n=new Ce.Item({props:{onSelect:function(){return e[30](e[35])},$$slots:{default:[Wf]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=ke(),w(n.$$.fragment),this.first=t},m(a,i){y(a,t,i),x(n,a,i),r=!0},p(a,i){e=a;const o={};1024&i[1]&&(o.$$scope={dirty:i,ctx:e}),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(n,a)}}}function si(s){let e,t,n,r;return e=new Ce.Separator({}),n=new Ce.Label({props:{$$slots:{default:[Hf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};2048&i[0]|1024&i[1]&&(o.$$scope={dirty:i,ctx:a}),n.$set(o)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Hf(s){let e,t=(s[11]!==void 0?s[19][s[11]].description:s[19][0])+"";return{c(){e=L(t)},m(n,r){y(n,e,r)},p(n,r){2048&r[0]&&t!==(t=(n[11]!==void 0?n[19][n[11]].description:n[19][0])+"")&&he(e,t)},d(n){n&&v(e)}}}function Kf(s){let e,t,n,r=[],a=new Map,i=_e(s[19]);const o=l=>l[35].id;for(let l=0;l<i.length;l+=1){let d=Qa(s,i,l),u=o(d);a.set(u,r[l]=ni(u,d))}let c=s[11]!==void 0&&si(s);return{c(){for(let l=0;l<r.length;l+=1)r[l].c();e=Z(),c&&c.c(),t=ke()},m(l,d){for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(l,d);y(l,e,d),c&&c.m(l,d),y(l,t,d),n=!0},p(l,d){1572864&d[0]&&(i=_e(l[19]),B(),r=Rt(r,d,o,1,l,i,a,e.parentNode,Ot,ni,e,Qa),J()),l[11]!==void 0?c?(c.p(l,d),2048&d[0]&&p(c,1)):(c=si(l),c.c(),p(c,1),c.m(t.parentNode,t)):c&&(B(),m(c,1,1,()=>{c=null}),J())},i(l){if(!n){for(let d=0;d<i.length;d+=1)p(r[d]);p(c),n=!0}},o(l){for(let d=0;d<r.length;d+=1)m(r[d]);m(c),n=!1},d(l){l&&(v(e),v(t));for(let d=0;d<r.length;d+=1)r[d].d(l);c&&c.d(l)}}}function Yf(s){let e,t,n,r;return e=new Ce.Trigger({props:{$$slots:{default:[Gf]},$$scope:{ctx:s}}}),n=new Ce.Content({props:{align:"start",side:"bottom",$$slots:{default:[Kf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){x(e,a,i),y(a,t,i),x(n,a,i),r=!0},p(a,i){const o={};1024&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};2048&i[0]|1024&i[1]&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&v(t),b(e,a),b(n,a)}}}function Xf(s){let e;return{c(){e=L("User Guidelines")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Qf(s){let e;return{c(){e=L("Learn more")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function eh(s){let e,t,n,r,a,i,o,c,l,d,u,g,f,$,h,k,A,E,N,T,M,I,U,Q,G,P,j,D,re,X,de,Wt,Ge,Pt,Ht,jt;n=new oe({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[Of]},$$scope:{ctx:s}}}),c=new oe({props:{size:1,weight:"regular",$$slots:{default:[Pf]},$$scope:{ctx:s}}});const ar=[Lf,jf],xt=[];function ir(Y,ue){return Y[10].length===0?0:1}function Yi(Y){s[31](Y)}function Xi(Y){s[32](Y)}u=ir(s),g=xt[u]=ar[u](s),h=new Ze({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[Bf]},$$scope:{ctx:s}}}),h.$on("click",s[29]);let Is={$$slots:{default:[Yf]},$$scope:{ctx:s}};return s[9]!==void 0&&(Is.requestClose=s[9]),s[8]!==void 0&&(Is.focusedIndex=s[8]),A=new Ce.Root({props:Is}),Me.push(()=>Ae(A,"requestClose",Yi)),Me.push(()=>Ae(A,"focusedIndex",Xi)),I=new oe({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[Xf]},$$scope:{ctx:s}}}),j=new oe({props:{size:1,weight:"regular",$$slots:{default:[Qf]},$$scope:{ctx:s}}}),re=new Hi({props:{userGuidelines:s[0],userGuidelinesLengthLimit:s[1],updateUserGuideline:s[2]}}),de=new hf({props:{show:s[12],errorMessage:s[13]}}),de.$on("create",s[23]),de.$on("cancel",s[24]),Ge=new Rf({props:{show:s[3],options:s[4],isLoading:s[5],errorMessage:s[6],successMessage:s[7]}}),Ge.$on("select",s[21]),Ge.$on("cancel",s[22]),{c(){e=C("div"),t=C("div"),w(n.$$.fragment),r=Z(),a=C("div"),i=L(`Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) `),o=C("a"),w(c.$$.fragment),l=Z(),d=C("div"),g.c(),f=Z(),$=C("div"),w(h.$$.fragment),k=Z(),w(A.$$.fragment),T=Z(),M=C("div"),w(I.$$.fragment),U=Z(),Q=C("div"),G=L(`User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. `),P=C("a"),w(j.$$.fragment),D=Z(),w(re.$$.fragment),X=Z(),w(de.$$.fragment),Wt=Z(),w(Ge.$$.fragment),_(o,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),_(o,"target","_blank"),_(d,"class","c-rules-list svelte-16sxavx"),_($,"class","c-rules-actions-container svelte-16sxavx"),_(t,"class","c-rules-section svelte-16sxavx"),_(P,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),_(P,"target","_blank"),_(M,"class","c-user-guidelines-section svelte-16sxavx"),_(e,"class","c-rules-category svelte-16sxavx")},m(Y,ue){y(Y,e,ue),S(e,t),x(n,t,null),S(t,r),S(t,a),S(a,i),S(a,o),x(c,o,null),S(t,l),S(t,d),xt[u].m(d,null),S(t,f),S(t,$),x(h,$,null),S($,k),x(A,$,null),S(e,T),S(e,M),x(I,M,null),S(M,U),S(M,Q),S(Q,G),S(Q,P),x(j,P,null),S(M,D),x(re,M,null),y(Y,X,ue),x(de,Y,ue),y(Y,Wt,ue),x(Ge,Y,ue),Pt=!0,Ht||(jt=Le(window,"message",s[14].onMessageFromExtension),Ht=!0)},p(Y,ue){const or={};1024&ue[1]&&(or.$$scope={dirty:ue,ctx:Y}),n.$set(or);const cr={};1024&ue[1]&&(cr.$$scope={dirty:ue,ctx:Y}),c.$set(cr);let Rs=u;u=ir(Y),u===Rs?xt[u].p(Y,ue):(B(),m(xt[Rs],1,1,()=>{xt[Rs]=null}),J(),g=xt[u],g?g.p(Y,ue):(g=xt[u]=ar[u](Y),g.c()),p(g,1),g.m(d,null));const lr={};1024&ue[1]&&(lr.$$scope={dirty:ue,ctx:Y}),h.$set(lr);const Xn={};2048&ue[0]|1024&ue[1]&&(Xn.$$scope={dirty:ue,ctx:Y}),!E&&512&ue[0]&&(E=!0,Xn.requestClose=Y[9],Ne(()=>E=!1)),!N&&256&ue[0]&&(N=!0,Xn.focusedIndex=Y[8],Ne(()=>N=!1)),A.$set(Xn);const dr={};1024&ue[1]&&(dr.$$scope={dirty:ue,ctx:Y}),I.$set(dr);const ur={};1024&ue[1]&&(ur.$$scope={dirty:ue,ctx:Y}),j.$set(ur);const Qn={};1&ue[0]&&(Qn.userGuidelines=Y[0]),2&ue[0]&&(Qn.userGuidelinesLengthLimit=Y[1]),4&ue[0]&&(Qn.updateUserGuideline=Y[2]),re.$set(Qn);const Os={};4096&ue[0]&&(Os.show=Y[12]),8192&ue[0]&&(Os.errorMessage=Y[13]),de.$set(Os);const Kt={};8&ue[0]&&(Kt.show=Y[3]),16&ue[0]&&(Kt.options=Y[4]),32&ue[0]&&(Kt.isLoading=Y[5]),64&ue[0]&&(Kt.errorMessage=Y[6]),128&ue[0]&&(Kt.successMessage=Y[7]),Ge.$set(Kt)},i(Y){Pt||(p(n.$$.fragment,Y),p(c.$$.fragment,Y),p(g),p(h.$$.fragment,Y),p(A.$$.fragment,Y),p(I.$$.fragment,Y),p(j.$$.fragment,Y),p(re.$$.fragment,Y),p(de.$$.fragment,Y),p(Ge.$$.fragment,Y),Pt=!0)},o(Y){m(n.$$.fragment,Y),m(c.$$.fragment,Y),m(g),m(h.$$.fragment,Y),m(A.$$.fragment,Y),m(I.$$.fragment,Y),m(j.$$.fragment,Y),m(re.$$.fragment,Y),m(de.$$.fragment,Y),m(Ge.$$.fragment,Y),Pt=!1},d(Y){Y&&(v(e),v(X),v(Wt)),b(n),b(c),xt[u].d(),b(h),b(A),b(I),b(j),b(re),b(de,Y),b(Ge,Y),Ht=!1,jt()}}}function th(s,e,t){let n,r,a,i,o=W,c=()=>(o(),o=Ss(U,P=>t(11,r=P)),U);s.$$.on_destroy.push(()=>o());let{userGuidelines:l=""}=e,{userGuidelinesLengthLimit:d}=e,{updateUserGuideline:u=()=>!1}=e;const g=new di(je),f=new Wm(g),$=new Hm(je,g,f);g.registerConsumer(f);const h=f.getRulesFiles();ot(s,h,P=>t(10,n=P));const k=$.getShowCreateRuleDialog();ot(s,k,P=>t(12,a=P));const A=$.getCreateRuleError();ot(s,A,P=>t(13,i=P));let E=!1,N=[],T=!1,M="",I="",U;c();let Q=()=>{};async function G(P){try{P.id==="select_file_or_directory"?await $.selectFileToImport():P.id==="auto_import"&&await async function(){try{t(6,M=""),t(7,I="");const j=await $.getAutoImportOptions();t(4,N=j.data.options),t(3,E=!0)}catch(j){console.error("Failed to get auto-import options:",j),t(6,M="Failed to detect existing rules in workspace.")}}()}catch(j){console.error("Failed to handle import select:",j)}Q&&Q()}return oi(()=>{f.requestRules()}),s.$$set=P=>{"userGuidelines"in P&&t(0,l=P.userGuidelines),"userGuidelinesLengthLimit"in P&&t(1,d=P.userGuidelinesLengthLimit),"updateUserGuideline"in P&&t(2,u=P.updateUserGuideline)},[l,d,u,E,N,T,M,I,U,Q,n,r,a,i,g,$,h,k,A,[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}],G,async function(P){const j=P.detail;try{t(5,T=!0),t(6,M="");const D=await $.processAutoImportSelection(j);let re=`Successfully imported ${D.importedRulesCount} rule${D.importedRulesCount!==1?"s":""} from ${j.label}`;D.duplicatesCount>0&&(re+=`, ${D.duplicatesCount} duplicate${D.duplicatesCount!==1?"s":""} skipped`),D.totalAttempted>D.importedRulesCount+D.duplicatesCount&&(re+=`, ${D.totalAttempted-D.importedRulesCount-D.duplicatesCount} failed`),t(7,I=re),setTimeout(()=>{t(3,E=!1),t(7,I="")},500)}catch(D){console.error("Failed to process auto-import selection:",D),t(6,M="Failed to import rules. Please try again.")}finally{t(5,T=!1)}},function(){t(3,E=!1),t(6,M=""),t(7,I="")},function(P){$.handleCreateRuleWithName(P.detail)},function(){$.hideCreateRuleDialog()},(P,j)=>{$.updateRuleType(`${mi}/${fi}/${P.path}`,P.content,j)},(P,j)=>{j.stopPropagation(),$.openRule(P.path)},(P,j)=>{j.stopPropagation(),$.deleteRule(P.path)},P=>$.openRule(P.path),()=>$.createRule(),P=>G(P),function(P){Q=P,t(9,Q)},function(P){U=P,c(t(8,U))}]}class nh extends ge{constructor(e){super(),$e(this,e,th,eh,fe,{userGuidelines:0,userGuidelinesLengthLimit:1,updateUserGuideline:2},null,[-1,-1])}}function sh(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=Te(r,n[a]);return{c(){e=Be("svg"),t=new Bn(!0),this.h()},l(a){e=Jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Gn(e);t=Wn(i,!0),i.forEach(v),this.h()},h(){t.a=null,dt(e,r)},m(a,i){Hn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',e)},p(a,[i]){dt(e,r=wt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:W,o:W,d(a){a&&v(e)}}}function rh(s,e,t){return s.$$set=n=>{t(0,e=Te(Te({},e),Qe(n)))},[e=Qe(e)]}class ah extends ge{constructor(e){super(),$e(this,e,rh,sh,fe,{})}}function ih(s){let e;return{c(){e=L("Sign Out")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function oh(s){let e,t;return e=new ah({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ch(s){let e,t;return e=new Ze({props:{loading:s[0],variant:"soft","data-testid":"sign-out-button",$$slots:{iconLeft:[oh],default:[ih]},$$scope:{ctx:s}}}),e.$on("click",s[1]),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.loading=n[0]),8&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function lh(s,e,t){let{onSignOut:n}=e,r=!1;return s.$$set=a=>{"onSignOut"in a&&t(2,n=a.onSignOut)},[r,function(){n(),t(0,r=!0)},n]}class dh extends ge{constructor(e){super(),$e(this,e,lh,ch,fe,{onSignOut:2})}}function uh(s){let e,t;return e=new Um({props:{tools:s[6],onAuthenticate:s[18],onRevokeAccess:s[19],servers:s[7],onMCPServerAdd:s[24],onMCPServerSave:s[25],onMCPServerDelete:s[26],onMCPServerToggleDisable:s[27],onMCPServerJSONImport:s[28],isMCPEnabled:s[8]&&s[2].mcpServerList,isMCPImportEnabled:s[8]&&s[2].mcpServerImport,supportedShells:s[9].supportedShells,selectedShell:s[9].selectedShell,startupScript:s[9].startupScript,onShellSelect:s[20],onStartupScriptChange:s[21],isTerminalEnabled:s[2].terminal}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};64&r[0]&&(a.tools=n[6]),128&r[0]&&(a.servers=n[7]),260&r[0]&&(a.isMCPEnabled=n[8]&&n[2].mcpServerList),260&r[0]&&(a.isMCPImportEnabled=n[8]&&n[2].mcpServerImport),512&r[0]&&(a.supportedShells=n[9].supportedShells),512&r[0]&&(a.selectedShell=n[9].selectedShell),512&r[0]&&(a.startupScript=n[9].startupScript),4&r[0]&&(a.isTerminalEnabled=n[2].terminal),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function ph(s){let e,t;return e=new dh({props:{onSignOut:s[22]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function mh(s){let e,t,n,r;const a=[$h,gh],i=[];function o(c,l){return c[2].rules?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=ke()},m(c,l){i[e].m(c,l),y(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(B(),m(i[d],1,1,()=>{i[d]=null}),J(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&v(n),i[e].d(c)}}}function fh(s){let e,t;return e=new Il({}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p:W,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function hh(s){return{c:W,m:W,p:W,i:W,o:W,d:W}}function gh(s){let e,t;return e=new Hi({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function $h(s){let e,t;return e=new nh({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function vh(s){let e,t,n,r,a;const i=[hh,fh,mh,ph,uh],o=[];function c(l,d){var u,g,f;return 8&d[1]&&(t=null),t==null&&(t=!Pi(l[34])),t?0:((u=l[34])==null?void 0:u.id)==="context"?1:((g=l[34])==null?void 0:g.id)==="guidelines"?2:((f=l[34])==null?void 0:f.id)==="account"?3:4}return n=c(s,[-1,-1]),r=o[n]=i[n](s),{c(){e=C("span"),r.c(),_(e,"slot","content")},m(l,d){y(l,e,d),o[n].m(e,null),a=!0},p(l,d){let u=n;n=c(l,d),n===u?o[n].p(l,d):(B(),m(o[u],1,1,()=>{o[u]=null}),J(),r=o[n],r?r.p(l,d):(r=o[n]=i[n](l),r.c()),p(r,1),r.m(e,null))},i(l){a||(p(r),a=!0)},o(l){m(r),a=!1},d(l){l&&v(e),o[n].d()}}}function yh(s){let e,t;return e=new id({props:{items:s[1],mode:"tree",class:"c-settings-navigation",selectedId:s[0],$$slots:{content:[vh,({item:n})=>({34:n}),({item:n})=>[0,n?8:0]]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){x(e,n,r),t=!0},p(n,r){const a={};2&r[0]&&(a.items=n[1]),1&r[0]&&(a.selectedId=n[0]),1012&r[0]|24&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){b(e,n)}}}function _h(s){let e,t,n,r;return e=new zo.Root({props:{$$slots:{default:[yh]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(a,i){x(e,a,i),t=!0,n||(r=Le(window,"message",s[11].onMessageFromExtension),n=!0)},p(a,i){const o={};1015&i[0]|16&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){b(e,a),n=!1,r()}}}function wh(s,e,t){let n,r,a,i,o,c,l,d,u,g,f=W;s.$$.on_destroy.push(()=>f());const $=new Ho(je),h=new nl(je),k=new sl(je),A=new di(je),E=new Ao,N=new Vo(je,A,E),T=$.getSettingsComponentSupported();ot(s,T,j=>t(2,o=j));const M=$.getEnableAgentMode();ot(s,M,j=>t(8,u=j)),A.registerConsumer($),A.registerConsumer(h),A.registerConsumer(k);const I=k.getTerminalSettings();let U;ot(s,I,j=>t(9,g=j));const Q={handleMessageFromExtension:j=>!(!j.data||j.data.type!==ve.navigateToSettingsSection)&&(j.data.data&&typeof j.data.data=="string"&&t(0,U=j.data.data),!0)};A.registerConsumer(Q);const G=$.getDisplayableTools();ot(s,G,j=>t(6,l=j));const P=$.getGuidelines();return ot(s,P,j=>t(23,c=j)),ci(()=>{$.dispose()}),$.notifyLoaded(),je.postMessage({type:ve.getOrientationStatus}),je.postMessage({type:ve.settingsPanelLoaded}),s.$$.update=()=>{var j,D,re;8388608&s.$$.dirty[0]&&t(5,n=(j=c.userGuidelines)==null?void 0:j.contents),8388608&s.$$.dirty[0]&&t(4,r=(D=c.userGuidelines)==null?void 0:D.lengthLimit),4&s.$$.dirty[0]&&t(1,i=[o.remoteTools?ts("Tools","",cd,"section-tools"):void 0,o.userGuidelines&&!o.rules?ts("User Guidelines","Guidelines for Augment Chat to follow.",io,"guidelines"):void 0,o.rules?ts("Rules and User Guidelines","",Bm,"guidelines"):void 0,o.workspaceContext?{name:"Context",description:"",icon:dd,id:"context"}:void 0,ts("Account","Manage your Augment account settings.",Mo,"account")].filter(Boolean)),3&s.$$.dirty[0]&&i.length>1&&!U&&t(0,U=(re=i[0])==null?void 0:re.id)},t(3,a=h.getServers()),f(),f=Ss(a,j=>t(7,d=j)),[U,i,o,a,r,n,l,d,u,g,h,A,T,M,I,G,P,function(j){const D=j.trim();return!(r&&D.length>r)&&($.updateLocalUserGuidelines(D),je.postMessage({type:ve.updateUserGuidelines,data:j}),!0)},function(j){je.postMessage({type:ve.toolConfigStartOAuth,data:{authUrl:j}}),$.startPolling()},async function(j){await N.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${j.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&je.postMessage({type:ve.toolConfigRevokeAccess,data:{toolId:j.identifier}})},function(j){k.updateSelectedShell(j)},function(j){k.updateStartupScript(j)},function(){je.postMessage({type:ve.signOut})},c,j=>h.addServer(j),j=>h.updateServer(j),j=>h.deleteServer(j),j=>h.toggleDisabledServer(j),j=>h.importServersFromJSON(j)]}class xh extends ge{constructor(e){super(),$e(this,e,wh,_h,fe,{},null,[-1,-1])}}(async function(){je&&je.initialize&&await je.initialize(),new xh({target:document.getElementById("app")})})();
