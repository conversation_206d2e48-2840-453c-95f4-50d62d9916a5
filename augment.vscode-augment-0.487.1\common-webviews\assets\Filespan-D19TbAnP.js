import{S as W,i as b,s as k,T as q,E as B,F as C,u as g,t as x,I as D,$ as y,W as z,J as _,K as w,c as v,a3 as I,e as E,f as m,a0 as F,a1 as J,a2 as K,M as S,h as A}from"./SpinnerAugment-BJAAUt-n.js";import{n as G,g as H,a as L}from"./file-paths-BcSg4gks.js";const N=e=>({}),M=e=>({}),O=e=>({}),T=e=>({});function j(e){let t,n,a;return{c(){t=z("div"),n=z("div"),a=w(e[3]),v(n,"class","c-filespan__dir-text svelte-sprpft"),v(t,"class","c-filespan__dir svelte-sprpft")},m(c,$){E(c,t,$),m(t,n),m(n,a)},p(c,$){8&$&&S(a,c[3])},d(c){c&&A(t)}}}function P(e){let t,n,a,c,$,h,d,r;const u=e[7].leftIcon,l=y(u,e,e[8],T);let p=!e[2]&&j(e);const i=e[7].rightIcon,o=y(i,e,e[8],M);return{c(){t=z("div"),l&&l.c(),n=_(),a=z("span"),c=w(e[4]),$=_(),p&&p.c(),h=_(),o&&o.c(),v(a,"class","c-filespan__filename svelte-sprpft"),v(t,"class",d=I(`c-filespan ${e[0]}`)+" svelte-sprpft")},m(s,f){E(s,t,f),l&&l.m(t,null),m(t,n),m(t,a),m(a,c),m(t,$),p&&p.m(t,null),m(t,h),o&&o.m(t,null),r=!0},p(s,f){l&&l.p&&(!r||256&f)&&F(l,u,s,s[8],r?K(u,s[8],f,O):J(s[8]),T),(!r||16&f)&&S(c,s[4]),s[2]?p&&(p.d(1),p=null):p?p.p(s,f):(p=j(s),p.c(),p.m(t,h)),o&&o.p&&(!r||256&f)&&F(o,i,s,s[8],r?K(i,s[8],f,N):J(s[8]),M),(!r||1&f&&d!==(d=I(`c-filespan ${s[0]}`)+" svelte-sprpft"))&&v(t,"class",d)},i(s){r||(g(l,s),g(o,s),r=!0)},o(s){x(l,s),x(o,s),r=!1},d(s){s&&A(t),l&&l.d(s),p&&p.d(),o&&o.d(s)}}}function Q(e){let t,n;return t=new q({props:{size:e[1],$$slots:{default:[P]},$$scope:{ctx:e}}}),{c(){B(t.$$.fragment)},m(a,c){C(t,a,c),n=!0},p(a,[c]){const $={};2&c&&($.size=a[1]),285&c&&($.$$scope={dirty:c,ctx:a}),t.$set($)},i(a){n||(g(t.$$.fragment,a),n=!0)},o(a){x(t.$$.fragment,a),n=!1},d(a){D(t,a)}}}function R(e,t,n){let a,c,$,{$$slots:h={},$$scope:d}=t,{class:r=""}=t,{filepath:u}=t,{size:l=1}=t,{nopath:p=!1}=t;return e.$$set=i=>{"class"in i&&n(0,r=i.class),"filepath"in i&&n(5,u=i.filepath),"size"in i&&n(1,l=i.size),"nopath"in i&&n(2,p=i.nopath),"$$scope"in i&&n(8,d=i.$$scope)},e.$$.update=()=>{32&e.$$.dirty&&n(6,a=G(u)),64&e.$$.dirty&&n(4,c=H(a)),64&e.$$.dirty&&n(3,$=L(a))},[r,l,p,$,c,u,a,h,d]}class X extends W{constructor(t){super(),b(this,t,R,Q,k,{class:0,filepath:5,size:1,nopath:2})}}export{X as F};
